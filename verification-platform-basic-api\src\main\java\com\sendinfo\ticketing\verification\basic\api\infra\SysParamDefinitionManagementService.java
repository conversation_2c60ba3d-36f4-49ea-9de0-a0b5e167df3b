package com.sendinfo.ticketing.verification.basic.api.infra;

import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamDefinitionUpdateRequest;
import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamDefinitionStatusSwitchRequest;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

/**
 * 系统参数定义管理服务
 * <AUTHOR> 2025-05-19 15:30:00
 */
public interface SysParamDefinitionManagementService {
    /**
     * 更新系统参数定义
     *
     * @param request 更新请求
     * @return 更新后的参数定义
     */
    ResultModel<Boolean> updateSysParamDefinition(SysParamDefinitionUpdateRequest request);

    /**
     * 启用系统参数定义
     *
     * @param request 状态切换请求
     * @return 操作结果
     */
    ResultModel<Boolean> enableSysParamDefinition(SysParamDefinitionStatusSwitchRequest request);
    
    /**
     * 禁用系统参数定义
     *
     * @param request 状态切换请求
     * @return 操作结果
     */
    ResultModel<Boolean> disableSysParamDefinition(SysParamDefinitionStatusSwitchRequest request);
} 