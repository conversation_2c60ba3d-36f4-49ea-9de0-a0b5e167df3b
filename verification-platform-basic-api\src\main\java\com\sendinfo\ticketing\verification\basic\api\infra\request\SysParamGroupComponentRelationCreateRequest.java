/**
 * System parameter group component relation create request
 *
 * <AUTHOR> 2025-01-24 15:30:00
 */
package com.sendinfo.ticketing.verification.basic.api.infra.request;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 系统参数分组组件关联创建请求
 */
@Getter
@Setter
@ToString
public class SysParamGroupComponentRelationCreateRequest implements Serializable {
    private static final long serialVersionUID = -7654321098765432107L;

    /**
     * 企业编码
     */
    @NotBlank(message = "企业编码不能为空")
    private String corpCode;

    /**
     * 分组编码
     */
    @NotBlank(message = "分组编码不能为空")
    @Size(max = 64, message = "分组编码长度不能超过64")
    private String groupCode;

    /**
     * 组件编码列表
     */
    @NotEmpty(message = "组件编码必选")
    @Size(min = 1, max = 10, message = "每次最多绑定10个")
    private List<String> componentCodeList;

    /**
     * 创建人
     */
    @NotBlank(message = "创建人不能为空")
    private String createBy;
}