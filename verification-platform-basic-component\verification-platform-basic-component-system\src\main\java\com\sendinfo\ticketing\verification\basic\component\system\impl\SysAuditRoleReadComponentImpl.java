package com.sendinfo.ticketing.verification.basic.component.system.impl;

import com.sendinfo.ticketing.verification.basic.component.system.SysAuditRoleReadComponent;
import com.sendinfo.ticketing.verification.basic.component.system.converter.SysAuditRoleConverter;
import com.sendinfo.ticketing.verification.basic.component.system.param.SysAuditRoleQueryParam;
import com.sendinfo.ticketing.verification.basic.model.system.SysAuditRole;
import com.sendinfo.ticketing.verification.basic.repository.system.arg.SysAuditRoleQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.system.dao.SysAuditRoleDao;
import com.sendinfo.ticketing.verification.basic.repository.system.dataobject.SysAuditRoleDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedCountRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedListRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleRead;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * 票型角色读取组件实现
 * <AUTHOR>
 */
@Getter
@Component("sysAuditRoleReadComponent")
public class SysAuditRoleReadComponentImpl implements SysAuditRoleReadComponent,
        DaoBasedSingleRead<Integer, SysAuditRoleQueryParam, SysAuditRole, SysAuditRoleDO>,
        DaoBasedCountRead<Integer, SysAuditRoleQueryParam, SysAuditRole, SysAuditRoleQueryArg>,
        DaoBasedListRead<Integer, SysAuditRoleQueryParam, SysAuditRole, SysAuditRoleDO, SysAuditRoleQueryArg> {

    private final SysAuditRoleDao dao;
    private final SysAuditRoleConverter converter;

    public SysAuditRoleReadComponentImpl(SysAuditRoleDao dao, SysAuditRoleConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }

    @Override
    public List<SysAuditRole> querySysAuditRoles(SysAuditRoleQueryParam queryParam) {
        List<SysAuditRoleDO> sysAuditRoleDOList = dao.queryByArg(converter.r_p2a(queryParam));
        return converter.r_ds2ms(sysAuditRoleDOList);
    }

    @Override
    public List<SysAuditRole> querySysAuditRoleByIds(Set<Integer> ids, String corpCode) {
        return dao.querySysAuditRoleByIds(ids, corpCode);
    }
} 