package com.sendinfo.ticketing.verification.basic.repository.goods.arg;

import com.sendinfo.ticketing.verification.common.repository.arg.AbstractUpdateArg;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
public class TktModelParamUpdateArg extends AbstractUpdateArg<Long> {
    private String corpCode;
    private String title;
    private String modelType;
    private Integer modelKind;
    private String backImg;
    private String pageContent;
    private String hnPrintContent;
    private String printContent;
    private Integer width;
    private Integer height;
    private Integer printDirection;
    private String pageInfo;
    private String useFlag;
    private String modelCode;
    private String continuousPrint;
    private String modifyBy;
}
