package com.sendinfo.ticketing.verification.basic.repository.pay.dataobject;

import com.sendinfo.ticketing.verification.common.repository.dataobject.AbstractTenantBaseDO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 支付渠道数据对象
 * 对应数据库表 pay_chanle
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class PayChanleDO extends AbstractTenantBaseDO<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 渠道名称：建行、农行
     */
    private String chanleName;

    /**
     * 渠道编码:对应支付中心的渠道产品编号
     */
    private String chanleCode;

    /**
     * 支付产品码：对应支付中心的支付产品码
     */
    private String payProductCode;

    /**
     * 备注
     */
    private String remark;

    /**
     * 启用标识T：启用，F：禁用
     */
    private String useFlag;
} 