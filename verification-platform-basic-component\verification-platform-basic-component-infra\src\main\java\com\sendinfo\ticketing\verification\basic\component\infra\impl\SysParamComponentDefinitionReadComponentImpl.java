package com.sendinfo.ticketing.verification.basic.component.infra.impl;

import lombok.Getter;
import org.springframework.stereotype.Component;

import com.sendinfo.ticketing.verification.basic.component.infra.SysParamComponentDefinitionReadComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.converter.SysParamComponentDefinitionConverter;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamComponentDefinitionQueryParam;
import com.sendinfo.ticketing.verification.basic.model.infra.SysParamComponentDefinition;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamComponentDefinitionQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.SysParamComponentDefinitionDao;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.SysParamComponentDefinitionDO;

import java.util.List;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedCountRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedPageRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleRead;

/**
 * 系统参数组件定义查询组件实现
 */
@Component("sysParamComponentDefinitionReadComponent")
@Getter
public class SysParamComponentDefinitionReadComponentImpl
        implements SysParamComponentDefinitionReadComponent,
        DaoBasedPageRead<Long, SysParamComponentDefinitionQueryParam, SysParamComponentDefinition, SysParamComponentDefinitionDO, SysParamComponentDefinitionQueryArg>,
        DaoBasedSingleRead<Long, SysParamComponentDefinitionQueryParam, SysParamComponentDefinition, SysParamComponentDefinitionDO>,
        DaoBasedCountRead<Long, SysParamComponentDefinitionQueryParam, SysParamComponentDefinition, SysParamComponentDefinitionQueryArg> {

    private final SysParamComponentDefinitionDao dao;
    private final SysParamComponentDefinitionConverter converter;

    public SysParamComponentDefinitionReadComponentImpl(SysParamComponentDefinitionDao dao,
            SysParamComponentDefinitionConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }

    @Override
    public SysParamComponentDefinition queryByComponentCode(String componentCode, String corpCode) {
        SysParamComponentDefinitionDO dataObject = dao.queryByComponentCode(componentCode, corpCode);
        return dataObject != null ? converter.r_d2m(dataObject) : null;
    }

    @Override
    public List<SysParamComponentDefinition> queryByComponentCodeList(List<String> componentCodeList, String corpCode) {
        List<SysParamComponentDefinitionDO> dataObjects = dao.queryByComponentCodeList(componentCodeList, corpCode);
        return converter.r_ds2ms(dataObjects);
    }

    @Override
    public int countByCategoryId(Long categoryId) {
        return dao.countByCategoryId(categoryId);
    }
}
