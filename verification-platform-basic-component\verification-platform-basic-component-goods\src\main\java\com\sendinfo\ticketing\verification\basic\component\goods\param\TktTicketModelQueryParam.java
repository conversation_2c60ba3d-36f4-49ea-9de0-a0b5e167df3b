package com.sendinfo.ticketing.verification.basic.component.goods.param;

import com.sendinfo.ticketing.verification.common.component.param.AbstractQueryParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Auther: chengjie.li
 * @Date: 2025/6/10 18:02
 */
@Getter
@Setter
@ToString(callSuper = true)
public class TktTicketModelQueryParam extends AbstractQueryParam {
    /**
     * 租户标识，必填，确保租户数据隔离
     */
    private String corpCode;

    /**
     * 票型编码
     */
    private String ticketCode;
}
