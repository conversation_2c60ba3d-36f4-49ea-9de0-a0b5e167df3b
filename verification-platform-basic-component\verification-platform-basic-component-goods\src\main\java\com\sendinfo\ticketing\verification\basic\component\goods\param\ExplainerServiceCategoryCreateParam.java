package com.sendinfo.ticketing.verification.basic.component.goods.param;

import com.sendinfo.ticketing.verification.common.component.param.AbstractCreateParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 服务类目创建参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class ExplainerServiceCategoryCreateParam extends AbstractCreateParam {

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 业态(系统字典)
     */
    private String businessType;

    /**
     * 服务编码
     */
    private String categoryNo;

    /**
     * 服务类型名称
     */
    private String categoryName;

    /**
     * 状态F禁用 T启用
     */
    private String useFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String createBy;
} 