package com.sendinfo.ticketing.verification.basic.model.pay;

import com.sendinfo.ticketing.verification.basic.model.pay.properties.MainbodyChanleDefinitionPropertyKey;
import com.sendinfo.ticketing.verification.common.model.properties.AbstractProperties;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 商户渠道配置模型对象
 * 用于业务逻辑层和数据展示层之间的数据传输
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class MainbodyChanle extends AbstractProperties<MainbodyChanleDefinitionPropertyKey> implements Serializable {

    private static final long serialVersionUID = 3837910077687338173L;
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 支付渠道ID
     */
    private Long payChanleId;

    /**
     * 主体ID
     */
    private Long mainbodyId;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String modifyBy;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 删除标志F未删除,T已删除
     */
    private String deleted;
} 