package com.sendinfo.ticketing.verification.basic.api.config.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2025/6/2 16:05
 **/
@Getter
@Setter
@ToString
public class TenantTesbConfigQueryCondition implements Serializable {
    private static final long serialVersionUID = -4759745866911163470L;

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 配置编码
     */
    private String configCode;

    /**
     * 配置名称，精确匹配
     */
    private String configName;

    /**
     * 配置父ID
     */
    private Long configParentId;

    /**
     * 状态：1-启用，0-禁用
     */
    private int status;
}
