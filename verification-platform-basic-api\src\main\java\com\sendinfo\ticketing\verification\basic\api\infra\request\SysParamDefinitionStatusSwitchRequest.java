package com.sendinfo.ticketing.verification.basic.api.infra.request;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 系统参数定义状态切换请求
 */
@Getter
@Setter
@ToString
public class SysParamDefinitionStatusSwitchRequest implements Serializable {
    private static final long serialVersionUID = -5432109876543210987L;

    /**
     * 主键ID
     */
    @NotNull(message = "ID不能为空")
    private Long id;

    /**
     * 目标状态：1-启用，0-禁用
     */
    @NotNull(message = "目标状态不能为空")
    private Integer targetStatus;

    /**
     * 修改人
     */
    @NotNull(message = "修改人不能为空")
    private String modifyBy;
} 