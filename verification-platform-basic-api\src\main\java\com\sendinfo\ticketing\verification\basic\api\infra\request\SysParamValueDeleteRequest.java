package com.sendinfo.ticketing.verification.basic.api.infra.request;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 *  系统参数值删除请求
 *
 *  <AUTHOR> 2025-05-20 15:40:00
 */
@Getter
@Setter
@ToString
public class SysParamValueDeleteRequest implements Serializable {
    private static final long serialVersionUID = -7654321098765432104L;

    /**
     * 主键ID
     */
    @NotNull(message = "ID不能为空")
    private Long id;

    /**
     * 企业编码
     */
    @NotBlank(message = "企业编码不能为空")
    private String corpCode;

    /**
     * 修改人
     */
    @NotBlank(message = "修改人不能为空")
    private String modifyBy;
} 