package com.sendinfo.ticketing.verification.basic.api.infra;

import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamNamespaceDeleteRequest;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

/**
 * 参数命名空间删除服务
 * <AUTHOR> 2025-07-21
 */
public interface SysParamNamespaceDeleteService {
    /**
     * 删除参数命名空间
     *
     * @param request 删除请求
     * @return 操作结果
     */
    ResultModel<String> deleteSysParamNamespace(SysParamNamespaceDeleteRequest request);
}