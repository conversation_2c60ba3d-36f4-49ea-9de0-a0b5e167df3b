package com.sendinfo.ticketing.verification.basic.component.customer;

import com.sendinfo.ticketing.verification.basic.component.customer.param.SalesmanRelationTravelQueryParam;
import com.sendinfo.ticketing.verification.basic.model.customer.SalesmanRelationTravel;
import com.sendinfo.ticketing.verification.common.component.ReadComponent;

import java.util.List;

/**
 * 业务员关联客户读取组件接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface SalesmanRelationTravelReadComponent extends ReadComponent<Long, SalesmanRelationTravelQueryParam, SalesmanRelationTravel> {


    /**
     * 根据客户ID查询业务员关联客户列表
     *
     * @param travelId 客户ID
     * @param corpCode 企业编码
     * @return 业务员关联客户列表
     */
    List<SalesmanRelationTravel> queryByTravelId(Long travelId, String corpCode);
} 