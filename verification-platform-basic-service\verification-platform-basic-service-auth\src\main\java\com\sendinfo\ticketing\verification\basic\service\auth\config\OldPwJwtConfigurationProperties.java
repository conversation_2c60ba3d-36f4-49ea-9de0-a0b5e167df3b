package com.sendinfo.ticketing.verification.basic.service.auth.config;

import io.jsonwebtoken.security.Keys;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.annotation.Validated;

import javax.annotation.PostConstruct;
import javax.crypto.SecretKey;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * <AUTHOR>
 * @since 2025-07-23 13:17:43
 */
@Configuration("OldPwJwtConfigurationProperties")
@ConfigurationProperties(prefix = "verification.old-pw.jwt")
@Validated
@Getter
@Setter
@ToString
@Slf4j
public class OldPwJwtConfigurationProperties {
    @NotEmpty(message = "JWT secret cannot be empty")
    private String secret;

    @NotNull(message = "JWT expiration time cannot be null")
    @Min(value = 60000, message = "JWT expiration time must be at least 60000ms (1 minute)")
    private Long expirationMs;

    @NotNull(message = "JWT head is null")
    private String headKey = "access-token";

    private SecretKey signingKey;

    /**
     * Initializes the signing key after properties are bound.
     * Decodes the Base64 secret and ensures it's strong enough for HS256.
     */
    @PostConstruct
    private void initSigningKey() {
        try {
            byte[] decodedKey = Base64.getDecoder().decode(secret.getBytes(StandardCharsets.UTF_8));
            this.signingKey = Keys.hmacShaKeyFor(decodedKey);
            log.info("JWT signing key initialized successfully.");
        } catch (IllegalArgumentException e) {
            log.error("Error initializing JWT signing key: {}", e.getMessage());
            // Rethrow or handle appropriately to potentially prevent startup
            throw new IllegalStateException("Invalid JWT secret configuration", e);
        }
    }
}
