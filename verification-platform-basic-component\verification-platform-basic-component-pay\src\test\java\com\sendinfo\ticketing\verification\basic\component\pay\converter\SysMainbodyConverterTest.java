package com.sendinfo.ticketing.verification.basic.component.pay.converter;

import com.sendinfo.ticketing.verification.basic.component.pay.param.SysMainbodyCreateParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.SysMainbodyQueryParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.SysMainbodyUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.pay.SysMainbody;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysMainbodyDO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;

/**
 * SysMainbodyConverter单元测试
 * 测试经营主体转换器的各种转换功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RunWith(MockitoJUnitRunner.class)
public class SysMainbodyConverterTest {

    @InjectMocks
    private SysMainbodyConverter sysMainbodyConverter;

    // 测试常量
    private static final String TEST_CORP_CODE = "TEST_CORP_001";
    private static final Long TEST_ID = 1L;
    private static final String TEST_MAINBODY_NAME = "测试经营主体";
    private static final String TEST_MAINBODY_SHORT_NAME = "测试主体";
    private static final String TEST_MERCHANT_NO = "TEST_MERCHANT_001";
    private static final String TEST_LINK_MAN = "张三";
    private static final String TEST_LINK_TEL = "13800138000";
    private static final String TEST_REMARK = "这是一个测试经营主体";
    private static final String TEST_MAINBODY_NUMBER = "MB_001";
    private static final Long TEST_DEPT_ID = 100L;
    private static final String TEST_CREATE_BY = "testUser";
    private static final String TEST_MODIFY_BY = "testUser";

    /**
     * 测试目的：验证SysMainbodyCreateParam转换为SysMainbodyDO的功能
     * 测试步骤：
     * 1. 创建完整的SysMainbodyCreateParam对象
     * 2. 调用c_p2d方法进行转换
     * 3. 验证转换结果的正确性
     */
    @Test
    public void testCreateParam2Do_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        SysMainbodyCreateParam createParam = createSysMainbodyCreateParam();

        // 2. 执行转换
        SysMainbodyDO result = sysMainbodyConverter.c_p2d(createParam);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", result);
        assertEquals("经营主体名称应该一致", TEST_MAINBODY_NAME, result.getMainbodyName());
        assertEquals("经营主体简称应该一致", TEST_MAINBODY_SHORT_NAME, result.getMainbodyShortName());
        assertEquals("主体商户号应该一致", TEST_MERCHANT_NO, result.getMerchantNo());
        assertEquals("联系人应该一致", TEST_LINK_MAN, result.getLinkMan());
        assertEquals("联系电话应该一致", TEST_LINK_TEL, result.getLinkTel());
        assertEquals("备注应该一致", TEST_REMARK, result.getRemark());
        assertEquals("经营主体编号应该一致", TEST_MAINBODY_NUMBER, result.getMainbodyNumber());
        assertEquals("部门ID应该一致", TEST_DEPT_ID, result.getDeptId());
        assertEquals("创建人应该一致", TEST_CREATE_BY, result.getCreateBy());
        assertEquals("修改人应该一致", TEST_MODIFY_BY, result.getModifyBy());
    }

    /**
     * 测试目的：验证SysMainbodyCreateParam为null时的处理
     */
    @Test
    public void testCreateParam2Do_WithNullInput_ShouldReturnNull() {
        // 1. 执行转换
        SysMainbodyDO result = sysMainbodyConverter.c_p2d(null);

        // 2. 验证结果
        assertNull("输入为null时应该返回null", result);
    }

    /**
     * 测试目的：验证SysMainbodyDO转换为SysMainbody的功能
     * 测试步骤：
     * 1. 创建完整的SysMainbodyDO对象
     * 2. 调用r_d2m方法进行转换
     * 3. 验证转换结果的正确性
     */
    @Test
    public void testReadDo2Model_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        SysMainbodyDO sysMainbodyDO = createSysMainbodyDO();

        // 2. 执行转换
        SysMainbody result = sysMainbodyConverter.r_d2m(sysMainbodyDO);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", result);
        assertEquals("ID应该一致", TEST_ID, result.getId());
        assertEquals("企业编码应该一致", TEST_CORP_CODE, result.getCorpCode());
        assertEquals("经营主体名称应该一致", TEST_MAINBODY_NAME, result.getMainbodyName());
        assertEquals("经营主体简称应该一致", TEST_MAINBODY_SHORT_NAME, result.getMainbodyShortName());
        assertEquals("主体商户号应该一致", TEST_MERCHANT_NO, result.getMerchantNo());
        assertEquals("联系人应该一致", TEST_LINK_MAN, result.getLinkMan());
        assertEquals("联系电话应该一致", TEST_LINK_TEL, result.getLinkTel());
        assertEquals("备注应该一致", TEST_REMARK, result.getRemark());
        assertEquals("经营主体编号应该一致", TEST_MAINBODY_NUMBER, result.getMainbodyNumber());
        assertEquals("部门ID应该一致", TEST_DEPT_ID, result.getDeptId());
        assertEquals("创建人应该一致", TEST_CREATE_BY, result.getCreateBy());
        assertEquals("修改人应该一致", TEST_MODIFY_BY, result.getModifyBy());
    }

    /**
     * 测试目的：验证SysMainbodyDO为null时的处理
     */
    @Test
    public void testReadDo2Model_WithNullInput_ShouldReturnNull() {
        // 1. 执行转换
        SysMainbody result = sysMainbodyConverter.r_d2m(null);

        // 2. 验证结果
        assertNull("输入为null时应该返回null", result);
    }

    /**
     * 测试目的：验证List<SysMainbodyDO>转换为List<SysMainbody>的功能
     * 测试步骤：
     * 1. 创建包含多个SysMainbodyDO的列表
     * 2. 调用r_ds2ms方法进行转换
     * 3. 验证转换结果的正确性
     */
    @Test
    public void testReadDoList2ModelList_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        List<SysMainbodyDO> sysMainbodyDOList = Arrays.asList(
            createSysMainbodyDO(),
            createSysMainbodyDO()
        );

        // 2. 执行转换
        List<SysMainbody> result = sysMainbodyConverter.r_ds2ms(sysMainbodyDOList);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", result);
        assertEquals("列表大小应该一致", 2, result.size());
        
        // 验证第一个元素
        SysMainbody firstResult = result.get(0);
        assertEquals("第一个元素的ID应该一致", TEST_ID, firstResult.getId());
        assertEquals("第一个元素的经营主体名称应该一致", TEST_MAINBODY_NAME, firstResult.getMainbodyName());
        
        // 验证第二个元素
        SysMainbody secondResult = result.get(1);
        assertEquals("第二个元素的ID应该一致", TEST_ID, secondResult.getId());
        assertEquals("第二个元素的经营主体名称应该一致", TEST_MAINBODY_NAME, secondResult.getMainbodyName());
    }

    /**
     * 测试目的：验证List<SysMainbodyDO>为null时的处理
     */
    @Test
    public void testReadDoList2ModelList_WithNullInput_ShouldReturnNull() {
        // 1. 执行转换
        List<SysMainbody> result = sysMainbodyConverter.r_ds2ms(null);

        // 2. 验证结果
        assertNull("输入为null时应该返回null", result);
    }

    /**
     * 测试目的：验证空列表的处理
     */
    @Test
    public void testReadDoList2ModelList_WithEmptyList_ShouldReturnEmptyList() {
        // 1. 创建空列表
        List<SysMainbodyDO> emptyList = Arrays.asList();

        // 2. 执行转换
        List<SysMainbody> result = sysMainbodyConverter.r_ds2ms(emptyList);

        // 3. 验证结果
        assertNotNull("结果不应为null", result);
        assertTrue("结果应该是空列表", result.isEmpty());
    }

    /**
     * 测试目的：验证SysMainbodyQueryParam转换为SysMainbodyQueryArg的功能
     * 测试步骤：
     * 1. 创建完整的SysMainbodyQueryParam对象
     * 2. 调用r_p2a方法进行转换
     * 3. 验证转换结果的正确性，包括分页参数处理
     */
    @Test
    public void testReadQueryParam2QueryArg_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        SysMainbodyQueryParam queryParam = createSysMainbodyQueryParam();

        // 2. 执行转换
        SysMainbodyQueryArg result = sysMainbodyConverter.r_p2a(queryParam);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", result);
        assertEquals("企业编码应该一致", TEST_CORP_CODE, result.getCorpCode());
        assertEquals("经营主体名称应该一致", TEST_MAINBODY_NAME, result.getMainbodyName());
        assertEquals("经营主体简称应该一致", TEST_MAINBODY_SHORT_NAME, result.getMainbodyShortName());
        assertEquals("主体商户号应该一致", TEST_MERCHANT_NO, result.getMerchantNo());
        assertEquals("联系人应该一致", TEST_LINK_MAN, result.getLinkMan());
        assertEquals("联系电话应该一致", TEST_LINK_TEL, result.getLinkTel());
        assertEquals("备注应该一致", TEST_REMARK, result.getRemark());
        assertEquals("经营主体编号应该一致", TEST_MAINBODY_NUMBER, result.getMainbodyNumber());
        assertEquals("部门ID应该一致", TEST_DEPT_ID, result.getDeptId());
        
        // 验证分页参数
        assertEquals("偏移量应该一致", Integer.valueOf(0), result.getOffset());
        assertEquals("限制数量应该一致", Integer.valueOf(10), result.getLimit());
    }

    /**
     * 测试目的：验证SysMainbodyQueryParam为null时的处理
     */
    @Test
    public void testReadQueryParam2QueryArg_WithNullInput_ShouldReturnNull() {
        // 1. 执行转换
        SysMainbodyQueryArg result = sysMainbodyConverter.r_p2a(null);

        // 2. 验证结果
        assertNull("输入为null时应该返回null", result);
    }

    /**
     * 测试目的：验证SysMainbodyUpdateParam转换为SysMainbodyUpdateArg的功能
     * 测试步骤：
     * 1. 创建完整的SysMainbodyUpdateParam对象
     * 2. 调用u_p2a方法进行转换
     * 3. 验证转换结果的正确性
     */
    @Test
    public void testUpdateParam2UpdateArg_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        SysMainbodyUpdateParam updateParam = createSysMainbodyUpdateParam();
        SysMainbody currentModel = createSysMainbody();

        // 2. 执行转换
        SysMainbodyUpdateArg result = sysMainbodyConverter.u_p2a(updateParam, currentModel);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", result);
        // AbstractUpdateArg没有getId方法，跳过ID验证
        assertEquals("经营主体名称应该一致", TEST_MAINBODY_NAME, result.getMainbodyName());
        assertEquals("经营主体简称应该一致", TEST_MAINBODY_SHORT_NAME, result.getMainbodyShortName());
        assertEquals("主体商户号应该一致", TEST_MERCHANT_NO, result.getMerchantNo());
        assertEquals("联系人应该一致", TEST_LINK_MAN, result.getLinkMan());
        assertEquals("联系电话应该一致", TEST_LINK_TEL, result.getLinkTel());
        assertEquals("备注应该一致", TEST_REMARK, result.getRemark());
        assertEquals("经营主体编号应该一致", TEST_MAINBODY_NUMBER, result.getMainbodyNumber());
        assertEquals("部门ID应该一致", TEST_DEPT_ID, result.getDeptId());
    }

    /**
     * 测试目的：验证SysMainbodyUpdateParam为null时的处理
     */
    @Test
    public void testUpdateParam2UpdateArg_WithNullInput_ShouldReturnNull() {
        // 1. 创建当前模型
        SysMainbody currentModel = createSysMainbody();

        // 2. 执行转换
        SysMainbodyUpdateArg result = sysMainbodyConverter.u_p2a(null, currentModel);

        // 3. 验证结果
        assertNull("输入为null时应该返回null", result);
    }

    // ==================== 测试数据构建方法 ====================

    /**
     * 创建SysMainbodyCreateParam测试数据
     */
    private SysMainbodyCreateParam createSysMainbodyCreateParam() {
        SysMainbodyCreateParam createParam = new SysMainbodyCreateParam();
        createParam.setMainbodyName(TEST_MAINBODY_NAME);
        createParam.setMainbodyShortName(TEST_MAINBODY_SHORT_NAME);
        createParam.setMerchantNo(TEST_MERCHANT_NO);
        createParam.setLinkMan(TEST_LINK_MAN);
        createParam.setLinkTel(TEST_LINK_TEL);
        createParam.setRemark(TEST_REMARK);
        createParam.setMainbodyNumber(TEST_MAINBODY_NUMBER);
        createParam.setDeptId(TEST_DEPT_ID);
        createParam.setCreateBy(TEST_CREATE_BY);
        createParam.setModifyBy(TEST_MODIFY_BY);
        return createParam;
    }

    /**
     * 创建SysMainbodyDO测试数据
     */
    private SysMainbodyDO createSysMainbodyDO() {
        SysMainbodyDO sysMainbodyDO = new SysMainbodyDO();
        sysMainbodyDO.setId(TEST_ID);
        sysMainbodyDO.setCorpCode(TEST_CORP_CODE);
        sysMainbodyDO.setMainbodyName(TEST_MAINBODY_NAME);
        sysMainbodyDO.setMainbodyShortName(TEST_MAINBODY_SHORT_NAME);
        sysMainbodyDO.setMerchantNo(TEST_MERCHANT_NO);
        sysMainbodyDO.setLinkMan(TEST_LINK_MAN);
        sysMainbodyDO.setLinkTel(TEST_LINK_TEL);
        sysMainbodyDO.setRemark(TEST_REMARK);
        sysMainbodyDO.setMainbodyNumber(TEST_MAINBODY_NUMBER);
        sysMainbodyDO.setDeptId(TEST_DEPT_ID);
        sysMainbodyDO.setCreateBy(TEST_CREATE_BY);
        sysMainbodyDO.setModifyBy(TEST_MODIFY_BY);
        return sysMainbodyDO;
    }

    /**
     * 创建SysMainbodyQueryParam测试数据
     */
    private SysMainbodyQueryParam createSysMainbodyQueryParam() {
        SysMainbodyQueryParam queryParam = new SysMainbodyQueryParam();
        queryParam.setCorpCode(TEST_CORP_CODE);
        queryParam.setMainbodyName(TEST_MAINBODY_NAME);
        queryParam.setMainbodyShortName(TEST_MAINBODY_SHORT_NAME);
        queryParam.setMerchantNo(TEST_MERCHANT_NO);
        queryParam.setLinkMan(TEST_LINK_MAN);
        queryParam.setLinkTel(TEST_LINK_TEL);
        queryParam.setRemark(TEST_REMARK);
        queryParam.setMainbodyNumber(TEST_MAINBODY_NUMBER);
        queryParam.setDeptId(TEST_DEPT_ID);
        queryParam.setStartIndex(0);
        queryParam.setPageSize(10);
        return queryParam;
    }

    /**
     * 创建SysMainbodyUpdateParam测试数据
     */
    private SysMainbodyUpdateParam createSysMainbodyUpdateParam() {
        SysMainbodyUpdateParam updateParam = new SysMainbodyUpdateParam();
        updateParam.setCorpCode(TEST_CORP_CODE);
        updateParam.setMainbodyName(TEST_MAINBODY_NAME);
        updateParam.setMainbodyShortName(TEST_MAINBODY_SHORT_NAME);
        updateParam.setMerchantNo(TEST_MERCHANT_NO);
        updateParam.setLinkMan(TEST_LINK_MAN);
        updateParam.setLinkTel(TEST_LINK_TEL);
        updateParam.setRemark(TEST_REMARK);
        updateParam.setMainbodyNumber(TEST_MAINBODY_NUMBER);
        updateParam.setDeptId(TEST_DEPT_ID);
        return updateParam;
    }

    /**
     * 创建SysMainbody测试数据
     */
    private SysMainbody createSysMainbody() {
        SysMainbody sysMainbody = new SysMainbody();
        sysMainbody.setId(TEST_ID);
        sysMainbody.setCorpCode(TEST_CORP_CODE);
        sysMainbody.setMainbodyName(TEST_MAINBODY_NAME);
        sysMainbody.setMainbodyShortName(TEST_MAINBODY_SHORT_NAME);
        sysMainbody.setMerchantNo(TEST_MERCHANT_NO);
        sysMainbody.setLinkMan(TEST_LINK_MAN);
        sysMainbody.setLinkTel(TEST_LINK_TEL);
        sysMainbody.setRemark(TEST_REMARK);
        sysMainbody.setMainbodyNumber(TEST_MAINBODY_NUMBER);
        sysMainbody.setDeptId(TEST_DEPT_ID);
        sysMainbody.setCreateBy(TEST_CREATE_BY);
        sysMainbody.setModifyBy(TEST_MODIFY_BY);
        return sysMainbody;
    }

} 