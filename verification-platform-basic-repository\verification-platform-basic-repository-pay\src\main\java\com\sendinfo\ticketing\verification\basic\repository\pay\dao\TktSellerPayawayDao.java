package com.sendinfo.ticketing.verification.basic.repository.pay.dao;

import com.sendinfo.ticketing.verification.basic.repository.pay.arg.TktSellerPayawayDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.TktSellerPayawayQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.TktSellerPayawayUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.TktSellerPayawayDO;
import com.sendinfo.ticketing.verification.common.repository.BatchInsertDAO;
import com.sendinfo.ticketing.verification.common.repository.CountableDAO;
import com.sendinfo.ticketing.verification.common.repository.GenericDAO;
import com.sendinfo.ticketing.verification.common.repository.QueryableDAO;

import java.util.List;

/**
 * 售票员收款方式数据访问接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface TktSellerPayawayDao extends GenericDAO<Long, TktSellerPayawayDO, TktSellerPayaway<PERSON>pdate<PERSON><PERSON>, TktSellerPayawayDeleteArg>,
        CountableDAO<TktSellerPayawayQueryArg>,
        QueryableDAO<TktSellerPayawayQueryArg, TktSellerPayawayDO>,
        BatchInsertDAO<Long, TktSellerPayawayDO> {

    /**
     * 根据账户ID查询售票员收款方式列表
     *
     * @param accId    账户ID
     * @param corpCode 企业编码
     * @return 售票员收款方式列表
     */
    List<TktSellerPayawayDO> queryByAccId(Long accId, String corpCode);

    /**
     * 根据参数查询售票员启用的支付方式
     *
     * @param accId      售票员ID
     * @param clientType 客户类型
     * @param saleModel  售票模式
     * @param corpCode   企业编码
     * @return 售票员启用的支付方式
     */
    List<TktSellerPayawayDO> queryEnableSellerPayAwayList(Long accId, Integer clientType, Integer saleModel, String corpCode);
}