/**
 *  企业编码工具类测试
 *
 *  <AUTHOR> 2025-07-29 16:30:00
 */
package com.sendinfo.ticketing.verification.basic.common.util;

import com.sendinfo.ticketing.verification.basic.model.corp.CorpInfo;
import org.junit.Assert;
import org.junit.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 企业编码工具类测试
 * 
 * <AUTHOR> 2025-07-29 16:30:00
 */
public class CorpCodeUtilTest {

    @Test
    public void testParseParentCorpCodes_ThreeLevel() {
        // Given
        String corpCode = "0001/324123/12312";

        // When
        List<String> result = CorpCodeUtil.parseParentCorpCodes(corpCode);

        // Then
        List<String> expected = Arrays.asList("0001/324123", "0001");
        Assert.assertEquals(expected, result);
    }

    @Test
    public void testParseParentCorpCodes_TwoLevel() {
        // Given
        String corpCode = "0001/324123";

        // When
        List<String> result = CorpCodeUtil.parseParentCorpCodes(corpCode);

        // Then
        List<String> expected = Arrays.asList("0001");
        Assert.assertEquals(expected, result);
    }

    @Test
    public void testParseParentCorpCodes_OneLevel() {
        // Given
        String corpCode = "0001";

        // When
        List<String> result = CorpCodeUtil.parseParentCorpCodes(corpCode);

        // Then
        Assert.assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testParseParentCorpCodes_EmptyString() {
        // Given
        String corpCode = "";

        // When
        List<String> result = CorpCodeUtil.parseParentCorpCodes(corpCode);

        // Then
        Assert.assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testParseParentCorpCodes_Null() {
        // Given
        String corpCode = null;

        // When
        List<String> result = CorpCodeUtil.parseParentCorpCodes(corpCode);

        // Then
        Assert.assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testParseParentCorpCodes_WithSpaces() {
        // Given
        String corpCode = "  0001/324123/12312  ";

        // When
        List<String> result = CorpCodeUtil.parseParentCorpCodes(corpCode);

        // Then
        List<String> expected = Arrays.asList("0001/324123", "0001");
        Assert.assertEquals(expected, result);
    }

    @Test
    public void testParseAllLevelCorpCodes_ThreeLevel() {
        // Given
        String corpCode = "0001/324123/12312";

        // When
        List<String> result = CorpCodeUtil.parseAllLevelCorpCodes(corpCode);

        // Then
        List<String> expected = Arrays.asList("0001/324123/12312", "0001/324123", "0001");
        Assert.assertEquals(expected, result);
    }

    @Test
    public void testParseAllLevelCorpCodes_OneLevel() {
        // Given
        String corpCode = "0001";

        // When
        List<String> result = CorpCodeUtil.parseAllLevelCorpCodes(corpCode);

        // Then
        List<String> expected = Arrays.asList("0001");
        Assert.assertEquals(expected, result);
    }

    @Test
    public void testParseAllLevelCorpCodes_Empty() {
        // Given
        String corpCode = "";

        // When
        List<String> result = CorpCodeUtil.parseAllLevelCorpCodes(corpCode);

        // Then
        Assert.assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testGetDirectParentCorpCode_ThreeLevel() {
        // Given
        String corpCode = "0001/324123/12312";

        // When
        String result = CorpCodeUtil.getDirectParentCorpCode(corpCode);

        // Then
        Assert.assertEquals("0001/324123", result);
    }

    @Test
    public void testGetDirectParentCorpCode_OneLevel() {
        // Given
        String corpCode = "0001";

        // When
        String result = CorpCodeUtil.getDirectParentCorpCode(corpCode);

        // Then
        Assert.assertNull(result);
    }

    @Test
    public void testGetTopLevelCorpCode_ThreeLevel() {
        // Given
        String corpCode = "0001/324123/12312";

        // When
        String result = CorpCodeUtil.getTopLevelCorpCode(corpCode);

        // Then
        Assert.assertEquals("0001", result);
    }

    @Test
    public void testGetTopLevelCorpCode_OneLevel() {
        // Given
        String corpCode = "0001";

        // When
        String result = CorpCodeUtil.getTopLevelCorpCode(corpCode);

        // Then
        Assert.assertEquals("0001", result);
    }

    @Test
    public void testGetTopLevelCorpCode_Null() {
        // Given
        String corpCode = null;

        // When
        String result = CorpCodeUtil.getTopLevelCorpCode(corpCode);

        // Then
        Assert.assertNull(result);
    }

    @Test
    public void testIsTopLevelCorpCode_True() {
        // Given
        String corpCode = "0001";

        // When
        boolean result = CorpCodeUtil.isTopLevelCorpCode(corpCode);

        // Then
        Assert.assertTrue(result);
    }

    @Test
    public void testIsTopLevelCorpCode_False() {
        // Given
        String corpCode = "0001/324123";

        // When
        boolean result = CorpCodeUtil.isTopLevelCorpCode(corpCode);

        // Then
        Assert.assertFalse(result);
    }

    @Test
    public void testIsTopLevelCorpCode_Null() {
        // Given
        String corpCode = null;

        // When
        boolean result = CorpCodeUtil.isTopLevelCorpCode(corpCode);

        // Then
        Assert.assertFalse(result);
    }

    @Test
    public void testIsTopLevelCorpCode_Empty() {
        // Given
        String corpCode = "";

        // When
        boolean result = CorpCodeUtil.isTopLevelCorpCode(corpCode);

        // Then
        Assert.assertFalse(result);
    }

    @Test
    public void testParseParentCorpCodes_FourLevel() {
        // Given
        String corpCode = "0001/324123/12312/56789";

        // When
        List<String> result = CorpCodeUtil.parseParentCorpCodes(corpCode);

        // Then
        List<String> expected = Arrays.asList("0001/324123/12312", "0001/324123", "0001");
        Assert.assertEquals(expected, result);
    }

    @Test
    public void testParseParentCorpCodes_FiveLevel() {
        // Given
        String corpCode = "0001/324123/12312/56789/99999";

        // When
        List<String> result = CorpCodeUtil.parseParentCorpCodes(corpCode);

        // Then
        List<String> expected = Arrays.asList("0001/324123/12312/56789", "0001/324123/12312", "0001/324123", "0001");
        Assert.assertEquals(expected, result);
    }

    // ========== CorpInfo 相关测试 ==========

    @Test
    public void testParseToCorpInfo_ThreeLevel() {
        // Given
        String corpCode = "0001/324123/12312";

        // When
        CorpInfo result = CorpCodeUtil.parseToCorpInfo(corpCode);

        // Then
        Assert.assertNotNull(result);
        Assert.assertEquals("0001/324123/12312", result.getCorpCode());

        // 验证父级链
        CorpInfo parent = result.getParent();
        Assert.assertNotNull(parent);
        Assert.assertEquals("0001/324123", parent.getCorpCode());

        CorpInfo grandParent = parent.getParent();
        Assert.assertNotNull(grandParent);
        Assert.assertEquals("0001", grandParent.getCorpCode());

        // 顶级没有父级
        Assert.assertNull(grandParent.getParent());
    }

    @Test
    public void testParseToCorpInfo_OneLevel() {
        // Given
        String corpCode = "0001";

        // When
        CorpInfo result = CorpCodeUtil.parseToCorpInfo(corpCode);

        // Then
        Assert.assertNotNull(result);
        Assert.assertEquals("0001", result.getCorpCode());
        Assert.assertNull(result.getParent());
    }

    @Test
    public void testParseToCorpInfo_TwoLevel() {
        // Given
        String corpCode = "0001/324123";

        // When
        CorpInfo result = CorpCodeUtil.parseToCorpInfo(corpCode);

        // Then
        Assert.assertNotNull(result);
        Assert.assertEquals("0001/324123", result.getCorpCode());

        CorpInfo parent = result.getParent();
        Assert.assertNotNull(parent);
        Assert.assertEquals("0001", parent.getCorpCode());
        Assert.assertNull(parent.getParent());
    }

    @Test
    public void testParseToCorpInfo_Null() {
        // Given
        String corpCode = null;

        // When
        CorpInfo result = CorpCodeUtil.parseToCorpInfo(corpCode);

        // Then
        Assert.assertNull(result);
    }

    @Test
    public void testParseToCorpInfo_Empty() {
        // Given
        String corpCode = "";

        // When
        CorpInfo result = CorpCodeUtil.parseToCorpInfo(corpCode);

        // Then
        Assert.assertNull(result);
    }

    @Test
    public void testParseToCorpInfo_WithSpaces() {
        // Given
        String corpCode = "  0001/324123  ";

        // When
        CorpInfo result = CorpCodeUtil.parseToCorpInfo(corpCode);

        // Then
        Assert.assertNotNull(result);
        Assert.assertEquals("0001/324123", result.getCorpCode());

        CorpInfo parent = result.getParent();
        Assert.assertNotNull(parent);
        Assert.assertEquals("0001", parent.getCorpCode());
        Assert.assertNull(parent.getParent());
    }

    @Test
    public void testParseToCorpInfo_FourLevel() {
        // Given
        String corpCode = "0001/324123/12312/56789";

        // When
        CorpInfo result = CorpCodeUtil.parseToCorpInfo(corpCode);

        // Then
        Assert.assertNotNull(result);
        Assert.assertEquals("0001/324123/12312/56789", result.getCorpCode());

        // 验证完整的父级链
        CorpInfo level3 = result.getParent();
        Assert.assertNotNull(level3);
        Assert.assertEquals("0001/324123/12312", level3.getCorpCode());

        CorpInfo level2 = level3.getParent();
        Assert.assertNotNull(level2);
        Assert.assertEquals("0001/324123", level2.getCorpCode());

        CorpInfo level1 = level2.getParent();
        Assert.assertNotNull(level1);
        Assert.assertEquals("0001", level1.getCorpCode());

        Assert.assertNull(level1.getParent());
    }
}
