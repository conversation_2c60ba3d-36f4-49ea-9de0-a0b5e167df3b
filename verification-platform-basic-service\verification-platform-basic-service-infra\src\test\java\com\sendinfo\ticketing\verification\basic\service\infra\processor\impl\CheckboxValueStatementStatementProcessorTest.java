/**
 * CHECKBOX类型参数值声明处理器测试
 *
 * <AUTHOR> 2025-08-04
 */
package com.sendinfo.ticketing.verification.basic.service.infra.processor.impl;

import com.sendinfo.ticketing.verification.basic.enums.SysParamDataType;
import com.sendinfo.ticketing.verification.basic.exception.VerificationBizRuntimeException;
import com.sendinfo.ticketing.verification.basic.model.infra.error.InfraErrorDef;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class CheckboxValueStatementStatementProcessorTest {

    private CheckboxValueStatementStatementProcessor processor;

    @Before
    public void setUp() {
        processor = new CheckboxValueStatementStatementProcessor();
    }

    @Test
    public void testGetDataType() {
        assertEquals(SysParamDataType.CHECKBOX, processor.getDataType());
    }

    // ========== validateStatement 测试 ==========

    @Test
    public void testValidateStatement_成功校验_单个默认选项() {
        String validStatement = "{\"options\":[" +
                "{\"value\":\"read\",\"label\":\"读取\",\"isDefault\":true}," +
                "{\"value\":\"write\",\"label\":\"写入\",\"isDefault\":false}," +
                "{\"value\":\"delete\",\"label\":\"删除\",\"isDefault\":false}" +
                "]}";
        
        // 不应该抛出异常
        processor.validateStatement(validStatement);
    }

    @Test
    public void testValidateStatement_成功校验_多个默认选项() {
        String validStatement = "{\"options\":[" +
                "{\"value\":\"read\",\"label\":\"读取\",\"isDefault\":true}," +
                "{\"value\":\"write\",\"label\":\"写入\",\"isDefault\":true}," +
                "{\"value\":\"delete\",\"label\":\"删除\",\"isDefault\":false}" +
                "]}";
        
        // 不应该抛出异常
        processor.validateStatement(validStatement);
    }

    @Test
    public void testValidateStatement_成功校验_所有选项都是默认() {
        String validStatement = "{\"options\":[" +
                "{\"value\":\"option1\",\"label\":\"选项1\",\"isDefault\":true}," +
                "{\"value\":\"option2\",\"label\":\"选项2\",\"isDefault\":true}," +
                "{\"value\":\"option3\",\"label\":\"选项3\",\"isDefault\":true}" +
                "]}";
        
        // 不应该抛出异常
        processor.validateStatement(validStatement);
    }

    @Test(expected = VerificationBizRuntimeException.class)
    public void testValidateStatement_参数为null() {
        processor.validateStatement(null);
    }

    @Test(expected = VerificationBizRuntimeException.class)
    public void testValidateStatement_参数为空字符串() {
        processor.validateStatement("");
    }

    @Test(expected = VerificationBizRuntimeException.class)
    public void testValidateStatement_JSON格式错误() {
        String invalidJson = "{\"options\":[{\"value\":\"option1\"}";
        processor.validateStatement(invalidJson);
    }

    @Test(expected = VerificationBizRuntimeException.class)
    public void testValidateStatement_options为空数组() {
        String statement = "{\"options\":[]}";
        processor.validateStatement(statement);
    }

    @Test(expected = VerificationBizRuntimeException.class)
    public void testValidateStatement_没有默认选项() {
        String statement = "{\"options\":[" +
                "{\"value\":\"option1\",\"label\":\"选项1\",\"isDefault\":false}," +
                "{\"value\":\"option2\",\"label\":\"选项2\",\"isDefault\":false}" +
                "]}";
        processor.validateStatement(statement);
    }

    @Test(expected = VerificationBizRuntimeException.class)
    public void testValidateStatement_选项value为空() {
        String statement = "{\"options\":[" +
                "{\"value\":\"\",\"label\":\"选项1\",\"isDefault\":true}" +
                "]}";
        processor.validateStatement(statement);
    }

    @Test
    public void testValidateStatement_异常信息验证() {
        try {
            processor.validateStatement(null);
            fail("应该抛出异常");
        } catch (VerificationBizRuntimeException e) {
            assertEquals(InfraErrorDef.SYSPARAM_VALUE_STATEMENT_VALIDATION_ERROR.code(), e.getErrorCode());
            assertTrue(e.getMessage().contains("CHECKBOX类型参数值声明不能为空"));
        }
    }

    // ========== parseAndGetDefaultSysParamValue 测试 ==========

    @Test
    public void testParseAndGetDefaultSysParamValue_成功解析单个默认值() {
        String statement = "{\"options\":[" +
                "{\"value\":\"read\",\"label\":\"读取\",\"isDefault\":true}," +
                "{\"value\":\"write\",\"label\":\"写入\",\"isDefault\":false}" +
                "]}";
        
        String result = processor.parseAndGetDefaultSysParamValue(statement);
        assertEquals("[\"read\"]", result);
    }

    @Test
    public void testParseAndGetDefaultSysParamValue_成功解析多个默认值() {
        String statement = "{\"options\":[" +
                "{\"value\":\"read\",\"label\":\"读取\",\"isDefault\":true}," +
                "{\"value\":\"write\",\"label\":\"写入\",\"isDefault\":true}," +
                "{\"value\":\"delete\",\"label\":\"删除\",\"isDefault\":false}" +
                "]}";
        
        String result = processor.parseAndGetDefaultSysParamValue(statement);
        // FastJSON 可能会改变数组元素的顺序，所以我们检查包含关系
        assertTrue(result.contains("read"));
        assertTrue(result.contains("write"));
        assertFalse(result.contains("delete"));
        assertTrue(result.startsWith("["));
        assertTrue(result.endsWith("]"));
    }

    @Test
    public void testParseAndGetDefaultSysParamValue_解析中文值() {
        String statement = "{\"options\":[" +
                "{\"value\":\"中文选项1\",\"label\":\"中文标签1\",\"isDefault\":true}," +
                "{\"value\":\"中文选项2\",\"label\":\"中文标签2\",\"isDefault\":true}" +
                "]}";
        
        String result = processor.parseAndGetDefaultSysParamValue(statement);
        assertTrue(result.contains("中文选项1"));
        assertTrue(result.contains("中文选项2"));
    }

    @Test
    public void testParseAndGetDefaultSysParamValue_解析特殊字符() {
        String statement = "{\"options\":[" +
                "{\"value\":\"option@#$%^&*()\",\"label\":\"特殊字符\",\"isDefault\":true}" +
                "]}";
        
        String result = processor.parseAndGetDefaultSysParamValue(statement);
        assertTrue(result.contains("option@#$%^&*()"));
    }

    @Test(expected = VerificationBizRuntimeException.class)
    public void testParseAndGetDefaultSysParamValue_参数为null() {
        processor.parseAndGetDefaultSysParamValue(null);
    }


    @Test(expected = VerificationBizRuntimeException.class)
    public void testParseAndGetDefaultSysParamValue_没有默认选项() {
        String statement = "{\"options\":[" +
                "{\"value\":\"option1\",\"label\":\"选项1\",\"isDefault\":false}" +
                "]}";
        processor.parseAndGetDefaultSysParamValue(statement);
    }

    // ========== 综合测试 ==========

    @Test
    public void testValidateAndParse_完整流程() {
        String statement = "{\"options\":[" +
                "{\"value\":\"create\",\"label\":\"创建\",\"isDefault\":true}," +
                "{\"value\":\"update\",\"label\":\"更新\",\"isDefault\":true}," +
                "{\"value\":\"delete\",\"label\":\"删除\",\"isDefault\":false}" +
                "]}";
        
        // 先校验
        processor.validateStatement(statement);
        
        // 再解析
        String result = processor.parseAndGetDefaultSysParamValue(statement);
        assertTrue(result.contains("create"));
        assertTrue(result.contains("update"));
        assertFalse(result.contains("delete"));
    }

    // ========== 业务场景测试 ==========

    @Test
    public void testCheckbox_权限选择() {
        String statement = "{\"options\":[" +
                "{\"value\":\"READ\",\"label\":\"读取权限\",\"isDefault\":true}," +
                "{\"value\":\"WRITE\",\"label\":\"写入权限\",\"isDefault\":false}," +
                "{\"value\":\"DELETE\",\"label\":\"删除权限\",\"isDefault\":false}," +
                "{\"value\":\"ADMIN\",\"label\":\"管理权限\",\"isDefault\":false}" +
                "]}";
        
        processor.validateStatement(statement);
        String result = processor.parseAndGetDefaultSysParamValue(statement);
        assertEquals("[\"READ\"]", result);
    }

    @Test
    public void testCheckbox_功能模块选择() {
        String statement = "{\"options\":[" +
                "{\"value\":\"USER_MGMT\",\"label\":\"用户管理\",\"isDefault\":true}," +
                "{\"value\":\"ROLE_MGMT\",\"label\":\"角色管理\",\"isDefault\":true}," +
                "{\"value\":\"SYSTEM_CONFIG\",\"label\":\"系统配置\",\"isDefault\":false}," +
                "{\"value\":\"LOG_AUDIT\",\"label\":\"日志审计\",\"isDefault\":false}" +
                "]}";
        
        processor.validateStatement(statement);
        String result = processor.parseAndGetDefaultSysParamValue(statement);
        assertTrue(result.contains("USER_MGMT"));
        assertTrue(result.contains("ROLE_MGMT"));
        assertFalse(result.contains("SYSTEM_CONFIG"));
        assertFalse(result.contains("LOG_AUDIT"));
    }

    @Test
    public void testCheckbox_通知方式选择() {
        String statement = "{\"options\":[" +
                "{\"value\":\"EMAIL\",\"label\":\"邮件通知\",\"isDefault\":true}," +
                "{\"value\":\"SMS\",\"label\":\"短信通知\",\"isDefault\":false}," +
                "{\"value\":\"WECHAT\",\"label\":\"微信通知\",\"isDefault\":true}," +
                "{\"value\":\"PUSH\",\"label\":\"推送通知\",\"isDefault\":false}" +
                "]}";
        
        processor.validateStatement(statement);
        String result = processor.parseAndGetDefaultSysParamValue(statement);
        assertTrue(result.contains("EMAIL"));
        assertTrue(result.contains("WECHAT"));
        assertFalse(result.contains("SMS"));
        assertFalse(result.contains("PUSH"));
    }

    @Test
    public void testCheckbox_兴趣爱好选择() {
        String statement = "{\"options\":[" +
                "{\"value\":\"READING\",\"label\":\"阅读\",\"isDefault\":true}," +
                "{\"value\":\"MUSIC\",\"label\":\"音乐\",\"isDefault\":true}," +
                "{\"value\":\"SPORTS\",\"label\":\"运动\",\"isDefault\":false}," +
                "{\"value\":\"TRAVEL\",\"label\":\"旅行\",\"isDefault\":true}," +
                "{\"value\":\"COOKING\",\"label\":\"烹饪\",\"isDefault\":false}" +
                "]}";
        
        processor.validateStatement(statement);
        String result = processor.parseAndGetDefaultSysParamValue(statement);
        assertTrue(result.contains("READING"));
        assertTrue(result.contains("MUSIC"));
        assertTrue(result.contains("TRAVEL"));
        assertFalse(result.contains("SPORTS"));
        assertFalse(result.contains("COOKING"));
    }

    @Test
    public void testCheckbox_工作日选择() {
        String statement = "{\"options\":[" +
                "{\"value\":\"MON\",\"label\":\"周一\",\"isDefault\":true}," +
                "{\"value\":\"TUE\",\"label\":\"周二\",\"isDefault\":true}," +
                "{\"value\":\"WED\",\"label\":\"周三\",\"isDefault\":true}," +
                "{\"value\":\"THU\",\"label\":\"周四\",\"isDefault\":true}," +
                "{\"value\":\"FRI\",\"label\":\"周五\",\"isDefault\":true}," +
                "{\"value\":\"SAT\",\"label\":\"周六\",\"isDefault\":false}," +
                "{\"value\":\"SUN\",\"label\":\"周日\",\"isDefault\":false}" +
                "]}";
        
        processor.validateStatement(statement);
        String result = processor.parseAndGetDefaultSysParamValue(statement);
        assertTrue(result.contains("MON"));
        assertTrue(result.contains("TUE"));
        assertTrue(result.contains("WED"));
        assertTrue(result.contains("THU"));
        assertTrue(result.contains("FRI"));
        assertFalse(result.contains("SAT"));
        assertFalse(result.contains("SUN"));
    }
}
