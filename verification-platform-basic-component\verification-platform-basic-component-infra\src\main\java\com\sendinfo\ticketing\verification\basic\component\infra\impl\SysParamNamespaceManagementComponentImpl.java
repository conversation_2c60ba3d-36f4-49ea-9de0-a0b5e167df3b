package com.sendinfo.ticketing.verification.basic.component.infra.impl;

import lombok.Getter;
import org.springframework.stereotype.Component;

import com.sendinfo.ticketing.verification.basic.component.infra.SysParamNamespaceManagementComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.converter.SysParamNamespaceConverter;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamNamespaceUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.infra.SysParamNamespace;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamNamespaceUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.SysParamNamespaceDao;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleUpdate;

/**
 * 参数命名空间管理组件实现类，负责更新参数命名空间及状态管理的业务逻辑
 *
 * <AUTHOR> 2025-07-21 15:30:00
 */
@Component("sysParamNamespaceManagementComponent")
@Getter
public class SysParamNamespaceManagementComponentImpl
        implements SysParamNamespaceManagementComponent,
        DaoBasedSingleUpdate<SysParamNamespaceUpdateParam, SysParamNamespaceUpdateArg, SysParamNamespace> {

    private final SysParamNamespaceDao dao;
    private final SysParamNamespaceConverter converter;

    public SysParamNamespaceManagementComponentImpl(
            SysParamNamespaceDao dao,
            SysParamNamespaceConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }
}