package com.sendinfo.ticketing.verification.basic.repository.pay.arg;

import com.sendinfo.ticketing.verification.common.repository.arg.AbstractQueryArg;
import com.sendinfo.ticketing.verification.common.repository.arg.Pageable;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 经营主体查询参数
 * 用于封装sys_mainbody表的查询条件，支持租户隔离、分页、排序
 * 包含必要的查询条件和可选的分页参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class SysMainbodyQueryArg extends AbstractQueryArg implements Pageable {
    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 经营主体名称
     */
    private String mainbodyName;

    /**
     * 经营主体简称
     */
    private String mainbodyShortName;

    /**
     * 主体商户号
     */
    private String merchantNo;

    /**
     * 联系人
     */
    private String linkMan;

    /**
     * 联系电话
     */
    private String linkTel;

    /**
     * 备注
     */
    private String remark;

    /**
     * 经营主体编号
     */
    private String mainbodyNumber;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 分页偏移量
     */
    private Integer offset;

    /**
     * 分页大小
     */
    private Integer limit;
} 