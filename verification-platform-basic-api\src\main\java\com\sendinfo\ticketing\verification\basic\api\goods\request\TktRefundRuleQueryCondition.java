package com.sendinfo.ticketing.verification.basic.api.goods.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class TktRefundRuleQueryCondition implements Serializable {

    /**
     * 企业编码
     */
    @NotEmpty
    private String corpCode;

    /**
     * 票型ID
     */
    @NotEmpty
    private Long ticketId;
} 