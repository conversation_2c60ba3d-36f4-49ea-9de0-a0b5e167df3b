/**
 * System parameter component definition update request
 *
 * <AUTHOR> 2025-07-21 15:40:00
 */
package com.sendinfo.ticketing.verification.basic.api.infra.request;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 系统参数组件定义更新请求
 */
@Getter
@Setter
@ToString
public class SysParamComponentDefinitionUpdateRequest implements Serializable {
    private static final long serialVersionUID = -7654321098765432104L;

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空")
    private Long id;

    /**
     * 组件名称
     */
    @Size(max = 128, message = "组件名称长度不能超过128")
    private String componentName;

    /**
     * 组件描述
     */
    @Size(max = 256, message = "组件描述长度不能超过256")
    private String description;

    /**
     * 排序
     */
    @Min(value = 1, message = "排序不能小于1")
    @Max(value = 999, message = "排序不能超过999")
    private Integer sortNo;

    /**
     * 状态：1-启用，0-禁用
     */
    private Integer status;

    /**
     * 修改人
     */
    @NotBlank(message = "修改人不能为空")
    private String modifyBy;
}