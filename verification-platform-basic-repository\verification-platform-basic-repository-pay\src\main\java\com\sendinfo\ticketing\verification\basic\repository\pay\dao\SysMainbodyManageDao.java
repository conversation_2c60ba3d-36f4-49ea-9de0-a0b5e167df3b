package com.sendinfo.ticketing.verification.basic.repository.pay.dao;

import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysMainbodyManageDO;
import com.sendinfo.ticketing.verification.common.repository.BatchInsertDAO;
import com.sendinfo.ticketing.verification.common.repository.CountableDAO;
import com.sendinfo.ticketing.verification.common.repository.GenericDAO;
import com.sendinfo.ticketing.verification.common.repository.QueryableDAO;

/**
 * 经营主体支付配置数据访问接口
 * 用于定义sys_mainbody_manage表的数据访问接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface SysMainbodyManageDao extends GenericDAO<Long, SysMainbodyManageDO, SysMainbodyManageUpdateArg, SysMainbodyManageDeleteArg>,
        CountableDAO<SysMainbodyManageQueryArg>,
        QueryableDAO<SysMainbodyManageQueryArg, SysMainbodyManageDO>,
        BatchInsertDAO<Long, SysMainbodyManageDO> {

    /**
     * 根据经营主体ID查询经营主体支付配置
     *
     * @param mainbodyId 经营主体ID
     * @param corpCode   企业编码
     * @return 经营主体支付配置信息
     */
    SysMainbodyManageDO queryByMainbodyId(Long mainbodyId, String corpCode);
} 