package com.sendinfo.ticketing.verification.basic.component.goods.mapper;

import com.sendinfo.ticketing.verification.basic.component.goods.param.UserTicketRelationCreateParam;
import com.sendinfo.ticketing.verification.basic.component.goods.param.UserTicketRelationDeleteParam;
import com.sendinfo.ticketing.verification.basic.component.goods.param.UserTicketRelationQueryParam;
import com.sendinfo.ticketing.verification.basic.component.goods.param.UserTicketRelationUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.goods.UserTicketRelation;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.UserTicketRelationDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.UserTicketRelationQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.UserTicketRelationUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.UserTicketRelationDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2025-07-24 10:49
 */
@Mapper
public interface UserTicketRelationMapper {

    UserTicketRelationMapper INSTANCE = Mappers.getMapper(UserTicketRelationMapper.class);

    /**
     * 数据对象转化为API对象
     *
     * @param dataObject DO对象
     * @return api对象
     */
    UserTicketRelation convert(UserTicketRelationDO dataObject);

    /**
     * 创建参数对象转化为数据对象
     *
     * @param param 创建参数对象
     * @return 数据对象
     */
    UserTicketRelationDO convert(UserTicketRelationCreateParam param);

    /**
     * 删除参数对象转化为数据删除对象
     *
     * @param param 删除参数对象
     * @return 数据删除对象
     */
    UserTicketRelationDeleteArg convert(UserTicketRelationDeleteParam param);

    /**
     * 查询参数对象转化为数据查询对象
     *
     * @param param 查询参数对象
     * @return 数据查询对象
     */
    UserTicketRelationQueryArg convert(UserTicketRelationQueryParam param);

    /**
     * 修改参数对象转化为数据修改对象
     *
     * @param param 修改参数对象
     * @return 数据修改对象
     */
    UserTicketRelationUpdateArg convert(UserTicketRelationUpdateParam param);

}
