/**
 * SELECT类型参数值声明处理器
 *
 * <AUTHOR> 2025-08-04
 */
package com.sendinfo.ticketing.verification.basic.service.infra.processor.impl;

import com.sendinfo.ticketing.verification.basic.enums.SysParamDataType;
import org.springframework.stereotype.Component;

/**
 * SELECT类型参数值声明处理器
 * 下拉选择类型，必须且仅有一个default=true的选项
 */
@Component("selectValueStatementProcessor")
public class SelectValueStatementStatementProcessor extends SingleValueStatementProcessor {

    @Override
    public SysParamDataType getDataType() {
        return SysParamDataType.SELECT;
    }

}
