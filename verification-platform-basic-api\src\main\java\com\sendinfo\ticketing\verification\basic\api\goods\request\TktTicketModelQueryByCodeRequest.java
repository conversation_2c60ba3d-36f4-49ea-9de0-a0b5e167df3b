package com.sendinfo.ticketing.verification.basic.api.goods.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Auther: chengjie.li
 * @Date: 2025/6/10 20:27
 */
@Getter
@Setter
@ToString(callSuper = true)
public class TktTicketModelQueryByCodeRequest implements Serializable {
    private static final long serialVersionUID = 4146707040185937138L;

    /**
     * 票型编码
     */
    @NotNull
    private String ticketCode;

    /**
     * 企业编码
     */
    @NotNull
    private String corpCode;
}
