package com.sendinfo.ticketing.verification.basic.component.customer.impl;

import com.sendinfo.ticketing.verification.basic.component.customer.SalesmanInfoReadComponent;
import com.sendinfo.ticketing.verification.basic.component.customer.converter.SalesmanInfoConverter;
import com.sendinfo.ticketing.verification.basic.component.customer.param.SalesmanInfoQueryParam;
import com.sendinfo.ticketing.verification.basic.model.customer.SalesmanInfo;
import com.sendinfo.ticketing.verification.basic.repository.customer.arg.SalesmanInfoQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.customer.dao.SalesmanInfoDao;
import com.sendinfo.ticketing.verification.basic.repository.customer.dataobject.SalesmanInfoDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedCountRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedListRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleRead;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * 业务员信息读取组件实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("salesmanInfoReadComponent")
@Getter
public class SalesmanInfoReadComponentImpl implements SalesmanInfoReadComponent,
        DaoBasedSingleRead<Long, SalesmanInfoQueryParam, SalesmanInfo, SalesmanInfoDO>,
        DaoBasedListRead<Long, SalesmanInfoQueryParam, SalesmanInfo, SalesmanInfoDO, SalesmanInfoQueryArg>,
        DaoBasedCountRead<Long, SalesmanInfoQueryParam, SalesmanInfo, SalesmanInfoQueryArg> {

    private final SalesmanInfoDao dao;
    private final SalesmanInfoConverter converter;

    public SalesmanInfoReadComponentImpl(SalesmanInfoDao dao, SalesmanInfoConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }

    @Override
    public List<SalesmanInfo> queryEnabledByIds(Set<Long> ids, String corpCode) {
        List<SalesmanInfoDO> dataObjects = dao.queryEnabledByIds(ids, corpCode);
        return converter.r_ds2ms(dataObjects);
    }
} 