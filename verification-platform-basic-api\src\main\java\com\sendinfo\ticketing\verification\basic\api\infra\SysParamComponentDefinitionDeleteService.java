package com.sendinfo.ticketing.verification.basic.api.infra;

import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamComponentDefinitionDeleteRequest;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

/**
 * 系统参数组件定义删除服务
 */
public interface SysParamComponentDefinitionDeleteService {
    
    /**
     * 删除系统参数组件定义
     *
     * @param request 删除请求
     * @return 操作结果
     */
    ResultModel<Boolean> deleteSysParamComponentDefinition(SysParamComponentDefinitionDeleteRequest request);
}
