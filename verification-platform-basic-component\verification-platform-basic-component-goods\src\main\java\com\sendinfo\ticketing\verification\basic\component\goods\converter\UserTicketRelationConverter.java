package com.sendinfo.ticketing.verification.basic.component.goods.converter;

import com.sendinfo.ticketing.verification.basic.component.goods.mapper.UserTicketRelationMapper;
import com.sendinfo.ticketing.verification.basic.component.goods.param.UserTicketRelationCreateParam;
import com.sendinfo.ticketing.verification.basic.component.goods.param.UserTicketRelationDeleteParam;
import com.sendinfo.ticketing.verification.basic.component.goods.param.UserTicketRelationQueryParam;
import com.sendinfo.ticketing.verification.basic.component.goods.param.UserTicketRelationUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.goods.UserTicketRelation;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.UserTicketRelationDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.UserTicketRelationQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.UserTicketRelationUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.UserTicketRelationDO;
import com.sendinfo.ticketing.verification.common.component.converter.*;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025-07-24 10:47
 */
@Component("userTicketRelationConverter")
public class UserTicketRelationConverter implements CreateParam2DoConverter<UserTicketRelationCreateParam, UserTicketRelationDO>,
        ReadParam2ArgConverter<UserTicketRelationQueryParam, UserTicketRelationQueryArg>,
        ReadDo2ModelConverter<UserTicketRelationDO, UserTicketRelation>,
        UpdateParam2ArgConverter<UserTicketRelationUpdateParam, UserTicketRelationUpdateArg, UserTicketRelation>,
        DeleteParam2ArgConverter<UserTicketRelationDeleteParam, UserTicketRelationDeleteArg> {
    @Override
    public UserTicketRelationDO c_p2d(UserTicketRelationCreateParam createParam) {
        return UserTicketRelationMapper.INSTANCE.convert(createParam);
    }

    @Override
    public UserTicketRelationDeleteArg d_p2a(UserTicketRelationDeleteParam param) {
        return UserTicketRelationMapper.INSTANCE.convert(param);
    }

    @Override
    public UserTicketRelation r_d2m(UserTicketRelationDO dataObject) {
        return UserTicketRelationMapper.INSTANCE.convert(dataObject);
    }

    @Override
    public UserTicketRelationQueryArg r_p2a(UserTicketRelationQueryParam param) {
        return UserTicketRelationMapper.INSTANCE.convert(param);
    }

    @Override
    public UserTicketRelationUpdateArg u_p2a(UserTicketRelationUpdateParam param, UserTicketRelation currentModel) {
        return UserTicketRelationMapper.INSTANCE.convert(param);
    }
}
