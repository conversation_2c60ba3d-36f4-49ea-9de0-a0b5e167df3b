package com.sendinfo.ticketing.verification.basic.api.customer.request;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 根据旅行社ID查询主管信息请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
public class DirectorInfoQueryByTravelIdRequest implements Serializable {

    private static final long serialVersionUID = -8492096266838555747L;
    /**
     * 旅行社ID
     */
    @NotNull(message = "旅行社ID不能为空")
    private Long travelId;

    /**
     * 企业编码
     */
    @NotNull(message = "企业编码不能为空")
    private String corpCode;
} 