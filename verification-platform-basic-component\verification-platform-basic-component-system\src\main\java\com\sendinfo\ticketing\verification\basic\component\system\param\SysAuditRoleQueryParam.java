package com.sendinfo.ticketing.verification.basic.component.system.param;

import com.sendinfo.ticketing.verification.common.component.param.AbstractPageQueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 票型角色查询参数
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysAuditRoleQueryParam extends AbstractPageQueryParam {

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 权限类型
     */
    private Integer roleType;

    /**
     * 子系统ID
     */
    private Integer subsystemId;

    /**
     * 启用状态 F:停用 T:启动
     */
    private String useFlag;
} 