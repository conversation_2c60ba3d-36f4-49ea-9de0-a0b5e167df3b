package com.sendinfo.ticketing.verification.basic.common.util;

import java.nio.ByteBuffer;

/**
 * <AUTHOR>
 * @since 2025-07-24 15:36
 */
public class DataTypeUtil {

    /**
     * long转bit
     */
    public static byte[] longToByte(long value) {
        ByteBuffer buffer = ByteBuffer.allocate(Long.BYTES);
        buffer.putLong(value);
        return buffer.array();
    }

    /**
     * bit转long
     */
    public static long byteToLong(byte[] bytes) {
        ByteBuffer buffer = ByteBuffer.wrap(bytes);
        return buffer.getLong();
    }
}
