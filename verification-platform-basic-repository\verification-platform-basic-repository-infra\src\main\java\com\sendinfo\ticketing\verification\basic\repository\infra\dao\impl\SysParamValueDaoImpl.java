package com.sendinfo.ticketing.verification.basic.repository.infra.dao.impl;

import com.sendinfo.ticketing.verification.repository.jdbc.Statement;
import com.sendinfo.ticketing.verification.repository.jdbc.mybatis.*;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamValueDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamValueQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamValueUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.SysParamValueDao;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.SysParamValueDO;
import lombok.Getter;
import org.apache.ibatis.session.SqlSession;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 系统参数值DAO实现
 *
 * <AUTHOR> 2025-05-19 16:34:25
 */
@Getter
@Repository("sysParamValueDao")
public class SysParamValueDaoImpl implements SysParamValueDao,
        SqlSessionGenericDAO<Long, SysParamValueDO, SysParamValueUpdateArg, SysParamValueDeleteArg>,
        SqlSessionCountableDAO<SysParamValueQueryArg>,
        SqlSessionPageableDAO<SysParamValueQueryArg, SysParamValueDO>,
        SqlSessionBatchInsertDAO<Long, SysParamValueDO>,
        SqlSessionDAO {

    private final SqlSession sqlSession;
    private final Statement statement;

    public SysParamValueDaoImpl(@Qualifier("ticketInfraSqlSessionTemplate") SqlSessionTemplate sqlSessionTemplate) {
        this.sqlSession = sqlSessionTemplate;
        this.statement = Statement.of(SysParamValueDao.class);
    }

    @Override
    public SysParamValueDO queryByParamCodeAndCorpCode(String paramCode, String corpCode) {
        Map<String, Object> params = new HashMap<>();
        params.put("paramCode", paramCode);
        params.put("corpCode", corpCode);
        return sqlSession.selectOne(statement.get("queryByParamCodeAndCorpCode"), params);
    }

    @Override
    public List<SysParamValueDO> queryByParamKeyList(List<String> paramKeyList) {
        if (paramKeyList == null || paramKeyList.isEmpty()) {
            return Collections.emptyList();
        }
        Map<String, Object> params = new HashMap<>();
        params.put("paramKeyList", paramKeyList);
        return sqlSession.selectList(statement.get("queryByParamKeyList"), params);
    }

    @Override
    public List<SysParamValueDO> queryByParamKeyList(String corpCode, List<String> paramKeyList) {
        if (paramKeyList == null || paramKeyList.isEmpty()) {
            return Collections.emptyList();
        }
        Map<String, Object> params = new HashMap<>();
        params.put("corpCode", corpCode);
        params.put("paramKeyList", paramKeyList);
        return sqlSession.selectList(statement.get("queryByParamKeyListWithCorpCode"), params);
    }

    @Override
    public SysParamValueDO queryByParamKeyAndTenantCorpCode(String paramKey, String tenantCorpCode) {
        Map<String, Object> params = new HashMap<>();
        params.put("paramKey", paramKey);
        params.put("tenantCorpCode", tenantCorpCode);
        return sqlSession.selectOne(statement.get("queryByParamKeyAndTenantCorpCode"), params);
    }

    @Override
    public void sort(SysParamValueQueryArg queryArg) {
        queryArg.desc("id");
    }
}