package com.sendinfo.ticketing.verification.basic.component.goods.converter;

import com.sendinfo.ticketing.verification.basic.api.goods.request.TktGroupInfoQueryRequest;
import com.sendinfo.ticketing.verification.basic.component.goods.mapper.TktGroupInfoMapper;
import com.sendinfo.ticketing.verification.basic.component.goods.param.TktGroupInfoQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TktGroupInfo;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TktGroupInfoQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TktGroupInfoDO;
import com.sendinfo.ticketing.verification.common.component.converter.ReadDo2ModelConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadParam2ArgConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadReq2ParamConverter;
import org.springframework.stereotype.Component;

/**
 * 票型分组信息转换器
 *
 * <AUTHOR>
 */
@Component("tktGroupInfoConverter")
public class TktGroupInfoConverter implements
        ReadParam2ArgConverter<TktGroupInfoQueryParam, TktGroupInfoQueryArg>,
        ReadDo2ModelConverter<TktGroupInfoDO, TktGroupInfo>,
        ReadReq2ParamConverter<TktGroupInfoQueryRequest, TktGroupInfoQueryParam> {
    @Override
    public TktGroupInfo r_d2m(TktGroupInfoDO dataObject) {
        return TktGroupInfoMapper.INSTANCE.convert(dataObject);
    }

    @Override
    public TktGroupInfoQueryArg r_p2a(TktGroupInfoQueryParam param) {
        return TktGroupInfoMapper.INSTANCE.convert(param);
    }

    @Override
    public TktGroupInfoQueryParam r_r2p(TktGroupInfoQueryRequest request) {
        return TktGroupInfoMapper.INSTANCE.convert(request);
    }
}