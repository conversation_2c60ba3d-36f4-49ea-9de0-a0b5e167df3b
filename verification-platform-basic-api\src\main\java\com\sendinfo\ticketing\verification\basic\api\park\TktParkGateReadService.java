package com.sendinfo.ticketing.verification.basic.api.park;

import com.sendinfo.ticketing.verification.basic.api.park.request.TktParkGateQueryCondition;
import com.sendinfo.ticketing.verification.basic.api.park.request.TktParkGateQueryRequest;
import com.sendinfo.ticketing.verification.basic.model.park.TktParkGate;
import com.sendinfo.ticketing.verification.common.api.request.PageRequest;
import com.sendinfo.ticketing.verification.common.api.result.PageResultModel;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import javax.validation.constraints.NotNull;
import java.util.List;

public interface TktParkGateReadService {
    /**
     * 分页查询景区通道列表
     * @param pageRequest
     * @return
     */
    PageResultModel<TktParkGate> searchParkGate(PageRequest<TktParkGateQueryCondition> pageRequest);

    /**
     * 查询景区通道列表
     * @param parkZoneId
     * @param corpCode
     * @return
     */
    ResultModel<List<TktParkGate>> queryParkGateList(@NotNull Long parkZoneId, @NotNull String corpCode);
    /**
     * 查询景区通道
     * @param parkGateId
     * @return
     */
    ResultModel<TktParkGate> queryParkGate(@NotNull Long parkGateId,@NotNull String corpCode);
    /**
     * 根据通道编号查询通道
     * @return
     */
    ResultModel<TktParkGate> queryParkGateByGateNo(TktParkGateQueryRequest request);
}
