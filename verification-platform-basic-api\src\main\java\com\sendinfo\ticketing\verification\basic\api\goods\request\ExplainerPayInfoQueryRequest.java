package com.sendinfo.ticketing.verification.basic.api.goods.request;

import com.sendinfo.ticketing.verification.basic.enums.ExplainerUserFlagEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Set;

/**
 * 服务类目计费规则查询参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString
public class ExplainerPayInfoQueryRequest implements Serializable {

    /**
     * 企业编码
     */
    @NotNull(message = "企业编码不能为空")
    private String corpCode;

    /**
     * 服务类目ID集合
     */
    private Set<Long> serviceCategoryIds;

    /**
     * 支付名称
     */
    private String payName;

    /**
     * 计费方式(票务字典)
     */
    private String payType;

    /**
     * 启用状态
     */
    private String useFlag;
} 