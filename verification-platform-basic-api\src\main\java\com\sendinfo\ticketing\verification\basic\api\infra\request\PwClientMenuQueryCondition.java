package com.sendinfo.ticketing.verification.basic.api.infra.request;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * 客户端菜单查询条件
 *
 * <AUTHOR> 2025-07-24
 */
@Getter
@Setter
@ToString
public class PwClientMenuQueryCondition implements Serializable {

    private static final long serialVersionUID = -2345678901234567890L;

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 业务类型：0:门票1:剧院2:滑雪场3:一卡通
     */
    private Integer businessType;

    /**
     * 菜单名称
     */
    private String menuName;

    /**
     * 菜单编码
     */
    private String menuCode;

    /**
     * 功能类别  menu（菜单）、column（栏目）、button（按钮）、TAB（标签栏）
     */
    private String functionType;

    /**
     * 应用类型ID
     */
    private Integer applyTypeId;

}
