package com.sendinfo.ticketing.verification.basic.component.pay;

import com.sendinfo.ticketing.verification.basic.component.pay.param.PayChanleQueryParam;
import com.sendinfo.ticketing.verification.basic.model.pay.PayChanle;
import com.sendinfo.ticketing.verification.common.component.ReadComponent;

/**
 * 支付渠道读取组件接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface PayChanleReadComponent extends ReadComponent<Long, PayChanleQueryParam, PayChanle> {

} 