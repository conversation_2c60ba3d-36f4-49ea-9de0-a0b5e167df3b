package com.sendinfo.ticketing.verification.basic.api.config;

import com.sendinfo.ticketing.verification.basic.api.config.request.TenantTesbConfigGetRequest;
import com.sendinfo.ticketing.verification.basic.api.config.result.TenantTesbConfigResult;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

public interface TenantTesbConfigReadService {
    /**
     * 根据配置编码和租户编码查询配置信息
     * 根据配置变量 request.withChildren 为 true时，返回配置信息及其子配置信息，否则只返回配置信息
     * @return
     */
    ResultModel<TenantTesbConfigResult> getTenantTesbConfig(TenantTesbConfigGetRequest request);
}
