package com.sendinfo.ticketing.verification.basic.component.infra.impl;

import lombok.Getter;
import org.springframework.stereotype.Component;

import com.sendinfo.ticketing.verification.basic.component.infra.SysParamValueDeleteComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.converter.SysParamValueConverter;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamValueDeleteParam;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamValueDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.SysParamValueDao;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleDelete;

/**
 *  系统参数值删除组件实现
 *
 *  <AUTHOR> 2025-05-20 15:40:00
 */
@Component("sysParamValueDeleteComponent")
@Getter
public class SysParamValueDeleteComponentImpl
        implements SysParamValueDeleteComponent,
        DaoBasedSingleDelete<Long, SysParamValueDeleteParam, SysParamValueDeleteArg> {

    private final SysParamValueDao dao;
    private final SysParamValueConverter converter;

    public SysParamValueDeleteComponentImpl(SysParamValueDao dao, SysParamValueConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }
} 