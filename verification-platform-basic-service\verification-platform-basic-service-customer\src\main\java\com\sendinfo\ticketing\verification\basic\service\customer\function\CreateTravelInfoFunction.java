package com.sendinfo.ticketing.verification.basic.service.customer.function;

import com.sendinfo.ticketing.verification.basic.api.account.request.CapitalAccountCreateRequest;
import com.sendinfo.ticketing.verification.basic.api.customer.request.TravelAssemblyCreateRequest;
import com.sendinfo.ticketing.verification.basic.api.customer.request.TravelInfoCreateRequest;
import com.sendinfo.ticketing.verification.basic.api.customer.request.TravelTaxCreateRequest;
import com.sendinfo.ticketing.verification.basic.component.account.CapitalAccountCreateComponent;
import com.sendinfo.ticketing.verification.basic.component.account.converter.CapitalAccountConverter;
import com.sendinfo.ticketing.verification.basic.component.customer.TravelInfoCreateComponent;
import com.sendinfo.ticketing.verification.basic.component.customer.TravelTaxCreateComponent;
import com.sendinfo.ticketing.verification.basic.component.customer.converter.TravelInfoConverter;
import com.sendinfo.ticketing.verification.basic.component.customer.converter.TravelTaxConverter;
import com.sendinfo.ticketing.verification.basic.model.customer.TravelInfo;
import com.sendinfo.ticketing.verification.basic.service.customer.enums.CustomerAttachmentKey;
import com.sendinfo.ticketing.verification.common.api.result.Results;
import com.sendinfo.ticketing.verification.common.service.stereotype.Function;
import com.sendinfo.ticketing.verification.flow.Hint;
import com.sendinfo.ticketing.verification.flow.Question;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.transaction.support.TransactionTemplate;

/**
 * 创建旅行社信息函数
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/22 17:16
 */
@Slf4j
@Getter
@Function("createTravelInfoFunction")
public class CreateTravelInfoFunction {

	private final TravelInfoConverter travelInfoConverter;
	private final TravelInfoCreateComponent travelInfoCreateComponent;

	private final TravelTaxConverter travelTaxConverter;
	private final TravelTaxCreateComponent travelTaxCreateComponent;

	private final CapitalAccountConverter capitalAccountConverter;
	private final CapitalAccountCreateComponent capitalAccountCreateComponent;

	private final TransactionTemplate transactionTemplate;

	public CreateTravelInfoFunction(TravelInfoConverter travelInfoConverter,
	                                TravelInfoCreateComponent travelInfoCreateComponent,
									TravelTaxConverter travelTaxConverter,
									TravelTaxCreateComponent travelTaxCreateComponent,
									CapitalAccountConverter capitalAccountConverter,
									CapitalAccountCreateComponent capitalAccountCreateComponent,
									@Qualifier("verificationPlatformSaasPwTransactionTemplate") TransactionTemplate transactionTemplate) {
		this.travelInfoConverter = travelInfoConverter;
		this.travelInfoCreateComponent = travelInfoCreateComponent;
		this.travelTaxConverter = travelTaxConverter;
		this.travelTaxCreateComponent = travelTaxCreateComponent;
		this.capitalAccountConverter = capitalAccountConverter;
		this.capitalAccountCreateComponent = capitalAccountCreateComponent;
		this.transactionTemplate = transactionTemplate;
	}

	/**
	 * 创建旅行社信息
	 *
	 * @param question  旅行社信息创建请求对象
	 * @return  Hint
	 */
	public Hint createTravelInfo(Question<TravelAssemblyCreateRequest> question) {
		TravelAssemblyCreateRequest createRequest = question.getBody();

		// 事务提交阶段
		return (Hint) Results.success(Boolean.TRUE.equals(transactionTemplate.execute(status -> {
			TravelInfoCreateRequest travelInfoCreateRequest = createRequest.getTravelInfoCreateRequest();
			// 创建旅行社信息
			TravelInfo travelInfo = travelInfoCreateComponent.createTravelInfo(travelInfoConverter.c_r2p(travelInfoCreateRequest));
			if (travelInfo == null) {
				return false;
			}
			TravelTaxCreateRequest travelTaxCreateRequest = createRequest.getTravelTaxCreateRequest();
			if (travelTaxCreateRequest != null) {
				travelTaxCreateRequest.setTravelId(travelInfo.getId());
				travelTaxCreateComponent.createTravelTax(travelTaxConverter.c_r2p(travelTaxCreateRequest));
			}
			CapitalAccountCreateRequest capitalAccountCreateRequest = createRequest.getCapitalAccountCreateRequest();
			if (capitalAccountCreateRequest != null) {
				capitalAccountCreateRequest.setTravelId(travelInfo.getId());
				capitalAccountCreateComponent.createCapitalAccount(capitalAccountConverter.c_r2p(capitalAccountCreateRequest));
			}

			question.setAttachment(CustomerAttachmentKey.TRAVEL_TAX_ATTACHMENT_KEY, travelInfo);

			return true;
		})));
	}
}
