package com.sendinfo.ticketing.verification.basic.component.customer.param;

import com.sendinfo.ticketing.verification.common.component.param.AbstractCreateParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;

/**
 * 业务员关联客户创建参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class SalesmanRelationTravelCreateParam extends AbstractCreateParam {

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 业务员ID
     */
    private Long salesmanId;

    /**
     * 客户ID
     */
    private Long travelId;

    /**
     * 关联周期-开始时间
     */
    private LocalDate openDate;

    /**
     * 关联周期-截止时间
     */
    private LocalDate endDate;

    /**
     * 是否周期无期限标识 T:是 F:否
     */
    private String foreverFlag;
} 