package com.sendinfo.ticketing.verification.basic.repository.pay.dao.impl;

import com.sendinfo.ticketing.verification.basic.repository.pay.arg.PayChanleDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.PayChanleQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.PayChanleUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dao.PayChanleDao;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.PayChanleDO;
import com.sendinfo.ticketing.verification.repository.jdbc.Statement;
import com.sendinfo.ticketing.verification.repository.jdbc.mybatis.*;
import lombok.Getter;
import org.apache.ibatis.session.SqlSession;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

/**
 * 支付渠道数据访问实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Repository("payChanleDao")
public class PayChanleDaoImpl implements PayChanleDao,
        SqlSessionGenericDAO<Long, PayChanleDO, PayChanleUpdateArg, PayChanleDeleteArg>,
        SqlSessionCountableDAO<PayChanleQueryArg>,
        SqlSessionQueryableDAO<PayChanleQueryArg, PayChanleDO>,
        SqlSessionBatchInsertDAO<Long, PayChanleDO>,
        SqlSessionDAO {

    private final SqlSession sqlSession;

    private final Statement statement;

    public PayChanleDaoImpl(@Qualifier("saasPwSqlSessionTemplate") SqlSessionTemplate sqlSessionTemplate) {
        this.sqlSession = sqlSessionTemplate;
        this.statement = Statement.of(PayChanleDao.class);
    }
} 