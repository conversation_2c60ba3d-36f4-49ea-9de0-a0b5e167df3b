package com.sendinfo.ticketing.verification.basic.repository.system.arg;

import com.sendinfo.ticketing.verification.common.repository.arg.AbstractUpdateArg;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 票型角色更新参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class SysAuditRoleUpdateArg extends AbstractUpdateArg<Integer> {

    /**
     * 企业码
     */
    private String corpCode;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 权限类型
     */
    private Integer roleType;

    /**
     * 子系统ID
     */
    private Integer subsystemId;

    /**
     * 启用状态
     */
    private String useFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 修改人
     */
    private String modifyBy;
} 