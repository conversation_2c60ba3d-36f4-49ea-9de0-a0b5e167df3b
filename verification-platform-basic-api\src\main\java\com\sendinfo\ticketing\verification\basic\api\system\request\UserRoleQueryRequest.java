package com.sendinfo.ticketing.verification.basic.api.system.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 角色用户表查询请求参数
 * 对应表：user_role
 * 字段、注释、注解、命名、风格严格对齐 system/api 层
 * <AUTHOR>
 */
@Getter
@Setter
@ToString(callSuper = true)
public class UserRoleQueryRequest implements Serializable {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 系统角色ID
     */
    private Long sysRoleId;


} 