package com.sendinfo.ticketing.verification.basic.api.system.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 角色业务权限查询请求参数
 * 对应表：role_business
 * 字段、注释、注解、命名、风格严格对齐 system/api 层
 * <AUTHOR>
 */
@Getter
@Setter
@ToString(callSuper = true)
public class RoleBusinessQueryRequest implements Serializable {

    /**
     * 企业码
     */
    @NotNull(message = "企业码不能为空")
    private String corpCode;

    /**
     * 角色ID
     */
    private Integer roleId;

    /**
     * 业务ID
     */
    private Integer businessId;

    /**
     * 业务类型，travel：旅行社
     */
    private String businessType;

} 