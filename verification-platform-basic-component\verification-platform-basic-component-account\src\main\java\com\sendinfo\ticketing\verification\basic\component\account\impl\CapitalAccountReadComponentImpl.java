package com.sendinfo.ticketing.verification.basic.component.account.impl;

import com.sendinfo.ticketing.verification.basic.component.account.CapitalAccountReadComponent;
import com.sendinfo.ticketing.verification.basic.component.account.converter.CapitalAccountConverter;
import com.sendinfo.ticketing.verification.basic.component.account.param.CapitalAccountQueryParam;
import com.sendinfo.ticketing.verification.basic.model.account.CapitalAccount;
import com.sendinfo.ticketing.verification.basic.repository.account.arg.CapitalAccountQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.account.dao.CapitalAccountDao;
import com.sendinfo.ticketing.verification.basic.repository.account.dataobject.CapitalAccountDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedCountRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedListRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleRead;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 资金账户读取组件实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("capitalAccountReadComponent")
@Getter
public class CapitalAccountReadComponentImpl implements
        CapitalAccountReadComponent,
        DaoBasedSingleRead<Long, CapitalAccountQueryParam, CapitalAccount, CapitalAccountDO>,
        DaoBasedListRead<Long, CapitalAccountQueryParam, CapitalAccount, CapitalAccountDO, CapitalAccountQueryArg>,
        DaoBasedCountRead<Long, CapitalAccountQueryParam, CapitalAccount, CapitalAccountQueryArg> {

    private final CapitalAccountDao dao;
    private final CapitalAccountConverter converter;

    public CapitalAccountReadComponentImpl(CapitalAccountDao dao, CapitalAccountConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }

    @Override
    public List<CapitalAccount> queryAccountList(CapitalAccountQueryParam queryParam) {
        return converter.r_ds2ms(dao.queryAccountList(converter.r_p2a(queryParam)));
    }
} 