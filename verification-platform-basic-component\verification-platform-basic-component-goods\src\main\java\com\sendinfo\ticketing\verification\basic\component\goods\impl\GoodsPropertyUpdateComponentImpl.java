package com.sendinfo.ticketing.verification.basic.component.goods.impl;

import com.sendinfo.ticketing.verification.basic.component.goods.GoodsPropertyUpdateComponent;
import com.sendinfo.ticketing.verification.basic.component.goods.converter.GoodsPropertyConverter;
import com.sendinfo.ticketing.verification.basic.component.goods.param.GoodsPropertyUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.goods.GoodsProperty;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.GoodsPropertyUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dao.GoodsPropertyDao;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleUpdate;
import lombok.Getter;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025-07-18 17:35
 */
@Getter
@Component("goodsPropertyUpdateComponent")
public class GoodsPropertyUpdateComponentImpl implements GoodsPropertyUpdateComponent,
        DaoBasedSingleUpdate<GoodsPropertyUpdateParam, GoodsPropertyUpdateArg, GoodsProperty> {


    private final GoodsPropertyDao dao;
    private final GoodsPropertyConverter converter;

    public GoodsPropertyUpdateComponentImpl(GoodsPropertyDao dao
            , GoodsPropertyConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }
}
