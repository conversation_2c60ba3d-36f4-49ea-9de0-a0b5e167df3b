package com.sendinfo.ticketing.verification.basic.model.auth.domain;

import com.sendinfo.ticketing.verification.common.model.domain.DomainDefinition;
import com.sendinfo.ticketing.verification.common.model.enums.ProductCode;

/**
 * <AUTHOR>
 * @since 2025-07-23 13:39:09
 */
public enum AuthDomainDefinition implements DomainDefinition {

    AUTH;

    @Override
    public ProductCode productCode() {
        return ProductCode.TICKET_PLATFORM;
    }

    @Override
    public String code() {
        return "015";
    }
}