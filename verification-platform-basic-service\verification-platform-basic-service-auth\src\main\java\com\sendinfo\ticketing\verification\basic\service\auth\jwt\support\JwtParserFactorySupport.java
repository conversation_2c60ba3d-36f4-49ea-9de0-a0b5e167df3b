package com.sendinfo.ticketing.verification.basic.service.auth.jwt.support;

import com.sendinfo.ticketing.verification.basic.service.auth.jwt.JwtIdentity;
import com.sendinfo.ticketing.verification.basic.service.auth.jwt.JwtParserFactory;
import io.jsonwebtoken.JwtParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.EnumMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025-07-23 13:24:31
 */
@Component("jwtParserFactorySupport")
@Slf4j
public class JwtParserFactorySupport implements JwtParserFactory {


    private final Map<JwtIdentity, JwtParserBuilder> id2BuilderMap = new EnumMap<>(JwtIdentity.class);

    public final Map<JwtIdentity, JwtParser> id2JwtParserMap = new EnumMap<>(JwtIdentity.class);

    public JwtParserFactorySupport(List<JwtParserBuilder> builders) {
        builders.forEach(builder -> {
            id2BuilderMap.put(builder.supportedIdentity(), builder);
            log.info("[JwtParserFactorySupport] supported identity: {}, builder: {}",
                    builder.supportedIdentity(), builder.getClass().getName());
        });
    }

    @Override
    public JwtParser getParser(JwtIdentity jwtIdentity) {
        JwtParser jwtParser = id2JwtParserMap.get(jwtIdentity);
        if (jwtParser == null) {
            synchronized (this) {
                jwtParser = id2JwtParserMap.get(jwtIdentity);
                if (jwtParser == null) {
                    jwtParser = id2BuilderMap.get(jwtIdentity).build();
                    id2JwtParserMap.put(jwtIdentity, jwtParser);
                }
            }
        }
        return jwtParser;
    }
}
