package com.sendinfo.ticketing.verification.basic.component.goods.converter;

import com.sendinfo.ticketing.verification.basic.api.goods.request.TktClassInfoQueryRequest;
import com.sendinfo.ticketing.verification.basic.component.goods.param.TktClassInfoQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TktClassInfo;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TktClassInfoQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TktClassInfoDO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

/**
 * @Auther: chengjie.li
 * @Date: 2025/6/18 11:45
 */
@RunWith(MockitoJUnitRunner.class)
public class TktClassInfoConverterTest {

    @InjectMocks
    private TktClassInfoConverter tktClassInfoConverter;

    private static final String TEST_CORP_CODE = "TEST_CORP_001";
    private static final String TEST_CLASS_NAME = "TEST_CLASS_NAME";
    private static final Integer TEST_SORT_NUM = 1;
    private static final String TEST_REMARK = "TEST_REMARK";
    private static final String TEST_USE_FLAG = "T";
    private static final String TEST_DEPT_ID = "TEST_DEPT_ID";
    private static final String TEST_CLASS_CODE = "TEST_CLASS_CODE";
    private static final Long TEST_PARANT_ID = 1L;
    private static final String TEST_CREATE_BY = "testUser";

    /**
     * 测试目的：验证TktClassInfoDO转换为TktClassInfo的功能
     * 测试步骤：
     * 1. 创建完整的TktClassInfoDO对象
     * 2. 调用r_d2m方法进行转换
     * 3. 验证转换结果的正确性
     */
    @Test
    public void testReadDo2Model_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        TktClassInfoDO tktClassInfoDO = createTktClassInfoDO();

        // 2. 执行转换
        TktClassInfo result = tktClassInfoConverter.r_d2m(tktClassInfoDO);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", result);
        assertEquals("企业编码应该一致", TEST_CORP_CODE, result.getCorpCode());
        assertEquals("种类名称应该一致", TEST_CLASS_NAME, result.getClassName());
        assertEquals("排序应该一致", TEST_SORT_NUM, result.getSortNum());
        assertEquals("备注应该一致", TEST_REMARK, result.getRemark());
        assertEquals("启用标志应该一致", TEST_USE_FLAG, result.getUseFlag());
        assertEquals("组织机构ID应该一致", TEST_DEPT_ID, result.getDeptId());
        assertEquals("种类编码应该一致", TEST_CLASS_CODE, result.getClassCode());
        assertEquals("父ID应该一致", TEST_PARANT_ID, result.getParantId());
        assertEquals("创建人应该一致", TEST_CREATE_BY, result.getCreateBy());
        assertEquals("修改人应该一致", TEST_CREATE_BY, result.getModifyBy());
    }

    /**
     * 测试目的：验证TktClassInfoQueryParam转换为TktClassInfoQueryArg的功能
     * 测试步骤：
     * 1. 创建完整的TktClassInfoQueryParam对象
     * 2. 调用r_p2a方法进行转换
     * 3. 验证转换结果的正确性
     */
    @Test
    public void testReadQueryParam2QueryArg_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        TktClassInfoQueryParam queryParam = createTktClassInfoQueryParam();

        // 2. 执行转换
        TktClassInfoQueryArg queryArg = tktClassInfoConverter.r_p2a(queryParam);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", queryArg);
        assertEquals("企业编码应该一致", TEST_CORP_CODE, queryArg.getCorpCode());
        assertEquals("启用标志应该一致", TEST_USE_FLAG, queryArg.getUseFlag());
    }

    /**
     * 测试目的：验证TktClassInfoQueryRequest转换为TktClassInfoQueryParam的功能
     * 测试步骤：
     * 1. 创建完整的TktClassInfoQueryRequest对象
     * 2. 调用r_r2p方法进行转换
     * 3. 验证转换结果的正确性
     */
    @Test
    public void testReadQueryRequest2QueryParam_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        TktClassInfoQueryRequest queryRequest = createTktClassInfoQueryRequest();

        // 2. 执行转换
        TktClassInfoQueryParam queryParam = tktClassInfoConverter.r_r2p(queryRequest);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", queryParam);
        assertEquals("企业编码应该一致", TEST_CORP_CODE, queryParam.getCorpCode());
        assertEquals("启用标志应该一致", TEST_USE_FLAG, queryParam.getUseFlag());
    }

    /**
     * 测试目的：验证List<TktClassInfoDO>转换为List<TktClassInfo>的功能
     * 测试步骤：
     * 1. 创建完整的TktClassInfoDO列表
     * 2. 调用r_ds2ms方法进行转换
     * 3. 验证转换结果的正确性
     */
    @Test
    public void testReadDos2Models_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        List<TktClassInfoDO> tktClassInfoDOList = createTktClassInfoDOList();

        // 2. 执行转换
        List<TktClassInfo> result = tktClassInfoConverter.r_ds2ms(tktClassInfoDOList);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", result);
        assertEquals("列表大小应该一致", tktClassInfoDOList.size(), result.size());

        // 验证第一个元素
        TktClassInfo firstResult = result.get(0);
        assertEquals("第一个元素的企业编码应该一致", TEST_CORP_CODE, firstResult.getCorpCode());
        assertEquals("第一个元素的种类名称应该一致", TEST_CLASS_NAME, firstResult.getClassName());
        assertEquals("第一个元素的排序应该一致", TEST_SORT_NUM, firstResult.getSortNum());

        // 验证第二个元素
        TktClassInfo secondResult = result.get(1);
        assertEquals("第二个元素的企业编码应该一致", TEST_CORP_CODE, secondResult.getCorpCode());
        assertEquals("第二个元素的种类名称应该一致", TEST_CLASS_NAME + "_2", secondResult.getClassName());
    }

    private TktClassInfoQueryRequest createTktClassInfoQueryRequest() {
        TktClassInfoQueryRequest queryRequest = new TktClassInfoQueryRequest();
        queryRequest.setCorpCode(TEST_CORP_CODE);
        queryRequest.setUseFlag(TEST_USE_FLAG);
        return queryRequest;
    }

    private TktClassInfoQueryParam createTktClassInfoQueryParam() {
        TktClassInfoQueryParam queryParam = new TktClassInfoQueryParam();
        queryParam.setCorpCode(TEST_CORP_CODE);
        queryParam.setClassName(TEST_CLASS_NAME);
        queryParam.setUseFlag(TEST_USE_FLAG);
        queryParam.setDeptId(TEST_DEPT_ID);
        queryParam.setClassCode(TEST_CLASS_CODE);
        queryParam.setParantId(TEST_PARANT_ID);
        return queryParam;
    }

    private TktClassInfoDO createTktClassInfoDO() {
        TktClassInfoDO classInfoDO = new TktClassInfoDO();
        classInfoDO.setCorpCode(TEST_CORP_CODE);
        classInfoDO.setClassName(TEST_CLASS_NAME);
        classInfoDO.setSortNum(TEST_SORT_NUM);
        classInfoDO.setRemark(TEST_REMARK);
        classInfoDO.setUseFlag(TEST_USE_FLAG);
        classInfoDO.setDeptId(TEST_DEPT_ID);
        classInfoDO.setClassCode(TEST_CLASS_CODE);
        classInfoDO.setParantId(TEST_PARANT_ID);
        classInfoDO.setCreateBy(TEST_CREATE_BY);
        classInfoDO.setModifyBy(TEST_CREATE_BY);
        return classInfoDO;
    }

    private List<TktClassInfoDO> createTktClassInfoDOList() {
        List<TktClassInfoDO> list = new ArrayList<>();

        TktClassInfoDO classInfoDO1 = new TktClassInfoDO();
        classInfoDO1.setCorpCode(TEST_CORP_CODE);
        classInfoDO1.setClassName(TEST_CLASS_NAME);
        classInfoDO1.setSortNum(TEST_SORT_NUM);
        classInfoDO1.setRemark(TEST_REMARK);
        classInfoDO1.setUseFlag(TEST_USE_FLAG);
        classInfoDO1.setDeptId(TEST_DEPT_ID);
        classInfoDO1.setClassCode(TEST_CLASS_CODE);
        classInfoDO1.setParantId(TEST_PARANT_ID);
        classInfoDO1.setCreateBy(TEST_CREATE_BY);
        classInfoDO1.setModifyBy(TEST_CREATE_BY);
        list.add(classInfoDO1);

        TktClassInfoDO classInfoDO2 = new TktClassInfoDO();
        classInfoDO2.setCorpCode(TEST_CORP_CODE);
        classInfoDO2.setClassName(TEST_CLASS_NAME + "_2");
        classInfoDO2.setSortNum(TEST_SORT_NUM + 1);
        classInfoDO2.setRemark(TEST_REMARK + "_2");
        classInfoDO2.setUseFlag(TEST_USE_FLAG);
        classInfoDO2.setDeptId(TEST_DEPT_ID + "_2");
        classInfoDO2.setClassCode(TEST_CLASS_CODE + "_2");
        classInfoDO2.setParantId(TEST_PARANT_ID + 1);
        classInfoDO2.setCreateBy(TEST_CREATE_BY);
        classInfoDO2.setModifyBy(TEST_CREATE_BY);
        list.add(classInfoDO2);

        return list;
    }
} 