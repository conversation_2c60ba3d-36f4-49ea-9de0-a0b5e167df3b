package com.sendinfo.ticketing.verification.basic.service.system.function;

import com.sendinfo.ticketing.verification.basic.api.system.request.SysSubsystemQueryCondition;
import com.sendinfo.ticketing.verification.basic.api.system.request.SysSubsystemQueryRequest;
import com.sendinfo.ticketing.verification.basic.component.system.SysSubsystemReadComponent;
import com.sendinfo.ticketing.verification.basic.component.system.converter.SysSubsystemConverter;
import com.sendinfo.ticketing.verification.basic.model.system.SysSubsystem;
import com.sendinfo.ticketing.verification.basic.model.system.error.SystemErrorDef;
import com.sendinfo.ticketing.verification.basic.service.system.enums.SystemAttachmentKey;
import com.sendinfo.ticketing.verification.common.api.request.PageRequest;
import com.sendinfo.ticketing.verification.common.service.function.QueryPageByConditionFunction;
import com.sendinfo.ticketing.verification.common.service.function.QuerySingleByIdFunction;
import com.sendinfo.ticketing.verification.common.service.stereotype.Function;
import com.sendinfo.ticketing.verification.common.service.support.QueryPageByConditionLogicAction;
import com.sendinfo.ticketing.verification.common.service.support.QuerySingleByIdLogicAction;
import com.sendinfo.ticketing.verification.flow.Hint;
import com.sendinfo.ticketing.verification.flow.Question;
import com.sendinfo.ticketing.verification.flow.function.LogicAction;
import lombok.Getter;

import java.util.List;

/**
 * 子系统管理查询功能
 * <AUTHOR>
 */
@Getter
@Function("querySysSubsystemFunction")
public class QuerySysSubsystemFunction implements
        QuerySingleByIdFunction<Long>,
        QueryPageByConditionFunction<SysSubsystemQueryCondition> {

    private final SysSubsystemReadComponent sysSubsystemReadComponent;
    private final SysSubsystemConverter converter;
    private final LogicAction<Long> querySingleById;
    private final LogicAction<PageRequest<SysSubsystemQueryCondition>> queryPageByCondition;

    public QuerySysSubsystemFunction(SysSubsystemReadComponent sysSubsystemReadComponent, SysSubsystemConverter converter) {
        this.sysSubsystemReadComponent = sysSubsystemReadComponent;
        this.converter = converter;
        this.querySingleById = new QuerySingleByIdLogicAction<>(sysSubsystemReadComponent,
                SystemAttachmentKey.SYS_SUBSYSTEM_ATTACHMENT_KEY,
                SystemErrorDef.QUERY_SUBSYSTEM_ERROR);
        this.queryPageByCondition = new QueryPageByConditionLogicAction<>(sysSubsystemReadComponent, converter::r_pr2p,
                SystemAttachmentKey.SYS_SUBSYSTEM_DATA_LIST_ATTACHMENT_KEY,
                SystemAttachmentKey.SYS_SUBSYSTEM_DATA_COUNT_ATTACHMENT_KEY);
    }

    /**
     * 查询子系统列表
     *
     * @param question
     * @return
     */
    public Hint querySysSubsystems(Question<SysSubsystemQueryRequest> question) {
        SysSubsystemQueryRequest req = question.getBody();
        List<SysSubsystem> sysSubsystemList = sysSubsystemReadComponent.list(converter.r_r2p(req));

        question.setAttachment(SystemAttachmentKey.SYS_SUBSYSTEM_DATA_LIST_ATTACHMENT_KEY, sysSubsystemList);
        return Hint.gotoNext();
    }
} 