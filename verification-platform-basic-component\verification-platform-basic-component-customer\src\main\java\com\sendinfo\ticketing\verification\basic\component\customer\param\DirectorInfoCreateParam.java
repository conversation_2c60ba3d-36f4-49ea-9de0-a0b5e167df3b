package com.sendinfo.ticketing.verification.basic.component.customer.param;

import com.sendinfo.ticketing.verification.common.component.param.AbstractCreateParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 主管信息创建参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class DirectorInfoCreateParam extends AbstractCreateParam {


    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 主管编码
     */
    private String directorCode;

    /**
     * 主管编号
     */
    private String directorNo;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 主管简码
     */
    private String directorSimpleCode;

    /**
     * 性别
     */
    private String sex;

    /**
     * 证件号码
     */
    private String certNo;

    /**
     * 旅行社ID
     */
    private Long travelId;

    /**
     * 旅行社名称
     */
    private String travelName;

    /**
     * 启用状态
     */
    private String enable;

    /**
     * 电话号码
     */
    private String tel;

    /**
     * 地址
     */
    private String addr;

    /**
     * 邮政编码
     */
    private String zipCode;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 备注
     */
    private String remark;
} 