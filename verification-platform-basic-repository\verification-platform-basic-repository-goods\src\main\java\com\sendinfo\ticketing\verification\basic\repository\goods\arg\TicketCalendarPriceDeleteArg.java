package com.sendinfo.ticketing.verification.basic.repository.goods.arg;

import com.sendinfo.ticketing.verification.common.repository.arg.AbstractDeleteArg;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
@Builder
public class TicketCalendarPriceDeleteArg extends AbstractDeleteArg {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 票型ID
     */
    private Long ticketId;

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 修改人
     */
    private String modifyBy;
}
