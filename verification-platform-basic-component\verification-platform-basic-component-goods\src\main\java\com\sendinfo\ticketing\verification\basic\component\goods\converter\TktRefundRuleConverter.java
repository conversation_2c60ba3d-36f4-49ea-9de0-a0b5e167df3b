package com.sendinfo.ticketing.verification.basic.component.goods.converter;

import com.sendinfo.ticketing.verification.basic.api.goods.request.TktRefundRuleQueryCondition;
import com.sendinfo.ticketing.verification.basic.component.goods.mapper.TktRefundRuleMapper;
import com.sendinfo.ticketing.verification.basic.component.goods.param.TktRefundRuleQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TktRefundRule;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TktRefundRuleQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TktRefundRuleDO;
import com.sendinfo.ticketing.verification.common.api.request.PageRequest;
import com.sendinfo.ticketing.verification.common.component.converter.ReadDo2ModelConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadPageReq2ParamConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadParam2ArgConverter;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component("tktRefundRuleConverter")
public class TktRefundRuleConverter implements
        ReadParam2ArgConverter<TktRefundRuleQueryParam, TktRefundRuleQueryArg>,
        ReadDo2ModelConverter<TktRefundRuleDO, TktRefundRule>,
        ReadPageReq2ParamConverter<TktRefundRuleQueryCondition, TktRefundRuleQueryParam> {

    @Override
    public TktRefundRuleQueryArg r_p2a(TktRefundRuleQueryParam param) {
        return TktRefundRuleMapper.INSTANCE.convert(param);
    }

    @Override
    public TktRefundRule r_d2m(TktRefundRuleDO dataObject) {
        return TktRefundRuleMapper.INSTANCE.convert(dataObject);
    }

    @Override
    public List<TktRefundRule> r_ds2ms(List<TktRefundRuleDO> dataObjects) {
        return dataObjects.stream()
                .map(TktRefundRuleMapper.INSTANCE::convert)
                .collect(Collectors.toList());
    }

    @Override
    public TktRefundRuleQueryParam r_pr2p(PageRequest<TktRefundRuleQueryCondition> pageCondition) {
        TktRefundRuleQueryParam queryParam = new TktRefundRuleQueryParam();
        TktRefundRuleQueryCondition condition = pageCondition.getCondition();
        if (condition != null) {
        }
        queryParam.setStartIndex(pageCondition.getStartIndex());
        queryParam.setPageSize(pageCondition.getPageSize());
        return queryParam;
    }
} 