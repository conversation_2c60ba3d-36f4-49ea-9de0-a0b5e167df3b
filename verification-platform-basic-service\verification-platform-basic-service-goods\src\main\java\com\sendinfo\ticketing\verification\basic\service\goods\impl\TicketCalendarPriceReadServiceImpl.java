package com.sendinfo.ticketing.verification.basic.service.goods.impl;

import com.sendinfo.ticketing.verification.basic.api.goods.TicketCalendarPriceReadService;
import com.sendinfo.ticketing.verification.basic.api.goods.request.TktClassInfoQueryRequest;
import com.sendinfo.ticketing.verification.basic.exception.TicketPlatformBizRuntimeException;
import com.sendinfo.ticketing.verification.basic.model.goods.TicketCalendarPrice;
import com.sendinfo.ticketing.verification.basic.model.goods.TktClassInfo;
import com.sendinfo.ticketing.verification.basic.model.goods.error.GoodsErrorDef;
import com.sendinfo.ticketing.verification.basic.service.goods.enums.GoodsAttachmentKey;
import com.sendinfo.ticketing.verification.basic.service.goods.function.QueryTicketCalendarPrice;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;
import com.sendinfo.ticketing.verification.common.api.result.Results;
import com.sendinfo.ticketing.verification.common.service.support.ApiCommonFailedBiActions;
import com.sendinfo.ticketing.verification.flow.FlowAgentBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/7/28 9:24
 **/
@Slf4j
@Service("ticketCalendarPriceReadService")
public class TicketCalendarPriceReadServiceImpl implements TicketCalendarPriceReadService {

    private final FlowAgentBuilder flowAgentBuilder;
    private final QueryTicketCalendarPrice queryTicketCalendarPrice;

    public TicketCalendarPriceReadServiceImpl(FlowAgentBuilder flowAgentBuilder, QueryTicketCalendarPrice queryTicketCalendarPrice) {
        this.flowAgentBuilder = flowAgentBuilder;
        this.queryTicketCalendarPrice = queryTicketCalendarPrice;
    }

    @Override
    public ResultModel<List<TicketCalendarPrice>> batchQueryByTicketIds(List<Long> ticketIds, String corpCode) {
        return flowAgentBuilder.<Pair<List<Long>,String>, ResultModel<List<TicketCalendarPrice>>>validateThenChooseBuilder()
                .appendLogicAction(queryTicketCalendarPrice::queryTicketCalendarPrice)
                .withSuccessfulAction(q -> {
                    List<TicketCalendarPrice> ticketCalendarPrices = q.getAttachment(GoodsAttachmentKey.TICKET_CALENDAR_PRICE_DATA_LIST_ATTACHMENT_KEY);
                    return Results.success(ticketCalendarPrices);
                })
                .withFailedBiAction(ApiCommonFailedBiActions::reasonMappingToResult)
                .withExceptionBiAction((q, th) -> {
                    log.error("[TicketCalendarPriceReadServiceImpl] batchQueryByTicketIds error, question is {}", q, th);
                    return Results.fail(GoodsErrorDef.QUERY_TKT_CALENDAR_PRICE_ERROR);
                })
                .rethrowException(TicketPlatformBizRuntimeException.class::isInstance)
                .build()
                .prompt(Pair.of(ticketIds, corpCode))
                .getResult();
    }
}
