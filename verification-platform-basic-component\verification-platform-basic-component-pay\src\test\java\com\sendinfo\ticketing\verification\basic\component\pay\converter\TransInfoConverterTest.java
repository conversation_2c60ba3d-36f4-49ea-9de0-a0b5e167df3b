package com.sendinfo.ticketing.verification.basic.component.pay.converter;

import com.sendinfo.ticketing.verification.basic.component.pay.param.TransInfoCreateParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.TransInfoQueryParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.TransInfoUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.pay.TransInfo;
import com.sendinfo.ticketing.verification.basic.model.pay.enums.BusinessTypeEnum;
import com.sendinfo.ticketing.verification.basic.model.pay.enums.PayStatusEnum;
import com.sendinfo.ticketing.verification.basic.model.pay.enums.TransTypeEnum;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.TransInfoQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.TransInfoUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.TransInfoDO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Date;

import static org.junit.Assert.*;

/**
 * TransInfoConverter单元测试
 * 测试交易记录转换器的各种转换功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RunWith(MockitoJUnitRunner.class)
public class TransInfoConverterTest {

    @InjectMocks
    private TransInfoConverter transInfoConverter;

    // 测试常量
    private static final String TEST_CORP_CODE = "TEST_CORP_001";
    private static final Long TEST_ID = 1L;
    private static final String TEST_TRANS_NO = "TRANS_NO_001";
    private static final TransTypeEnum TEST_TRANS_TYPE = TransTypeEnum.PAY;
    private static final Long TEST_TRADE_ID = 1001L;
    private static final String TEST_TRADE_CODE = "TRADE_CODE_001";
    private static final String TEST_PAY_AWAY = "现金";
    private static final Integer TEST_GATEWAY = 1;
    private static final String TEST_GATEWAY_TRANS_NO = "GATEWAY_TRANS_001";
    private static final BusinessTypeEnum TEST_BUSINESS_TYPE = BusinessTypeEnum.TICKET;
    private static final BigDecimal TEST_PAY_AMOUNT = new BigDecimal("100.00");
    private static final Long TEST_LOGIN_ID = 2001L;
    private static final String TEST_LOGIN_NAME = "testUser";
    private static final PayStatusEnum TEST_PAY_STATUS = PayStatusEnum.PAID;
    private static final Date TEST_REPORT_TIME = new Date();
    private static final Date TEST_PAY_TIME = new Date();
    private static final String TEST_REPORT_INFO = "测试报文";
    private static final String TEST_PAY_TYPE_VIEW = "现金支付";
    private static final BigDecimal TEST_PAY_TOTAL = new BigDecimal("100.00");
    private static final String TEST_REFUND_PAY_TRANSNO = "REFUND_TRANS_001";
    private static final String TEST_PAY_TYPE = "CASH";
    private static final Long TEST_MAINBODY_PAYID = 3001L;
    private static final String TEST_PAY_INFO = "支付信息";
    private static final String TEST_EXTEND_PARAM_JSON = "{\"key\":\"value\"}";
    private static final String TEST_TID = "TID_001";
    private static final Integer TEST_PAY_INTEGRAL_SUM = 10;
    private static final BigDecimal TEST_PAY_INTEGRAL_TOTAL = new BigDecimal("5.00");
    private static final String TEST_OTHER_TRANS_NO = "OTHER_TRANS_001";
    private static final String TEST_SETTLEMENT = "T";
    private static final String TEST_CREATE_BY = "creator";
    private static final String TEST_MODIFY_BY = "modifier";

    /**
     * 构造TransInfoCreateParam测试数据
     */
    private TransInfoCreateParam createTransInfoCreateParam() {
        TransInfoCreateParam param = new TransInfoCreateParam();
        param.setCorpCode(TEST_CORP_CODE);
        param.setTransNo(TEST_TRANS_NO);
        param.setTransType(TEST_TRANS_TYPE);
        param.setTradeId(TEST_TRADE_ID);
        param.setTradeCode(TEST_TRADE_CODE);
        param.setPayAway(TEST_PAY_AWAY);
        param.setGateway(TEST_GATEWAY);
        param.setGatewayTransNo(TEST_GATEWAY_TRANS_NO);
        param.setBusinessType(TEST_BUSINESS_TYPE);
        param.setPayAmount(TEST_PAY_AMOUNT);
        param.setLoginId(TEST_LOGIN_ID);
        param.setLoginName(TEST_LOGIN_NAME);
        param.setPayStatus(TEST_PAY_STATUS);
        param.setReportTime(TEST_REPORT_TIME);
        param.setPayTime(TEST_PAY_TIME);
        param.setReportInfo(TEST_REPORT_INFO);
        param.setPayTypeView(TEST_PAY_TYPE_VIEW);
        param.setPayTotal(TEST_PAY_TOTAL);
        param.setRefundPayTransno(TEST_REFUND_PAY_TRANSNO);
        param.setPayType(TEST_PAY_TYPE);
        param.setMainbodyPayid(TEST_MAINBODY_PAYID);
        param.setPayInfo(TEST_PAY_INFO);
        param.setExtendParamJson(TEST_EXTEND_PARAM_JSON);
        param.setTid(TEST_TID);
        param.setPayIntegralSum(TEST_PAY_INTEGRAL_SUM);
        param.setPayIntegralTotal(TEST_PAY_INTEGRAL_TOTAL);
        param.setOtherTransNo(TEST_OTHER_TRANS_NO);
        param.setSettlement(TEST_SETTLEMENT);
        param.setCreateBy(TEST_CREATE_BY);
        param.setModifyBy(TEST_MODIFY_BY);
        return param;
    }

    /**
     * 测试目的：验证TransInfoCreateParam转换为TransInfoDO的功能
     */
    @Test
    public void testCreateParam2Do_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        TransInfoCreateParam createParam = createTransInfoCreateParam();
        // 2. 执行转换
        TransInfoDO result = transInfoConverter.c_p2d(createParam);
        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", result);
        assertEquals("企业编码应该一致", TEST_CORP_CODE, result.getCorpCode());
        assertEquals("交易类型应该一致", TEST_TRANS_TYPE.getCode(), result.getTransType().intValue());
        assertEquals("主交易ID应该一致", TEST_TRADE_ID, result.getTradeId());
        assertEquals("创建人应该一致", TEST_CREATE_BY, result.getCreateBy());
        assertEquals("修改人应该一致", TEST_MODIFY_BY, result.getModifyBy());
    }

    /**
     * 测试目的：验证TransInfoCreateParam为null时的处理
     */
    @Test
    public void testCreateParam2Do_WithNullInput_ShouldReturnNull() {
        TransInfoDO result = transInfoConverter.c_p2d(null);
        assertNull("输入为null时应该返回null", result);
    }

    /**
     * 测试目的：验证TransInfoDO转换为TransInfo的功能
     */
    @Test
    public void testReadDo2Model_ShouldConvertSuccessfully() {
        TransInfoDO doObj = createTransInfoDO();
        TransInfo result = transInfoConverter.r_d2m(doObj);
        assertNotNull("转换结果不应为null", result);
        assertEquals("ID应该一致", TEST_ID, result.getId());
        assertEquals("企业编码应该一致", TEST_CORP_CODE, result.getCorpCode());
        assertEquals("交易类型应该一致", TEST_TRANS_TYPE, result.getTransType());
    }

    /**
     * 测试目的：验证TransInfoDO为null时的处理
     */
    @Test
    public void testReadDo2Model_WithNullInput_ShouldReturnNull() {
        TransInfo result = transInfoConverter.r_d2m(null);
        assertNull("输入为null时应该返回null", result);
    }

    /**
     * 测试目的：验证TransInfoQueryParam转换为TransInfoQueryArg的功能（含分页参数）
     */
    @Test
    public void testReadQueryParam2QueryArg_ShouldConvertSuccessfully() {
        TransInfoQueryParam queryParam = createTransInfoQueryParam();
        queryParam.setStartIndex(5);
        queryParam.setPageSize(20);
        TransInfoQueryArg result = transInfoConverter.r_p2a(queryParam);
        assertNotNull("转换结果不应为null", result);
        assertEquals("企业编码应该一致", TEST_CORP_CODE, result.getCorpCode());
        assertEquals("偏移量应该一致", Integer.valueOf(5), result.getOffset());
        assertEquals("限制数量应该一致", Integer.valueOf(20), result.getLimit());
    }

    /**
     * 测试目的：验证TransInfoQueryParam为null时的处理
     */
    @Test
    public void testReadQueryParam2QueryArg_WithNullInput_ShouldReturnNull() {
        TransInfoQueryArg result = transInfoConverter.r_p2a(null);
        assertNull("输入为null时应该返回null", result);
    }

    /**
     * 测试目的：验证分页参数边界值
     */
    @Test
    public void testReadQueryParam2QueryArg_WithEdgePaging() {
        TransInfoQueryParam queryParam = createTransInfoQueryParam();
        queryParam.setStartIndex(0);
        queryParam.setPageSize(0);
        TransInfoQueryArg result = transInfoConverter.r_p2a(queryParam);
        assertNotNull(result);
        assertEquals(Integer.valueOf(0), result.getOffset());
        assertEquals(Integer.valueOf(0), result.getLimit());
        // 负数
        queryParam.setStartIndex(-1);
        queryParam.setPageSize(-10);
        result = transInfoConverter.r_p2a(queryParam);
        assertNotNull(result);
        assertEquals(Integer.valueOf(-1), result.getOffset());
        assertEquals(Integer.valueOf(-10), result.getLimit());
    }

    /**
     * 测试目的：验证TransInfoUpdateParam转换为TransInfoUpdateArg的功能（含payStatusUpdater）
     */
    @Test
    public void testUpdateParam2UpdateArg_ShouldConvertSuccessfully() {
        TransInfoUpdateParam updateParam = createTransInfoUpdateParam();
        updateParam.setPayStatus(PayStatusEnum.PAID);
        TransInfo currentModel = createTransInfo();
        currentModel.setPayStatus(PayStatusEnum.UNPAID);
        TransInfoUpdateArg result = transInfoConverter.u_p2a(updateParam, currentModel);
        assertNotNull("转换结果不应为null", result);
        assertEquals("修改人应该一致", TEST_MODIFY_BY, result.getModifyBy());
        assertNotNull("payStatusUpdater应该被设置", result.getPayStatusUpdater());
        assertEquals("原状态应该一致", PayStatusEnum.UNPAID.getCode(), result.getPayStatusUpdater().getCurrent());
        assertEquals("新状态应该一致", PayStatusEnum.PAID.getCode(), result.getPayStatusUpdater().getTarget());
    }

    /**
     * 测试目的：验证TransInfoUpdateParam为null时的处理
     */
    @Test
    public void testUpdateParam2UpdateArg_WithNullInput_ShouldReturnNull() {
        TransInfo currentModel = createTransInfo();
        TransInfoUpdateArg result = transInfoConverter.u_p2a(null, currentModel);
        assertNull("输入为null时应该返回null", result);
    }

    /**
     * 测试目的：payStatus为null时payStatusUpdater不应被设置
     */
    @Test
    public void testUpdateParam2UpdateArg_WithNullPayStatus() {
        TransInfoUpdateParam updateParam = createTransInfoUpdateParam();
        updateParam.setPayStatus(null);
        TransInfo currentModel = createTransInfo();
        currentModel.setPayStatus(PayStatusEnum.PAID);
        TransInfoUpdateArg result = transInfoConverter.u_p2a(updateParam, currentModel);
        assertNotNull(result);
        assertNull("payStatusUpdater为null", result.getPayStatusUpdater());
    }

    // ========== 测试数据构造方法补充 ==========
    private TransInfoDO createTransInfoDO() {
        TransInfoDO doObj = new TransInfoDO();
        doObj.setId(TEST_ID);
        doObj.setCorpCode(TEST_CORP_CODE);
        doObj.setTransNo(TEST_TRANS_NO);
        doObj.setTransType(TEST_TRANS_TYPE.ordinal());
        doObj.setTradeId(TEST_TRADE_ID);
        doObj.setCreateBy(TEST_CREATE_BY);
        doObj.setModifyBy(TEST_MODIFY_BY);
        return doObj;
    }

    private TransInfo createTransInfo() {
        TransInfo info = new TransInfo();
        info.setId(TEST_ID);
        info.setCorpCode(TEST_CORP_CODE);
        info.setTransNo(TEST_TRANS_NO);
        info.setTransType(TEST_TRANS_TYPE);
        info.setTradeId(TEST_TRADE_ID);
        info.setCreateBy(TEST_CREATE_BY);
        info.setModifyBy(TEST_MODIFY_BY);
        return info;
    }

    private TransInfoQueryParam createTransInfoQueryParam() {
        TransInfoQueryParam param = new TransInfoQueryParam();
        param.setCorpCode(TEST_CORP_CODE);
        param.setTransNo(TEST_TRANS_NO);
        param.setTransType(TEST_TRANS_TYPE);
        param.setTradeId(TEST_TRADE_ID);
        return param;
    }

    private TransInfoUpdateParam createTransInfoUpdateParam() {
        TransInfoUpdateParam param = new TransInfoUpdateParam();
        param.setCorpCode(TEST_CORP_CODE);
        param.setTransNo(TEST_TRANS_NO);
        param.setTradeId(TEST_TRADE_ID);
        param.setModifyBy(TEST_MODIFY_BY);
        return param;
    }



}