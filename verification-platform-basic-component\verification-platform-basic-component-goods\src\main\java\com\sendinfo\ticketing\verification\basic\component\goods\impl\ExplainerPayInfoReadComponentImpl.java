package com.sendinfo.ticketing.verification.basic.component.goods.impl;

import com.sendinfo.ticketing.verification.basic.component.goods.ExplainerPayInfoReadComponent;
import com.sendinfo.ticketing.verification.basic.component.goods.converter.ExplainerPayInfoConverter;
import com.sendinfo.ticketing.verification.basic.component.goods.param.ExplainerPayInfoQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.ExplainerPayInfo;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.ExplainerPayInfoQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dao.ExplainerPayInfoDao;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.ExplainerPayInfoDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedCountRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedListRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleRead;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 服务类目计费规则读取组件实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("explainerPayInfoReadComponent")
@Getter
public class ExplainerPayInfoReadComponentImpl implements ExplainerPayInfoReadComponent,
        DaoBasedSingleRead<Long, ExplainerPayInfoQueryParam, ExplainerPayInfo, ExplainerPayInfoDO>,
        DaoBasedListRead<Long, ExplainerPayInfoQueryParam, ExplainerPayInfo, ExplainerPayInfoDO, ExplainerPayInfoQueryArg>,
        DaoBasedCountRead<Long, ExplainerPayInfoQueryParam, ExplainerPayInfo, ExplainerPayInfoQueryArg> {

    private final ExplainerPayInfoDao dao;
    private final ExplainerPayInfoConverter converter;

    public ExplainerPayInfoReadComponentImpl(ExplainerPayInfoDao dao,
                                             ExplainerPayInfoConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }

    @Override
    public List<ExplainerPayInfo> queryPayInfoList(ExplainerPayInfoQueryParam queryParam) {
        List<ExplainerPayInfoDO> doList = dao.queryPayInfoList(converter.r_p2a(queryParam));
        return doList.stream().map(converter::r_d2m).collect(Collectors.toList());
    }
} 