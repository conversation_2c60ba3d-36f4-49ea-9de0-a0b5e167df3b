package com.sendinfo.ticketing.verification.basic.component.pay;

import com.sendinfo.ticketing.verification.basic.component.pay.param.SysMainbodyQueryParam;
import com.sendinfo.ticketing.verification.basic.model.pay.SysMainbody;
import com.sendinfo.ticketing.verification.common.component.ReadComponent;

/**
 * 经营主体读取组件接口
 * 定义读取经营主体的业务操作契约
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface SysMainbodyReadComponent extends ReadComponent<Long, SysMainbodyQueryParam, SysMainbody> {

} 