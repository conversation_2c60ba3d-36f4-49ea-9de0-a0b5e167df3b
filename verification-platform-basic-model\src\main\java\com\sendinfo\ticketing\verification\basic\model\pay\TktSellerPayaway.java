package com.sendinfo.ticketing.verification.basic.model.pay;

import com.sendinfo.ticketing.verification.basic.enums.CommonUseFlagEnum;
import com.sendinfo.ticketing.verification.basic.model.pay.enums.ClientTypeEnum;
import com.sendinfo.ticketing.verification.basic.model.pay.enums.SaleModelEnum;
import com.sendinfo.ticketing.verification.basic.model.pay.properties.TktSellerPayawayDefinitionPropertyKey;
import com.sendinfo.ticketing.verification.common.model.properties.AbstractProperties;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 售票员收款方式模型对象
 * 用于业务逻辑层和数据展示层之间的数据传输
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@ToString(callSuper = true)
public class TktSellerPayaway extends AbstractProperties<TktSellerPayawayDefinitionPropertyKey> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 售票员
     */
    private String seller;

    /**
     * 账户ID
     */
    private Long accId;

    /**
     * 售票模式：1:正常出票 2:预售票 3:电子商务票 4:手工票补录 5:剧院售票6：自助机7:扫码入园
     */
    private SaleModelEnum saleModel;

    /**
     * 客户类型（1：散客 2：团队...）
     */
    private ClientTypeEnum clientType;

    /**
     * 支付方式
     */
    private String payAway;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 启用状态
     */
    private CommonUseFlagEnum useFlag;

    /**
     * 支付ID
     */
    private Long payId;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String modifyBy;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 删除标志F未删除,T已删除
     */
    private String deleted;
} 