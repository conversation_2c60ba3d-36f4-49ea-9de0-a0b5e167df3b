package com.sendinfo.ticketing.verification.basic.repository.pay.dao.impl;

import com.sendinfo.ticketing.verification.basic.repository.pay.arg.MainbodyChanleDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.MainbodyChanleQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.MainbodyChanleUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dao.MainbodyChanleDao;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.MainbodyChanleDO;
import com.sendinfo.ticketing.verification.repository.jdbc.Statement;
import com.sendinfo.ticketing.verification.repository.jdbc.mybatis.*;
import lombok.Getter;
import org.apache.ibatis.session.SqlSession;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

/**
 * 商户渠道配置数据访问实现类
 * 实现MainbodyChanleDao接口的所有方法
 * 使用MyBatis进行数据访问，支持租户隔离和软删除
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Repository("mainbodyChanleDao")
public class MainbodyChanleDaoImpl implements MainbodyChanleDao,
        SqlSessionGenericDAO<Long, MainbodyChanleDO, MainbodyChanleUpdateArg, MainbodyChanleDeleteArg>,
        SqlSessionCountableDAO<MainbodyChanleQueryArg>,
        SqlSessionQueryableDAO<MainbodyChanleQueryArg, MainbodyChanleDO>,
        SqlSessionBatchInsertDAO<Long, MainbodyChanleDO>,
        SqlSessionDAO {

    private final SqlSession sqlSession;

    private final Statement statement;

    public MainbodyChanleDaoImpl(@Qualifier("saasPwSqlSessionTemplate") SqlSessionTemplate sqlSessionTemplate) {
        this.sqlSession = sqlSessionTemplate;
        this.statement = Statement.of(MainbodyChanleDao.class);
    }
} 