package com.sendinfo.ticketing.verification.basic.component.infra.converter;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

import com.sendinfo.ticketing.verification.basic.component.infra.mapper.PwSysSubsystemMapper;
import com.sendinfo.ticketing.verification.basic.model.infra.PwSysSubsystem;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.PwSysSubsystemDO;
import com.sendinfo.ticketing.verification.common.component.converter.ReadDo2ModelConverter;

/**
 * 子系统管理转换器，负责不同层级对象间的转换，包括请求对象、参数对象、数据对象和模型对象
 *
 * <AUTHOR> 2025-07-24 15:40:00
 */
@Component("pwSysSubsystemConverter")
public class PwSysSubsystemConverter
        implements ReadDo2ModelConverter<PwSysSubsystemDO, PwSysSubsystem>{


    @Override
    public PwSysSubsystem r_d2m(PwSysSubsystemDO dataObject) {
        return PwSysSubsystemMapper.INSTANCE.convert(dataObject);
    }

    @Override
    public List<PwSysSubsystem> r_ds2ms(List<PwSysSubsystemDO> dataObjects) {
        return dataObjects.stream()
                .map(this::r_d2m)
                .collect(Collectors.toList());
    }

}