package com.sendinfo.ticketing.verification.basic.component.infra.impl;

import lombok.Getter;
import org.springframework.stereotype.Component;

import com.sendinfo.ticketing.verification.basic.component.infra.SysParamNamespaceDeleteComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.converter.SysParamNamespaceConverter;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamNamespaceDeleteParam;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamNamespaceDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.SysParamNamespaceDao;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleDelete;

/**
 * 参数命名空间删除组件实现类，负责删除参数命名空间的业务逻辑
 *
 * <AUTHOR> 2025-07-21 15:30:00
 */
@Component("sysParamNamespaceDeleteComponent")
@Getter
public class SysParamNamespaceDeleteComponentImpl
        implements SysParamNamespaceDeleteComponent,
        DaoBasedSingleDelete<Long, SysParamNamespaceDeleteParam, SysParamNamespaceDeleteArg> {

    private final SysParamNamespaceDao dao;
    private final SysParamNamespaceConverter converter;

    public SysParamNamespaceDeleteComponentImpl(SysParamNamespaceDao dao, SysParamNamespaceConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }


}