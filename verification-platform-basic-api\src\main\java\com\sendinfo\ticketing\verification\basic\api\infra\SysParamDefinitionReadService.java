package com.sendinfo.ticketing.verification.basic.api.infra;

import java.util.List;

import com.sendinfo.ticketing.verification.basic.model.infra.SysParamDefinition;
import com.sendinfo.ticketing.verification.common.api.result.PageResultModel;
import com.sendinfo.ticketing.verification.common.api.request.PageRequest;
import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamDefinitionQueryCondition;
import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamDefinitionQueryByParamCodeRequest;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

/**
 * 系统参数定义查询服务
 * 
 * <AUTHOR> 2025-05-19 15:30:00
 */
public interface SysParamDefinitionReadService {
    /**
     * 分页查询系统参数定义
     *
     * @param pageRequest 分页查询条件
     * @return 分页结果
     */
    PageResultModel<SysParamDefinition> queryPageList(PageRequest<SysParamDefinitionQueryCondition> pageRequest);

    /**
     * 根据ID查询系统参数定义
     *
     * @param id 参数ID
     * @return 参数定义
     */
    ResultModel<SysParamDefinition> queryById(Long id);

    /**
     * 根据参数编码和业务域查询系统参数定义
     *
     * @param request 查询请求参数
     * @return 参数定义
     */
    ResultModel<SysParamDefinition> queryByParamCode(SysParamDefinitionQueryByParamCodeRequest request);

    /**
     * 根据组件编码查询系统参数定义列表
     * 
     * @param componentCode
     * @return
     */
    ResultModel<List<SysParamDefinition>> queryByComponentCode(String componentCode);
}