package com.sendinfo.ticketing.verification.basic.api.infra;

import com.sendinfo.ticketing.verification.common.api.result.PageResultModel;
import com.sendinfo.ticketing.verification.common.api.request.PageRequest;
import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamNamespaceQueryCondition;
import com.sendinfo.ticketing.verification.basic.model.infra.SysParamNamespace;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import java.util.List;

/**
 * 参数命名空间查询服务
 * <AUTHOR> 2025-07-21
 */
public interface SysParamNamespaceReadService {
    /**
     * 分页查询参数命名空间
     *
     * @param pageRequest 分页查询条件
     * @return 分页结果
     */
    PageResultModel<SysParamNamespace> queryPageList(PageRequest<SysParamNamespaceQueryCondition> pageRequest);

    /**
     * 根据ID查询参数命名空间
     *
     * @param id 命名空间ID
     * @return 命名空间信息
     */
    ResultModel<SysParamNamespace> querySysParamNamespaceById(Long id);

    /**
     * 根据命名空间编码和企业编码查询参数命名空间
     * 
     * @param namespaceCode 命名空间编码
     * @param corpCode 企业编码
     * @return 命名空间信息
     */
    ResultModel<SysParamNamespace> queryByNamespaceCode(String namespaceCode, String corpCode);
    
    /**
     * 根据模块编码和企业编码查询参数命名空间列表
     * 
     * @param moduleCode 模块编码
     * @param corpCode 企业编码
     * @return 命名空间列表
     */
    ResultModel<List<SysParamNamespace>> queryListByModuleCode(String moduleCode, String corpCode);
    
    /**
     * 根据分组编码和企业编码查询参数命名空间列表
     * 
     * @param groupCode 分组编码
     * @param corpCode 企业编码
     * @return 命名空间列表
     */
    ResultModel<List<SysParamNamespace>> queryListByGroupCode(String groupCode, String corpCode);
}