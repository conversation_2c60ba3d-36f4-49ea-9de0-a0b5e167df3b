<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>app-parent</artifactId>
        <groupId>com.sendinfo.paas</groupId>
        <version>0.0.1</version>
    </parent>
    <groupId>com.sendinfo.ticketing</groupId>
    <artifactId>verification-platform-basic</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>pom</packaging>
    <description>核销中台服务端</description>
    <modules>
        <module>verification-platform-basic-common</module>
        <module>verification-platform-basic-bootstrap</module>
        <module>verification-platform-basic-service</module>
        <module>verification-platform-basic-repository</module>
        <module>verification-platform-basic-component</module>
        <module>verification-platform-basic-model</module>
        <module>verification-platform-basic-api</module>
    </modules>
    <properties>
        <java.version>11</java.version>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <sendinfo-paas-shared-boot.version>0.0.3</sendinfo-paas-shared-boot.version>
        <sendinfo-paas-3rd-bom.version>0.2.2</sendinfo-paas-3rd-bom.version>
        <sendinfo-paas-spring-bom.version>0.0.4</sendinfo-paas-spring-bom.version>
        <sendinfo-paas-poseidon.version>0.2.8</sendinfo-paas-poseidon.version>
        <spring-boot-maven-plugin.version>2.7.18</spring-boot-maven-plugin.version>
        <sendinfo-paas-shared.version>0.0.9</sendinfo-paas-shared.version>
        <sendinfo-paas-bom.version>1.5.3</sendinfo-paas-bom.version>
        <snakeyaml.version>1.33</snakeyaml.version>
        <caffeine.version>2.9.2</caffeine.version>
        <spring-retry.version>1.2.5.RELEASE</spring-retry.version>
        <mockito-inline.version>3.6.0</mockito-inline.version>
        <wiremock-jre8.version>2.35.0</wiremock-jre8.version>
        <h2.version>2.1.214</h2.version>
        <lombok.version>1.18.30</lombok.version>
        <mapstruct.version>1.4.1.Final</mapstruct.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
        <aviator.version>5.4.3</aviator.version>
        <verification-common.version>0.0.1-SNAPSHOT</verification-common.version>
        <jjwt.version>0.11.5</jjwt.version>
        <pinyin4j.version>2.5.1</pinyin4j.version>
        <sendinfo-business-server-api.version>0.0.1-SNAPSHOT</sendinfo-business-server-api.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.sendinfo.paas</groupId>
                <artifactId>sendinfo-paas-bom</artifactId>
                <version>${sendinfo-paas-bom.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.paas</groupId>
                <artifactId>sendinfo-paas-3rd-bom</artifactId>
                <version>${sendinfo-paas-3rd-bom.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.paas</groupId>
                <artifactId>sendinfo-paas-spring-bom</artifactId>
                <version>${sendinfo-paas-spring-bom.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.paas</groupId>
                <artifactId>sendinfo-paas-poseidon-server-admin-api</artifactId>
                <version>${sendinfo-paas-poseidon.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.paas</groupId>
                <artifactId>sendinfo-paas-poseidon-server-puller-api</artifactId>
                <version>${sendinfo-paas-poseidon.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.ticketing</groupId>
                <artifactId>verification-platform-basic-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.ticketing</groupId>
                <artifactId>verification-platform-basic-bootstrap</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.ticketing</groupId>
                <artifactId>verification-platform-basic-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.ticketing</groupId>
                <artifactId>verification-platform-basic-repository-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.ticketing</groupId>
                <artifactId>verification-platform-basic-repository-infra</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.ticketing</groupId>
                <artifactId>verification-platform-basic-component-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.ticketing</groupId>
                <artifactId>verification-platform-basic-component-infra</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.ticketing</groupId>
                <artifactId>verification-platform-basic-service-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.ticketing</groupId>
                <artifactId>verification-platform-basic-service-infra</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.ticketing</groupId>
                <artifactId>verification-platform-basic-service-auth</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.ticketing</groupId>
                <artifactId>verification-platform-basic-model</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.ticketing</groupId>
                <artifactId>verification-common-component</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.ticketing</groupId>
                <artifactId>verification-common-repository</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.ticketing</groupId>
                <artifactId>verification-common-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.ticketing</groupId>
                <artifactId>verification-common-repository-jdbc</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.googlecode.aviator</groupId>
                <artifactId>aviator</artifactId>
                <version>${aviator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>${snakeyaml.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>${caffeine.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.retry</groupId>
                <artifactId>spring-retry</artifactId>
                <version>${spring-retry.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-inline</artifactId>
                <version>${mockito-inline.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.tomakehurst</groupId>
                <artifactId>wiremock-jre8</artifactId>
                <version>${wiremock-jre8.version}</version>
            </dependency>
            <dependency>
                <groupId>com.h2database</groupId>
                <artifactId>h2</artifactId>
                <version>${h2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.ticketing</groupId>
                <artifactId>verification-common-flow-spring-boot-starter</artifactId>
                <version>${verification-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.ticketing</groupId>
                <artifactId>verification-common-api</artifactId>
                <version>${verification-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.ticketing</groupId>
                <artifactId>verification-common-model</artifactId>
                <version>${verification-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.ticketing</groupId>
                <artifactId>verification-common-audit-log-spring-boot-plugin-mysql-starter</artifactId>
                <version>${verification-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.ticketing</groupId>
                <artifactId>verification-common-audit-log-web-starter</artifactId>
                <version>${verification-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.ticketing</groupId>
                <artifactId>verification-common-audit-log-spring-boot-starter</artifactId>
                <version>${verification-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.ticketing</groupId>
                <artifactId>verification-common-audit-log-api</artifactId>
                <version>${verification-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.ticketing</groupId>
                <artifactId>verification-platform-basic-repository-park</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.ticketing</groupId>
                <artifactId>verification-platform-basic-service-park</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.ticketing</groupId>
                <artifactId>verification-platform-basic-component-park</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.ticketing</groupId>
                <artifactId>verification-platform-basic-repository-goods</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.ticketing</groupId>
                <artifactId>verification-platform-basic-service-goods</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.ticketing</groupId>
                <artifactId>verification-platform-basic-component-goods</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.ticketing</groupId>
                <artifactId>verification-platform-basic-repository-pay</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.ticketing</groupId>
                <artifactId>verification-platform-basic-service-pay</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.ticketing</groupId>
                <artifactId>verification-platform-basic-component-pay</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.ticketing</groupId>
                <artifactId>verification-platform-basic-repository-system</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.ticketing</groupId>
                <artifactId>verification-platform-basic-service-system</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.ticketing</groupId>
                <artifactId>verification-platform-basic-component-system</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.ticketing</groupId>
                <artifactId>verification-platform-basic-repository-customer</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.ticketing</groupId>
                <artifactId>verification-platform-basic-component-customer</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.ticketing</groupId>
                <artifactId>verification-platform-basic-service-customer</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.ticketing</groupId>
                <artifactId>verification-platform-basic-repository-account</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.ticketing</groupId>
                <artifactId>verification-platform-basic-component-account</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.ticketing</groupId>
                <artifactId>verification-platform-basic-service-account</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-api</artifactId>
                <version>${jjwt.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-impl</artifactId>
                <version>${jjwt.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-jackson</artifactId>
                <version>${jjwt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.belerweb</groupId>
                <artifactId>pinyin4j</artifactId>
                <version>${pinyin4j.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sendinfo.sendinfo-business-server-api</groupId>
                <artifactId>sendinfo-business-server-api</artifactId>
                <version>${sendinfo-business-server-api.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.2.0</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.2.0</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>cobertura-maven-plugin</artifactId>
                <version>2.7</version>
                <configuration>
                    <instrumentation>
                        <excludes>
                            <exclude>**/*DO.class</exclude>
                            <exclude>**/*Config.class</exclude>
                            <exclude>**/*Param.class</exclude>
                            <exclude>**/*Enum.class</exclude>
                        </excludes>
                    </instrumentation>
                    <check/>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.7</version>
                <executions>
                    <execution>
                        <id>prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>post-unit-test</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                        <configuration>
                            <dataFile>target/jacoco.exec</dataFile>
                            <outputDirectory>target/jacoco-ut</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
