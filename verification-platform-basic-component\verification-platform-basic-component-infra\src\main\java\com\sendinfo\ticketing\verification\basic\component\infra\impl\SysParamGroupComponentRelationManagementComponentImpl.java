/**
 * System parameter group component relation management component implementation
 *
 * <AUTHOR> 2025-01-24 15:30:00
 */
package com.sendinfo.ticketing.verification.basic.component.infra.impl;

import lombok.Getter;
import org.springframework.stereotype.Component;

import com.sendinfo.ticketing.verification.basic.component.infra.SysParamGroupComponentRelationManagementComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.converter.SysParamGroupComponentRelationConverter;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamGroupComponentRelationUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.infra.SysParamGroupComponentRelation;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamGroupComponentRelationUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.SysParamGroupComponentRelationDao;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleUpdate;

/**
 * 参数分组组件关联管理组件实现类，负责更新参数分组组件关联的业务逻辑
 *
 * <AUTHOR> 2025-01-24 15:30:00
 */
@Component("sysParamGroupComponentRelationManagementComponent")
@Getter
public class SysParamGroupComponentRelationManagementComponentImpl
        implements SysParamGroupComponentRelationManagementComponent,
        DaoBasedSingleUpdate<SysParamGroupComponentRelationUpdateParam, SysParamGroupComponentRelationUpdateArg, SysParamGroupComponentRelation> {

    private final SysParamGroupComponentRelationDao dao;
    private final SysParamGroupComponentRelationConverter converter;

    public SysParamGroupComponentRelationManagementComponentImpl(
            SysParamGroupComponentRelationDao dao,
            SysParamGroupComponentRelationConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }
}