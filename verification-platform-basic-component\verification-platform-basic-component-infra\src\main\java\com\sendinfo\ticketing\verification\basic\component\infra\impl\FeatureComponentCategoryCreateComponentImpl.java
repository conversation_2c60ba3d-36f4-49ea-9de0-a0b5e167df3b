package com.sendinfo.ticketing.verification.basic.component.infra.impl;

import lombok.Getter;
import org.springframework.stereotype.Component;

import com.sendinfo.ticketing.verification.basic.component.infra.FeatureComponentCategoryCreateComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.converter.FeatureComponentCategoryConverter;
import com.sendinfo.ticketing.verification.basic.component.infra.param.FeatureComponentCategoryCreateParam;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.FeatureComponentCategoryDao;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.FeatureComponentCategoryDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleCreate;

/**
 * 组件分类创建组件实现
 * 
 * <AUTHOR> 2025-07-22 11:45:32
 */
@Component("featureComponentCategoryCreateComponent")
@Getter
public class FeatureComponentCategoryCreateComponentImpl
        implements FeatureComponentCategoryCreateComponent,
        DaoBasedSingleCreate<Long, FeatureComponentCategoryCreateParam, FeatureComponentCategoryDO> {

    private final FeatureComponentCategoryDao dao;
    private final FeatureComponentCategoryConverter converter;

    public FeatureComponentCategoryCreateComponentImpl(FeatureComponentCategoryDao dao, FeatureComponentCategoryConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }
}
