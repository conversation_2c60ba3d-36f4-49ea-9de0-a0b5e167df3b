package com.sendinfo.ticketing.verification.basic.api.customer.request;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Auther: chengjie.li
 * @Date: 2025/6/23 17:58
 */
@Getter
@Setter
public class GuideQueryRequest implements Serializable {
    private static final long serialVersionUID = 3424573713708106734L;

    /**
     * 姓名
     */
    private String guiderName;

    /**
     * 导游证
     */
    private String guideNo;

    /**
     * 企业编码
     */
    @NotNull(message = "企业编码不能为空")
    private String corpCode;
}
