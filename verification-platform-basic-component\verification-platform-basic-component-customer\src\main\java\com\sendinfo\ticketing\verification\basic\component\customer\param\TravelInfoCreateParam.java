package com.sendinfo.ticketing.verification.basic.component.customer.param;

import com.sendinfo.ticketing.verification.common.component.param.AbstractCreateParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 旅行社信息创建参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class TravelInfoCreateParam extends AbstractCreateParam {
    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 旅行社名称
     */
    private String travelName;

    /**
     * 客户简称
     */
    private String travelNameAbbreviation;

    /**
     * 客户编号
     */
    private String travelCode;

    /**
     * 旅行社类型ID
     */
    private String travelTypeId;

    /**
     * 团队折扣率
     */
    private Integer teamDiscountRate;

    /**
     * 联系人(计调员姓名)
     */
    private String linkman;

    /**
     * 电话
     */
    private String telphone;

    /**
     * 联系人电话
     */
    private String linkmanTel;

    /**
     * 联系地址
     */
    private String addr;

    /**
     * 旅行社组ID
     */
    private Long travelGroupId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 计调员身份证
     */
    private String linkmanCard;

    /**
     * 计调员邮箱
     */
    private String linkmanMail;

    /**
     * 拼音简码
     */
    private String pingCode;

    /**
     * 是否允许签单T:允许（默认）F不予许
     */
    private String allowSingBill;

    /**
     * 经营许可证号
     */
    private String code;

    /**
     * 等级
     */
    private Integer travelLevel;

    /**
     * 法人代表姓名
     */
    private String artificialPerson;

    /**
     * 法人代表手机号（重置密码用到）
     */
    private String artificialMobile;

    /**
     * 法人代表身份证号
     */
    private String artificialCard;

    /**
     * 区域编码
     */
    private String areaCode;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 邮编
     */
    private String zip;

    /**
     * 审核状态 T ：已审核F：未审核
     */
    private String auditStatus;

    /**
     * 固定电话
     */
    private String fixedTel;

    /**
     * 到期时间(未年审没有到期时间，年审后设置到期时间)
     */
    private LocalDateTime expireTime;

    /**
     * 传真
     */
    private String fax;

    /**
     * 统一社会信用代码
     */
    private String creditCode;

    /**
     * 年审状态 0:未年审1:待年审 2:已年审(审核周期内是待年审，过来审核周期是未年审和已年审)
     */
    private String yearAudit;

    /**
     * 营业执照照片
     */
    private String busLicenceImg;

    /**
     * 所属联合体(取系统参数)
     */
    private String affiliatedConsortium;

    /**
     * 客户类型(取系统参数)
     */
    private String customerTypeNature;

    /**
     * 是否限制签单上限:F:不限制T:限制
     */
    private String singlMaxFlag;

    /**
     * 签单上限额度
     */
    private BigDecimal singMaxLimit;

    /**
     * 自动开通预付款账户:F:不开通T:开通
     */
    private String capitalFlag;

    /**
     * 所属片区(取数据字典)
     */
    private String respeArea;

    /**
     * 经营许可证照片
     */
    private String imgUrl;

    /**
     * 法人身份照片（正面）
     */
    private String artificialCardImg;

    /**
     * 支付方式
     */
    private String payTypeCode;

    /**
     * 同步操作类型 0:已同步，1：新增，2：修改，3：删除
     */
    private String syncOperateType;

    /**
     * 最近修改人：真实姓名（用户名）
     */
    private String recentlyUpdater;

    /**
     * 所属部门
     */
    private Integer deptParentId;

    /**
     * 是否地接社
     */
    private String travelIsLocal;

    /**
     * 是否组团社
     */
    private String travelIsClub;

    /**
     * 经销商代码
     */
    private String dealerCode;

    /**
     * 迁移数据
     */
    private String transferParam;

    /**
     * 许可证
     */
    private String licence;

    /**
     * 拼音
     */
    private String pingYin;

    /**
     * 客户标签，维护源在票务字典中搜索clientLable
     */
    private String clientTag;

    /**
     * 可用支付方式标志
     */
    private String usablePayAwayFlag;

    /**
     * 可用支付方式ID列表
     */
    private String usablePayAwayIds;

    /**
     * 支付方式ID
     */
    private Long payAwayId;

    /**
     * 是否集团同步 0租户 1集团
     */
    private Integer isGroupCoSync;

    /**
     * 操作后签单余额
     */
    private BigDecimal singLeftLimit;
} 