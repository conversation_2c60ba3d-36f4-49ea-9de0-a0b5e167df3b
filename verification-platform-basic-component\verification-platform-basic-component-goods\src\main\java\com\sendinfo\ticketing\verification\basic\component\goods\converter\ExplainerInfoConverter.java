package com.sendinfo.ticketing.verification.basic.component.goods.converter;

import com.sendinfo.ticketing.verification.basic.api.goods.request.ExplainerInfoQueryRequest;
import com.sendinfo.ticketing.verification.basic.component.goods.mapper.ExplainerInfoMapper;
import com.sendinfo.ticketing.verification.basic.component.goods.param.ExplainerInfoQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.ExplainerInfo;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.ExplainerInfoQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.ExplainerInfoDO;
import com.sendinfo.ticketing.verification.common.component.converter.ReadDo2ModelConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadParam2ArgConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadReq2ParamConverter;
import org.springframework.stereotype.Component;

/**
 * 讲解人转换器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("explainerInfoConverter")
public class ExplainerInfoConverter implements ReadDo2ModelConverter<ExplainerInfoDO, ExplainerInfo>,
        ReadParam2ArgConverter<ExplainerInfoQueryParam, ExplainerInfoQueryArg>,
        ReadReq2ParamConverter<ExplainerInfoQueryRequest, ExplainerInfoQueryParam> {
    @Override
    public ExplainerInfo r_d2m(ExplainerInfoDO dataObject) {
        return ExplainerInfoMapper.INSTANCE.convert(dataObject);
    }

    @Override
    public ExplainerInfoQueryArg r_p2a(ExplainerInfoQueryParam param) {
        return ExplainerInfoMapper.INSTANCE.convert(param);
    }

    @Override
    public ExplainerInfoQueryParam r_r2p(ExplainerInfoQueryRequest queryRequest) {
        return ExplainerInfoMapper.INSTANCE.convert(queryRequest);
    }
}