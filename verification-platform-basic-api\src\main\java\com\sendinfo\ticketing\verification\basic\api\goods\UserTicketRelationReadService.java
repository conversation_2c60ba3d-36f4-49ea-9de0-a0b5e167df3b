package com.sendinfo.ticketing.verification.basic.api.goods;

import com.sendinfo.ticketing.verification.basic.api.goods.request.UserTicketRelationQueryRequest;
import com.sendinfo.ticketing.verification.basic.model.goods.UserTicketRelationPullData;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025-07-24 17:33
 */
public interface UserTicketRelationReadService {

    /**
     * 查询用户票型列表
     * @param request 请求入参
     * @return ResultModel
     */
    ResultModel<UserTicketRelationPullData> getUserTicketList(UserTicketRelationQueryRequest request);

    ResultModel<Boolean> checkExpired(UserTicketRelationQueryRequest request);
}
