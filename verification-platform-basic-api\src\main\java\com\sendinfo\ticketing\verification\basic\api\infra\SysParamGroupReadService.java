package com.sendinfo.ticketing.verification.basic.api.infra;

import com.sendinfo.ticketing.verification.basic.model.infra.SysParamGroup;
import com.sendinfo.ticketing.verification.common.api.result.PageResultModel;
import com.sendinfo.ticketing.verification.common.api.request.PageRequest;
import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamGroupQueryCondition;
import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamGroupQueryByModuleCodeRequest;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import java.util.List;

/**
 * 系统参数分组查询服务
 * 
 * <AUTHOR> 2025-07-22 11:45:32
 */
public interface SysParamGroupReadService {

    /**
     * 分页查询系统参数分组
     *
     * @param pageRequest 分页查询条件
     * @return 分页结果
     */
    PageResultModel<SysParamGroup> queryPageList(PageRequest<SysParamGroupQueryCondition> pageRequest);

    /**
     * 根据ID查询系统参数分组
     *
     * @param id 分组ID
     * @return 参数分组
     */
    ResultModel<SysParamGroup> queryById(Long id);

    /**
     * 根据分组编码和企业编码查询系统参数分组
     *
     * @param groupCode 分组编码
     * @param corpCode  企业编码
     * @return 参数分组
     */
    ResultModel<SysParamGroup> queryByGroupCode(String groupCode, String corpCode);

    /**
     * 根据模块编码和企业编码查询参数分组列表
     *
     * @param request 查询请求参数
     * @return 参数分组列表
     */
    ResultModel<List<SysParamGroup>> queryListByModuleCode(SysParamGroupQueryByModuleCodeRequest request);
}
