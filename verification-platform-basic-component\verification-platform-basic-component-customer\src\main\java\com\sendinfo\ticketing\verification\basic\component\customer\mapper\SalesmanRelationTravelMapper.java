package com.sendinfo.ticketing.verification.basic.component.customer.mapper;

import com.sendinfo.ticketing.verification.basic.component.customer.param.SalesmanRelationTravelCreateParam;
import com.sendinfo.ticketing.verification.basic.component.customer.param.SalesmanRelationTravelQueryParam;
import com.sendinfo.ticketing.verification.basic.component.customer.param.SalesmanRelationTravelUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.customer.SalesmanRelationTravel;
import com.sendinfo.ticketing.verification.basic.repository.customer.arg.SalesmanRelationTravelQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.customer.arg.SalesmanRelationTravelUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.customer.dataobject.SalesmanRelationTravelDO;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 业务员关联客户对象转换映射器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface SalesmanRelationTravelMapper {

    SalesmanRelationTravelMapper INSTANCE = Mappers.getMapper(SalesmanRelationTravelMapper.class);

    /**
     * 创建参数转换为数据对象
     *
     * @param createParam 创建参数
     * @return 数据对象
     */
    SalesmanRelationTravelDO convert(SalesmanRelationTravelCreateParam createParam);

    /**
     * 查询参数转换为查询参数
     *
     * @param queryParam 查询参数
     * @return 查询参数
     */
    SalesmanRelationTravelQueryArg convert(SalesmanRelationTravelQueryParam queryParam);

    /**
     * 更新参数转换为更新参数
     *
     * @param updateParam 更新参数
     * @return 更新参数
     */
    SalesmanRelationTravelUpdateArg convert(SalesmanRelationTravelUpdateParam updateParam);

    /**
     * 数据对象转换为模型对象
     *
     * @param dataObject 数据对象
     * @return 模型对象
     */
    SalesmanRelationTravel convert(SalesmanRelationTravelDO dataObject);
} 