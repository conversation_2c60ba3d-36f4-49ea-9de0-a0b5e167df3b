package com.sendinfo.ticketing.verification.basic.repository.pay.dataobject;

import com.sendinfo.ticketing.verification.common.repository.dataobject.AbstractTenantBaseDO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 交易记录数据对象
 * 对应数据库表 trans_info
 * 继承租户基础DO，包含租户隔离字段corpCode
 * 所有字段与表结构保持一致，便于MyBatis自动映射
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class TransInfoDO extends AbstractTenantBaseDO<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 内部交易号
     */
    private String transNo;

    /**
     * 交易类型： 0支付1退款
     */
    private Integer transType;

    /**
     * 交易ID
     */
    private Long tradeId;

    /**
     * 客户代码+交易ID号
     */
    private String tradeCode;

    /**
     * 支付方式中文说明（如支付宝扫码付）
     */
    private String payAway;

    /**
     * 0：支付宝 1:微信 2:银联3：备用金 9:手功
     */
    private Integer gateway;

    /**
     * 网关交易号（第三方交易号）
     */
    private String gatewayTransNo;

    /**
     * 业务类型：0:门票1：剧院2：滑雪场4:一卡通6:租赁13:存包柜
     */
    private Integer businessType;

    /**
     * 交易金额
     */
    private BigDecimal payAmount;

    /**
     * 登录ID
     */
    private Long loginId;

    /**
     * 操作员名
     */
    private String loginName;

    /**
     * 状态： 0:待支付 1:支付未完成 2:未支付 3:已支付
     */
    private String payStatus;

    /**
     * 报告时间
     */
    private Date reportTime;

    /**
     * 实际支付时间
     */
    private Date payTime;

    /**
     * 交易报文
     */
    private String reportInfo;

    /**
     * 页面展示支付方式
     */
    private String payTypeView;

    /**
     * 支付总金额
     */
    private BigDecimal payTotal;

    /**
     * 退单支付交易号
     */
    private String refundPayTransno;

    /**
     * 子支付方式
     */
    private String payType;

    /**
     * 主体支付ID
     */
    private Long mainbodyPayid;

    /**
     * 付款信息
     */
    private String payInfo;

    /**
     * 扩展参数
     */
    private String extendParamJson;

    /**
     * 终端号
     */
    private String tid;

    /**
     * 积分数量
     */
    private Integer payIntegralSum;

    /**
     * 积分支付抵扣金额
     */
    private BigDecimal payIntegralTotal;

    /**
     * 其他交易号
     */
    private String otherTransNo;

    /**
     * 结算标识T结算F未结算
     */
    private String settlement;
} 