/**
 * CHECKBOX类型参数值声明处理器
 *
 * <AUTHOR> 2025-08-04
 */
package com.sendinfo.ticketing.verification.basic.service.infra.processor.impl;

import com.sendinfo.ticketing.verification.basic.enums.SysParamDataType;
import org.springframework.stereotype.Component;

/**
 * CHECKBOX类型参数值声明处理器
 * 复选框类型，至少需要一个default=true的选项
 */
@Component("checkboxValueStatementProcessor")
public class CheckboxValueStatementStatementProcessor extends MultipleValueStatementProcessor {

    @Override
    public SysParamDataType getDataType() {
        return SysParamDataType.CHECKBOX;
    }

}
