package com.sendinfo.ticketing.verification.basic.repository.pay.dao;

import com.alibaba.nacos.api.exception.NacosException;
import com.sendinfo.ticketing.verification.basic.repository.common.config.SaasPwMybatisConfig;
import com.sendinfo.ticketing.verification.basic.repository.common.sharding.DynamicDataSourceMappingConfigPullStorage;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageMmSubMerchantsDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageMmSubMerchantsQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageMmSubMerchantsUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dao.impl.SysMainbodyManageMmSubMerchantsDaoImpl;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysMainbodyManageMmSubMerchantsDO;
import org.junit.Test;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.AbstractTransactionalJUnit4SpringContextTests;

import java.util.*;

import static org.junit.Assert.*;

/**
 * 经营主体支付配置子商户关联表数据访问层单元测试
 * <p>
 * 本测试类覆盖了SysMainbodyManageMmSubMerchantsDao的所有方法，包括：
 * 1. 插入操作测试
 * 2. 批量插入操作测试
 * 3. 根据ID查询测试
 * 4. 条件查询测试
 * 5. 计数查询测试
 * 6. 更新操作测试
 * 7. 软删除操作测试
 * 8. 自定义查询方法测试
 * <p>
 * 测试目的：验证SysMainbodyManageMmSubMerchantsDao的所有方法都能正常工作，确保数据访问层的正确性
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootTest(classes = SysMainbodyManageMmSubMerchantsDaoTest.Config.class)
@EnableAutoConfiguration
@TestPropertySource(locations = "classpath:test-application.properties")
public class SysMainbodyManageMmSubMerchantsDaoTest extends AbstractTransactionalJUnit4SpringContextTests {

    @Autowired
    private SysMainbodyManageMmSubMerchantsDao sysMainbodyManageMmSubMerchantsDao;

    /**
     * 测试用的企业编码
     */
    private static final String TEST_CORP_CODE = "TEST_CORP_001";

    /**
     * 测试用户
     */
    private static final String TEST_USER = "test_user";

    /**
     * 商户号最小值
     */
    private static final Integer MERCHANT_NO_MIN = 10000;

    /**
     * 商户号最大值
     */
    private static final Integer MERCHANT_NO_MAX = 99999;

    /**
     * 测试主体运营ID
     */
    private static final Long TEST_MAINBODY_MANAGE_ID = 1L;

    /**
     * 测试子商户ID
     */
    private static final Long TEST_SUB_MERCHANTS_ID = 1L;

    /**
     * 测试配置类
     * 配置MyBatis和DAO Bean
     */
    @Configuration
    @Import(SaasPwMybatisConfig.class)
    static class Config {

        @Bean
        public SysMainbodyManageMmSubMerchantsDao sysMainbodyManageMmSubMerchantsDao(@Qualifier(SaasPwMybatisConfig.BEAN_NAME_SQL_SESSION_TEMPLATE) SqlSessionTemplate sqlSessionTemplate) {
            return new SysMainbodyManageMmSubMerchantsDaoImpl(sqlSessionTemplate);
        }

        @Bean
        DynamicDataSourceMappingConfigPullStorage dynamicDataSourceMappingConfigPullStorage(@Value("${nacos.config.server-addr}") String serverAddr,
                                                                                            @Value("${nacos.config.namespace}") String namespace,
                                                                                            @Value("${verification-platform-basic.nacos.tenant-datasource-mapping.data-id:saas-pw-datasource-sharding-mapping.json}") String dataId,
                                                                                            @Value("${verification-platform-basic.nacos.tenant-datasource-mapping.group:DEFAULT_GROUP}") String group,
                                                                                            @Value("${verification-platform-basic.nacos.tenant-datasource-mapping.pulled.timeout:3000}") Integer timeout) throws NacosException {
            return new DynamicDataSourceMappingConfigPullStorage(serverAddr, namespace, dataId, group, timeout);
        }
    }

    /**
     * 测试插入操作
     * <p>
     * 测试步骤：
     * 1. 创建测试经营主体支付配置子商户关联数据
     * 2. 执行插入操作
     * 3. 验证主键ID被自动生成
     * 4. 通过ID查询验证记录确实被插入
     * 5. 验证插入的数据字段值正确
     */
    @Test
    public void testInsert() {
        // 1. 创建测试经营主体支付配置子商户关联数据
        SysMainbodyManageMmSubMerchantsDO record = createTestDO();

        // 2. 执行插入操作
        sysMainbodyManageMmSubMerchantsDao.insert(record);

        // 3. 验证主键ID被自动生成
        assertNotNull("插入后主键ID应该被自动生成", record.getId());

        // 4. 通过ID查询验证记录确实被插入
        SysMainbodyManageMmSubMerchantsDO insertedRecord = sysMainbodyManageMmSubMerchantsDao.queryById(record.getId());
        assertNotNull("插入后应能通过ID查询到记录", insertedRecord);
        assertEquals("主体运营ID应该一致", record.getMainbodyManageId(), insertedRecord.getMainbodyManageId());
        assertEquals("子商户ID应该一致", record.getSubMerchantsId(), insertedRecord.getSubMerchantsId());
        assertEquals("子商户名称应该一致", record.getSubMerchantsName(), insertedRecord.getSubMerchantsName());
        assertEquals("商户号应该一致", record.getMerchantNo(), insertedRecord.getMerchantNo());
        assertEquals("企业编码应该一致", TEST_CORP_CODE, insertedRecord.getCorpCode());
    }

    /**
     * 测试批量插入操作
     * <p>
     * 测试步骤：
     * 1. 创建多个测试经营主体支付配置子商户关联数据
     * 2. 执行批量插入操作
     * 3. 验证所有记录的主键ID都被自动生成
     * 4. 通过ID查询验证所有记录都被正确插入
     */
    @Test
    public void testBatchInsert() {
        // 1. 创建多个测试经营主体支付配置子商户关联数据
        List<SysMainbodyManageMmSubMerchantsDO> recordList = new java.util.ArrayList<>();
        for (int i = 0; i < 3; i++) {
            SysMainbodyManageMmSubMerchantsDO record = createTestDO();
            recordList.add(record);
        }

        // 2. 执行批量插入操作
        sysMainbodyManageMmSubMerchantsDao.batchInsert(recordList);

        // 3. 查询插入数据
        SysMainbodyManageMmSubMerchantsQueryArg queryArg = new SysMainbodyManageMmSubMerchantsQueryArg();
        queryArg.setCorpCode(TEST_CORP_CODE);
        List<SysMainbodyManageMmSubMerchantsDO> recordDOList = sysMainbodyManageMmSubMerchantsDao.queryByArg(queryArg);

        // 4. 通过ID查询验证所有记录都被正确插入
        for (SysMainbodyManageMmSubMerchantsDO record : recordDOList) {
            assertNotNull("每个记录都应该生成ID", record.getId());
        }
    }

    /**
     * 测试根据ID查询操作
     * <p>
     * 测试步骤：
     * 1. 插入测试记录
     * 2. 使用插入记录的ID进行查询
     * 3. 验证查询结果正确
     */
    @Test
    public void testQueryById() {
        // 1. 插入测试记录
        SysMainbodyManageMmSubMerchantsDO record = createTestDO();
        sysMainbodyManageMmSubMerchantsDao.insert(record);

        // 2. 使用插入记录的ID进行查询
        SysMainbodyManageMmSubMerchantsDO queriedRecord = sysMainbodyManageMmSubMerchantsDao.queryById(record.getId());

        // 3. 验证查询结果正确
        assertNotNull("应该能查询到记录", queriedRecord);
        assertEquals("ID应该一致", record.getId(), queriedRecord.getId());
        assertEquals("主体运营ID应该一致", record.getMainbodyManageId(), queriedRecord.getMainbodyManageId());
        assertEquals("子商户ID应该一致", record.getSubMerchantsId(), queriedRecord.getSubMerchantsId());
        assertEquals("企业编码应该一致", TEST_CORP_CODE, queriedRecord.getCorpCode());
    }

    /**
     * 测试基本的更新功能
     * <p>
     * 目的：验证 updateByArg 方法的基本更新功能
     * 步骤：
     * 1. 插入一条测试记录
     * 2. 创建更新参数，修改部分字段
     * 3. 执行更新操作
     * 4. 查询更新后的记录，验证字段值已正确更新
     */
    @Test
    public void testUpdateByArg() {
        // 1. 插入测试记录
        SysMainbodyManageMmSubMerchantsDO record = createTestDO();
        sysMainbodyManageMmSubMerchantsDao.insert(record);

        // 2. 创建更新参数
        SysMainbodyManageMmSubMerchantsUpdateArg updateArg = new SysMainbodyManageMmSubMerchantsUpdateArg();
        updateArg.setId(record.getId());
        updateArg.setCorpCode(TEST_CORP_CODE);
        updateArg.setSubMerchantsName("更新后的子商户名称");
        updateArg.setMerchantNo("UPDATED" + String.valueOf(new Random().nextInt(MERCHANT_NO_MAX - MERCHANT_NO_MIN + 1) + MERCHANT_NO_MIN));
        updateArg.setModifyBy("testUpdater");

        // 3. 执行更新操作
        int updateCount = sysMainbodyManageMmSubMerchantsDao.updateByArg(updateArg);
        assertEquals("应该更新1条记录", 1, updateCount);

        // 4. 验证更新结果
        SysMainbodyManageMmSubMerchantsDO updatedRecord = sysMainbodyManageMmSubMerchantsDao.queryById(record.getId());
        assertEquals("子商户名称应该被更新", "更新后的子商户名称", updatedRecord.getSubMerchantsName());
        assertNotEquals("商户号应该被更新", record.getMerchantNo(), updatedRecord.getMerchantNo());
    }

    /**
     * 测试软删除功能
     * <p>
     * 目的：验证 softDeleteByArg 方法的软删除功能
     * 步骤：
     * 1. 插入一条测试记录
     * 2. 创建删除参数
     * 3. 执行软删除操作
     * 4. 验证记录被标记为已删除（deleted = 'T'）
     * 5. 验证通过ID查询无法再查询到该记录
     */
    @Test
    public void testSoftDeleteByArg() {
        // 1. 插入测试记录
        SysMainbodyManageMmSubMerchantsDO record = createTestDO();
        sysMainbodyManageMmSubMerchantsDao.insert(record);

        // 2. 创建删除参数
        SysMainbodyManageMmSubMerchantsDeleteArg deleteArg = new SysMainbodyManageMmSubMerchantsDeleteArg();
        deleteArg.setId(record.getId());
        deleteArg.setCorpCode(TEST_CORP_CODE);
        deleteArg.setModifyBy("testDeleter");

        // 3. 执行软删除操作
        int deleteCount = sysMainbodyManageMmSubMerchantsDao.softDeleteByArg(deleteArg);
        assertEquals("应该删除1条记录", 1, deleteCount);

        // 4. 验证软删除结果
        SysMainbodyManageMmSubMerchantsDO deletedRecord = sysMainbodyManageMmSubMerchantsDao.queryById(record.getId());
        assertNull("软删除后应该无法通过ID查询到记录", deletedRecord);
    }

    /**
     * 测试条件查询功能
     * <p>
     * 目的：验证 queryByArg 方法的条件查询功能
     * 步骤：
     * 1. 插入多条测试记录
     * 2. 创建查询参数，设置不同的查询条件
     * 3. 执行查询操作
     * 4. 验证查询结果符合预期
     */
    @Test
    public void testQueryByArg() {
        // 1. 插入多条测试记录
        SysMainbodyManageMmSubMerchantsDO record1 = createTestDO();
        record1.setMainbodyManageId(1L);
        record1.setSubMerchantsName("测试子商户1");
        sysMainbodyManageMmSubMerchantsDao.insert(record1);

        SysMainbodyManageMmSubMerchantsDO record2 = createTestDO();
        record2.setMainbodyManageId(1L);
        record2.setSubMerchantsName("测试子商户2");
        sysMainbodyManageMmSubMerchantsDao.insert(record2);

        // 2. 创建查询参数
        SysMainbodyManageMmSubMerchantsQueryArg queryArg = new SysMainbodyManageMmSubMerchantsQueryArg();
        queryArg.setCorpCode(TEST_CORP_CODE);
        queryArg.setMainbodyManageId(1L);

        // 3. 执行查询操作
        List<SysMainbodyManageMmSubMerchantsDO> resultList = sysMainbodyManageMmSubMerchantsDao.queryByArg(queryArg);

        // 4. 验证查询结果
        assertNotNull("查询结果不应该为null", resultList);
        assertTrue("应该查询到至少2条记录", resultList.size() >= 2);

        // 验证所有记录的主体运营ID都是1
        for (SysMainbodyManageMmSubMerchantsDO result : resultList) {
            assertEquals("主体运营ID应该都是1", Long.valueOf(1L), result.getMainbodyManageId());
            assertEquals("企业编码应该一致", TEST_CORP_CODE, result.getCorpCode());
        }
    }

    /**
     * 测试计数查询功能
     * <p>
     * 目的：验证 countByArg 方法的计数查询功能
     * 步骤：
     * 1. 插入多条测试记录
     * 2. 创建查询参数
     * 3. 执行计数查询操作
     * 4. 验证计数结果正确
     */
    @Test
    public void testCountByArg() {
        // 1. 插入多条测试记录
        for (int i = 0; i < 3; i++) {
            SysMainbodyManageMmSubMerchantsDO record = createTestDO();
            record.setMainbodyManageId(2L);
            sysMainbodyManageMmSubMerchantsDao.insert(record);
        }

        // 2. 创建查询参数
        SysMainbodyManageMmSubMerchantsQueryArg queryArg = new SysMainbodyManageMmSubMerchantsQueryArg();
        queryArg.setCorpCode(TEST_CORP_CODE);
        queryArg.setMainbodyManageId(2L);

        // 3. 执行计数查询操作
        int count = sysMainbodyManageMmSubMerchantsDao.countByArg(queryArg);

        // 4. 验证计数结果
        assertTrue("应该查询到至少3条记录", count >= 3);
    }

    /**
     * 测试根据ID删除功能
     * <p>
     * 目的：验证 deleteById 方法的删除功能
     * 步骤：
     * 1. 插入一条测试记录
     * 2. 执行根据ID删除操作
     * 3. 验证记录被删除
     */
    @Test
    public void testDeleteById() {
        // 1. 插入测试记录
        SysMainbodyManageMmSubMerchantsDO record = createTestDO();
        sysMainbodyManageMmSubMerchantsDao.insert(record);

        // 2. 执行根据ID删除操作
        int deleteCount = sysMainbodyManageMmSubMerchantsDao.deleteById(record.getId());
        assertEquals("应该删除1条记录", 1, deleteCount);

        // 3. 验证删除结果
        SysMainbodyManageMmSubMerchantsDO deletedRecord = sysMainbodyManageMmSubMerchantsDao.queryById(record.getId());
        assertNull("删除后应该无法通过ID查询到记录", deletedRecord);
    }

    /**
     * 测试根据经营主体支付配置ID查询功能
     * <p>
     * 目的：验证 queryByMainbodyManageId 方法的查询功能
     * 步骤：
     * 1. 插入多条测试记录
     * 2. 执行根据经营主体支付配置ID查询操作
     * 3. 验证查询结果正确
     */
    @Test
    public void testQueryByMainbodyManageId() {
        // 1. 插入多条测试记录
        SysMainbodyManageMmSubMerchantsDO record1 = createTestDO();
        record1.setMainbodyManageId(3L);
        sysMainbodyManageMmSubMerchantsDao.insert(record1);

        SysMainbodyManageMmSubMerchantsDO record2 = createTestDO();
        record2.setMainbodyManageId(3L);
        sysMainbodyManageMmSubMerchantsDao.insert(record2);

        // 2. 执行根据主体运营ID查询操作
        List<SysMainbodyManageMmSubMerchantsDO> resultList = sysMainbodyManageMmSubMerchantsDao.queryByMainbodyManageId(3L, TEST_CORP_CODE);

        // 3. 验证查询结果
        assertNotNull("查询结果不应该为null", resultList);
        assertTrue("应该查询到至少2条记录", resultList.size() >= 2);

        // 验证所有记录的主体运营ID都是3
        for (SysMainbodyManageMmSubMerchantsDO result : resultList) {
            assertEquals("主体运营ID应该都是3", Long.valueOf(3L), result.getMainbodyManageId());
            assertEquals("企业编码应该一致", TEST_CORP_CODE, result.getCorpCode());
        }
    }

    /**
     * 测试根据子商户ID查询功能
     * <p>
     * 目的：验证 queryBySubMerchantsId 方法的查询功能
     * 步骤：
     * 1. 插入多条测试记录
     * 2. 执行根据子商户ID查询操作
     * 3. 验证查询结果正确
     */
    @Test
    public void testQueryBySubMerchantsId() {
        // 1. 插入多条测试记录
        SysMainbodyManageMmSubMerchantsDO record1 = createTestDO();
        record1.setSubMerchantsId(4L);
        sysMainbodyManageMmSubMerchantsDao.insert(record1);

        SysMainbodyManageMmSubMerchantsDO record2 = createTestDO();
        record2.setSubMerchantsId(4L);
        sysMainbodyManageMmSubMerchantsDao.insert(record2);

        // 2. 执行根据子商户ID查询操作
        List<SysMainbodyManageMmSubMerchantsDO> resultList = sysMainbodyManageMmSubMerchantsDao.queryBySubMerchantsId(4L, TEST_CORP_CODE);

        // 3. 验证查询结果
        assertNotNull("查询结果不应该为null", resultList);
        assertTrue("应该查询到至少2条记录", resultList.size() >= 2);

        // 验证所有记录的子商户ID都是4
        for (SysMainbodyManageMmSubMerchantsDO result : resultList) {
            assertEquals("子商户ID应该都是4", Long.valueOf(4L), result.getSubMerchantsId());
            assertEquals("企业编码应该一致", TEST_CORP_CODE, result.getCorpCode());
        }
    }

    /**
     * 测试根据子商户ID集合查询功能
     * <p>
     * 目的：验证 queryBySubMerchantsIdSet 方法的批量查询功能
     * 步骤：
     * 1. 插入多条测试记录，包含不同的子商户ID
     * 2. 创建子商户ID集合
     * 3. 执行根据子商户ID集合查询操作
     * 4. 验证查询结果正确
     */
    @Test
    public void testQueryBySubMerchantsIdSet() {
        // 1. 插入多条测试记录，包含不同的子商户ID
        SysMainbodyManageMmSubMerchantsDO record1 = createTestDO();
        record1.setSubMerchantsId(5L);
        sysMainbodyManageMmSubMerchantsDao.insert(record1);

        SysMainbodyManageMmSubMerchantsDO record2 = createTestDO();
        record2.setSubMerchantsId(6L);
        sysMainbodyManageMmSubMerchantsDao.insert(record2);

        SysMainbodyManageMmSubMerchantsDO record3 = createTestDO();
        record3.setSubMerchantsId(7L);
        sysMainbodyManageMmSubMerchantsDao.insert(record3);

        // 2. 创建子商户ID集合
        Set<Long> subMerchantsIdSet = new HashSet<>();
        subMerchantsIdSet.add(5L);
        subMerchantsIdSet.add(6L);

        // 3. 执行根据子商户ID集合查询操作
        List<SysMainbodyManageMmSubMerchantsDO> resultList = sysMainbodyManageMmSubMerchantsDao.queryBySubMerchantsIdSet(subMerchantsIdSet, TEST_CORP_CODE);

        // 4. 验证查询结果
        assertNotNull("查询结果不应该为null", resultList);
        assertTrue("应该查询到至少2条记录", resultList.size() >= 2);

        // 验证所有记录的子商户ID都在集合中
        for (SysMainbodyManageMmSubMerchantsDO result : resultList) {
            assertTrue("子商户ID应该在查询集合中", subMerchantsIdSet.contains(result.getSubMerchantsId()));
            assertEquals("企业编码应该一致", TEST_CORP_CODE, result.getCorpCode());
        }
    }

    /**
     * 创建测试用的数据对象
     *
     * @return 测试用的SysMainbodyManageMmSubMerchantsDO对象
     */
    private SysMainbodyManageMmSubMerchantsDO createTestDO() {
        SysMainbodyManageMmSubMerchantsDO record = new SysMainbodyManageMmSubMerchantsDO();

        record.setCorpCode(TEST_CORP_CODE);
        record.setMainbodyManageId(TEST_MAINBODY_MANAGE_ID);
        record.setSubMerchantsId(TEST_SUB_MERCHANTS_ID);
        record.setSubMerchantsName("测试子商户" + String.valueOf(new Random().nextInt(1000)));
        record.setMerchantNo("MERCHANT" + String.valueOf(new Random().nextInt(MERCHANT_NO_MAX - MERCHANT_NO_MIN + 1) + MERCHANT_NO_MIN));
        record.setCreateTime(new Date());
        record.setCreateBy(TEST_USER);
        record.setModifyTime(new Date());
        record.setModifyBy(TEST_USER);
        record.setDeleted("F");

        return record;
    }
} 