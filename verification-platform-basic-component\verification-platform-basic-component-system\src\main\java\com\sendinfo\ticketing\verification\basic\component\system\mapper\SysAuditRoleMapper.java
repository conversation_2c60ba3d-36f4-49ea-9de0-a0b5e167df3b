package com.sendinfo.ticketing.verification.basic.component.system.mapper;

import com.sendinfo.ticketing.verification.basic.api.system.request.SysAuditRoleQueryRequest;
import com.sendinfo.ticketing.verification.basic.component.system.param.SysAuditRoleQueryParam;
import com.sendinfo.ticketing.verification.basic.model.system.SysAuditRole;
import com.sendinfo.ticketing.verification.basic.repository.system.arg.SysAuditRoleQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.system.dataobject.SysAuditRoleDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 票型角色对象映射
 * <AUTHOR>
 */
@Mapper
public interface SysAuditRoleMapper {

    SysAuditRoleMapper INSTANCE = Mappers.getMapper(SysAuditRoleMapper.class);

    /**
     * SysAuditRoleDO 转 SysAuditRole
     *
     * @param dataObject 票型角色数据对象
     * @return SysAuditRole
     */
    SysAuditRole convert(SysAuditRoleDO dataObject);

    /**
     * SysAuditRoleQueryParam 转 SysAuditRoleQueryArg
     * @param param 票型角色查询参数
     * @return  SysAuditRoleQueryArg
     */
    SysAuditRoleQueryArg convert(SysAuditRoleQueryParam param);

    /**
     * SysAuditRoleQueryRequest 转 SysAuditRoleQueryParam
     * @param request   票型角色查询请求参数
     * @return  SysAuditRoleQueryParam
     */
    SysAuditRoleQueryParam convert(SysAuditRoleQueryRequest request);
} 