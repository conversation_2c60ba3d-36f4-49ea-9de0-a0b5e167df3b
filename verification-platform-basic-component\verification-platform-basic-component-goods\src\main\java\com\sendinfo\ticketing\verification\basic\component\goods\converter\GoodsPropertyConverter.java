package com.sendinfo.ticketing.verification.basic.component.goods.converter;

import com.sendinfo.ticketing.verification.basic.api.goods.request.GoodsPropertyCreateRequest;
import com.sendinfo.ticketing.verification.basic.api.goods.request.GoodsPropertyDeleteRequest;
import com.sendinfo.ticketing.verification.basic.api.goods.request.GoodsPropertyQueryRequest;
import com.sendinfo.ticketing.verification.basic.api.goods.request.GoodsPropertyUpdateRequest;
import com.sendinfo.ticketing.verification.basic.component.goods.mapper.GoodsPropertyMapper;
import com.sendinfo.ticketing.verification.basic.component.goods.param.GoodsPropertyCreateParam;
import com.sendinfo.ticketing.verification.basic.component.goods.param.GoodsPropertyDeleteParam;
import com.sendinfo.ticketing.verification.basic.component.goods.param.GoodsPropertyQueryParam;
import com.sendinfo.ticketing.verification.basic.component.goods.param.GoodsPropertyUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.goods.GoodsProperty;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.GoodsPropertyDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.GoodsPropertyQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.GoodsPropertyUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.GoodsPropertyDO;
import com.sendinfo.ticketing.verification.common.component.converter.*;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025-07-18 16:39
 */
@Component("goodsPropertyConverter")
public class GoodsPropertyConverter
        implements CreateParam2DoConverter<GoodsPropertyCreateParam, GoodsPropertyDO>,
        ReadParam2ArgConverter<GoodsPropertyQueryParam, GoodsPropertyQueryArg>,
        ReadDo2ModelConverter<GoodsPropertyDO, GoodsProperty>,
        UpdateParam2ArgConverter<GoodsPropertyUpdateParam, GoodsPropertyUpdateArg, GoodsProperty>,
        DeleteParam2ArgConverter<GoodsPropertyDeleteParam, GoodsPropertyDeleteArg>,
        DeleteReq2ParamConverter<GoodsPropertyDeleteRequest, GoodsPropertyDeleteParam>,
        CreateReq2ParamConverter<GoodsPropertyCreateRequest, GoodsPropertyCreateParam>,
        UpdateReq2ParamConverter<GoodsPropertyUpdateRequest, GoodsPropertyUpdateParam>,
        ReadReq2ParamConverter<GoodsPropertyQueryRequest, GoodsPropertyQueryParam> {
    @Override
    public GoodsPropertyDO c_p2d(GoodsPropertyCreateParam createParam) {
        return GoodsPropertyMapper.INSTANCE.convert(createParam);
    }

    @Override
    public GoodsPropertyCreateParam c_r2p(GoodsPropertyCreateRequest req) {
        return GoodsPropertyMapper.INSTANCE.convert(req);
    }

    @Override
    public GoodsPropertyDeleteArg d_p2a(GoodsPropertyDeleteParam param) {
        return GoodsPropertyMapper.INSTANCE.convert(param);
    }

    @Override
    public GoodsPropertyDeleteParam d_r2p(GoodsPropertyDeleteRequest req) {
        return GoodsPropertyMapper.INSTANCE.convert(req);
    }

    @Override
    public GoodsProperty r_d2m(GoodsPropertyDO dataObject) {
        return GoodsPropertyMapper.INSTANCE.convert(dataObject);
    }

    @Override
    public GoodsPropertyQueryArg r_p2a(GoodsPropertyQueryParam param) {
        return GoodsPropertyMapper.INSTANCE.convert(param);
    }

    @Override
    public GoodsPropertyQueryParam r_r2p(GoodsPropertyQueryRequest req) {
        return GoodsPropertyMapper.INSTANCE.convert(req);
    }

    @Override
    public GoodsPropertyUpdateArg u_p2a(GoodsPropertyUpdateParam param, GoodsProperty currentModel) {
        return GoodsPropertyMapper.INSTANCE.convert(param);
    }

    @Override
    public GoodsPropertyUpdateParam u_r2p(GoodsPropertyUpdateRequest req) {
        return GoodsPropertyMapper.INSTANCE.convert(req);
    }

    public List<GoodsPropertyCreateParam> c_rs2ps(List<GoodsPropertyCreateRequest> reqList) {
        return reqList.stream()
                .map(this::c_r2p)
                .collect(Collectors.toList());
    }
}
