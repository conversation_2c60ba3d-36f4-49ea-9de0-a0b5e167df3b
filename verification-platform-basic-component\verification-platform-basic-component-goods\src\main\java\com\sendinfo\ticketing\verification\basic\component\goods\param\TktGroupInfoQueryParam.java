package com.sendinfo.ticketing.verification.basic.component.goods.param;

import com.sendinfo.ticketing.verification.common.component.param.AbstractQueryParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 票型分组信息查询参数
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString(callSuper = true)
public class TktGroupInfoQueryParam extends AbstractQueryParam {

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 分组名称
     */
    private String groupName;

    /**
     * 排序号
     */
    private Integer sortNum;

    /**
     * 备注
     */
    private String remark;

    /**
     * 可用标识
     */
    private String useFlag;

    /**
     * 转参
     */
    private String transferParam;

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 分组编码
     */
    private String groupCode;

    /**
     * 等级
     */
    private Integer grade;

    /**
     * 父级ID
     */
    private String parentId;
} 