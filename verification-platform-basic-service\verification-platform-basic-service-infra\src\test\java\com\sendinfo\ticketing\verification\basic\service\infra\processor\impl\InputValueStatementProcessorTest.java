/**
 * INPUT类型参数值声明处理器测试
 *
 * <AUTHOR> 2025-08-04
 */
package com.sendinfo.ticketing.verification.basic.service.infra.processor.impl;

import com.sendinfo.ticketing.verification.basic.enums.SysParamDataType;
import com.sendinfo.ticketing.verification.basic.exception.VerificationBizRuntimeException;
import com.sendinfo.ticketing.verification.basic.model.infra.error.InfraErrorDef;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class InputValueStatementProcessorTest {

    private InputValueStatementProcessor processor;

    @Before
    public void setUp() {
        processor = new InputValueStatementProcessor();
    }

    @Test
    public void testGetDataType() {
        assertEquals(SysParamDataType.INPUT, processor.getDataType());
    }

    // ========== validateStatement 测试 ==========

    @Test
    public void testValidateStatement_成功校验() {
        String validStatement = "{\"defaultValue\":\"test_value\"}";
        
        // 不应该抛出异常
        processor.validateStatement(validStatement);
    }

    @Test(expected = VerificationBizRuntimeException.class)
    public void testValidateStatement_参数为null() {
        processor.validateStatement(null);
    }

    @Test(expected = VerificationBizRuntimeException.class)
    public void testValidateStatement_参数为空字符串() {
        processor.validateStatement("");
    }

    @Test(expected = VerificationBizRuntimeException.class)
    public void testValidateStatement_参数为空白字符串() {
        processor.validateStatement("   ");
    }

    @Test(expected = VerificationBizRuntimeException.class)
    public void testValidateStatement_JSON格式错误() {
        String invalidJson = "{\"defaultValue\":\"test_value\"";  // 缺少右括号
        processor.validateStatement(invalidJson);
    }

    @Test(expected = VerificationBizRuntimeException.class)
    public void testValidateStatement_defaultValue为null() {
        String statement = "{\"defaultValue\":null}";
        processor.validateStatement(statement);
    }

    @Test(expected = VerificationBizRuntimeException.class)
    public void testValidateStatement_defaultValue为空字符串() {
        String statement = "{\"defaultValue\":\"\"}";
        processor.validateStatement(statement);
    }

    @Test(expected = VerificationBizRuntimeException.class)
    public void testValidateStatement_defaultValue为空白字符串() {
        String statement = "{\"defaultValue\":\"   \"}";
        processor.validateStatement(statement);
    }

    @Test(expected = VerificationBizRuntimeException.class)
    public void testValidateStatement_缺少defaultValue字段() {
        String statement = "{\"otherField\":\"value\"}";
        processor.validateStatement(statement);
    }

    @Test
    public void testValidateStatement_包含其他字段() {
        String statement = "{\"defaultValue\":\"test_value\",\"otherField\":\"other_value\"}";
        
        // 不应该抛出异常
        processor.validateStatement(statement);
    }

    @Test
    public void testValidateStatement_异常信息验证() {
        try {
            processor.validateStatement(null);
            fail("应该抛出异常");
        } catch (VerificationBizRuntimeException e) {
            assertEquals(InfraErrorDef.SYSPARAM_VALUE_STATEMENT_VALIDATION_ERROR.code(), e.getErrorCode());
            assertTrue(e.getMessage().contains("INPUT类型参数值声明不能为空"));
        }
    }

    // ========== parseAndGetDefaultSysParamValue 测试 ==========

    @Test
    public void testParseAndGetDefaultSysParamValue_成功解析() {
        String statement = "{\"defaultValue\":\"test_value\"}";
        String result = processor.parseAndGetDefaultSysParamValue(statement);
        assertEquals("test_value", result);
    }

    @Test
    public void testParseAndGetDefaultSysParamValue_解析中文值() {
        String statement = "{\"defaultValue\":\"测试值\"}";
        String result = processor.parseAndGetDefaultSysParamValue(statement);
        assertEquals("测试值", result);
    }

    @Test
    public void testParseAndGetDefaultSysParamValue_解析特殊字符() {
        String statement = "{\"defaultValue\":\"test@#$%^&*()_+value\"}";
        String result = processor.parseAndGetDefaultSysParamValue(statement);
        assertEquals("test@#$%^&*()_+value", result);
    }

    @Test
    public void testParseAndGetDefaultSysParamValue_解析数字字符串() {
        String statement = "{\"defaultValue\":\"12345\"}";
        String result = processor.parseAndGetDefaultSysParamValue(statement);
        assertEquals("12345", result);
    }




    // ========== 综合测试 ==========

    @Test
    public void testValidateAndParse_完整流程() {
        String statement = "{\"defaultValue\":\"complete_test_value\"}";
        
        // 先校验
        processor.validateStatement(statement);
        
        // 再解析
        String result = processor.parseAndGetDefaultSysParamValue(statement);
        assertEquals("complete_test_value", result);
    }

    @Test
    public void testValidateAndParse_复杂JSON() {
        String statement = "{\"defaultValue\":\"test_value\",\"description\":\"测试描述\",\"required\":true}";
        
        // 先校验
        processor.validateStatement(statement);
        
        // 再解析
        String result = processor.parseAndGetDefaultSysParamValue(statement);
        assertEquals("test_value", result);
    }
}
