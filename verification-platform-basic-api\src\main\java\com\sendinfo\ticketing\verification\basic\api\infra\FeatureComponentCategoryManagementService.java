package com.sendinfo.ticketing.verification.basic.api.infra;

import com.sendinfo.ticketing.verification.basic.api.infra.request.FeatureComponentCategoryUpdateRequest;
import com.sendinfo.ticketing.verification.basic.model.infra.FeatureComponentCategory;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

/**
 * 组件分类管理服务
 * 
 * <AUTHOR> 2025-07-22 11:45:32
 */
public interface FeatureComponentCategoryManagementService {
    
    /**
     * 更新组件分类
     *
     * @param request 更新请求
     * @return 更新后的组件分类
     */
    ResultModel<Boolean> updateFeatureComponentCategory(FeatureComponentCategoryUpdateRequest request);
}
