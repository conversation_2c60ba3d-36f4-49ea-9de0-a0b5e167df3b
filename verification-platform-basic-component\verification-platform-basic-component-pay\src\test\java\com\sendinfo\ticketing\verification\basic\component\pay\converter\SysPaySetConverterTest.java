package com.sendinfo.ticketing.verification.basic.component.pay.converter;

import com.sendinfo.ticketing.verification.basic.component.pay.param.SysPaySetCreateParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.SysPaySetQueryParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.SysPaySetUpdateParam;
import com.sendinfo.ticketing.verification.basic.enums.CommonUseFlagEnum;
import com.sendinfo.ticketing.verification.basic.model.pay.SysPaySet;
import com.sendinfo.ticketing.verification.basic.model.pay.enums.PayDefaultEnum;
import com.sendinfo.ticketing.verification.basic.model.pay.enums.PayTypeEnum;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysPaySetQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysPaySetUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysPaySetDO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;

/**
 * SysPaySetConverter单元测试
 * 测试支付标签转换器的各种转换功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RunWith(MockitoJUnitRunner.class)
public class SysPaySetConverterTest {

    @InjectMocks
    private SysPaySetConverter sysPaySetConverter;

    // 测试常量
    private static final String TEST_CORP_CODE = "TEST_CORP_001";
    private static final Long TEST_ID = 1L;
    private static final String TEST_PAY_LABEL = "测试支付标签";
    private static final String TEST_PAY_TYPE_CODE = "PAY_TYPE_001";
    private static final String TEST_PAY_LABEL_ICON = "icon_pay.png";
    private static final String TEST_REMARK = "这是一个测试支付标签";
    private static final Long TEST_MAINBODY_ID = 100L;
    private static final String TEST_PAY_NAME = "自定义支付";
    private static final String TEST_TRANSFER_PARAM = "transfer_data";
    private static final String TEST_CREATE_BY = "testUser";
    private static final String TEST_MODIFY_BY = "testUser";

    /**
     * 测试目的：验证SysPaySetCreateParam转换为SysPaySetDO的功能
     * 测试步骤：
     * 1. 创建完整的SysPaySetCreateParam对象
     * 2. 调用c_p2d方法进行转换
     * 3. 验证转换结果的正确性
     */
    @Test
    public void testCreateParam2Do_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        SysPaySetCreateParam createParam = createSysPaySetCreateParam();

        // 2. 执行转换
        SysPaySetDO result = sysPaySetConverter.c_p2d(createParam);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", result);
        assertEquals("支付标签应该一致", TEST_PAY_LABEL, result.getPaylableName());
        assertEquals("支付平台编码应该一致", TEST_PAY_TYPE_CODE, result.getPayTypeCode());
        assertEquals("支付标签图标应该一致", TEST_PAY_LABEL_ICON, result.getPaylableIcon());
        assertEquals("支付类型应该一致", PayTypeEnum.CASH_PAYMENT.getCode(), result.getPayType());
        assertEquals("启用状态应该一致", CommonUseFlagEnum.ENABLED.getCode(), result.getUseFlag());
        assertEquals("默认支付方式应该一致", PayDefaultEnum.YES.getCode(), result.getPayDefault());
        assertEquals("备注应该一致", TEST_REMARK, result.getRemark());
        assertEquals("主体ID应该一致", TEST_MAINBODY_ID, result.getMainbodyId());
        assertEquals("自定义支付名称应该一致", TEST_PAY_NAME, result.getPayName());
        assertEquals("迁移数据应该一致", TEST_TRANSFER_PARAM, result.getTransferParam());
    }

    /**
     * 测试目的：验证SysPaySetCreateParam为null时的处理
     */
    @Test
    public void testCreateParam2Do_WithNullInput_ShouldReturnNull() {
        // 1. 执行转换
        SysPaySetDO result = sysPaySetConverter.c_p2d(null);

        // 2. 验证结果
        assertNull("输入为null时应该返回null", result);
    }

    /**
     * 测试目的：验证SysPaySetDO转换为SysPaySet的功能
     * 测试步骤：
     * 1. 创建完整的SysPaySetDO对象
     * 2. 调用r_d2m方法进行转换
     * 3. 验证转换结果的正确性
     */
    @Test
    public void testReadDo2Model_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        SysPaySetDO sysPaySetDO = createSysPaySetDO();

        // 2. 执行转换
        SysPaySet result = sysPaySetConverter.r_d2m(sysPaySetDO);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", result);
        assertEquals("ID应该一致", TEST_ID, result.getId());
        assertEquals("支付标签应该一致", TEST_PAY_LABEL, result.getPaylableName());
        assertEquals("支付平台编码应该一致", TEST_PAY_TYPE_CODE, result.getPayTypeCode());
        assertEquals("支付标签图标应该一致", TEST_PAY_LABEL_ICON, result.getPaylableIcon());
        assertEquals("支付类型应该一致", PayTypeEnum.CASH_PAYMENT, result.getPayType());
        assertEquals("启用状态应该一致", CommonUseFlagEnum.ENABLED, result.getUseFlag());
        assertEquals("默认支付方式应该一致", PayDefaultEnum.YES, result.getPayDefault());
        assertEquals("备注应该一致", TEST_REMARK, result.getRemark());
        assertEquals("主体ID应该一致", TEST_MAINBODY_ID, result.getMainbodyId());
        assertEquals("自定义支付名称应该一致", TEST_PAY_NAME, result.getPayName());
        assertEquals("迁移数据应该一致", TEST_TRANSFER_PARAM, result.getTransferParam());
        assertEquals("创建人应该一致", TEST_CREATE_BY, result.getCreateBy());
        assertEquals("修改人应该一致", TEST_MODIFY_BY, result.getModifyBy());
    }

    /**
     * 测试目的：验证SysPaySetDO为null时的处理
     */
    @Test
    public void testReadDo2Model_WithNullInput_ShouldReturnNull() {
        // 1. 执行转换
        SysPaySet result = sysPaySetConverter.r_d2m(null);

        // 2. 验证结果
        assertNull("输入为null时应该返回null", result);
    }

    /**
     * 测试目的：验证List<SysPaySetDO>转换为List<SysPaySet>的功能
     * 测试步骤：
     * 1. 创建包含多个SysPaySetDO的列表
     * 2. 调用r_ds2ms方法进行转换
     * 3. 验证转换结果的正确性
     */
    @Test
    public void testReadDoList2ModelList_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        List<SysPaySetDO> sysPaySetDOList = Arrays.asList(
                createSysPaySetDO(),
                createSysPaySetDO()
        );

        // 2. 执行转换
        List<SysPaySet> result = sysPaySetConverter.r_ds2ms(sysPaySetDOList);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", result);
        assertEquals("列表大小应该一致", 2, result.size());

        // 验证第一个元素
        SysPaySet firstResult = result.get(0);
        assertEquals("第一个元素的ID应该一致", TEST_ID, firstResult.getId());
        assertEquals("第一个元素的支付标签应该一致", TEST_PAY_LABEL, firstResult.getPaylableName());
        assertEquals("第一个元素的支付平台编码应该一致", TEST_PAY_TYPE_CODE, firstResult.getPayTypeCode());
        assertEquals("第一个元素的支付标签图标应该一致", TEST_PAY_LABEL_ICON, firstResult.getPaylableIcon());
        assertEquals("第一个元素的支付类型应该一致", PayTypeEnum.CASH_PAYMENT, firstResult.getPayType());
        assertEquals("第一个元素的启用状态应该一致", CommonUseFlagEnum.ENABLED, firstResult.getUseFlag());
        assertEquals("第一个元素的默认支付方式应该一致", PayDefaultEnum.YES, firstResult.getPayDefault());
        assertEquals("第一个元素的备注应该一致", TEST_REMARK, firstResult.getRemark());
        assertEquals("第一个元素的主体ID应该一致", TEST_MAINBODY_ID, firstResult.getMainbodyId());
        assertEquals("第一个元素的自定义支付名称应该一致", TEST_PAY_NAME, firstResult.getPayName());
        assertEquals("第一个元素的迁移数据应该一致", TEST_TRANSFER_PARAM, firstResult.getTransferParam());

        // 验证第二个元素
        SysPaySet secondResult = result.get(1);
        assertEquals("第二个元素的ID应该一致", TEST_ID, secondResult.getId());
        assertEquals("第二个元素的支付标签应该一致", TEST_PAY_LABEL, secondResult.getPaylableName());
        assertEquals("第二个元素的支付平台编码应该一致", TEST_PAY_TYPE_CODE, secondResult.getPayTypeCode());
        assertEquals("第二个元素的支付标签图标应该一致", TEST_PAY_LABEL_ICON, secondResult.getPaylableIcon());
        assertEquals("第二个元素的支付类型应该一致", PayTypeEnum.CASH_PAYMENT, secondResult.getPayType());
        assertEquals("第二个元素的启用状态应该一致", CommonUseFlagEnum.ENABLED, secondResult.getUseFlag());
        assertEquals("第二个元素的默认支付方式应该一致", PayDefaultEnum.YES, secondResult.getPayDefault());
        assertEquals("第二个元素的备注应该一致", TEST_REMARK, secondResult.getRemark());
        assertEquals("第二个元素的主体ID应该一致", TEST_MAINBODY_ID, secondResult.getMainbodyId());
        assertEquals("第二个元素的自定义支付名称应该一致", TEST_PAY_NAME, secondResult.getPayName());
        assertEquals("第二个元素的迁移数据应该一致", TEST_TRANSFER_PARAM, secondResult.getTransferParam());
    }

    /**
     * 测试目的：验证List<SysPaySetDO>为null时的处理
     */
    @Test
    public void testReadDoList2ModelList_WithNullInput_ShouldReturnNull() {
        // 1. 执行转换
        List<SysPaySet> result = sysPaySetConverter.r_ds2ms(null);

        // 2. 验证结果
        assertNull("输入为null时应该返回null", result);
    }

    /**
     * 测试目的：验证空列表的处理
     */
    @Test
    public void testReadDoList2ModelList_WithEmptyList_ShouldReturnEmptyList() {
        // 1. 创建空列表
        List<SysPaySetDO> emptyList = Arrays.asList();

        // 2. 执行转换
        List<SysPaySet> result = sysPaySetConverter.r_ds2ms(emptyList);

        // 3. 验证结果
        assertNotNull("结果不应为null", result);
        assertTrue("结果应该是空列表", result.isEmpty());
    }

    /**
     * 测试目的：验证SysPaySetQueryParam转换为SysPaySetQueryArg的功能
     * 测试步骤：
     * 1. 创建完整的SysPaySetQueryParam对象
     * 2. 调用r_p2a方法进行转换
     * 3. 验证转换结果的正确性，包括分页参数处理
     */
    @Test
    public void testReadQueryParam2QueryArg_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        SysPaySetQueryParam queryParam = createSysPaySetQueryParam();

        // 2. 执行转换
        SysPaySetQueryArg result = sysPaySetConverter.r_p2a(queryParam);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", result);
        assertEquals("企业编码应该一致", TEST_CORP_CODE, result.getCorpCode());
        assertEquals("支付标签应该一致", TEST_PAY_LABEL, result.getPaylableName());
        assertEquals("支付平台编码应该一致", TEST_PAY_TYPE_CODE, result.getPayTypeCode());
        assertEquals("支付类型应该一致", PayTypeEnum.CASH_PAYMENT.getCode(), result.getPayType());
        assertEquals("启用状态应该一致", CommonUseFlagEnum.ENABLED.getCode(), result.getUseFlag());
        assertEquals("默认支付方式应该一致", PayDefaultEnum.YES.getCode(), result.getPayDefault());
        assertEquals("主体ID应该一致", TEST_MAINBODY_ID, result.getMainbodyId());
        assertEquals("自定义支付名称应该一致", TEST_PAY_NAME, result.getPayName());

        // 验证分页参数
        assertEquals("偏移量应该一致", Integer.valueOf(0), result.getOffset());
        assertEquals("限制数量应该一致", Integer.valueOf(10), result.getLimit());
    }

    /**
     * 测试目的：验证SysPaySetQueryParam为null时的处理
     */
    @Test
    public void testReadQueryParam2QueryArg_WithNullInput_ShouldReturnNull() {
        // 1. 执行转换
        SysPaySetQueryArg result = sysPaySetConverter.r_p2a(null);

        // 2. 验证结果
        assertNull("输入为null时应该返回null", result);
    }

    /**
     * 测试目的：验证SysPaySetUpdateParam转换为SysPaySetUpdateArg的功能
     * 测试步骤：
     * 1. 创建完整的SysPaySetUpdateParam对象
     * 2. 调用u_p2a方法进行转换
     * 3. 验证转换结果的正确性
     */
    @Test
    public void testUpdateParam2UpdateArg_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        SysPaySetUpdateParam updateParam = createSysPaySetUpdateParam();
        SysPaySet currentModel = createSysPaySet();

        // 2. 执行转换
        SysPaySetUpdateArg result = sysPaySetConverter.u_p2a(updateParam, currentModel);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", result);
        assertEquals("支付标签图标应该一致", TEST_PAY_LABEL_ICON, result.getPaylableIcon());
        assertEquals("支付类型应该一致", PayTypeEnum.CASH_PAYMENT.getCode(), result.getPayType());
        assertEquals("启用状态应该一致", CommonUseFlagEnum.ENABLED.getCode(), result.getUseFlag());
        assertEquals("默认支付方式应该一致", PayDefaultEnum.YES.getCode(), result.getPayDefault());
        assertEquals("备注应该一致", TEST_REMARK, result.getRemark());
        assertEquals("主体ID应该一致", TEST_MAINBODY_ID, result.getMainbodyId());
        assertEquals("自定义支付名称应该一致", TEST_PAY_NAME, result.getPayName());
        assertEquals("迁移数据应该一致", TEST_TRANSFER_PARAM, result.getTransferParam());
        assertEquals("修改人应该一致", TEST_MODIFY_BY, result.getModifyBy());
    }

    /**
     * 测试目的：验证SysPaySetUpdateParam为null时的处理
     */
    @Test
    public void testUpdateParam2UpdateArg_WithNullInput_ShouldReturnNull() {
        // 1. 创建当前模型
        SysPaySet currentModel = createSysPaySet();

        // 2. 执行转换
        SysPaySetUpdateArg result = sysPaySetConverter.u_p2a(null, currentModel);

        // 3. 验证结果
        assertNull("输入为null时应该返回null", result);
    }

    // ==================== 测试数据构建方法 ====================

    /**
     * 创建SysPaySetCreateParam测试数据
     */
    private SysPaySetCreateParam createSysPaySetCreateParam() {
        SysPaySetCreateParam createParam = new SysPaySetCreateParam();
        createParam.setCorpCode(TEST_CORP_CODE);
        createParam.setPaylableName(TEST_PAY_LABEL);
        createParam.setPayTypeCode(TEST_PAY_TYPE_CODE);
        createParam.setPaylableIcon(TEST_PAY_LABEL_ICON);
        createParam.setUseFlag(CommonUseFlagEnum.ENABLED);
        createParam.setRemark(TEST_REMARK);
        createParam.setPayDefault(PayDefaultEnum.YES);
        createParam.setMainbodyId(TEST_MAINBODY_ID);
        createParam.setPayType(PayTypeEnum.CASH_PAYMENT);
        createParam.setPayName(TEST_PAY_NAME);
        createParam.setTransferParam(TEST_TRANSFER_PARAM);
        createParam.setCreateBy(TEST_CREATE_BY);
        createParam.setModifyBy(TEST_MODIFY_BY);
        return createParam;
    }

    /**
     * 创建SysPaySetDO测试数据
     */
    private SysPaySetDO createSysPaySetDO() {
        SysPaySetDO sysPaySetDO = new SysPaySetDO();
        sysPaySetDO.setId(TEST_ID);
        sysPaySetDO.setPaylableName(TEST_PAY_LABEL);
        sysPaySetDO.setPayTypeCode(TEST_PAY_TYPE_CODE);
        sysPaySetDO.setPaylableIcon(TEST_PAY_LABEL_ICON);
        sysPaySetDO.setUseFlag(CommonUseFlagEnum.ENABLED.getCode());
        sysPaySetDO.setRemark(TEST_REMARK);
        sysPaySetDO.setPayDefault(PayDefaultEnum.YES.getCode());
        sysPaySetDO.setMainbodyId(TEST_MAINBODY_ID);
        sysPaySetDO.setPayType(PayTypeEnum.CASH_PAYMENT.getCode());
        sysPaySetDO.setPayName(TEST_PAY_NAME);
        sysPaySetDO.setTransferParam(TEST_TRANSFER_PARAM);
        sysPaySetDO.setCreateBy(TEST_CREATE_BY);
        sysPaySetDO.setModifyBy(TEST_MODIFY_BY);
        return sysPaySetDO;
    }

    /**
     * 创建SysPaySetQueryParam测试数据
     */
    private SysPaySetQueryParam createSysPaySetQueryParam() {
        SysPaySetQueryParam queryParam = new SysPaySetQueryParam();
        queryParam.setCorpCode(TEST_CORP_CODE);
        queryParam.setPaylableName(TEST_PAY_LABEL);
        queryParam.setPayType(PayTypeEnum.CASH_PAYMENT);
        queryParam.setUseFlag(CommonUseFlagEnum.ENABLED);
        queryParam.setPayDefault(PayDefaultEnum.YES);
        queryParam.setPayTypeCode(TEST_PAY_TYPE_CODE);
        queryParam.setMainbodyId(TEST_MAINBODY_ID);
        queryParam.setPayName(TEST_PAY_NAME);
        queryParam.setStartIndex(0);
        queryParam.setPageSize(10);
        return queryParam;
    }

    /**
     * 创建SysPaySetUpdateParam测试数据
     */
    private SysPaySetUpdateParam createSysPaySetUpdateParam() {
        SysPaySetUpdateParam updateParam = new SysPaySetUpdateParam();
        updateParam.setCorpCode(TEST_CORP_CODE);
        updateParam.setPaylableIcon(TEST_PAY_LABEL_ICON);
        updateParam.setUseFlag(CommonUseFlagEnum.ENABLED);
        updateParam.setRemark(TEST_REMARK);
        updateParam.setPayDefault(PayDefaultEnum.YES);
        updateParam.setMainbodyId(TEST_MAINBODY_ID);
        updateParam.setPayType(PayTypeEnum.CASH_PAYMENT);
        updateParam.setPayName(TEST_PAY_NAME);
        updateParam.setTransferParam(TEST_TRANSFER_PARAM);
        updateParam.setModifyBy(TEST_MODIFY_BY);
        return updateParam;
    }

    /**
     * 创建SysPaySet测试数据
     */
    private SysPaySet createSysPaySet() {
        SysPaySet sysPaySet = new SysPaySet();
        sysPaySet.setId(TEST_ID);
        sysPaySet.setPaylableName(TEST_PAY_LABEL);
        sysPaySet.setPayTypeCode(TEST_PAY_TYPE_CODE);
        sysPaySet.setPaylableIcon(TEST_PAY_LABEL_ICON);
        sysPaySet.setUseFlag(CommonUseFlagEnum.ENABLED);
        sysPaySet.setRemark(TEST_REMARK);
        sysPaySet.setPayDefault(PayDefaultEnum.YES);
        sysPaySet.setMainbodyId(TEST_MAINBODY_ID);
        sysPaySet.setPayType(PayTypeEnum.CASH_PAYMENT);
        sysPaySet.setPayName(TEST_PAY_NAME);
        sysPaySet.setTransferParam(TEST_TRANSFER_PARAM);
        sysPaySet.setCreateBy(TEST_CREATE_BY);
        sysPaySet.setModifyBy(TEST_MODIFY_BY);
        return sysPaySet;
    }

} 