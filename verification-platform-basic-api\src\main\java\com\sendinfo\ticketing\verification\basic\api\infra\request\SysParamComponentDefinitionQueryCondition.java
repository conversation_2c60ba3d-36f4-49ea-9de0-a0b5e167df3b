/**
 * System parameter component definition query condition
 *
 * <AUTHOR> 2025-07-21 15:40:00
 */
package com.sendinfo.ticketing.verification.basic.api.infra.request;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 系统参数组件定义查询条件
 */
@Getter
@Setter
@ToString
public class SysParamComponentDefinitionQueryCondition implements Serializable {
    private static final long serialVersionUID = -7654321098765432105L;

    /**
     * 企业编码
     */
    @NotBlank(message = "企业编码不能为空")
    private String corpCode;

    /**
     * 组件分类Id，infra_feature_component_category主键id
     */
    private Long categoryId;

    /**
     * 组件编码
     */
    @Size(max = 64, message = "组件编码长度不能超过64")
    private String componentCode;

    /**
     * 组件名称
     */
    @Size(max = 128, message = "组件名称长度不能超过128")
    private String componentName;

    /**
     * 状态：1-启用，0-禁用
     */
    private Integer status;
}