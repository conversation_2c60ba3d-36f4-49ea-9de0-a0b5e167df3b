package com.sendinfo.ticketing.verification.basic.api.infra;

import com.sendinfo.ticketing.verification.common.api.result.PageResultModel;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;
import com.sendinfo.ticketing.verification.common.api.request.PageRequest;
import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamValueQueryCondition;
import com.sendinfo.ticketing.verification.basic.model.infra.SysParamValue;

/**
 *  系统参数值查询服务
 *
 *  <AUTHOR> 2025-05-20 15:40:00
 */
public interface SysParamValueReadService {
    /**
     * 分页查询系统参数值
     *
     * @param pageRequest 分页查询条件
     * @return 分页结果
     */
    PageResultModel<SysParamValue> queryPageList(PageRequest<SysParamValueQueryCondition> pageRequest);

    /**
     * 根据ID查询系统参数值
     *
     * @param id 参数值ID
     * @return 参数值
     */
    ResultModel<SysParamValue> querySysParamValueById(Long id);

    /**
     * 根据参数编码和企业编码查询系统参数值
     *
     * @param paramCode 参数编码
     * @param corpCode 企业编码
     * @return 参数值
     */
    ResultModel<SysParamValue> queryByParamCodeAndCorpCode(String paramCode, String corpCode);

} 