prompt： 使用 @verification-common-repository-dao-generation-guide.mdc 规则，生成完整的数据访问层代码。


## 基本信息
- 业务模块： verification-platform-basic-repository-pay
- DAO包路径：com.sendinfo.ticketing.verification.basic.repository.pay.dao
- DO包路径：com.sendinfo.ticketing.verification.basic.repository.pay.dataobject
- ARG包路径：com.sendinfo.ticketing.verification.basic.repository.pay.arg
- Mapper文件路径：resources/saaspw-mapper

## 数据表定义
CREATE TABLE `pay_chanle` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `CORP_CODE` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT '' COMMENT '企业编码',
  `PRODUCT_ID` bigint unsigned DEFAULT NULL,
  `CHANLE_NAME` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '' COMMENT '渠道名称：建行、农行',
  `CHANLE_CODE` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '' COMMENT '渠道编码:对应支付中心的渠道产品编号',
  `PAY_PRODUCT_CODE` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT '支付产品码：对应支付中心的支付产品码',
  `REMARK` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT '备注',
  `CREATE_BY` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT '' COMMENT '创建人',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `MODIFY_BY` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT '' COMMENT '修改人',
  `MODIFY_TIME` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `DELETED` char(1) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '删除标志F未删除,T已删除',
  `USE_FLAG` char(1) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT '' COMMENT '启用标识T：F',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_modify_time` (`MODIFY_TIME`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7087 DEFAULT CHARSET=utf8mb3 COLLATE=utf8_bin ROW_FORMAT=DYNAMIC COMMENT='支付渠道'
```

## 数据对象(DO)设计
- 主键类型：Long
- 是否为租户表(含corp_code字段)：是
- 特殊字段映射：无


## 查询参数(QueryArg)设计
- 必要查询条件：deleted = 'F'
- 可选查询条件：PRODUCT_ID、CHANLE_CODE
- 是否支持分页：不支持
- 默认排序规则：id降序


## 更新参数(UpdateArg)设计
- 可更新字段： 除去不可更新字段
- 不可更新字段：无
- 更新条件字段：id


## 删除参数(DeleteArg)设计

## 支持删除
- 删除条件字段：id
- 是否支持软删除：是
- 是否支持物理删除：否



## 批量操作方法
- 是否支持批量插入：是
- 是否支持批量更新：否
- 是否支持批量删除：否
- 批量操作的特殊要求：否
- 参考已经写好的softDeleteByArg方法  deleteById方法


## 特殊业务规则
- <AUTHOR>
- 单测用junit的包

## 参考示例
可参考已有的 com.sendinfo.ticketing.verification.basic.repository.system.dao.DictDetailDao 实现

## 单元测试
- 单元测试必须克制，且能够覆盖所有方法
- 单元测试必须通过注释将单元测试目的和步骤详细编写，新手能够快速入门
- 可以参考的单元测试模板 com.sendinfo.ticketing.verification.basic.repository.system.dao.DictDetailDaoTest
- 不需要运行单测案例