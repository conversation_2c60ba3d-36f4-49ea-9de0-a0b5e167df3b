package com.sendinfo.ticketing.verification.basic.repository.pay.dataobject;

import com.sendinfo.ticketing.verification.common.repository.dataobject.AbstractTenantBaseDO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 经营主体子商户数据对象
 * 对应数据库表 sys_mainbody_manage_sub_merchants
 * 继承租户基础DO，包含租户隔离字段corpCode
 * 所有字段与表结构保持一致，便于MyBatis自动映射
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class SysMainbodyManageSubMerchantsDO extends AbstractTenantBaseDO<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 子商户名称
     */
    private String subMerchantsName;

    /**
     * 子商户号
     */
    private String merchantNo;
} 