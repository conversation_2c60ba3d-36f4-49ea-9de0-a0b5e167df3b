package com.sendinfo.ticketing.verification.basic.service.auth.cache;

import com.sendinfo.ticketing.verification.basic.model.auth.UserSessionData;
import com.sendinfo.ticketing.verification.basic.model.auth.enums.JwtTokenSource;

/**
 * <AUTHOR>
 * @since 2025-07-23 15:07:05
 */
public interface UserSessionDataCacheService {

    /**
     * 获取用户ID 从老票务的登录session
     *
     * @param expectedToken  token
     * @param userId 用户ID
     * @param source 来源
     * @return 用户会话数据
     */
    UserSessionData getUserSessionByUserIdFromOldPw(String expectedToken, String userId, JwtTokenSource source);
}
