package com.sendinfo.ticketing.verification.basic.component.goods.impl;

import com.sendinfo.ticketing.verification.basic.component.goods.converter.TktTicketDivRuleConverter;
import com.sendinfo.ticketing.verification.basic.model.goods.TktTicketDivRule;
import com.sendinfo.ticketing.verification.basic.repository.goods.dao.TktTicketDivRuleDao;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TktTicketDivRuleDO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

/**
 * TktTicketDivRuleReadComponent 单元测试类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RunWith(MockitoJUnitRunner.class)
public class TktTicketDivRuleReadComponentImplTest {

    @Mock
    private TktTicketDivRuleConverter converter;

    @Mock
    private TktTicketDivRuleDao dao;

    @InjectMocks
    private TktTicketDivRuleReadComponentImpl tktTicketDivRuleReadComponent;

    // 测试常量
    private static final String TEST_CORP_CODE = "TEST_CORP_CODE";
    private static final Long TEST_TICKET_ID_1 = 1L;
    private static final Long TEST_TICKET_ID_2 = 2L;
    private static final String TEST_TICKET_CODE = "TEST_TICKET_001";
    private static final String TEST_GROUP_FLAG = "F";
    private static final Integer TEST_PARK_ID = 100;
    private static final String TEST_PARK_NAME = "测试景区";
    private static final String TEST_TICKET_TYPE = "门票";
    private static final String TEST_DIV_METHOD = "0";
    private static final BigDecimal TEST_DIV_MONEY = new BigDecimal("50.00");
    private static final Integer TEST_DECIMAL_PROCESS_METHOD = 0;
    private static final BigDecimal TEST_PARK_PRICE = new BigDecimal("100.00");
    private static final Integer TEST_PRIORITY = 1;
    private static final Integer TEST_USE_FLAG = 1;
    private static final String TEST_DIV_MUST_FLAG = "T";
    private static final String TEST_DIV_CAMPSUR_FLAG = "F";
    private static final Integer TEST_CAMPSUR_SCALE = 80;
    private static final BigDecimal TEST_TRAVEL_DIV_MONEY = new BigDecimal("20.00");
    private static final String TEST_CREATE_BY = "TEST_CREATE_BY";
    private static final String TEST_MODIFY_BY = "TEST_MODIFY_BY";

    private TktTicketDivRuleDO tktTicketDivRuleDO1;
    private TktTicketDivRuleDO tktTicketDivRuleDO2;
    private TktTicketDivRule tktTicketDivRule1;
    private TktTicketDivRule tktTicketDivRule2;

    @Before
    public void setUp() {
        // 准备第一个测试数据对象
        tktTicketDivRuleDO1 = new TktTicketDivRuleDO();
        tktTicketDivRuleDO1.setId(1L);
        tktTicketDivRuleDO1.setCorpCode(TEST_CORP_CODE);
        tktTicketDivRuleDO1.setTicketId(TEST_TICKET_ID_1);
        tktTicketDivRuleDO1.setTicketCode(TEST_TICKET_CODE);
        tktTicketDivRuleDO1.setGroupFlag(TEST_GROUP_FLAG);
        tktTicketDivRuleDO1.setParkId(TEST_PARK_ID);
        tktTicketDivRuleDO1.setParkName(TEST_PARK_NAME);
        tktTicketDivRuleDO1.setTicketType(TEST_TICKET_TYPE);
        tktTicketDivRuleDO1.setDivMethod(TEST_DIV_METHOD);
        tktTicketDivRuleDO1.setDivMoney(TEST_DIV_MONEY);
        tktTicketDivRuleDO1.setDecimalProcessMethod(TEST_DECIMAL_PROCESS_METHOD);
        tktTicketDivRuleDO1.setParkPrice(TEST_PARK_PRICE);
        tktTicketDivRuleDO1.setPriority(TEST_PRIORITY);
        tktTicketDivRuleDO1.setUseFlag(TEST_USE_FLAG);
        tktTicketDivRuleDO1.setDivMustFlag(TEST_DIV_MUST_FLAG);
        tktTicketDivRuleDO1.setDivCampsurFlag(TEST_DIV_CAMPSUR_FLAG);
        tktTicketDivRuleDO1.setCampsurScale(TEST_CAMPSUR_SCALE);
        tktTicketDivRuleDO1.setTravelDivMoney(TEST_TRAVEL_DIV_MONEY);
        tktTicketDivRuleDO1.setCreateBy(TEST_CREATE_BY);
        tktTicketDivRuleDO1.setModifyBy(TEST_MODIFY_BY);

        // 准备第二个测试数据对象
        tktTicketDivRuleDO2 = new TktTicketDivRuleDO();
        tktTicketDivRuleDO2.setId(2L);
        tktTicketDivRuleDO2.setCorpCode(TEST_CORP_CODE);
        tktTicketDivRuleDO2.setTicketId(TEST_TICKET_ID_2);
        tktTicketDivRuleDO2.setTicketCode("TEST_TICKET_002");
        tktTicketDivRuleDO2.setGroupFlag(TEST_GROUP_FLAG);
        tktTicketDivRuleDO2.setParkId(101);
        tktTicketDivRuleDO2.setParkName("测试景区2");
        tktTicketDivRuleDO2.setTicketType(TEST_TICKET_TYPE);
        tktTicketDivRuleDO2.setDivMethod("1");
        tktTicketDivRuleDO2.setDivMoney(new BigDecimal("0.3"));
        tktTicketDivRuleDO2.setDecimalProcessMethod(TEST_DECIMAL_PROCESS_METHOD);
        tktTicketDivRuleDO2.setParkPrice(new BigDecimal("80.00"));
        tktTicketDivRuleDO2.setPriority(2);
        tktTicketDivRuleDO2.setUseFlag(TEST_USE_FLAG);
        tktTicketDivRuleDO2.setDivMustFlag("F");
        tktTicketDivRuleDO2.setDivCampsurFlag(TEST_DIV_CAMPSUR_FLAG);
        tktTicketDivRuleDO2.setCampsurScale(70);
        tktTicketDivRuleDO2.setTravelDivMoney(new BigDecimal("15.00"));
        tktTicketDivRuleDO2.setCreateBy(TEST_CREATE_BY);
        tktTicketDivRuleDO2.setModifyBy(TEST_MODIFY_BY);

        // 准备对应的模型对象
        tktTicketDivRule1 = new TktTicketDivRule();
        tktTicketDivRule1.setCorpCode(TEST_CORP_CODE);
        tktTicketDivRule1.setTicketId(TEST_TICKET_ID_1);
        tktTicketDivRule1.setTicketCode(TEST_TICKET_CODE);
        tktTicketDivRule1.setGroupFlag(TEST_GROUP_FLAG);
        tktTicketDivRule1.setParkId(TEST_PARK_ID);
        tktTicketDivRule1.setParkName(TEST_PARK_NAME);
        tktTicketDivRule1.setTicketType(TEST_TICKET_TYPE);
        tktTicketDivRule1.setDivMethod(TEST_DIV_METHOD);
        tktTicketDivRule1.setDivMoney(TEST_DIV_MONEY);
        tktTicketDivRule1.setDecimalProcessMethod(TEST_DECIMAL_PROCESS_METHOD);
        tktTicketDivRule1.setParkPrice(TEST_PARK_PRICE);
        tktTicketDivRule1.setPriority(TEST_PRIORITY);
        tktTicketDivRule1.setUseFlag(TEST_USE_FLAG);
        tktTicketDivRule1.setDivMustFlag(TEST_DIV_MUST_FLAG);
        tktTicketDivRule1.setDivCampsurFlag(TEST_DIV_CAMPSUR_FLAG);
        tktTicketDivRule1.setCampsurScale(TEST_CAMPSUR_SCALE);
        tktTicketDivRule1.setTravelDivMoney(TEST_TRAVEL_DIV_MONEY);
        tktTicketDivRule1.setCreateBy(TEST_CREATE_BY);
        tktTicketDivRule1.setModifyBy(TEST_MODIFY_BY);

        tktTicketDivRule2 = new TktTicketDivRule();
        tktTicketDivRule2.setCorpCode(TEST_CORP_CODE);
        tktTicketDivRule2.setTicketId(TEST_TICKET_ID_2);
        tktTicketDivRule2.setTicketCode("TEST_TICKET_002");
        tktTicketDivRule2.setGroupFlag(TEST_GROUP_FLAG);
        tktTicketDivRule2.setParkId(101);
        tktTicketDivRule2.setParkName("测试景区2");
        tktTicketDivRule2.setTicketType(TEST_TICKET_TYPE);
        tktTicketDivRule2.setDivMethod("1");
        tktTicketDivRule2.setDivMoney(new BigDecimal("0.3"));
        tktTicketDivRule2.setDecimalProcessMethod(TEST_DECIMAL_PROCESS_METHOD);
        tktTicketDivRule2.setParkPrice(new BigDecimal("80.00"));
        tktTicketDivRule2.setPriority(2);
        tktTicketDivRule2.setUseFlag(TEST_USE_FLAG);
        tktTicketDivRule2.setDivMustFlag("F");
        tktTicketDivRule2.setDivCampsurFlag(TEST_DIV_CAMPSUR_FLAG);
        tktTicketDivRule2.setCampsurScale(70);
        tktTicketDivRule2.setTravelDivMoney(new BigDecimal("15.00"));
        tktTicketDivRule2.setCreateBy(TEST_CREATE_BY);
        tktTicketDivRule2.setModifyBy(TEST_MODIFY_BY);
    }

    /**
     * 测试根据票型ID查询分成规则 - 正常场景
     */
    @Test
    public void testListTicketDivRuleByTicketId_ShouldReturnList() {
        // 1. 准备测试数据
        List<TktTicketDivRuleDO> divRuleDOList = Arrays.asList(tktTicketDivRuleDO1, tktTicketDivRuleDO2);
        List<TktTicketDivRule> expectedDivRuleList = Arrays.asList(tktTicketDivRule1, tktTicketDivRule2);

        when(dao.listTicketDivRuleByTicketId(TEST_TICKET_ID_1, TEST_CORP_CODE))
                .thenReturn(divRuleDOList);
        when(converter.r_ds2ms(divRuleDOList)).thenReturn(expectedDivRuleList);

        // 2. 执行测试
        List<TktTicketDivRule> result = tktTicketDivRuleReadComponent.listTicketDivRuleByTicketId(
                TEST_TICKET_ID_1, TEST_CORP_CODE);

        // 3. 验证结果
        assertNotNull("结果不应为null", result);
        assertEquals("结果列表大小应该一致", 2, result.size());
        assertEquals("第一个元素的企业编码应该一致", TEST_CORP_CODE, result.get(0).getCorpCode());
        assertEquals("第一个元素的票型ID应该一致", TEST_TICKET_ID_1, result.get(0).getTicketId());
        assertEquals("第一个元素的票型编号应该一致", TEST_TICKET_CODE, result.get(0).getTicketCode());
        assertEquals("第一个元素的景区名称应该一致", TEST_PARK_NAME, result.get(0).getParkName());
        assertEquals("第一个元素的分成方式应该一致", TEST_DIV_METHOD, result.get(0).getDivMethod());
        assertEquals("第一个元素的分成金额应该一致", TEST_DIV_MONEY, result.get(0).getDivMoney());
        assertEquals("第一个元素的启用标志应该一致", TEST_USE_FLAG, result.get(0).getUseFlag());
        assertEquals("第一个元素的创建人应该一致", TEST_CREATE_BY, result.get(0).getCreateBy());
        assertEquals("第一个元素的修改人应该一致", TEST_MODIFY_BY, result.get(0).getModifyBy());

        assertEquals("第二个元素的票型ID应该一致", TEST_TICKET_ID_2, result.get(1).getTicketId());
        assertEquals("第二个元素的分成方式应该一致", "1", result.get(1).getDivMethod());
        assertEquals("第二个元素的分成金额应该一致", new BigDecimal("0.3"), result.get(1).getDivMoney());

        // 4. 验证方法调用
        Mockito.verify(dao).listTicketDivRuleByTicketId(TEST_TICKET_ID_1, TEST_CORP_CODE);
        Mockito.verify(converter).r_ds2ms(divRuleDOList);
    }

    /**
     * 测试根据票型ID查询分成规则 - 无结果场景
     */
    @Test
    public void testListTicketDivRuleByTicketId_WhenNoResult_ShouldReturnEmptyList() {
        // 1. 准备测试数据
        when(dao.listTicketDivRuleByTicketId(TEST_TICKET_ID_1, TEST_CORP_CODE))
                .thenReturn(Collections.emptyList());
        when(converter.r_ds2ms(Collections.emptyList())).thenReturn(Collections.emptyList());

        // 2. 执行测试
        List<TktTicketDivRule> result = tktTicketDivRuleReadComponent.listTicketDivRuleByTicketId(
                TEST_TICKET_ID_1, TEST_CORP_CODE);

        // 3. 验证结果
        assertNotNull("结果不应为null", result);
        assertTrue("结果应该是空列表", result.isEmpty());

        // 4. 验证方法调用
        Mockito.verify(dao).listTicketDivRuleByTicketId(TEST_TICKET_ID_1, TEST_CORP_CODE);
        Mockito.verify(converter).r_ds2ms(Collections.emptyList());
    }

    /**
     * 测试批量查询分成规则 - 正常场景
     */
    @Test
    public void testBatchListTicketDivRuleByTicketIds_ShouldReturnList() {
        // 1. 准备测试数据
        Set<Long> ticketIds = new HashSet<>(Arrays.asList(TEST_TICKET_ID_1, TEST_TICKET_ID_2));
        List<TktTicketDivRuleDO> divRuleDOList = Arrays.asList(tktTicketDivRuleDO1, tktTicketDivRuleDO2);
        List<TktTicketDivRule> expectedDivRuleList = Arrays.asList(tktTicketDivRule1, tktTicketDivRule2);

        when(dao.batchListTicketDivRuleByTicketIds(ticketIds, TEST_CORP_CODE))
                .thenReturn(divRuleDOList);
        when(converter.r_ds2ms(divRuleDOList)).thenReturn(expectedDivRuleList);

        // 2. 执行测试
        List<TktTicketDivRule> result = tktTicketDivRuleReadComponent.batchListTicketDivRuleByTicketIds(
                ticketIds, TEST_CORP_CODE);

        // 3. 验证结果
        assertNotNull("结果不应为null", result);
        assertEquals("结果列表大小应该一致", 2, result.size());
        assertEquals("第一个元素的企业编码应该一致", TEST_CORP_CODE, result.get(0).getCorpCode());
        assertEquals("第一个元素的票型ID应该一致", TEST_TICKET_ID_1, result.get(0).getTicketId());
        assertEquals("第二个元素的票型ID应该一致", TEST_TICKET_ID_2, result.get(1).getTicketId());

        // 4. 验证方法调用
        Mockito.verify(dao).batchListTicketDivRuleByTicketIds(ticketIds, TEST_CORP_CODE);
        Mockito.verify(converter).r_ds2ms(divRuleDOList);
    }

    /**
     * 测试批量查询分成规则 - 空集合场景
     */
    @Test
    public void testBatchListTicketDivRuleByTicketIds_WhenEmptySet_ShouldReturnEmptyList() {
        // 1. 准备测试数据
        Set<Long> emptyTicketIds = new HashSet<>();
        when(dao.batchListTicketDivRuleByTicketIds(emptyTicketIds, TEST_CORP_CODE))
                .thenReturn(Collections.emptyList());
        when(converter.r_ds2ms(Collections.emptyList())).thenReturn(Collections.emptyList());

        // 2. 执行测试
        List<TktTicketDivRule> result = tktTicketDivRuleReadComponent.batchListTicketDivRuleByTicketIds(
                emptyTicketIds, TEST_CORP_CODE);

        // 3. 验证结果
        assertNotNull("结果不应为null", result);
        assertTrue("结果应该是空列表", result.isEmpty());

        // 4. 验证方法调用
        Mockito.verify(dao).batchListTicketDivRuleByTicketIds(emptyTicketIds, TEST_CORP_CODE);
        Mockito.verify(converter).r_ds2ms(Collections.emptyList());
    }

    /**
     * 测试批量查询分成规则 - 无结果场景
     */
    @Test
    public void testBatchListTicketDivRuleByTicketIds_WhenNoResult_ShouldReturnEmptyList() {
        // 1. 准备测试数据
        Set<Long> ticketIds = new HashSet<>(Arrays.asList(TEST_TICKET_ID_1, TEST_TICKET_ID_2));
        when(dao.batchListTicketDivRuleByTicketIds(ticketIds, TEST_CORP_CODE))
                .thenReturn(Collections.emptyList());
        when(converter.r_ds2ms(Collections.emptyList())).thenReturn(Collections.emptyList());

        // 2. 执行测试
        List<TktTicketDivRule> result = tktTicketDivRuleReadComponent.batchListTicketDivRuleByTicketIds(
                ticketIds, TEST_CORP_CODE);

        // 3. 验证结果
        assertNotNull("结果不应为null", result);
        assertTrue("结果应该是空列表", result.isEmpty());

        // 4. 验证方法调用
        Mockito.verify(dao).batchListTicketDivRuleByTicketIds(ticketIds, TEST_CORP_CODE);
        Mockito.verify(converter).r_ds2ms(Collections.emptyList());
    }
} 