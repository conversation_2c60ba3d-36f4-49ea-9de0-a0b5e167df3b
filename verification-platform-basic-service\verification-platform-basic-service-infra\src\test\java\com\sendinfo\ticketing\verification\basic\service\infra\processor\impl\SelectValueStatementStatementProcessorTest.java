/**
 * SELECT类型参数值声明处理器测试
 *
 * <AUTHOR> 2025-08-04
 */
package com.sendinfo.ticketing.verification.basic.service.infra.processor.impl;

import com.sendinfo.ticketing.verification.basic.enums.SysParamDataType;
import com.sendinfo.ticketing.verification.basic.exception.VerificationBizRuntimeException;
import com.sendinfo.ticketing.verification.basic.model.infra.error.InfraErrorDef;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class SelectValueStatementStatementProcessorTest {

    private SelectValueStatementStatementProcessor processor;

    @Before
    public void setUp() {
        processor = new SelectValueStatementStatementProcessor();
    }

    @Test
    public void testGetDataType() {
        assertEquals(SysParamDataType.SELECT, processor.getDataType());
    }

    // ========== validateStatement 测试 ==========

    @Test
    public void testValidateStatement_成功校验() {
        String validStatement = "{\"options\":[" +
                "{\"value\":\"beijing\",\"label\":\"北京\",\"isDefault\":true}," +
                "{\"value\":\"shanghai\",\"label\":\"上海\",\"isDefault\":false}," +
                "{\"value\":\"guangzhou\",\"label\":\"广州\",\"isDefault\":false}" +
                "]}";

        // 不应该抛出异常
        processor.validateStatement(validStatement);
    }

    @Test
    public void testValidateStatement_成功校验_单个选项() {
        String validStatement = "{\"options\":[" +
                "{\"value\":\"default\",\"label\":\"默认选项\",\"isDefault\":true}" +
                "]}";

        // 不应该抛出异常
        processor.validateStatement(validStatement);
    }

    @Test
    public void testValidateStatement_成功校验_多个选项() {
        String validStatement = "{\"options\":[" +
                "{\"value\":\"small\",\"label\":\"小\",\"isDefault\":false}," +
                "{\"value\":\"medium\",\"label\":\"中\",\"isDefault\":true}," +
                "{\"value\":\"large\",\"label\":\"大\",\"isDefault\":false}," +
                "{\"value\":\"xlarge\",\"label\":\"超大\",\"isDefault\":false}" +
                "]}";

        // 不应该抛出异常
        processor.validateStatement(validStatement);
    }

    @Test(expected = VerificationBizRuntimeException.class)
    public void testValidateStatement_参数为null() {
        processor.validateStatement(null);
    }

    @Test(expected = VerificationBizRuntimeException.class)
    public void testValidateStatement_参数为空字符串() {
        processor.validateStatement("");
    }

    @Test(expected = VerificationBizRuntimeException.class)
    public void testValidateStatement_JSON格式错误() {
        String invalidJson = "{\"options\":[{\"value\":\"option1\"}";
        processor.validateStatement(invalidJson);
    }

    @Test(expected = VerificationBizRuntimeException.class)
    public void testValidateStatement_options为空数组() {
        String statement = "{\"options\":[]}";
        processor.validateStatement(statement);
    }

    @Test(expected = VerificationBizRuntimeException.class)
    public void testValidateStatement_没有默认选项() {
        String statement = "{\"options\":[" +
                "{\"value\":\"option1\",\"label\":\"选项1\",\"isDefault\":false}," +
                "{\"value\":\"option2\",\"label\":\"选项2\",\"isDefault\":false}" +
                "]}";
        processor.validateStatement(statement);
    }

    @Test(expected = VerificationBizRuntimeException.class)
    public void testValidateStatement_多个默认选项() {
        String statement = "{\"options\":[" +
                "{\"value\":\"option1\",\"label\":\"选项1\",\"isDefault\":true}," +
                "{\"value\":\"option2\",\"label\":\"选项2\",\"isDefault\":true}" +
                "]}";
        processor.validateStatement(statement);
    }

    @Test(expected = VerificationBizRuntimeException.class)
    public void testValidateStatement_选项value为空() {
        String statement = "{\"options\":[" +
                "{\"value\":\"\",\"label\":\"选项1\",\"isDefault\":true}" +
                "]}";
        processor.validateStatement(statement);
    }

    @Test
    public void testValidateStatement_异常信息验证() {
        try {
            processor.validateStatement(null);
            fail("应该抛出异常");
        } catch (VerificationBizRuntimeException e) {
            assertEquals(InfraErrorDef.SYSPARAM_VALUE_STATEMENT_VALIDATION_ERROR.code(), e.getErrorCode());
            assertTrue(e.getMessage().contains("SELECT类型参数值声明不能为空"));
        }
    }

    // ========== parseAndGetDefaultSysParamValue 测试 ==========

    @Test
    public void testParseAndGetDefaultSysParamValue_成功解析() {
        String statement = "{\"options\":[" +
                "{\"value\":\"beijing\",\"label\":\"北京\",\"isDefault\":true}," +
                "{\"value\":\"shanghai\",\"label\":\"上海\",\"isDefault\":false}" +
                "]}";

        String result = processor.parseAndGetDefaultSysParamValue(statement);
        assertEquals("beijing", result);
    }

    // ========== 综合测试 ==========

    @Test
    public void testValidateAndParse_完整流程() {
        String statement = "{\"options\":[" +
                "{\"value\":\"prod\",\"label\":\"生产环境\",\"isDefault\":false}," +
                "{\"value\":\"test\",\"label\":\"测试环境\",\"isDefault\":true}," +
                "{\"value\":\"dev\",\"label\":\"开发环境\",\"isDefault\":false}" +
                "]}";

        // 先校验
        processor.validateStatement(statement);

        // 再解析
        String result = processor.parseAndGetDefaultSysParamValue(statement);
        assertEquals("test", result);
    }

    // ========== 业务场景测试 ==========

    @Test
    public void testSelect_城市选择() {
        String statement = "{\"options\":[" +
                "{\"value\":\"BJ\",\"label\":\"北京\",\"isDefault\":true}," +
                "{\"value\":\"SH\",\"label\":\"上海\",\"isDefault\":false}," +
                "{\"value\":\"GZ\",\"label\":\"广州\",\"isDefault\":false}," +
                "{\"value\":\"SZ\",\"label\":\"深圳\",\"isDefault\":false}" +
                "]}";

        processor.validateStatement(statement);
        String result = processor.parseAndGetDefaultSysParamValue(statement);
        assertEquals("BJ", result);
    }

    @Test
    public void testSelect_优先级选择() {
        String statement = "{\"options\":[" +
                "{\"value\":\"1\",\"label\":\"高优先级\",\"isDefault\":false}," +
                "{\"value\":\"2\",\"label\":\"中优先级\",\"isDefault\":true}," +
                "{\"value\":\"3\",\"label\":\"低优先级\",\"isDefault\":false}" +
                "]}";

        processor.validateStatement(statement);
        String result = processor.parseAndGetDefaultSysParamValue(statement);
        assertEquals("2", result);
    }

    @Test
    public void testSelect_状态选择() {
        String statement = "{\"options\":[" +
                "{\"value\":\"DRAFT\",\"label\":\"草稿\",\"isDefault\":true}," +
                "{\"value\":\"PUBLISHED\",\"label\":\"已发布\",\"isDefault\":false}," +
                "{\"value\":\"ARCHIVED\",\"label\":\"已归档\",\"isDefault\":false}" +
                "]}";

        processor.validateStatement(statement);
        String result = processor.parseAndGetDefaultSysParamValue(statement);
        assertEquals("DRAFT", result);
    }

    @Test
    public void testSelect_语言选择() {
        String statement = "{\"options\":[" +
                "{\"value\":\"zh_CN\",\"label\":\"简体中文\",\"isDefault\":true}," +
                "{\"value\":\"zh_TW\",\"label\":\"繁体中文\",\"isDefault\":false}," +
                "{\"value\":\"en_US\",\"label\":\"English\",\"isDefault\":false}," +
                "{\"value\":\"ja_JP\",\"label\":\"日本語\",\"isDefault\":false}" +
                "]}";

        processor.validateStatement(statement);
        String result = processor.parseAndGetDefaultSysParamValue(statement);
        assertEquals("zh_CN", result);
    }

    @Test
    public void testSelect_部门选择() {
        String statement = "{\"options\":[" +
                "{\"value\":\"IT\",\"label\":\"信息技术部\",\"isDefault\":false}," +
                "{\"value\":\"HR\",\"label\":\"人力资源部\",\"isDefault\":false}," +
                "{\"value\":\"FINANCE\",\"label\":\"财务部\",\"isDefault\":true}," +
                "{\"value\":\"SALES\",\"label\":\"销售部\",\"isDefault\":false}" +
                "]}";

        processor.validateStatement(statement);
        String result = processor.parseAndGetDefaultSysParamValue(statement);
        assertEquals("FINANCE", result);
    }
}
