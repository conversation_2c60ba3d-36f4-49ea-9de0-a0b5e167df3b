package com.sendinfo.ticketing.verification.basic.api.customer.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * 旅行社信息角色查询请求对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/17 14:55
 */
@Getter
@Setter
@ToString
public class TravelInfoRoleQueryRequest extends TravelInfoQueryRequest implements Serializable {

	/**
	 * 业务ID列表
	 */
	private Set<Long> IdList;
}
