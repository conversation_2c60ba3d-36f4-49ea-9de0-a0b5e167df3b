package com.sendinfo.ticketing.verification.basic.component.goods.mapper;

import com.sendinfo.ticketing.verification.basic.api.goods.request.TktClassInfoQueryRequest;
import com.sendinfo.ticketing.verification.basic.component.goods.param.TktClassInfoQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TktClassInfo;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TktClassInfoQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TktClassInfoDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


@Mapper
public interface TktClassInfoMapper {
    TktClassInfoMapper INSTANCE = Mappers.getMapper(TktClassInfoMapper.class);

    // DO -> Model
    TktClassInfo convert(TktClassInfoDO dataObject);

    // QueryParam -> QueryArg
    TktClassInfoQueryArg convert(TktClassInfoQueryParam param);

    TktClassInfoQueryParam convert(TktClassInfoQueryRequest request);

} 