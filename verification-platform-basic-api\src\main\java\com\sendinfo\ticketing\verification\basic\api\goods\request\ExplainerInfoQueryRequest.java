package com.sendinfo.ticketing.verification.basic.api.goods.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 讲解人信息查询参数
 *
 * <AUTHOR>
 * @Date: 2025/6/23 17:58
 */
@Getter
@Setter
@ToString
public class ExplainerInfoQueryRequest implements Serializable {

    private static final long serialVersionUID = 3424573713708106735L;

    /**
     * 讲解人名称
     */
    private String explainerName;

    /**
     * 讲解人工号
     */
    private String explainerNo;

    /**
     * 职工状态(票务字典)
     */
    private String status;

    /**
     * 启用状态
     */
    private String useFlag;

    /**
     * 企业编码
     */
    @NotNull(message = "企业编码不能为空")
    private String corpCode;
} 