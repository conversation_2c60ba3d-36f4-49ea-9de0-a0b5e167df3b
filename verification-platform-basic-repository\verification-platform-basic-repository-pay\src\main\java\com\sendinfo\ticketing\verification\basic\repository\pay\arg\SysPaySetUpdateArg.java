package com.sendinfo.ticketing.verification.basic.repository.pay.arg;

import com.sendinfo.ticketing.verification.common.repository.arg.AbstractUpdateArg;
import com.sendinfo.ticketing.verification.common.repository.arg.StatusUpdater;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 支付标签更新参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class SysPaySetUpdateArg extends AbstractUpdateArg<Long> {
    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 支付标签图标
     */
    private String paylableIcon;

    /**
     * 启用状态
     */
    private String useFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 售票员默认收款方式
     */
    private String payDefault;

    /**
     * 主体ID
     */
    private Long mainbodyId;

    /**
     * 支付类型（主扫，被扫，现金）
     */
    private String payType;

    /**
     * 自定义支付名称
     */
    private String payName;

    /**
     * 迁移数据
     */
    private String transferParam;

    /**
     * 启用状态更新器
     */
    private StatusUpdater<String> useFlagUpdater;

    /**
     * 售票员默认收款方式更新器
     */
    private StatusUpdater<String> payDefaultUpdater;

    /**
     * 修改人
     */
    private String modifyBy;
} 