package com.sendinfo.ticketing.verification.basic.service.auth.jwt;

import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2025-07-23 14:21:33
 */
public interface JwtTokenGetter {
    /**
     * 支持的jwtIdentity
     *
     * @return id
     */
    JwtIdentity support();

    /**
     * 获取token
     *
     * @param headers 请求
     * @return token
     */
    Optional<String> getJwtToken(Map<String, String> headers);

}
