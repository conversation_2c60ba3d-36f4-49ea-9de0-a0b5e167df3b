package com.sendinfo.ticketing.verification.basic.repository.pay.dao.impl;

import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dao.SysMainbodyDao;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysMainbodyDO;
import com.sendinfo.ticketing.verification.repository.jdbc.Statement;
import com.sendinfo.ticketing.verification.repository.jdbc.mybatis.*;
import lombok.Getter;
import org.apache.ibatis.session.SqlSession;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

/**
 * 经营主体数据访问实现类
 * 用于实现sys_mainbody表的数据访问逻辑
 * 使用MyBatis SqlSession进行数据库操作
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Repository("sysMainbodyDao")
public class SysMainbodyDaoImpl implements SysMainbodyDao,
        SqlSessionGenericDAO<Long, SysMainbodyDO, SysMainbodyUpdateArg, SysMainbodyDeleteArg>,
        SqlSessionCountableDAO<SysMainbodyQueryArg>,
        SqlSessionQueryableDAO<SysMainbodyQueryArg, SysMainbodyDO>,
        SqlSessionBatchInsertDAO<Long, SysMainbodyDO>,
        SqlSessionDAO {

    private final SqlSession sqlSession;

    private final Statement statement;

    public SysMainbodyDaoImpl(@Qualifier("saasPwSqlSessionTemplate") SqlSessionTemplate sqlSessionTemplate) {
        this.sqlSession = sqlSessionTemplate;
        this.statement = Statement.of(SysMainbodyDao.class);
    }
} 