package com.sendinfo.ticketing.verification.basic.service.pay.function;

import com.sendinfo.ticketing.verification.basic.api.pay.request.TktSellerPayAwayEnableQueryRequest;
import com.sendinfo.ticketing.verification.basic.component.pay.TktSellerPayawayReadComponent;
import com.sendinfo.ticketing.verification.basic.model.pay.TktSellerPayaway;
import com.sendinfo.ticketing.verification.basic.service.pay.enums.PayAttachmentKey;
import com.sendinfo.ticketing.verification.common.service.stereotype.Function;
import com.sendinfo.ticketing.verification.flow.Hint;
import com.sendinfo.ticketing.verification.flow.Question;
import lombok.Getter;

import java.util.List;

/**
 * @Auther: chengjie.li
 * @Date: 2025/7/25 14:19
 */
@Getter
@Function("queryTktSellerPayAwayFunction")
public class QueryTktSellerPayAwayFunction {

    private final TktSellerPayawayReadComponent tktSellerPayawayReadComponent;

    public QueryTktSellerPayAwayFunction(TktSellerPayawayReadComponent tktSellerPayawayReadComponent) {
        this.tktSellerPayawayReadComponent = tktSellerPayawayReadComponent;
    }

    public Hint queryEnableSellerPayAwayList(Question<TktSellerPayAwayEnableQueryRequest> question) {
        TktSellerPayAwayEnableQueryRequest request = question.getBody();
        List<TktSellerPayaway> tktSellerPayawayList = tktSellerPayawayReadComponent.queryEnableSellerPayAwayList(request.getAccId(), request.getClientType(), request.getSaleModel(), request.getCorpCode());
        question.setAttachment(PayAttachmentKey.TKT_SELLER_PAY_AWAY_DATA_LIST_ATTACHMENT_KEY, tktSellerPayawayList);
        return Hint.gotoNext();
    }
}
