package com.sendinfo.ticketing.verification.basic.model.pay;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 经营主体模型对象
 * 用于业务逻辑层和数据展示层之间的数据传输
 * 对应数据库表 sys_mainbody
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@ToString(callSuper = true)
public class SysMainbody implements Serializable {

    private static final long serialVersionUID = 948586651692574209L;
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 经营主体名称
     */
    private String mainbodyName;

    /**
     * 经营主体简称
     */
    private String mainbodyShortName;

    /**
     * 主体商户号
     */
    private String merchantNo;

    /**
     * 联系人
     */
    private String linkMan;

    /**
     * 联系电话
     */
    private String linkTel;

    /**
     * 备注
     */
    private String remark;

    /**
     * 经营主体编号
     */
    private String mainbodyNumber;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 修改人
     */
    private String modifyBy;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 删除标志F未删除,T已删除
     */
    private String deleted;
} 