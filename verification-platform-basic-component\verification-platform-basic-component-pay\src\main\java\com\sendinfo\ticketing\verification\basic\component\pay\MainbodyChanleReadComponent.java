package com.sendinfo.ticketing.verification.basic.component.pay;

import com.sendinfo.ticketing.verification.basic.component.pay.param.MainbodyChanleQueryParam;
import com.sendinfo.ticketing.verification.basic.model.pay.MainbodyChanle;
import com.sendinfo.ticketing.verification.common.component.ReadComponent;

/**
 * 商户渠道配置读取组件接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface MainbodyChanleReadComponent extends ReadComponent<Long, MainbodyChanleQueryParam, MainbodyChanle> {

} 