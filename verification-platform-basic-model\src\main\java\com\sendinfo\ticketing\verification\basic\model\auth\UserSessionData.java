package com.sendinfo.ticketing.verification.basic.model.auth;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户会话信息获取
 *
 * <AUTHOR>
 * @since 2025-07-23 12:04:06
 */
@Getter
@Setter
@ToString
public class UserSessionData implements Serializable {
    private static final long serialVersionUID = -4921656153482046516L;
    private String sessionId;
    private Long id;
    private String name;
    private String realName;
    private long time;
    private String ip;
    private boolean isAdmin;
    private String source;
    private String token;
    private String corpCode;
    private String accType;
    private Map<String, String> authOper = new HashMap<>();
    private String accNo;
}
