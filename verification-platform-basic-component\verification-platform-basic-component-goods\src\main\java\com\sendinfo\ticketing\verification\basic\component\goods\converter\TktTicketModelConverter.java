package com.sendinfo.ticketing.verification.basic.component.goods.converter;

import com.sendinfo.ticketing.verification.basic.component.goods.mapper.TktTicketModelMapper;
import com.sendinfo.ticketing.verification.basic.component.goods.param.TktTicketModelQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TktTicketModel;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TktTicketModelQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TktTicketModelDO;
import com.sendinfo.ticketing.verification.common.component.converter.ReadDo2ModelConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadParam2ArgConverter;
import org.springframework.stereotype.Component;

/**
 * @Auther: chengjie.li
 * @Date: 2025/6/10 18:01
 */
@Component("tktTicketModelConverter")
public class TktTicketModelConverter implements
        ReadParam2ArgConverter<TktTicketModelQueryParam, TktTicketModelQueryArg>,
        ReadDo2ModelConverter<TktTicketModelDO, TktTicketModel> {

    @Override
    public TktTicketModel r_d2m(TktTicketModelDO tktTicketModelDO) {
        return TktTicketModelMapper.INSTANCE.convert(tktTicketModelDO);
    }

    @Override
    public TktTicketModelQueryArg r_p2a(TktTicketModelQueryParam queryParam) {
        return TktTicketModelMapper.INSTANCE.convert(queryParam);
    }
}
