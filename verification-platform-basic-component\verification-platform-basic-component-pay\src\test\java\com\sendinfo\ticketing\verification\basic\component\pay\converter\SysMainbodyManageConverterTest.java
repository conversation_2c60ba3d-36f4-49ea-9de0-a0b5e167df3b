package com.sendinfo.ticketing.verification.basic.component.pay.converter;

import com.sendinfo.ticketing.verification.basic.component.pay.param.SysMainbodyManageCreateParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.SysMainbodyManageQueryParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.SysMainbodyManageUpdateParam;
import com.sendinfo.ticketing.verification.basic.enums.CommonUseFlagEnum;
import com.sendinfo.ticketing.verification.basic.model.pay.SysMainbodyManage;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysMainbodyManageDO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;

/**
 * SysMainbodyManageConverter单元测试
 * 测试经营主体支付配置转换器的各种转换功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RunWith(MockitoJUnitRunner.class)
public class SysMainbodyManageConverterTest {

    @InjectMocks
    private SysMainbodyManageConverter sysMainbodyManageConverter;

    // 测试常量
    private static final String TEST_CORP_CODE = "TEST_CORP_001";
    private static final Long TEST_ID = 1L;
    private static final String TEST_MAINBODY_NAME = "测试经营主体";
    private static final String TEST_MAINBODY_SHORT_NAME = "测试主体";
    private static final String TEST_MERCHANT_NO = "123456789";
    private static final String TEST_MERCHANT_SOURCE_NO = "APP001";
    private static final String TEST_PAY_CHANNEL_NAME = "微信支付";
    private static final String TEST_PAY_CHANNEL_CODE = "WECHAT";
    private static final String TEST_PAYCENTER_PUBLIC_KEY = "public_key_123";
    private static final String TEST_MCH_PRIVATE_KEY = "private_key_123";
    private static final String TEST_LINK_MAN = "张三";
    private static final String TEST_LINK_TEL = "13800138000";
    private static final String TEST_LOGO = "logo.png";
    private static final String TEST_REMARK = "测试备注";
    private static final Long TEST_MAINBODY_ID = 100L;
    private static final String TEST_MAINBODY_NUMBER = "MB001";
    private static final String TEST_SEETTLEMENT_MERCHANT_NO = "SETTLE001";
    private static final String TEST_CREATE_BY = "testUser";
    private static final String TEST_MODIFY_BY = "testUser";

    /**
     * 测试目的：验证SysMainbodyManageCreateParam转换为SysMainbodyManageDO的功能
     * 测试步骤：
     * 1. 创建完整的SysMainbodyManageCreateParam对象
     * 2. 调用c_p2d方法进行转换
     * 3. 验证转换结果的正确性
     */
    @Test
    public void testCreateParam2Do_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        SysMainbodyManageCreateParam createParam = createSysMainbodyManageCreateParam();

        // 2. 执行转换
        SysMainbodyManageDO result = sysMainbodyManageConverter.c_p2d(createParam);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", result);
        assertEquals("企业编码应该一致", TEST_CORP_CODE, result.getCorpCode());
        assertEquals("经营主体名称应该一致", TEST_MAINBODY_NAME, result.getMainbodyName());
        assertEquals("经营主体简称应该一致", TEST_MAINBODY_SHORT_NAME, result.getMainbodyShortName());
        assertEquals("主体商户号应该一致", TEST_MERCHANT_NO, result.getMerchantNo());
        assertEquals("支付应用ID应该一致", TEST_MERCHANT_SOURCE_NO, result.getMerchantSourceNo());
        assertEquals("支付渠道名称应该一致", TEST_PAY_CHANNEL_NAME, result.getPayChannelName());
        assertEquals("支付渠道编号应该一致", TEST_PAY_CHANNEL_CODE, result.getPayChannelCode());
        assertEquals("商户公钥应该一致", TEST_PAYCENTER_PUBLIC_KEY, result.getPaycenterPublicKey());
        assertEquals("商户私钥应该一致", TEST_MCH_PRIVATE_KEY, result.getMchPrivateKey());
        assertEquals("启用状态应该一致", CommonUseFlagEnum.ENABLED.getCode(), result.getUseFlag());
        assertEquals("联系人应该一致", TEST_LINK_MAN, result.getLinkMan());
        assertEquals("联系电话应该一致", TEST_LINK_TEL, result.getLinkTel());
        assertEquals("logo应该一致", TEST_LOGO, result.getLogo());
        assertEquals("备注应该一致", TEST_REMARK, result.getRemark());
        assertEquals("经营主体ID应该一致", TEST_MAINBODY_ID, result.getMainbodyId());
        assertEquals("经营主体编号应该一致", TEST_MAINBODY_NUMBER, result.getMainbodyNumber());
        assertEquals("结算主体商户号应该一致", TEST_SEETTLEMENT_MERCHANT_NO, result.getSeettlementMerchantNo());
        assertEquals("创建人应该一致", TEST_CREATE_BY, result.getCreateBy());
    }

    /**
     * 测试目的：验证SysMainbodyManageCreateParam为null时的处理
     */
    @Test
    public void testCreateParam2Do_WithNullInput_ShouldReturnNull() {
        // 1. 执行转换
        SysMainbodyManageDO result = sysMainbodyManageConverter.c_p2d(null);

        // 2. 验证结果
        assertNull("输入为null时应该返回null", result);
    }

    /**
     * 测试目的：验证SysMainbodyManageDO转换为SysMainbodyManage的功能
     * 测试步骤：
     * 1. 创建完整的SysMainbodyManageDO对象
     * 2. 调用r_d2m方法进行转换
     * 3. 验证转换结果的正确性
     */
    @Test
    public void testReadDo2Model_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        SysMainbodyManageDO sysMainbodyManageDO = createSysMainbodyManageDO();

        // 2. 执行转换
        SysMainbodyManage result = sysMainbodyManageConverter.r_d2m(sysMainbodyManageDO);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", result);
        assertEquals("ID应该一致", TEST_ID, result.getId());
        assertEquals("企业编码应该一致", TEST_CORP_CODE, result.getCorpCode());
        assertEquals("经营主体名称应该一致", TEST_MAINBODY_NAME, result.getMainbodyName());
        assertEquals("经营主体简称应该一致", TEST_MAINBODY_SHORT_NAME, result.getMainbodyShortName());
        assertEquals("主体商户号应该一致", TEST_MERCHANT_NO, result.getMerchantNo());
        assertEquals("支付应用ID应该一致", TEST_MERCHANT_SOURCE_NO, result.getMerchantSourceNo());
        assertEquals("支付渠道名称应该一致", TEST_PAY_CHANNEL_NAME, result.getPayChannelName());
        assertEquals("支付渠道编号应该一致", TEST_PAY_CHANNEL_CODE, result.getPayChannelCode());
        assertEquals("商户公钥应该一致", TEST_PAYCENTER_PUBLIC_KEY, result.getPaycenterPublicKey());
        assertEquals("商户私钥应该一致", TEST_MCH_PRIVATE_KEY, result.getMchPrivateKey());
        assertEquals("启用状态应该一致", CommonUseFlagEnum.ENABLED, result.getUseFlag());
        assertEquals("联系人应该一致", TEST_LINK_MAN, result.getLinkMan());
        assertEquals("联系电话应该一致", TEST_LINK_TEL, result.getLinkTel());
        assertEquals("logo应该一致", TEST_LOGO, result.getLogo());
        assertEquals("备注应该一致", TEST_REMARK, result.getRemark());
        assertEquals("经营主体ID应该一致", TEST_MAINBODY_ID, result.getMainbodyId());
        assertEquals("经营主体编号应该一致", TEST_MAINBODY_NUMBER, result.getMainbodyNumber());
        assertEquals("结算主体商户号应该一致", TEST_SEETTLEMENT_MERCHANT_NO, result.getSeettlementMerchantNo());
        assertEquals("创建人应该一致", TEST_CREATE_BY, result.getCreateBy());
        assertEquals("修改人应该一致", TEST_MODIFY_BY, result.getModifyBy());
    }

    /**
     * 测试目的：验证SysMainbodyManageDO为null时的处理
     */
    @Test
    public void testReadDo2Model_WithNullInput_ShouldReturnNull() {
        // 1. 执行转换
        SysMainbodyManage result = sysMainbodyManageConverter.r_d2m(null);

        // 2. 验证结果
        assertNull("输入为null时应该返回null", result);
    }

    /**
     * 测试目的：验证List<SysMainbodyManageDO>转换为List<SysMainbodyManage>的功能
     * 测试步骤：
     * 1. 创建包含多个SysMainbodyManageDO的列表
     * 2. 调用r_ds2ms方法进行转换
     * 3. 验证转换结果的正确性
     */
    @Test
    public void testReadDoList2ModelList_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        List<SysMainbodyManageDO> sysMainbodyManageDOList = Arrays.asList(
                createSysMainbodyManageDO(),
                createSysMainbodyManageDO()
        );

        // 2. 执行转换
        List<SysMainbodyManage> result = sysMainbodyManageConverter.r_ds2ms(sysMainbodyManageDOList);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", result);
        assertEquals("列表大小应该一致", 2, result.size());

        // 验证第一个元素
        SysMainbodyManage firstResult = result.get(0);
        assertEquals("第一个元素的ID应该一致", TEST_ID, firstResult.getId());
        assertEquals("第一个元素的企业编码应该一致", TEST_CORP_CODE, firstResult.getCorpCode());
        assertEquals("第一个元素的经营主体名称应该一致", TEST_MAINBODY_NAME, firstResult.getMainbodyName());
        assertEquals("第一个元素的经营主体简称应该一致", TEST_MAINBODY_SHORT_NAME, firstResult.getMainbodyShortName());
        assertEquals("第一个元素的主体商户号应该一致", TEST_MERCHANT_NO, firstResult.getMerchantNo());
        assertEquals("第一个元素的支付应用ID应该一致", TEST_MERCHANT_SOURCE_NO, firstResult.getMerchantSourceNo());
        assertEquals("第一个元素的支付渠道名称应该一致", TEST_PAY_CHANNEL_NAME, firstResult.getPayChannelName());
        assertEquals("第一个元素的支付渠道编号应该一致", TEST_PAY_CHANNEL_CODE, firstResult.getPayChannelCode());
        assertEquals("第一个元素的商户公钥应该一致", TEST_PAYCENTER_PUBLIC_KEY, firstResult.getPaycenterPublicKey());
        assertEquals("第一个元素的商户私钥应该一致", TEST_MCH_PRIVATE_KEY, firstResult.getMchPrivateKey());
        assertEquals("第一个元素的启用状态应该一致", CommonUseFlagEnum.ENABLED, firstResult.getUseFlag());
        assertEquals("第一个元素的联系人应该一致", TEST_LINK_MAN, firstResult.getLinkMan());
        assertEquals("第一个元素的联系电话应该一致", TEST_LINK_TEL, firstResult.getLinkTel());
        assertEquals("第一个元素的logo应该一致", TEST_LOGO, firstResult.getLogo());
        assertEquals("第一个元素的备注应该一致", TEST_REMARK, firstResult.getRemark());
        assertEquals("第一个元素的经营主体ID应该一致", TEST_MAINBODY_ID, firstResult.getMainbodyId());
        assertEquals("第一个元素的经营主体编号应该一致", TEST_MAINBODY_NUMBER, firstResult.getMainbodyNumber());
        assertEquals("第一个元素的结算主体商户号应该一致", TEST_SEETTLEMENT_MERCHANT_NO, firstResult.getSeettlementMerchantNo());

        // 验证第二个元素
        SysMainbodyManage secondResult = result.get(1);
        assertEquals("第二个元素的ID应该一致", TEST_ID, secondResult.getId());
        assertEquals("第二个元素的企业编码应该一致", TEST_CORP_CODE, secondResult.getCorpCode());
        assertEquals("第二个元素的经营主体名称应该一致", TEST_MAINBODY_NAME, secondResult.getMainbodyName());
        assertEquals("第二个元素的经营主体简称应该一致", TEST_MAINBODY_SHORT_NAME, secondResult.getMainbodyShortName());
        assertEquals("第二个元素的主体商户号应该一致", TEST_MERCHANT_NO, secondResult.getMerchantNo());
        assertEquals("第二个元素的支付应用ID应该一致", TEST_MERCHANT_SOURCE_NO, secondResult.getMerchantSourceNo());
        assertEquals("第二个元素的支付渠道名称应该一致", TEST_PAY_CHANNEL_NAME, secondResult.getPayChannelName());
        assertEquals("第二个元素的支付渠道编号应该一致", TEST_PAY_CHANNEL_CODE, secondResult.getPayChannelCode());
        assertEquals("第二个元素的商户公钥应该一致", TEST_PAYCENTER_PUBLIC_KEY, secondResult.getPaycenterPublicKey());
        assertEquals("第二个元素的商户私钥应该一致", TEST_MCH_PRIVATE_KEY, secondResult.getMchPrivateKey());
        assertEquals("第二个元素的启用状态应该一致", CommonUseFlagEnum.ENABLED, secondResult.getUseFlag());
        assertEquals("第二个元素的联系人应该一致", TEST_LINK_MAN, secondResult.getLinkMan());
        assertEquals("第二个元素的联系电话应该一致", TEST_LINK_TEL, secondResult.getLinkTel());
        assertEquals("第二个元素的logo应该一致", TEST_LOGO, secondResult.getLogo());
        assertEquals("第二个元素的备注应该一致", TEST_REMARK, secondResult.getRemark());
        assertEquals("第二个元素的经营主体ID应该一致", TEST_MAINBODY_ID, secondResult.getMainbodyId());
        assertEquals("第二个元素的经营主体编号应该一致", TEST_MAINBODY_NUMBER, secondResult.getMainbodyNumber());
        assertEquals("第二个元素的结算主体商户号应该一致", TEST_SEETTLEMENT_MERCHANT_NO, secondResult.getSeettlementMerchantNo());
    }

    /**
     * 测试目的：验证List<SysMainbodyManageDO>为null时的处理
     */
    @Test
    public void testReadDoList2ModelList_WithNullInput_ShouldReturnNull() {
        // 1. 执行转换
        List<SysMainbodyManage> result = sysMainbodyManageConverter.r_ds2ms(null);

        // 2. 验证结果
        assertNull("输入为null时应该返回null", result);
    }

    /**
     * 测试目的：验证空列表的处理
     */
    @Test
    public void testReadDoList2ModelList_WithEmptyList_ShouldReturnEmptyList() {
        // 1. 创建空列表
        List<SysMainbodyManageDO> emptyList = Arrays.asList();

        // 2. 执行转换
        List<SysMainbodyManage> result = sysMainbodyManageConverter.r_ds2ms(emptyList);

        // 3. 验证结果
        assertNotNull("结果不应为null", result);
        assertTrue("结果应该是空列表", result.isEmpty());
    }

    /**
     * 测试目的：验证SysMainbodyManageQueryParam转换为SysMainbodyManageQueryArg的功能
     * 测试步骤：
     * 1. 创建完整的SysMainbodyManageQueryParam对象
     * 2. 调用r_p2a方法进行转换
     * 3. 验证转换结果的正确性
     */
    @Test
    public void testReadQueryParam2QueryArg_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        SysMainbodyManageQueryParam queryParam = createSysMainbodyManageQueryParam();

        // 2. 执行转换
        SysMainbodyManageQueryArg result = sysMainbodyManageConverter.r_p2a(queryParam);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", result);
        assertEquals("企业编码应该一致", TEST_CORP_CODE, result.getCorpCode());
        assertEquals("经营主体ID应该一致", TEST_MAINBODY_ID, result.getMainbodyId());
        assertEquals("主体商户号应该一致", TEST_MERCHANT_NO, result.getMerchantNo());
        assertEquals("经营主体名称应该一致", TEST_MAINBODY_NAME, result.getMainbodyName());
        assertEquals("经营主体简称应该一致", TEST_MAINBODY_SHORT_NAME, result.getMainbodyShortName());
        assertEquals("支付渠道名称应该一致", TEST_PAY_CHANNEL_NAME, result.getPayChannelName());
        assertEquals("支付渠道编号应该一致", TEST_PAY_CHANNEL_CODE, result.getPayChannelCode());
        assertEquals("启用状态应该一致", CommonUseFlagEnum.ENABLED.getCode(), result.getUseFlag());
        assertEquals("联系人应该一致", TEST_LINK_MAN, result.getLinkMan());
        assertEquals("联系电话应该一致", TEST_LINK_TEL, result.getLinkTel());
        assertEquals("经营主体编号应该一致", TEST_MAINBODY_NUMBER, result.getMainbodyNumber());
    }

    /**
     * 测试目的：验证SysMainbodyManageQueryParam为null时的处理
     */
    @Test
    public void testReadQueryParam2QueryArg_WithNullInput_ShouldReturnNull() {
        // 1. 执行转换
        SysMainbodyManageQueryArg result = sysMainbodyManageConverter.r_p2a(null);

        // 2. 验证结果
        assertNull("输入为null时应该返回null", result);
    }

    /**
     * 测试目的：验证SysMainbodyManageUpdateParam转换为SysMainbodyManageUpdateArg的功能
     * 测试步骤：
     * 1. 创建完整的SysMainbodyManageUpdateParam对象
     * 2. 调用u_p2a方法进行转换
     * 3. 验证转换结果的正确性
     */
    @Test
    public void testUpdateParam2UpdateArg_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        SysMainbodyManageUpdateParam updateParam = createSysMainbodyManageUpdateParam();
        SysMainbodyManage currentModel = createSysMainbodyManage();

        // 2. 执行转换
        SysMainbodyManageUpdateArg result = sysMainbodyManageConverter.u_p2a(updateParam, currentModel);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", result);
        assertEquals("企业编码应该一致", TEST_CORP_CODE, result.getCorpCode());
        assertEquals("主体商户号应该一致", TEST_MERCHANT_NO, result.getMerchantNo());
        assertEquals("支付应用ID应该一致", TEST_MERCHANT_SOURCE_NO, result.getMerchantSourceNo());
        assertEquals("支付渠道名称应该一致", TEST_PAY_CHANNEL_NAME, result.getPayChannelName());
        assertEquals("支付渠道编号应该一致", TEST_PAY_CHANNEL_CODE, result.getPayChannelCode());
        assertEquals("商户公钥应该一致", TEST_PAYCENTER_PUBLIC_KEY, result.getPaycenterPublicKey());
        assertEquals("商户私钥应该一致", TEST_MCH_PRIVATE_KEY, result.getMchPrivateKey());
        assertEquals("启用状态应该一致", CommonUseFlagEnum.ENABLED.getCode(), result.getUseFlag());
        assertEquals("logo应该一致", TEST_LOGO, result.getLogo());
        assertEquals("备注应该一致", TEST_REMARK, result.getRemark());
        assertEquals("结算主体商户号应该一致", TEST_SEETTLEMENT_MERCHANT_NO, result.getSeettlementMerchantNo());
        assertEquals("修改人应该一致", TEST_MODIFY_BY, result.getModifyBy());
    }

    /**
     * 测试目的：验证SysMainbodyManageUpdateParam为null时的处理
     */
    @Test
    public void testUpdateParam2UpdateArg_WithNullInput_ShouldReturnNull() {
        // 1. 创建当前模型
        SysMainbodyManage currentModel = createSysMainbodyManage();

        // 2. 执行转换
        SysMainbodyManageUpdateArg result = sysMainbodyManageConverter.u_p2a(null, currentModel);

        // 3. 验证结果
        assertNull("输入为null时应该返回null", result);
    }

    // ==================== 测试数据构建方法 ====================

    /**
     * 创建SysMainbodyManageCreateParam测试数据
     */
    private SysMainbodyManageCreateParam createSysMainbodyManageCreateParam() {
        SysMainbodyManageCreateParam createParam = new SysMainbodyManageCreateParam();
        createParam.setCorpCode(TEST_CORP_CODE);
        createParam.setMainbodyName(TEST_MAINBODY_NAME);
        createParam.setMainbodyShortName(TEST_MAINBODY_SHORT_NAME);
        createParam.setMerchantNo(TEST_MERCHANT_NO);
        createParam.setMerchantSourceNo(TEST_MERCHANT_SOURCE_NO);
        createParam.setPayChannelName(TEST_PAY_CHANNEL_NAME);
        createParam.setPayChannelCode(TEST_PAY_CHANNEL_CODE);
        createParam.setPaycenterPublicKey(TEST_PAYCENTER_PUBLIC_KEY);
        createParam.setMchPrivateKey(TEST_MCH_PRIVATE_KEY);
        createParam.setUseFlag(CommonUseFlagEnum.ENABLED);
        createParam.setLinkMan(TEST_LINK_MAN);
        createParam.setLinkTel(TEST_LINK_TEL);
        createParam.setLogo(TEST_LOGO);
        createParam.setRemark(TEST_REMARK);
        createParam.setMainbodyId(TEST_MAINBODY_ID);
        createParam.setMainbodyNumber(TEST_MAINBODY_NUMBER);
        createParam.setSeettlementMerchantNo(TEST_SEETTLEMENT_MERCHANT_NO);
        createParam.setCreateBy(TEST_CREATE_BY);
        createParam.setModifyBy(TEST_MODIFY_BY);
        return createParam;
    }

    /**
     * 创建SysMainbodyManageDO测试数据
     */
    private SysMainbodyManageDO createSysMainbodyManageDO() {
        SysMainbodyManageDO sysMainbodyManageDO = new SysMainbodyManageDO();
        sysMainbodyManageDO.setId(TEST_ID);
        sysMainbodyManageDO.setCorpCode(TEST_CORP_CODE);
        sysMainbodyManageDO.setMainbodyName(TEST_MAINBODY_NAME);
        sysMainbodyManageDO.setMainbodyShortName(TEST_MAINBODY_SHORT_NAME);
        sysMainbodyManageDO.setMerchantNo(TEST_MERCHANT_NO);
        sysMainbodyManageDO.setMerchantSourceNo(TEST_MERCHANT_SOURCE_NO);
        sysMainbodyManageDO.setPayChannelName(TEST_PAY_CHANNEL_NAME);
        sysMainbodyManageDO.setPayChannelCode(TEST_PAY_CHANNEL_CODE);
        sysMainbodyManageDO.setPaycenterPublicKey(TEST_PAYCENTER_PUBLIC_KEY);
        sysMainbodyManageDO.setMchPrivateKey(TEST_MCH_PRIVATE_KEY);
        sysMainbodyManageDO.setUseFlag(CommonUseFlagEnum.ENABLED.getCode());
        sysMainbodyManageDO.setLinkMan(TEST_LINK_MAN);
        sysMainbodyManageDO.setLinkTel(TEST_LINK_TEL);
        sysMainbodyManageDO.setLogo(TEST_LOGO);
        sysMainbodyManageDO.setRemark(TEST_REMARK);
        sysMainbodyManageDO.setMainbodyId(TEST_MAINBODY_ID);
        sysMainbodyManageDO.setMainbodyNumber(TEST_MAINBODY_NUMBER);
        sysMainbodyManageDO.setSeettlementMerchantNo(TEST_SEETTLEMENT_MERCHANT_NO);
        sysMainbodyManageDO.setCreateBy(TEST_CREATE_BY);
        sysMainbodyManageDO.setModifyBy(TEST_MODIFY_BY);
        return sysMainbodyManageDO;
    }

    /**
     * 创建SysMainbodyManageQueryParam测试数据
     */
    private SysMainbodyManageQueryParam createSysMainbodyManageQueryParam() {
        SysMainbodyManageQueryParam queryParam = new SysMainbodyManageQueryParam();
        queryParam.setCorpCode(TEST_CORP_CODE);
        queryParam.setMainbodyId(TEST_MAINBODY_ID);
        queryParam.setMerchantNo(TEST_MERCHANT_NO);
        queryParam.setMainbodyName(TEST_MAINBODY_NAME);
        queryParam.setMainbodyShortName(TEST_MAINBODY_SHORT_NAME);
        queryParam.setPayChannelName(TEST_PAY_CHANNEL_NAME);
        queryParam.setPayChannelCode(TEST_PAY_CHANNEL_CODE);
        queryParam.setUseFlag(CommonUseFlagEnum.ENABLED);
        queryParam.setLinkMan(TEST_LINK_MAN);
        queryParam.setLinkTel(TEST_LINK_TEL);
        queryParam.setMainbodyNumber(TEST_MAINBODY_NUMBER);
        return queryParam;
    }

    /**
     * 创建SysMainbodyManageUpdateParam测试数据
     */
    private SysMainbodyManageUpdateParam createSysMainbodyManageUpdateParam() {
        SysMainbodyManageUpdateParam updateParam = new SysMainbodyManageUpdateParam();
        updateParam.setCorpCode(TEST_CORP_CODE);
        updateParam.setMerchantNo(TEST_MERCHANT_NO);
        updateParam.setMerchantSourceNo(TEST_MERCHANT_SOURCE_NO);
        updateParam.setPayChannelName(TEST_PAY_CHANNEL_NAME);
        updateParam.setPayChannelCode(TEST_PAY_CHANNEL_CODE);
        updateParam.setPaycenterPublicKey(TEST_PAYCENTER_PUBLIC_KEY);
        updateParam.setMchPrivateKey(TEST_MCH_PRIVATE_KEY);
        updateParam.setUseFlag(CommonUseFlagEnum.ENABLED);
        updateParam.setLogo(TEST_LOGO);
        updateParam.setRemark(TEST_REMARK);
        updateParam.setSeettlementMerchantNo(TEST_SEETTLEMENT_MERCHANT_NO);
        updateParam.setModifyBy(TEST_MODIFY_BY);
        return updateParam;
    }

    /**
     * 创建SysMainbodyManage测试数据
     */
    private SysMainbodyManage createSysMainbodyManage() {
        SysMainbodyManage sysMainbodyManage = new SysMainbodyManage();
        sysMainbodyManage.setId(TEST_ID);
        sysMainbodyManage.setCorpCode(TEST_CORP_CODE);
        sysMainbodyManage.setMainbodyName(TEST_MAINBODY_NAME);
        sysMainbodyManage.setMainbodyShortName(TEST_MAINBODY_SHORT_NAME);
        sysMainbodyManage.setMerchantNo(TEST_MERCHANT_NO);
        sysMainbodyManage.setMerchantSourceNo(TEST_MERCHANT_SOURCE_NO);
        sysMainbodyManage.setPayChannelName(TEST_PAY_CHANNEL_NAME);
        sysMainbodyManage.setPayChannelCode(TEST_PAY_CHANNEL_CODE);
        sysMainbodyManage.setPaycenterPublicKey(TEST_PAYCENTER_PUBLIC_KEY);
        sysMainbodyManage.setMchPrivateKey(TEST_MCH_PRIVATE_KEY);
        sysMainbodyManage.setUseFlag(CommonUseFlagEnum.ENABLED);
        sysMainbodyManage.setLinkMan(TEST_LINK_MAN);
        sysMainbodyManage.setLinkTel(TEST_LINK_TEL);
        sysMainbodyManage.setLogo(TEST_LOGO);
        sysMainbodyManage.setRemark(TEST_REMARK);
        sysMainbodyManage.setMainbodyId(TEST_MAINBODY_ID);
        sysMainbodyManage.setMainbodyNumber(TEST_MAINBODY_NUMBER);
        sysMainbodyManage.setSeettlementMerchantNo(TEST_SEETTLEMENT_MERCHANT_NO);
        sysMainbodyManage.setCreateBy(TEST_CREATE_BY);
        sysMainbodyManage.setModifyBy(TEST_MODIFY_BY);
        return sysMainbodyManage;
    }

} 