package com.sendinfo.ticketing.verification.basic.component.goods.param;

import com.sendinfo.ticketing.verification.basic.enums.ExplainerStatusEnum;
import com.sendinfo.ticketing.verification.basic.enums.ExplainerUserFlagEnum;
import com.sendinfo.ticketing.verification.common.component.param.AbstractQueryParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 讲解人查询参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class ExplainerInfoQueryParam extends AbstractQueryParam {

    /** 企业编码 */
    private String corpCode;
    /** 服务类别ID */
    private Long serviceCategoryId;
    /** 业态(系统字典) */
    private String businessType;
    /** 讲解人工号 */
    private String explainerNo;
    /** 讲解人名称 */
    private String explainerName;
    /** 导游证 */
    private String guideCard;
    /** 身份证 */
    private String idCard;
    /** 手机号 */
    private String phone;
    /** 职工状态(票务字典) */
    private ExplainerStatusEnum status;
    /** 禁启用状态 */
    private ExplainerUserFlagEnum useFlag;
} 