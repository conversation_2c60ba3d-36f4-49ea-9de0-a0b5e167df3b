<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property name="APP_NAME" value="sendinfo-paas-cache-quickstart-samples"/>
    <property name="LOG_PATH" value="${user.home}/logs/${APP_NAME}"/>
    <property name="FILE_LOG_PATTERN"
              value="${FILE_LOG_PATTERN:-%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS}} ${LOG_LEVEL_PATTERN:-%5p} ${PID:- } --- [%t] %-40.40logger{39} : %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>

    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <include resource="paas/sendinfo/rds/client/sendinfo-paas-rds-client-log.xml"/>
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
    </root>
    <logger name="org.springframework.test.context.transaction.TransactionContext" additivity="false">
        <level value="WARN"/>
        <appender-ref ref="CONSOLE"/>
    </logger>
</configuration>
