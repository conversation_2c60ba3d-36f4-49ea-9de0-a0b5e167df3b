package com.sendinfo.ticketing.verification.basic.component.infra.impl;

import lombok.Getter;
import org.springframework.stereotype.Component;

import com.sendinfo.ticketing.verification.basic.component.infra.SysParamDefinitionDeleteComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.converter.SysParamDefinitionConverter;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamDefinitionDeleteParam;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamDefinitionDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.SysParamDefinitionDao;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleDelete;

@Component("sysParamDefinitionDeleteComponent")
@Getter
public class SysParamDefinitionDeleteComponentImpl
        implements SysParamDefinitionDeleteComponent,
        DaoBasedSingleDelete<Long, SysParamDefinitionDeleteParam, SysParamDefinitionDeleteArg> {

    private final SysParamDefinitionDao dao;
    private final SysParamDefinitionConverter converter;

    public SysParamDefinitionDeleteComponentImpl(SysParamDefinitionDao dao, SysParamDefinitionConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }
} 