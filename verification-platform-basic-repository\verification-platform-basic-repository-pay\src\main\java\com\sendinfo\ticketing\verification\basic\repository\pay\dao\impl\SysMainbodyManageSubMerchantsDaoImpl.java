package com.sendinfo.ticketing.verification.basic.repository.pay.dao.impl;

import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageSubMerchantsDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageSubMerchantsQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageSubMerchantsUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dao.SysMainbodyManageSubMerchantsDao;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysMainbodyManageSubMerchantsDO;
import com.sendinfo.ticketing.verification.repository.jdbc.Statement;
import com.sendinfo.ticketing.verification.repository.jdbc.mybatis.*;
import lombok.Getter;
import org.apache.ibatis.session.SqlSession;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

/**
 * 经营主体子商户数据访问实现类
 * 实现SysMainbodyManageSubMerchantsDao接口的所有方法
 * 使用MyBatis进行数据访问，支持租户隔离和软删除
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Repository("sysMainbodyManageSubMerchantsDao")
public class SysMainbodyManageSubMerchantsDaoImpl implements SysMainbodyManageSubMerchantsDao,
        SqlSessionGenericDAO<Long, SysMainbodyManageSubMerchantsDO, SysMainbodyManageSubMerchantsUpdateArg, SysMainbodyManageSubMerchantsDeleteArg>,
        SqlSessionCountableDAO<SysMainbodyManageSubMerchantsQueryArg>,
        SqlSessionQueryableDAO<SysMainbodyManageSubMerchantsQueryArg, SysMainbodyManageSubMerchantsDO>,
        SqlSessionBatchInsertDAO<Long, SysMainbodyManageSubMerchantsDO>,
        SqlSessionDAO {

    private final SqlSession sqlSession;

    private final Statement statement;

    public SysMainbodyManageSubMerchantsDaoImpl(@Qualifier("saasPwSqlSessionTemplate") SqlSessionTemplate sqlSessionTemplate) {
        this.sqlSession = sqlSessionTemplate;
        this.statement = Statement.of(SysMainbodyManageSubMerchantsDao.class);
    }
} 