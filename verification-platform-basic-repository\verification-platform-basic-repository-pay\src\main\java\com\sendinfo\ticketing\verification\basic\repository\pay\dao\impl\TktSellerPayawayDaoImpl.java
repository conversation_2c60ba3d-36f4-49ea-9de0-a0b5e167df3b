package com.sendinfo.ticketing.verification.basic.repository.pay.dao.impl;

import com.sendinfo.ticketing.verification.basic.repository.pay.arg.TktSellerPayawayDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.TktSellerPayawayQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.TktSellerPayawayUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dao.TktSellerPayawayDao;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.TktSellerPayawayDO;
import com.sendinfo.ticketing.verification.repository.jdbc.Statement;
import com.sendinfo.ticketing.verification.repository.jdbc.mybatis.*;
import lombok.Getter;
import org.apache.ibatis.session.SqlSession;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 售票员收款方式数据访问实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Repository("tktSellerPayawayDao")
public class TktSellerPayawayDaoImpl implements TktSellerPayawayDao,
        SqlSessionGenericDAO<Long, TktSellerPayawayDO, TktSellerPayawayUpdateArg, TktSellerPayawayDeleteArg>,
        SqlSessionCountableDAO<TktSellerPayawayQueryArg>,
        SqlSessionQueryableDAO<TktSellerPayawayQueryArg, TktSellerPayawayDO>,
        SqlSessionBatchInsertDAO<Long, TktSellerPayawayDO>,
        SqlSessionDAO {

    private final SqlSession sqlSession;

    private final Statement statement;

    public TktSellerPayawayDaoImpl(@Qualifier("saasPwSqlSessionTemplate") SqlSessionTemplate sqlSessionTemplate) {
        this.sqlSession = sqlSessionTemplate;
        this.statement = Statement.of(TktSellerPayawayDao.class);
    }

    @Override
    public List<TktSellerPayawayDO> queryByAccId(Long accId, String corpCode) {
        Map<String, Object> params = new HashMap<>();
        params.put("accId", accId);
        params.put("corpCode", corpCode);
        return sqlSession.selectList(statement.get("queryByAccId"), params);
    }

    @Override
    public List<TktSellerPayawayDO> queryEnableSellerPayAwayList(Long accId, Integer clientType, Integer saleModel, String corpCode) {
        Map<String, Object> params = new HashMap<>();
        params.put("accId", accId);
        params.put("clientType", clientType);
        params.put("saleModel", saleModel);
        params.put("corpCode", corpCode);
        return sqlSession.selectList(statement.get("queryEnableSellerPayAwayList"), params);
    }
} 