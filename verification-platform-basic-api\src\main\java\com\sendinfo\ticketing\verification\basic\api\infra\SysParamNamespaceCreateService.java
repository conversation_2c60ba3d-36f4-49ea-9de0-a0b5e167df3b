package com.sendinfo.ticketing.verification.basic.api.infra;

import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamNamespaceCreateRequest;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

/**
 * 参数命名空间创建服务
 * <AUTHOR> 2025-07-21
 */
public interface SysParamNamespaceCreateService {
    /**
     * 创建参数命名空间
     *
     * @param request 创建请求
     * @return 命名空间ID
     */
    ResultModel<Long> createSysParamNamespace(SysParamNamespaceCreateRequest request);
}