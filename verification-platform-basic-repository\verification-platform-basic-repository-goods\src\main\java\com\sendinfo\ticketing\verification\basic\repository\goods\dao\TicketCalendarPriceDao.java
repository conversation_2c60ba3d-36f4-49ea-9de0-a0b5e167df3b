package com.sendinfo.ticketing.verification.basic.repository.goods.dao;

import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TicketCalendarPriceDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TicketCalendarPriceQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TicketCalendarPriceUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TicketCalendarPriceDO;
import com.sendinfo.ticketing.verification.common.repository.BatchInsertDAO;
import com.sendinfo.ticketing.verification.common.repository.CountableDAO;
import com.sendinfo.ticketing.verification.common.repository.GenericDAO;
import com.sendinfo.ticketing.verification.common.repository.QueryableDAO;

import java.util.List;

public interface TicketCalendarPriceDao extends
        GenericDAO<Long, TicketCalendarPriceDO, TicketCalendarPriceUpdateArg, TicketCalendarPriceDeleteArg>,
        CountableDAO<TicketCalendarPriceQueryArg>,
        QueryableDAO<TicketCalendarPriceQueryArg, TicketCalendarPriceDO>,
        BatchInsertDAO<Long, TicketCalendarPriceDO> {

    /**
     * 根据票型ID查询日历价格列表
     * @param ticketId 票型ID
     * @param corpCode 企业编码
     * @return 日历价格列表
     */
    TicketCalendarPriceDO queryByTicketId(Long ticketId, String corpCode);

    /**
     * 批量获取日历价格
     * @param ticketIds
     * @param corpCode
     * @return
     */
    List<TicketCalendarPriceDO> batchQueryByTicketIds(List<Long> ticketIds, String corpCode);

}
