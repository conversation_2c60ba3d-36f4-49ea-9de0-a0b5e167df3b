package com.sendinfo.ticketing.verification.basic.component.pay;

import com.sendinfo.ticketing.verification.basic.component.pay.param.TktSellerPayawayQueryParam;
import com.sendinfo.ticketing.verification.basic.model.pay.TktSellerPayaway;
import com.sendinfo.ticketing.verification.common.component.ReadComponent;

import java.util.List;

/**
 * 售票员收款方式读取组件接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface TktSellerPayawayReadComponent extends ReadComponent<Long, TktSellerPayawayQueryParam, TktSellerPayaway> {

    /**
     * 根据参数查询售票员启用的支付方式
     *
     * @param accId      售票员ID
     * @param clientType 客户类型
     * @param saleModel  售票模式
     * @param corpCode   企业编码
     * @return 售票员启用的支付方式
     */
    List<TktSellerPayaway> queryEnableSellerPayAwayList(Long accId, Integer clientType, Integer saleModel, String corpCode);
}