package com.sendinfo.ticketing.verification.basic.service.pay.function;

import lombok.Getter;

import com.sendinfo.ticketing.verification.basic.api.pay.request.TktMainbodyBusQueryByTicketIdRequest;
import com.sendinfo.ticketing.verification.basic.component.pay.TktMainbodyBusReadComponent;
import com.sendinfo.ticketing.verification.basic.component.pay.converter.TktMainbodyBusConverter;
import com.sendinfo.ticketing.verification.basic.model.pay.TktMainbodyBus;
import com.sendinfo.ticketing.verification.basic.model.pay.error.PayErrorDef;
import com.sendinfo.ticketing.verification.basic.service.pay.enums.TktMainbodyBusAttachmentKey;
import com.sendinfo.ticketing.verification.common.service.stereotype.Function;
import com.sendinfo.ticketing.verification.flow.Hint;
import com.sendinfo.ticketing.verification.flow.Question;

/**
 * 查询门票经营主体
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/7/28 11:24
 */
@Getter
@Function("queryTktMainbodyBusByTicketIdFunction")
public class QueryTktMainbodyBusByTicketIdFunction {

    private final TktMainbodyBusReadComponent tktMainbodyBusReadComponent;

    private final TktMainbodyBusConverter converter;


    public QueryTktMainbodyBusByTicketIdFunction(TktMainbodyBusReadComponent tktMainbodyBusReadComponent,
                                                 TktMainbodyBusConverter converter) {
        this.tktMainbodyBusReadComponent = tktMainbodyBusReadComponent;
        this.converter = converter;
    }

    /**
     * 查询门票经营主体
     *
     * @param question 查询门票经营主体参数
     * @return Hint
     */
    public Hint queryTktMainbodyBusByTicketId(Question<TktMainbodyBusQueryByTicketIdRequest> question) {
        TktMainbodyBusQueryByTicketIdRequest request = question.getBody();
        TktMainbodyBus tktMainbodyBus = tktMainbodyBusReadComponent.queryTktMainbodyBusByTicketId(request.getTicketId(),
                request.getCorpCode());
        question.setAttachment(TktMainbodyBusAttachmentKey.TKT_MAINBODY_BUS_ATTACHMENT_KEY, tktMainbodyBus);
        return Hint.gotoNext();
    }
}
