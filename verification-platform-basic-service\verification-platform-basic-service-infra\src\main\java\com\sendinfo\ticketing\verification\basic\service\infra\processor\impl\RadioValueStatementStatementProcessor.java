/**
 * RADIO类型参数值声明处理器
 *
 * <AUTHOR> 2025-08-04
 */
package com.sendinfo.ticketing.verification.basic.service.infra.processor.impl;

import com.sendinfo.ticketing.verification.basic.enums.SysParamDataType;
import org.springframework.stereotype.Component;

/**
 * RADIO类型参数值声明处理器
 * 单选框类型，必须且仅有一个default=true的选项
 */
@Component("radioValueStatementProcessor")
public class RadioValueStatementStatementProcessor extends SingleValueStatementProcessor {

    @Override
    public SysParamDataType getDataType() {
        return SysParamDataType.RADIO;
    }

}
