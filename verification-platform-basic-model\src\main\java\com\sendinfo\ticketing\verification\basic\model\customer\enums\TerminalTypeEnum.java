package com.sendinfo.ticketing.verification.basic.model.customer.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 终端类型枚举
 * 定义终端类型
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
public enum TerminalTypeEnum {
    PC(1, "PC端"),
    MOBILE(2, "移动端");

    @JsonValue
    private final Integer code;
    private final String description;

    TerminalTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    // --- 高效反向查找机制 ---
    private static final Map<Integer, TerminalTypeEnum> CODE_MAP = 
        Arrays.stream(values()).collect(Collectors.toMap(TerminalTypeEnum::getCode, Function.identity()));

    /**
     * 根据 code 获取枚举实例
     *
     * @param code 终端类型编码
     * @return 对应的枚举实例，如果找不到则返回 null
     */
    @JsonCreator
    public static TerminalTypeEnum ofCode(Integer code) {
        if (code == null) {
            return null;
        }
        return CODE_MAP.get(code);
    }
} 