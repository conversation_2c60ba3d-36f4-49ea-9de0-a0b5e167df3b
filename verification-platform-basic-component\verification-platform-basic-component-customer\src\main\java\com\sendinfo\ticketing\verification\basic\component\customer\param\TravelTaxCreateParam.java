package com.sendinfo.ticketing.verification.basic.component.customer.param;

import com.sendinfo.ticketing.verification.common.component.param.AbstractCreateParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * travel_tax信息创建参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class TravelTaxCreateParam extends AbstractCreateParam {
    /**
     * 企业编码
     */
    private String corpCode;
    /**
     * 旅行社ID
     */
    private Long travelId;
    /**
     * 实际抬头
     */
    private String taxTitle;
    /**
     * 纳税人识别号
     */
    private String taxNo;
    /**
     * 开户银行
     */
    private String taxBank;
    /**
     * 银行账号
     */
    private String taxAccount;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private String createBy;
} 