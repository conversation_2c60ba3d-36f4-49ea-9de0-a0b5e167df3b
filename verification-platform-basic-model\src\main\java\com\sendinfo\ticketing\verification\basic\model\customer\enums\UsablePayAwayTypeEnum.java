package com.sendinfo.ticketing.verification.basic.model.customer.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 可用支付方式类型
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
public enum UsablePayAwayTypeEnum {
    ALL("0", "全部"),
    PART("1", "部分");

    @JsonValue
    private final String code;
    private final String description;

    UsablePayAwayTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    // --- 高效反向查找机制 ---
    private static final Map<String, UsablePayAwayTypeEnum> CODE_MAP =
            Arrays.stream(values()).collect(Collectors.toMap(UsablePayAwayTypeEnum::getCode, Function.identity()));

    /**
     * 根据 code 获取枚举实例
     *
     * @param code 可用支付方式类型编码
     * @return 对应的枚举实例，如果找不到则返回 null
     */
    @JsonCreator
    public static UsablePayAwayTypeEnum ofCode(String code) {
        if (code == null) {
            return null;
        }
        return CODE_MAP.get(code);
    }
} 