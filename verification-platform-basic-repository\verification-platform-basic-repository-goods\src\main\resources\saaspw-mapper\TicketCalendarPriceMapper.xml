<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sendinfo.ticketing.verification.basic.repository.goods.dao.TicketCalendarPriceDao">

    <resultMap id="BaseResultMap" type="com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TicketCalendarPriceDO">
        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="TICKET_ID" property="ticketId" jdbcType="BIGINT"/>
        <result column="PRICE" property="price" jdbcType="DECIMAL"/>
        <result column="COST_PRICE" property="costPrice" jdbcType="DECIMAL"/>
        <result column="TICKET_TAX" property="ticketTax" jdbcType="DECIMAL"/>
        <result column="PRINT_PRICE" property="printPrice" jdbcType="DECIMAL"/>
        <result column="BEGIN_TIME" property="beginTime" jdbcType="TIMESTAMP"/>
        <result column="END_TIME" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="WEE_DAY" property="weeDay" jdbcType="VARCHAR"/>
        <result column="CREATE_BY" property="createBy" jdbcType="VARCHAR"/>
        <result column="MODIFY_BY" property="modifyBy" jdbcType="VARCHAR"/>
        <result column="CORP_CODE" property="corpCode" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="DELETED" property="deleted" jdbcType="CHAR"/>
        <result column="REMARK" property="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID, TICKET_ID, PRICE, COST_PRICE, TICKET_TAX, PRINT_PRICE, 
        BEGIN_TIME, END_TIME, WEE_DAY, CREATE_BY, MODIFY_BY, 
        CORP_CODE, CREATE_TIME, MODIFY_TIME, DELETED, REMARK
    </sql>

    <!-- Base CRUD operations -->
    <insert id="insert" parameterType="com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TicketCalendarPriceDO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tkt_ticket_calendar_price (
            TICKET_ID, PRICE, COST_PRICE, TICKET_TAX, PRINT_PRICE,
            BEGIN_TIME, END_TIME, WEE_DAY, CREATE_BY, MODIFY_BY,
            CORP_CODE, DELETED, REMARK
        ) VALUES (
            #{ticketId}, #{price}, #{costPrice}, #{ticketTax}, #{printPrice},
            #{beginTime}, #{endTime}, #{weeDay}, #{createBy}, #{modifyBy},
            #{corpCode}, 'F', #{remark}
        )
    </insert>

    <select id="queryById" resultMap="BaseResultMap" parameterType="map">
        SELECT <include refid="Base_Column_List"/>
        FROM tkt_ticket_calendar_price
        WHERE ID = #{id,jdbcType=BIGINT}
        AND DELETED = 'F'
    </select>

    <update id="updateByArg" parameterType="com.sendinfo.ticketing.verification.basic.repository.goods.arg.TicketCalendarPriceUpdateArg">
        UPDATE tkt_ticket_calendar_price
        <set>
            <if test="price != null">PRICE = #{price},</if>
            <if test="costPrice != null">COST_PRICE = #{costPrice},</if>
            <if test="ticketTax != null">TICKET_TAX = #{ticketTax},</if>
            <if test="printPrice != null">PRINT_PRICE = #{printPrice},</if>
            <if test="beginTime != null">BEGIN_TIME = #{beginTime},</if>
            <if test="endTime != null">END_TIME = #{endTime},</if>
            <if test="weeDay != null">WEE_DAY = #{weeDay},</if>
            <if test="modifyBy != null">MODIFY_BY = #{modifyBy},</if>
            <if test="remark != null">REMARK = #{remark},</if>
            MODIFY_TIME = NOW()
        </set>
        WHERE ID = #{id,jdbcType=BIGINT}
        AND CORP_CODE = #{corpCode,jdbcType=VARCHAR}
        AND DELETED = 'F'
    </update>

    <update id="softDeleteByArg" parameterType="com.sendinfo.ticketing.verification.basic.repository.goods.arg.TicketCalendarPriceDeleteArg">
        UPDATE tkt_ticket_calendar_price
        SET DELETED = 'T',
            MODIFY_BY = #{modifyBy,jdbcType=VARCHAR},
            MODIFY_TIME = NOW()
        WHERE ID = #{id,jdbcType=BIGINT}
        AND CORP_CODE = #{corpCode,jdbcType=VARCHAR}
        AND DELETED = 'F'
    </update>

    <!-- Custom queries -->
    <select id="queryByTicketId" resultMap="BaseResultMap" parameterType="map">
        SELECT <include refid="Base_Column_List"/>
        FROM tkt_ticket_calendar_price
        WHERE TICKET_ID = #{ticketId,jdbcType=BIGINT}
        AND CORP_CODE = #{corpCode,jdbcType=VARCHAR}
        AND DELETED = 'F'
        ORDER BY BEGIN_TIME ASC
    </select>

    <select id="batchQueryByTicketIds" resultMap="BaseResultMap" parameterType="map">
        SELECT <include refid="Base_Column_List"/>
        FROM tkt_ticket_calendar_price
        WHERE TICKET_ID IN
            <foreach item="item" collection="ticketIds" separator="," open="(" close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        AND CORP_CODE = #{corpCode,jdbcType=VARCHAR}
        AND DELETED = 'F'
        <![CDATA[
            and END_TIME > now()
            and BEGIN_TIME < now()
        ]]>
    </select>

    <select id="queryByTimeRange" resultMap="BaseResultMap" parameterType="map">
        SELECT <include refid="Base_Column_List"/>
        FROM tkt_ticket_calendar_price
        WHERE CORP_CODE = #{corpCode,jdbcType=VARCHAR}
        AND DELETED = 'F'
        AND BEGIN_TIME &lt;= #{endTime,jdbcType=TIMESTAMP}
        AND END_TIME &gt;= #{beginTime,jdbcType=TIMESTAMP}
        ORDER BY BEGIN_TIME ASC
    </select>

    <!-- Other required methods -->
    <select id="countByArg" resultType="int" parameterType="com.sendinfo.ticketing.verification.basic.repository.goods.arg.TicketCalendarPriceQueryArg">
        SELECT COUNT(*)
        FROM tkt_ticket_calendar_price
        WHERE CORP_CODE = #{corpCode,jdbcType=VARCHAR}
        AND DELETED = 'F'
    </select>

    <select id="queryByArg" resultMap="BaseResultMap" parameterType="com.sendinfo.ticketing.verification.basic.repository.goods.arg.TicketCalendarPriceQueryArg">
        SELECT <include refid="Base_Column_List"/>
        FROM tkt_ticket_calendar_price
        WHERE CORP_CODE = #{corpCode,jdbcType=VARCHAR}
        AND DELETED = 'F'
        <if test="sortItems != null and !sortItems.isEmpty()">
            ORDER BY
            <foreach collection="sortItems" item="sortItem" separator=",">
                ${sortItem.column} ${sortItem.type.name}
            </foreach>
        </if>
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO tkt_ticket_calendar_price (
            TICKET_ID, PRICE, COST_PRICE, TICKET_TAX, PRINT_PRICE,
            BEGIN_TIME, END_TIME, WEE_DAY, CREATE_BY, MODIFY_BY,
            CORP_CODE, DELETED, REMARK
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.ticketId}, #{item.price}, #{item.costPrice}, #{item.ticketTax}, #{item.printPrice},
                #{item.beginTime}, #{item.endTime}, #{item.weeDay}, #{item.createBy}, #{item.modifyBy},
                #{item.corpCode}, 'F', #{item.remark}
            )
        </foreach>
    </insert>
</mapper>
