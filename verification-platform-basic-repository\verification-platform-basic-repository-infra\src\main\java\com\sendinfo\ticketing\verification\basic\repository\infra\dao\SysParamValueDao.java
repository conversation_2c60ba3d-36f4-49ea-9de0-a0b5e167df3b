package com.sendinfo.ticketing.verification.basic.repository.infra.dao;

import com.sendinfo.ticketing.verification.common.repository.*;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.SysParamValueDO;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamValueQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamValueUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamValueDeleteArg;

import java.util.List;

/**
 * 系统参数值数据访问接口
 *
 * <AUTHOR> 2025-05-19 16:34:25
 */
public interface SysParamValueDao
        extends GenericDAO<Long, SysParamValueDO, SysParamValueUpdateArg, SysParamValueDeleteArg>,
        CountableDAO<SysParamValueQueryArg>,
        PageableDAO<SysParamValueQueryArg, SysParamValueDO>,
        BatchInsertDAO<Long, SysParamValueDO> {

    /**
     * 根据参数编码和企业编码查询参数值
     *
     * @param paramCode 参数编码
     * @param corpCode  企业编码
     * @return 系统参数值
     */
    SysParamValueDO queryByParamCodeAndCorpCode(String paramCode, String corpCode);

    /**
     * 根据参数Key列表查询参数值列表
     *
     * @param paramKeyList 参数Key列表
     * @return 系统参数值列表
     */
    List<SysParamValueDO> queryByParamKeyList(List<String> paramKeyList);

    /**
     * 根据参数Key列表和企业编码查询参数值列表
     *
     * @param corpCode     企业编码
     * @param paramKeyList 参数Key列表
     * @return 系统参数值列表
     */
    List<SysParamValueDO> queryByParamKeyList(String corpCode, List<String> paramKeyList);

    /**
     * 根据参数Key和租户企业编码查询参数值
     *
     * @param paramKey       参数Key
     * @param tenantCorpCode 租户企业编码
     * @return 系统参数值
     */
    SysParamValueDO queryByParamKeyAndTenantCorpCode(String paramKey, String tenantCorpCode);
}