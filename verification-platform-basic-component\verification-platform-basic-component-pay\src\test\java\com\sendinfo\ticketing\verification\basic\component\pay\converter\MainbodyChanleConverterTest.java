package com.sendinfo.ticketing.verification.basic.component.pay.converter;

import com.sendinfo.ticketing.verification.basic.component.pay.param.MainbodyChanleCreateParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.MainbodyChanleQueryParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.MainbodyChanleUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.pay.MainbodyChanle;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.MainbodyChanleQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.MainbodyChanleUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.MainbodyChanleDO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;

/**
 * MainbodyChanleConverter单元测试
 * 测试商户渠道配置转换器的各种转换功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RunWith(MockitoJUnitRunner.class)
public class MainbodyChanleConverterTest {

    @InjectMocks
    private MainbodyChanleConverter converter;

    // 测试常量
    private static final Long TEST_ID = 1L;
    private static final String TEST_CORP_CODE = "CORP001";
    private static final Long TEST_PAY_CHANLE_ID = 100L;
    private static final Long TEST_MAINBODY_ID = 200L;
    private static final Long TEST_MERCHANT_ID = 300L;
    private static final String TEST_REMARK = "测试备注";
    private static final String TEST_CREATE_BY = "testUser";
    private static final String TEST_MODIFY_BY = "testUser";

    @Test
    public void testCreateParam2Do_ShouldConvertSuccessfully() {
        MainbodyChanleCreateParam createParam = createCreateParam();
        MainbodyChanleDO result = converter.c_p2d(createParam);
        assertNotNull(result);
        assertEquals(TEST_CORP_CODE, result.getCorpCode());
        assertEquals(TEST_PAY_CHANLE_ID, result.getPayChanleId());
        assertEquals(TEST_MAINBODY_ID, result.getMainbodyId());
        assertEquals(TEST_MERCHANT_ID, result.getMerchantId());
        assertEquals(TEST_REMARK, result.getRemark());
        assertEquals(TEST_CREATE_BY, result.getCreateBy());
    }

    @Test
    public void testCreateParam2Do_WithNullInput_ShouldReturnNull() {
        assertNull(converter.c_p2d(null));
    }

    @Test
    public void testReadDo2Model_ShouldConvertSuccessfully() {
        MainbodyChanleDO dataObject = createDO();
        MainbodyChanle result = converter.r_d2m(dataObject);
        assertNotNull(result);
        assertEquals(TEST_ID, result.getId());
        assertEquals(TEST_CORP_CODE, result.getCorpCode());
        assertEquals(TEST_PAY_CHANLE_ID, result.getPayChanleId());
        assertEquals(TEST_MAINBODY_ID, result.getMainbodyId());
        assertEquals(TEST_MERCHANT_ID, result.getMerchantId());
        assertEquals(TEST_REMARK, result.getRemark());
        assertEquals(TEST_CREATE_BY, result.getCreateBy());
        assertEquals(TEST_MODIFY_BY, result.getModifyBy());
    }

    @Test
    public void testReadDo2Model_WithNullInput_ShouldReturnNull() {
        assertNull(converter.r_d2m(null));
    }

    @Test
    public void testReadDoList2ModelList_ShouldConvertSuccessfully() {
        List<MainbodyChanleDO> doList = Arrays.asList(createDO(), createDO());
        List<MainbodyChanle> result = converter.r_ds2ms(doList);
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(TEST_ID, result.get(0).getId());
        assertEquals(TEST_PAY_CHANLE_ID, result.get(0).getPayChanleId());
        assertEquals(TEST_ID, result.get(1).getId());
        assertEquals(TEST_PAY_CHANLE_ID, result.get(1).getPayChanleId());
    }

    @Test
    public void testReadDoList2ModelList_WithNullInput_ShouldReturnNull() {
        assertNull(converter.r_ds2ms(null));
    }

    @Test
    public void testReadDoList2ModelList_WithEmptyList_ShouldReturnEmptyList() {
        List<MainbodyChanleDO> emptyList = Arrays.asList();
        List<MainbodyChanle> result = converter.r_ds2ms(emptyList);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testReadQueryParam2QueryArg_ShouldConvertSuccessfully() {
        MainbodyChanleQueryParam queryParam = createQueryParam();
        MainbodyChanleQueryArg result = converter.r_p2a(queryParam);
        assertNotNull(result);
        assertEquals(TEST_CORP_CODE, result.getCorpCode());
        assertEquals(TEST_PAY_CHANLE_ID, result.getPayChanleId());
        assertEquals(TEST_MAINBODY_ID, result.getMainbodyId());
        assertEquals(TEST_MERCHANT_ID, result.getMerchantId());
    }

    @Test
    public void testReadQueryParam2QueryArg_WithNullInput_ShouldReturnNull() {
        assertNull(converter.r_p2a(null));
    }

    @Test
    public void testUpdateParam2UpdateArg_ShouldConvertSuccessfully() {
        MainbodyChanleUpdateParam updateParam = createUpdateParam();
        MainbodyChanle currentModel = createModel();
        MainbodyChanleUpdateArg result = converter.u_p2a(updateParam, currentModel);
        assertNotNull(result);
        assertEquals(TEST_CORP_CODE, result.getCorpCode());
        assertEquals(TEST_PAY_CHANLE_ID, result.getPayChanleId());
        assertEquals(TEST_MAINBODY_ID, result.getMainbodyId());
        assertEquals(TEST_MERCHANT_ID, result.getMerchantId());
        assertEquals(TEST_REMARK, result.getRemark());
        assertEquals(TEST_MODIFY_BY, result.getModifyBy());
    }

    @Test
    public void testUpdateParam2UpdateArg_WithNullInput_ShouldReturnNull() {
        MainbodyChanle currentModel = createModel();
        assertNull(converter.u_p2a(null, currentModel));
    }

    // ==================== 测试数据构建方法 ====================
    private MainbodyChanleCreateParam createCreateParam() {
        MainbodyChanleCreateParam param = new MainbodyChanleCreateParam();
        param.setCorpCode(TEST_CORP_CODE);
        param.setPayChanleId(TEST_PAY_CHANLE_ID);
        param.setMainbodyId(TEST_MAINBODY_ID);
        param.setMerchantId(TEST_MERCHANT_ID);
        param.setRemark(TEST_REMARK);
        param.setCreateBy(TEST_CREATE_BY);
        param.setModifyBy(TEST_MODIFY_BY);
        return param;
    }

    private MainbodyChanleDO createDO() {
        MainbodyChanleDO dataObject = new MainbodyChanleDO();
        dataObject.setId(TEST_ID);
        dataObject.setCorpCode(TEST_CORP_CODE);
        dataObject.setPayChanleId(TEST_PAY_CHANLE_ID);
        dataObject.setMainbodyId(TEST_MAINBODY_ID);
        dataObject.setMerchantId(TEST_MERCHANT_ID);
        dataObject.setRemark(TEST_REMARK);
        dataObject.setCreateBy(TEST_CREATE_BY);
        dataObject.setModifyBy(TEST_MODIFY_BY);
        return dataObject;
    }

    private MainbodyChanleQueryParam createQueryParam() {
        MainbodyChanleQueryParam param = new MainbodyChanleQueryParam();
        param.setCorpCode(TEST_CORP_CODE);
        param.setPayChanleId(TEST_PAY_CHANLE_ID);
        param.setMainbodyId(TEST_MAINBODY_ID);
        param.setMerchantId(TEST_MERCHANT_ID);
        param.setRemark(TEST_REMARK);
        return param;
    }

    private MainbodyChanleUpdateParam createUpdateParam() {
        MainbodyChanleUpdateParam param = new MainbodyChanleUpdateParam();
        param.setCorpCode(TEST_CORP_CODE);
        param.setPayChanleId(TEST_PAY_CHANLE_ID);
        param.setMainbodyId(TEST_MAINBODY_ID);
        param.setMerchantId(TEST_MERCHANT_ID);
        param.setRemark(TEST_REMARK);
        param.setModifyBy(TEST_MODIFY_BY);
        return param;
    }

    private MainbodyChanle createModel() {
        MainbodyChanle model = new MainbodyChanle();
        model.setId(TEST_ID);
        model.setCorpCode(TEST_CORP_CODE);
        model.setPayChanleId(TEST_PAY_CHANLE_ID);
        model.setMainbodyId(TEST_MAINBODY_ID);
        model.setMerchantId(TEST_MERCHANT_ID);
        model.setRemark(TEST_REMARK);
        model.setCreateBy(TEST_CREATE_BY);
        model.setModifyBy(TEST_MODIFY_BY);
        return model;
    }
} 