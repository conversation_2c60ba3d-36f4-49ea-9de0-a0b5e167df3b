package com.sendinfo.ticketing.verification.basic.component.goods.param;

import com.sendinfo.ticketing.verification.common.component.param.AbstractDeleteParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 讲解人删除参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class ExplainerInfoDeleteParam extends AbstractDeleteParam {

    /** 主键ID */
    private Long id;
    /** 企业编码 */
    private String corpCode;
    /**
     * 修改人
     */
    private String modifyBy;
} 