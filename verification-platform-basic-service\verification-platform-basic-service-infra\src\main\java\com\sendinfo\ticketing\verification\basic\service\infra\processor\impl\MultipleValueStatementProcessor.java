/**
 * 多值选择类型参数值声明处理器抽象类
 *
 * <AUTHOR> 2025-08-04
 */
package com.sendinfo.ticketing.verification.basic.service.infra.processor.impl;

import com.alibaba.fastjson.JSON;
import com.sendinfo.ticketing.verification.basic.exception.VerificationBizRuntimeException;
import com.sendinfo.ticketing.verification.basic.model.infra.error.InfraErrorDef;
import com.sendinfo.ticketing.verification.basic.service.infra.processor.SysParamValueStatementProcessor;
import com.sendinfo.ticketing.verification.basic.service.infra.processor.model.OptionValueStatement;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 多值选择类型参数值声明处理器抽象类
 * 适用于CHECKBOX、MULTI_SELECT类型，要求至少有一个default=true的选项
 */
@Slf4j
public abstract class MultipleValueStatementProcessor implements SysParamValueStatementProcessor {

    @Override
    public void validateStatement(String paramValueStatement) {
        if (StringUtils.isBlank(paramValueStatement)) {
            throw new VerificationBizRuntimeException(InfraErrorDef.SYSPARAM_VALUE_STATEMENT_VALIDATION_ERROR,
                    getDataType().getCode() + "类型参数值声明不能为空");
        }

        try {
            OptionValueStatement statement = JSON.parseObject(paramValueStatement, OptionValueStatement.class);

            if (statement.getOptions() == null || statement.getOptions().isEmpty()) {
                throw new VerificationBizRuntimeException(InfraErrorDef.SYSPARAM_VALUE_STATEMENT_VALIDATION_ERROR,
                        getDataType().getCode() + "类型参数值声明中options不能为空");
            }

            // 统计default=true的选项数量
            long defaultCount = statement.getOptions().stream()
                    .filter(option -> option.getIsDefault() != null && option.getIsDefault())
                    .count();

            if (defaultCount < 1) {
                throw new VerificationBizRuntimeException(InfraErrorDef.SYSPARAM_VALUE_STATEMENT_VALIDATION_ERROR,
                        getDataType().getCode() + "类型参数值声明至少需要一个默认值选项");
            }

            // 校验每个选项的value不能为空
            boolean hasEmptyValue = statement.getOptions().stream()
                    .anyMatch(option -> StringUtils.isBlank(option.getValue()));

            if (hasEmptyValue) {
                throw new VerificationBizRuntimeException(InfraErrorDef.SYSPARAM_VALUE_STATEMENT_VALIDATION_ERROR,
                        getDataType().getCode() + "类型参数值声明中存在value为空的选项");
            }

        } catch (VerificationBizRuntimeException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            throw new VerificationBizRuntimeException(InfraErrorDef.SYSPARAM_VALUE_STATEMENT_VALIDATION_ERROR,
                    getDataType().getCode() + "类型参数值声明格式错误: " + paramValueStatement, e);
        }
    }

    @Override
    public String parseAndGetDefaultSysParamValue(String paramValueStatement) {
        OptionValueStatement statement = JSON.parseObject(paramValueStatement, OptionValueStatement.class);
        // 查找所有default=true的选项
        List<String> defaultValues = statement.getOptions().stream()
                .filter(option -> option.getIsDefault() != null && option.getIsDefault())
                .map(OptionValueStatement.Option::getValue)
                .collect(Collectors.toList());

        if (defaultValues.isEmpty()) {
            throw new VerificationBizRuntimeException(
                    InfraErrorDef.SYSPARAM_VALUE_STATEMENT_PARSE_DEFAULT_VALUE_ERROR,
                    getDataType().getCode() + "类型参数值声明中未找到默认值选项");
        }

        // 将多个默认值转换为JSON数组字符串
        return JSON.toJSONString(defaultValues);
    }

}
