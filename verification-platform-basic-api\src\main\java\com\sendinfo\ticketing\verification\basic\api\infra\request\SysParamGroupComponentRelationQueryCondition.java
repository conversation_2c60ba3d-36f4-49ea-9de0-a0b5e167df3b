/**
 * System parameter group component relation query condition
 *
 * <AUTHOR> 2025-01-24 15:30:00
 */
package com.sendinfo.ticketing.verification.basic.api.infra.request;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 系统参数分组组件关联查询条件
 */
@Getter
@Setter
@ToString
public class SysParamGroupComponentRelationQueryCondition implements Serializable {
    private static final long serialVersionUID = -7654321098765432109L;

    /**
     * 企业编码
     */
    @NotBlank(message = "企业编码不能为空")
    private String corpCode;

    /**
     * 分组编码
     */
    @Size(max = 64, message = "分组编码长度不能超过64")
    private String groupCode;

    /**
     * 组件编码
     */
    @Size(max = 64, message = "组件编码长度不能超过64")
    private String componentCode;

    /**
     * 页码
     */
    @Min(value = 1, message = "页码不能小于1")
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小不能小于1")
    @Max(value = 100, message = "每页大小不能大于100")
    private Integer pageSize = 10;
}