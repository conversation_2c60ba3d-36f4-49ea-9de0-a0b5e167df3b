package com.sendinfo.ticketing.verification.basic.service.system;

import com.alibaba.fastjson.JSON;
import com.sendinfo.ticketing.verification.basic.api.system.SysRolePlugsReadService;
import com.sendinfo.ticketing.verification.basic.api.system.request.SysRolePlugsQueryRequest;
import com.sendinfo.ticketing.verification.basic.model.system.SysRolePlugs;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.apache.dubbo.config.bootstrap.DubboBootstrap;

import java.util.ArrayList;
import java.util.List;

/**
 * 系统菜单角色权限读取服务客户端测试
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/4 16:20
 */
public class SysRolePlugsReadClientTest {

	public static void main(String[] args) {
		// 配置注册中心
		RegistryConfig registryConfig = new RegistryConfig();
		registryConfig.setAddress("nacos://testnacos.sendinfocs.com:8848?namespace=bb13d33c-bda0-4552-91c3-173151d43186");

		// 配置DubboBootstrap
		DubboBootstrap bootstrap = DubboBootstrap.getInstance()
				.application("sys-role-plugs-read-client")
				.registry(registryConfig);

		// 创建服务引用
		ReferenceConfig<SysRolePlugsReadService> referenceConfig = new ReferenceConfig<>();
		referenceConfig.setInterface(SysRolePlugsReadService.class);
		referenceConfig.setVersion("1.0.0");
		referenceConfig.setTimeout(10000);
		referenceConfig.setCheck(false);

		bootstrap.reference(referenceConfig);
		bootstrap.start();

		// 获取服务代理
		SysRolePlugsReadService sysRolePlugsReadService = referenceConfig.get();

		try {
			// 测试根据主键ID查询系统菜单角色权限
			System.out.println("=== 测试根据主键ID查询系统菜单角色权限 ===");
			ResultModel<SysRolePlugs> singleResult = sysRolePlugsReadService.queryById(1L, "0001");
			if (singleResult != null && singleResult.isSuccess()) {
				System.out.println("单个查询成功：" + JSON.toJSONString(singleResult.getModel()));
			} else {
				System.out.println("单个查询失败：" + (singleResult != null ? singleResult.getErrorMessage() : "返回null"));
			}

			// 测试根据条件查询系统菜单角色权限列表
			System.out.println("\n=== 测试根据条件查询系统菜单角色权限列表 ===");
			SysRolePlugsQueryRequest queryRequest = new SysRolePlugsQueryRequest();
			queryRequest.setSysRoleId(1L);
			queryRequest.setSysFunctionsId(1L);
			queryRequest.setRoleModelType(1);
			queryRequest.setSysOpt("view");
			queryRequest.setSubsystemId(1);
			queryRequest.setRoleLevel(1);
			queryRequest.setSubApplyId(1);
			queryRequest.setDataPermission("test");
			ResultModel<List<SysRolePlugs>> listResult = sysRolePlugsReadService.queryList(queryRequest);
			if (listResult != null && listResult.isSuccess()) {
				System.out.println("列表查询成功，数据条数：" + (listResult.getModel() != null ? listResult.getModel().size() : 0));
				System.out.println("返回数据：" + JSON.toJSONString(listResult.getModel()));
			} else {
				System.out.println("列表查询失败：" + (listResult != null ? listResult.getErrorMessage() : "返回null"));
			}

			// 测试根据系统角色ID集合和权限类型查询系统菜单角色权限列表
			System.out.println("\n=== 测试根据系统角色ID集合和权限类型查询系统菜单角色权限列表 ===");
			List<Long> sysRoleIds = new ArrayList<>();
			sysRoleIds.add(1L);
			sysRoleIds.add(2L);
			sysRoleIds.add(3L);
			Integer roleModelType = 1;
			ResultModel<List<SysRolePlugs>> roleIdsResult = sysRolePlugsReadService.queryListBySysRoleIdRoleModelType(sysRoleIds, roleModelType);
			if (roleIdsResult != null && roleIdsResult.isSuccess()) {
				System.out.println("角色ID集合查询成功，数据条数：" + (roleIdsResult.getModel() != null ? roleIdsResult.getModel().size() : 0));
				System.out.println("返回数据：" + JSON.toJSONString(roleIdsResult.getModel()));
			} else {
				System.out.println("角色ID集合查询失败：" + (roleIdsResult != null ? roleIdsResult.getErrorMessage() : "返回null"));
			}

		} catch (Exception e) {
			System.err.println("调用异常：" + e.getMessage());
			e.printStackTrace();
		} finally {
			// 清理资源
			bootstrap.stop();
		}
	}
} 