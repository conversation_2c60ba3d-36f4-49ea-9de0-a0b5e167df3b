package com.sendinfo.ticketing.verification.basic.api.customer.request;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Auther: chengjie.li
 * @Date: 2025/6/24 14:18
 */
@Getter
@Setter
public class GuideFuzzyQueryRequest implements Serializable {
    private static final long serialVersionUID = -8449232131830662921L;

    /**
     * 企业编码
     */
    @NotNull
    private String corpCode;

    /**
     * 模糊查询名称
     * 导游名称或者导游证号
     */
    private String fuzzyName;

}
