package com.sendinfo.ticketing.verification.basic.component.customer;

import com.sendinfo.ticketing.verification.basic.component.customer.param.SalesmanInfoQueryParam;
import com.sendinfo.ticketing.verification.basic.model.customer.SalesmanInfo;
import com.sendinfo.ticketing.verification.common.component.ReadComponent;

import java.util.List;
import java.util.Set;

/**
 * 业务员信息读取组件接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface SalesmanInfoReadComponent extends ReadComponent<Long, SalesmanInfoQueryParam, SalesmanInfo> {

    /**
     * 根据ID集合批量查询启用的业务员信息
     *
     * @param ids      ID集合
     * @param corpCode 企业编码
     * @return 启用的业务员信息列表
     */
    List<SalesmanInfo> queryEnabledByIds(Set<Long> ids, String corpCode);
} 