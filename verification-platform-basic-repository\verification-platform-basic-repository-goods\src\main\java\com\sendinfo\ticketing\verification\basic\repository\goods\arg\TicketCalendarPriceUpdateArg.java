package com.sendinfo.ticketing.verification.basic.repository.goods.arg;

import com.sendinfo.ticketing.verification.common.repository.arg.AbstractUpdateArg;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@ToString(callSuper = true)
@Builder
public class TicketCalendarPriceUpdateArg extends AbstractUpdateArg<Long> {
    /**
     * 票型ID
     */
    private Long ticketId;

    /**
     * 销售价格
     */
    private BigDecimal price;

    /**
     * 成本价
     */
    private BigDecimal costPrice;

    /**
     * 票型税额
     */
    private BigDecimal ticketTax;

    /**
     * 票面打印价
     */
    private BigDecimal printPrice;

    /**
     * 开始时间
     */
    private Date beginTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 星期几
     */
    private String weeDay;

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 修改人
     */
    private String modifyBy;

    /**
     * 备注
     */
    private String remark;
}
