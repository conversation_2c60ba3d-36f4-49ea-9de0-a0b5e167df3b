package com.sendinfo.ticketing.verification.basic.api.infra;

import com.sendinfo.ticketing.verification.basic.api.infra.request.FeatureComponentCategoryCreateRequest;
import com.sendinfo.ticketing.verification.basic.model.infra.FeatureComponentCategory;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

/**
 * 组件分类创建服务
 * 
 * <AUTHOR> 2025-07-22 11:45:32
 */
public interface FeatureComponentCategoryCreateService {
    
    /**
     * 创建组件分类
     *
     * @param request 创建请求
     * @return 创建分类ID
     */
    ResultModel<Long> createFeatureComponentCategory(FeatureComponentCategoryCreateRequest request);
}
