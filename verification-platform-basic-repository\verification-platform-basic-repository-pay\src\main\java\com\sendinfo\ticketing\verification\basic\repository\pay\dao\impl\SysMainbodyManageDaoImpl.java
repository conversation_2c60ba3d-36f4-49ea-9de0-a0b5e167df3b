package com.sendinfo.ticketing.verification.basic.repository.pay.dao.impl;

import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dao.SysMainbodyManageDao;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysMainbodyManageDO;
import com.sendinfo.ticketing.verification.repository.jdbc.Statement;
import com.sendinfo.ticketing.verification.repository.jdbc.mybatis.*;
import lombok.Getter;
import org.apache.ibatis.session.SqlSession;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.Map;

/**
 * 经营主体支付配置数据访问实现类
 * 用于实现sys_mainbody_manage表的数据访问逻辑
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Repository("sysMainbodyManageDao")
public class SysMainbodyManageDaoImpl implements SysMainbodyManageDao,
        SqlSessionGenericDAO<Long, SysMainbodyManageDO, SysMainbodyManageUpdateArg, SysMainbodyManageDeleteArg>,
        SqlSessionCountableDAO<SysMainbodyManageQueryArg>,
        SqlSessionQueryableDAO<SysMainbodyManageQueryArg, SysMainbodyManageDO>,
        SqlSessionBatchInsertDAO<Long, SysMainbodyManageDO>,
        SqlSessionDAO {

    private final SqlSession sqlSession;

    private final Statement statement;

    public SysMainbodyManageDaoImpl(@Qualifier("saasPwSqlSessionTemplate") SqlSessionTemplate sqlSessionTemplate) {
        this.sqlSession = sqlSessionTemplate;
        this.statement = Statement.of(SysMainbodyManageDao.class);
    }

    @Override
    public SysMainbodyManageDO queryByMainbodyId(Long mainbodyId, String corpCode) {
        Map<String, Object> params = new HashMap<>();
        params.put("mainbodyId", mainbodyId);
        params.put("corpCode", corpCode);
        return sqlSession.selectOne(statement.get("queryByMainbodyId"), params);
    }
} 