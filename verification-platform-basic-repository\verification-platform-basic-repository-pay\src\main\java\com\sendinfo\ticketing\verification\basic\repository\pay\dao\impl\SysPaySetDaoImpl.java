package com.sendinfo.ticketing.verification.basic.repository.pay.dao.impl;

import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysPaySetDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysPaySetQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysPaySetUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dao.SysPaySetDao;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysPaySetDO;
import com.sendinfo.ticketing.verification.repository.jdbc.Statement;
import com.sendinfo.ticketing.verification.repository.jdbc.mybatis.*;
import lombok.Getter;
import org.apache.ibatis.session.SqlSession;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 支付标签数据访问实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Repository("sysPaySetDao")
public class SysPaySetDaoImpl implements SysPaySetDao,
        SqlSessionGenericDAO<Long, SysPaySetDO, SysPaySetUpdateArg, SysPaySetDeleteArg>,
        SqlSessionCountableDAO<SysPaySetQueryArg>,
        SqlSessionQueryableDAO<SysPaySetQueryArg, SysPaySetDO>,
        SqlSessionBatchInsertDAO<Long, SysPaySetDO>,
        SqlSessionDAO {

    private final SqlSession sqlSession;

    private final Statement statement;

    public SysPaySetDaoImpl(@Qualifier("saasPwSqlSessionTemplate") SqlSessionTemplate sqlSessionTemplate) {
        this.sqlSession = sqlSessionTemplate;
        this.statement = Statement.of(SysPaySetDao.class);
    }

    @Override
    public List<SysPaySetDO> queryEnableListByPayTypeAndId(String payType, Set<Long> idSet, String corpCode) {
        Map<String, Object> params = new HashMap<>();
        params.put("payType", payType);
        params.put("idSet", idSet);
        params.put("corpCode", corpCode);
        return sqlSession.selectList(statement.get("queryEnableListByPayTypeAndId"), params);
    }
}