package com.sendinfo.ticketing.verification.basic.component.customer.param;

import com.sendinfo.ticketing.verification.common.component.param.AbstractUpdateParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;

/**
 * 业务员关联客户更新参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString
public class SalesmanRelationTravelUpdateParam extends AbstractUpdateParam<Long> {

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 业务员ID
     */
    private Long salesmanId;

    /**
     * 客户ID
     */
    private Long travelId;

    /**
     * 关联周期-开始时间
     */
    private LocalDate openDate;

    /**
     * 关联周期-截止时间
     */
    private LocalDate endDate;

    /**
     * 是否周期无期限标识 T:是 F:否
     */
    private String foreverFlag;

    /**
     * 修改人
     */
    private String modifyBy;
} 