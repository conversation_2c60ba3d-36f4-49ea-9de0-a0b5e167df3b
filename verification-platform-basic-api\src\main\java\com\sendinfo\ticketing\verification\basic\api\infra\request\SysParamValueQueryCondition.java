package com.sendinfo.ticketing.verification.basic.api.infra.request;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * 系统参数值查询条件
 *
 * <AUTHOR> 2025-05-20 15:40:00
 */
@Getter
@Setter
@ToString
public class SysParamValueQueryCondition implements Serializable {
    private static final long serialVersionUID = -8765432109876543210L;

    /**
     * 参数编码
     */
    private String paramCode;

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 租户企业编码 (0001代表全局默认值)
     */
    private String tenantCorpCode;

    /**
     * 状态：1-启用，0-禁用
     */
    private Integer status;
}