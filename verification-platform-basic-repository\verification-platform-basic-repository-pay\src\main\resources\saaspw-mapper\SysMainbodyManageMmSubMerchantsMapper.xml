<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sendinfo.ticketing.verification.basic.repository.pay.dao.SysMainbodyManageMmSubMerchantsDao">

    <resultMap id="BaseResultMap" type="com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysMainbodyManageMmSubMerchantsDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="CORP_CODE" property="corpCode" jdbcType="VARCHAR"/>
        <result column="MAINBODY_MANAGE_ID" property="mainbodyManageId" jdbcType="BIGINT"/>
        <result column="SUB_MERCHANTS_ID" property="subMerchantsId" jdbcType="BIGINT"/>
        <result column="SUB_MERCHANTS_NAME" property="subMerchantsName" jdbcType="VARCHAR"/>
        <result column="MERCHANT_NO" property="merchantNo" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="CREATE_BY" property="createBy" jdbcType="VARCHAR"/>
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="MODIFY_BY" property="modifyBy" jdbcType="VARCHAR"/>
        <result column="DELETED" property="deleted" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="tableName">sys_mainbody_manage_mm_sub_merchants</sql>

    <sql id="allColumns">
        id, CORP_CODE, MAINBODY_MANAGE_ID, SUB_MERCHANTS_ID, SUB_MERCHANTS_NAME, MERCHANT_NO,
        CREATE_TIME, CREATE_BY, MODIFY_TIME, MODIFY_BY, DELETED
    </sql>

    <sql id="insertColumns">
        CORP_CODE, MAINBODY_MANAGE_ID, SUB_MERCHANTS_ID, SUB_MERCHANTS_NAME, MERCHANT_NO,
        CREATE_BY, MODIFY_BY, DELETED, CREATE_TIME, MODIFY_TIME
    </sql>

    <insert id="insert" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysMainbodyManageMmSubMerchantsDO" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO <include refid="tableName"/> (<include refid="insertColumns"/>)
        VALUES (
            #{corpCode,jdbcType=VARCHAR}, #{mainbodyManageId,jdbcType=BIGINT}, #{subMerchantsId,jdbcType=BIGINT},
            #{subMerchantsName,jdbcType=VARCHAR}, #{merchantNo,jdbcType=VARCHAR},
            #{createBy,jdbcType=VARCHAR}, #{modifyBy,jdbcType=VARCHAR}, #{deleted,jdbcType=VARCHAR},
            NOW(), NOW()
        )
    </insert>

    <select id="queryById" resultMap="BaseResultMap" parameterType="map">
        SELECT <include refid="allColumns"/>
        FROM <include refid="tableName"/>
        WHERE id = #{id,jdbcType=BIGINT}
          AND deleted = 'F'
    </select>

    <update id="updateByArg" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageMmSubMerchantsUpdateArg">
        UPDATE <include refid="tableName"/>
        <set>
            <if test="subMerchantsName != null and subMerchantsName != ''">SUB_MERCHANTS_NAME = #{subMerchantsName,jdbcType=VARCHAR},</if>
            <if test="merchantNo != null and merchantNo != ''">MERCHANT_NO = #{merchantNo,jdbcType=VARCHAR},</if>
            <if test="modifyBy != null and modifyBy != ''">MODIFY_BY = #{modifyBy,jdbcType=VARCHAR},</if>
            MODIFY_TIME = NOW()
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
          AND CORP_CODE = #{corpCode,jdbcType=VARCHAR}
          AND deleted = 'F'
    </update>

    <update id="softDeleteByArg" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageMmSubMerchantsDeleteArg">
        UPDATE <include refid="tableName"/>
        SET deleted = 'T',
            <choose>
                <when test="modifyBy != null and modifyBy != ''">MODIFY_BY = #{modifyBy,jdbcType=VARCHAR},</when>
                <otherwise>MODIFY_BY = 'SYSTEM_SDA',</otherwise>
            </choose>
            MODIFY_TIME = NOW()
        WHERE CORP_CODE = #{corpCode,jdbcType=VARCHAR}
          AND deleted = 'F'
          <if test="id != null">AND id = #{id,jdbcType=BIGINT}</if>
    </update>

    <update id="deleteById" parameterType="java.lang.Long">
        UPDATE <include refid="tableName"/>
        SET deleted = 'T',
            MODIFY_BY = 'SYSTEM_DBI',
            MODIFY_TIME = NOW()
        WHERE id = #{id,jdbcType=BIGINT}
          AND deleted = 'F'
    </update>

    <select id="countByArg" resultType="int" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageMmSubMerchantsQueryArg">
        SELECT COUNT(*)
        FROM <include refid="tableName"/>
        <where>
            CORP_CODE = #{corpCode,jdbcType=VARCHAR}
            AND deleted = 'F'
            <if test="mainbodyManageId != null">AND MAINBODY_MANAGE_ID = #{mainbodyManageId,jdbcType=BIGINT}</if>
            <if test="subMerchantsId != null">AND SUB_MERCHANTS_ID = #{subMerchantsId,jdbcType=BIGINT}</if>
            <if test="subMerchantsName != null and subMerchantsName != ''">AND SUB_MERCHANTS_NAME LIKE CONCAT('%', #{subMerchantsName,jdbcType=VARCHAR}, '%')</if>
            <if test="merchantNo != null and merchantNo != ''">AND MERCHANT_NO = #{merchantNo,jdbcType=VARCHAR}</if>
        </where>
    </select>

    <select id="queryByArg" resultMap="BaseResultMap" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageMmSubMerchantsQueryArg">
        SELECT <include refid="allColumns"/>
        FROM <include refid="tableName"/>
        <where>
            CORP_CODE = #{corpCode,jdbcType=VARCHAR}
            AND deleted = 'F'
            <if test="mainbodyManageId != null">AND MAINBODY_MANAGE_ID = #{mainbodyManageId,jdbcType=BIGINT}</if>
            <if test="subMerchantsId != null">AND SUB_MERCHANTS_ID = #{subMerchantsId,jdbcType=BIGINT}</if>
            <if test="subMerchantsName != null and subMerchantsName != ''">AND SUB_MERCHANTS_NAME LIKE CONCAT('%', #{subMerchantsName,jdbcType=VARCHAR}, '%')</if>
            <if test="merchantNo != null and merchantNo != ''">AND MERCHANT_NO = #{merchantNo,jdbcType=VARCHAR}</if>
        </where>
        ORDER BY id DESC
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO <include refid="tableName"/> (<include refid="insertColumns"/>)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.corpCode,jdbcType=VARCHAR}, #{item.mainbodyManageId,jdbcType=BIGINT}, #{item.subMerchantsId,jdbcType=BIGINT},
                #{item.subMerchantsName,jdbcType=VARCHAR}, #{item.merchantNo,jdbcType=VARCHAR},
                #{item.createBy,jdbcType=VARCHAR}, #{item.modifyBy,jdbcType=VARCHAR}, #{item.deleted,jdbcType=VARCHAR},
                NOW(), NOW()
            )
        </foreach>
    </insert>

    <select id="queryByMainbodyManageId" resultMap="BaseResultMap">
        SELECT <include refid="allColumns"/>
        FROM <include refid="tableName"/>
        WHERE deleted = 'F'
          AND CORP_CODE = #{corpCode,jdbcType=VARCHAR}
          AND MAINBODY_MANAGE_ID = #{mainbodyManageId,jdbcType=BIGINT}
        ORDER BY id DESC
    </select>

    <select id="queryBySubMerchantsId" resultMap="BaseResultMap">
        SELECT <include refid="allColumns"/>
        FROM <include refid="tableName"/>
        WHERE deleted = 'F'
          AND CORP_CODE = #{corpCode,jdbcType=VARCHAR}
          AND SUB_MERCHANTS_ID = #{subMerchantsId,jdbcType=BIGINT}
        ORDER BY id DESC
    </select>

    <select id="queryBySubMerchantsIdSet" resultMap="BaseResultMap">
        SELECT <include refid="allColumns"/>
        FROM <include refid="tableName"/>
        WHERE deleted = 'F'
          AND CORP_CODE = #{corpCode,jdbcType=VARCHAR}
          AND SUB_MERCHANTS_ID IN
          <foreach collection="subMerchantsIdSet" item="subMerchantsId" open="(" separator="," close=")">
              #{subMerchantsId,jdbcType=BIGINT}
          </foreach>
        ORDER BY id DESC
    </select>

</mapper> 