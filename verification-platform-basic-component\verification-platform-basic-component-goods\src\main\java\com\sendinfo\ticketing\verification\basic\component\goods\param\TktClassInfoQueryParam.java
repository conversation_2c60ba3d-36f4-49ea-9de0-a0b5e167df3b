package com.sendinfo.ticketing.verification.basic.component.goods.param;

import com.sendinfo.ticketing.verification.common.component.param.AbstractQueryParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
public class TktClassInfoQueryParam extends AbstractQueryParam {
    /**
     * 种类名称
     */
    private String className;

    /**
     * 企业码
     */
    private String corpCode;

    /**
     * 启用标志
     */
    private String useFlag;

    /**
     * 组织机构id
     */
    private String deptId;

    /**
     * 种类编码
     */
    private String classCode;

    /**
     * 父ID
     */
    private Long parantId;
} 