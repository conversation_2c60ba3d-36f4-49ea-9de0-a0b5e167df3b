package com.sendinfo.ticketing.verification.basic.component.goods.impl;

import com.sendinfo.ticketing.verification.basic.component.goods.TktGroupInfoReadComponent;
import com.sendinfo.ticketing.verification.basic.component.goods.converter.TktGroupInfoConverter;
import com.sendinfo.ticketing.verification.basic.component.goods.param.TktGroupInfoQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TktGroupInfo;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TktGroupInfoQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dao.TktGroupInfoDao;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TktGroupInfoDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedCountRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedListRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleRead;
import lombok.Getter;
import org.springframework.stereotype.Component;

/**
 * 票型分组信息组件实现
 *
 * <AUTHOR>
 */
@Getter
@Component("tktGroupInfoComponent")
public class TktGroupInfoReadComponentImpl implements TktGroupInfoReadComponent,
        DaoBasedSingleRead<Long, TktGroupInfoQueryParam, TktGroupInfo, TktGroupInfoDO>,
        DaoBasedCountRead<Long, TktGroupInfoQueryParam, TktGroupInfo, TktGroupInfoQueryArg>,
        DaoBasedListRead<Long, TktGroupInfoQueryParam, TktGroupInfo, TktGroupInfoDO, TktGroupInfoQueryArg> {

    private final TktGroupInfoDao dao;
    private final TktGroupInfoConverter converter;

    public TktGroupInfoReadComponentImpl(TktGroupInfoDao dao, TktGroupInfoConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }

} 