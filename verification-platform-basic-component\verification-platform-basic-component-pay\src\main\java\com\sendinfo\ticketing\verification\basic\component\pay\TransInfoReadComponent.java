package com.sendinfo.ticketing.verification.basic.component.pay;

import com.sendinfo.ticketing.verification.basic.component.pay.param.TransInfoQueryParam;
import com.sendinfo.ticketing.verification.basic.model.pay.TransInfo;
import com.sendinfo.ticketing.verification.common.component.ReadComponent;

import java.util.List;

/**
 * 交易记录读取组件接口
 *
 * @since 1.0.0
 */
public interface TransInfoReadComponent extends ReadComponent<Long, TransInfoQueryParam, TransInfo> {

	/**
	 * 根据订单编号和公司编号查询交易记录
	 *
	 * @param orderCode 订单编号
	 * @param corpCode  企业编码
	 * @return  List<TransInfo>
	 */
	List<TransInfo> queryTransInfoList(String orderCode, String corpCode);

    /**
     * 根据订单号和交易类型查询
     *
     * @param tradeCode 订单号
     * @param transType 交易类型
     * @param corpCode  企业编码
     * @return 交易记录
     */
    List<TransInfo> queryListByTradeCodeAndTransType(String tradeCode, Integer transType, String corpCode);
}