package com.sendinfo.ticketing.verification.basic.repository.infra.dao;

import com.sendinfo.ticketing.verification.basic.repository.common.config.TicketInfraMybatisConfig;
import com.sendinfo.ticketing.verification.basic.repository.infra.TestMybatisConfig;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamDefinitionDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamDefinitionQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamDefinitionUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.impl.SysParamDefinitionDaoImpl;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.SysParamDefinitionDO;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.SysParamDefinitionCountByComponentDO;
import com.sendinfo.ticketing.verification.common.repository.arg.StatusUpdater;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.AbstractTransactionalJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.List;

@SpringBootTest(classes = SysParamDefinitionDaoTest.Config.class)
@EnableAutoConfiguration
@RunWith(SpringRunner.class)
@TestPropertySource(locations = "classpath:test-application.properties")
public class SysParamDefinitionDaoTest extends AbstractTransactionalJUnit4SpringContextTests {
    @Autowired
    private SysParamDefinitionDao sysParamDefinitionDao;

    private static final String TEST_COMPONENT_CODE = "TEST_COMPONENT_CODE8888";
    private static final String TEST_COMPONENT_CODE_2 = "TEST_COMPONENT_CODE_28888";
    private static final String TEST_COMPONENT_CODE_3 = "TEST_COMPONENT_CODE_3";
    private static final String TEST_CORP_CODE = "TEST_CORP_CODE";

    @Configuration
    @Import({ TicketInfraMybatisConfig.class })
    static class Config {

        @Bean
        SysParamDefinitionDao sysParamDefinitionDao(SqlSessionTemplate sqlSessionTemplate) {
            return new SysParamDefinitionDaoImpl(sqlSessionTemplate);
        }
    }

    @Before
    public void setUp() {
        // 初始化测试数据 - 为第一个组件创建10个参数
        for (int i = 1; i <= 10; i++) {
            SysParamDefinitionDO param = createTestParam(i, TEST_COMPONENT_CODE, TEST_CORP_CODE);
            sysParamDefinitionDao.insert(param);
        }

        // 为第二个组件创建5个参数
        for (int i = 11; i <= 15; i++) {
            SysParamDefinitionDO param = createTestParam(i, TEST_COMPONENT_CODE_2, TEST_CORP_CODE);
            sysParamDefinitionDao.insert(param);
        }

    }

    @Test
    public void testInsert() {
        // Given
        SysParamDefinitionDO param = createTestParam(100);

        // When
        sysParamDefinitionDao.insert(param);

        // Then
        SysParamDefinitionDO found = sysParamDefinitionDao.queryById(param.getId());
        Assert.assertNotNull(found);
        Assert.assertEquals(param.getParamCode(), found.getParamCode());
        Assert.assertEquals(param.getParamName(), found.getParamName());
    }

    @Test
    public void testQueryByParamCode() {
        // Given
        String paramCode = "TEST_CODE_1";
        String corpCode = TEST_CORP_CODE;

        // When
        SysParamDefinitionDO found = sysParamDefinitionDao.queryByParamCode(paramCode, corpCode);

        // Then
        Assert.assertNotNull(found);
        Assert.assertEquals(paramCode, found.getParamCode());
    }

    @Test
    public void testQueryByComponentCode() {
        // Given
        String componentCode = TEST_COMPONENT_CODE;
        String corpCode = TEST_CORP_CODE;

        // When
        List<SysParamDefinitionDO> results = sysParamDefinitionDao.queryByComponentCode(componentCode);

        // Then
        Assert.assertNotNull(results);
        Assert.assertEquals(10, results.size()); // setUp方法中为第一个组件插入了10条数据

        // 验证所有结果都有相同的componentCode
        for (SysParamDefinitionDO param : results) {
            Assert.assertEquals(componentCode, param.getComponentCode());
            Assert.assertEquals(corpCode, param.getCorpCode());
            Assert.assertEquals("F", param.getDeleted()); // 确保没有被软删除
        }

        // 验证结果按ID降序排列
        for (int i = 0; i < results.size() - 1; i++) {
            Assert.assertTrue("Results should be ordered by ID desc",
                    results.get(i).getId() > results.get(i + 1).getId());
        }
    }

    @Test
    public void testQueryByComponentCode_NotFound() {
        // Given
        String componentCode = "NON_EXISTENT_COMPONENT";
        String corpCode = "TEST_CORP_CODE";

        // When
        List<SysParamDefinitionDO> results = sysParamDefinitionDao.queryByComponentCode(componentCode);

        // Then
        Assert.assertNotNull(results);
        Assert.assertTrue("Should return empty list for non-existent component", results.isEmpty());
    }

    @Test
    public void testQueryByComponentCode_WithSoftDeletedRecords() {
        // Given
        String componentCode = TEST_COMPONENT_CODE;
        String corpCode = TEST_CORP_CODE;

        // 软删除一条记录
        SysParamDefinitionDeleteArg deleteArg = new SysParamDefinitionDeleteArg();
        deleteArg.setId(1L);
        deleteArg.setModifyBy("test_user");
        sysParamDefinitionDao.softDeleteByArg(deleteArg);

        // When
        List<SysParamDefinitionDO> results = sysParamDefinitionDao.queryByComponentCode(componentCode);

        // Then
        Assert.assertNotNull(results);

        // 验证返回的记录中不包含被软删除的记录
        for (SysParamDefinitionDO param : results) {
            Assert.assertNotEquals(Long.valueOf(1L), param.getId());
            Assert.assertEquals("F", param.getDeleted());
        }
    }

    @Test
    public void testQueryByComponentCodeList() {
        // Given
        List<String> componentCodeList = Arrays.asList(TEST_COMPONENT_CODE, TEST_COMPONENT_CODE_2);

        // When
        List<SysParamDefinitionDO> results = sysParamDefinitionDao.queryByComponentCodeList(componentCodeList);

        // Then
        Assert.assertNotNull(results);
        Assert.assertEquals(15, results.size()); // 10 + 5 from setUp

        // 验证所有结果都属于指定的组件
        for (SysParamDefinitionDO param : results) {
            Assert.assertTrue("Component code should be in the list",
                    componentCodeList.contains(param.getComponentCode()));
            Assert.assertEquals("F", param.getDeleted()); // 确保没有被软删除
        }

        // 验证结果按组件编码和ID排序
        String previousComponentCode = null;
        Long previousId = null;
        for (SysParamDefinitionDO param : results) {
            if (previousComponentCode != null && previousComponentCode.equals(param.getComponentCode())) {
                Assert.assertTrue("Results should be ordered by component_code, id desc",
                        previousId == null || previousId > param.getId());
            }
            previousComponentCode = param.getComponentCode();
            previousId = param.getId();
        }
    }

    @Test
    public void testQueryByComponentCodeList_EmptyList() {
        // Given
        List<String> emptyList = Arrays.asList();

        // When
        List<SysParamDefinitionDO> results = sysParamDefinitionDao.queryByComponentCodeList(emptyList);

        // Then
        Assert.assertNotNull(results);
        Assert.assertTrue("Should return empty list for empty input", results.isEmpty());
    }

    @Test
    public void testQueryByComponentCodeList_NonExistentComponents() {
        // Given
        List<String> nonExistentList = Arrays.asList("NON_EXISTENT_1", "NON_EXISTENT_2");

        // When
        List<SysParamDefinitionDO> results = sysParamDefinitionDao.queryByComponentCodeList(nonExistentList);

        // Then
        Assert.assertNotNull(results);
        Assert.assertTrue("Should return empty list for non-existent components", results.isEmpty());
    }

    @Test
    public void testUpdateByArg() {
        // Given
        Long id = 1L;
        String validationRules = "{}";

        SysParamDefinitionUpdateArg updateArg = new SysParamDefinitionUpdateArg();
        updateArg.setId(id);
        updateArg.setValidationRules(validationRules);

        StatusUpdater<Integer> statusUpdater = new StatusUpdater<>(1, 0);

        updateArg.setStatusUpdater(statusUpdater);

        // When
        sysParamDefinitionDao.updateByArg(updateArg);

    }

    @Test
    public void testSoftDeleteByArg() {
        // Given
        Long id = 1L;
        SysParamDefinitionDeleteArg deleteArg = new SysParamDefinitionDeleteArg();
        deleteArg.setId(id);
        deleteArg.setModifyBy("test_user");

        // When
        sysParamDefinitionDao.softDeleteByArg(deleteArg);

        // Then
        SysParamDefinitionDO found = sysParamDefinitionDao.queryById(id);
        Assert.assertNull(found);
    }

    @Test
    public void testCountByArg() {
        // Given
        SysParamDefinitionQueryArg queryArg = new SysParamDefinitionQueryArg();
        queryArg.setComponentCode(TEST_COMPONENT_CODE);
        queryArg.setCorpCode(TEST_CORP_CODE);

        // When
        int count = sysParamDefinitionDao.countByArg(queryArg);

        // Then
        Assert.assertEquals(10, count); // 第一个组件有10个参数
    }

    @Test
    public void testQueryByArg() {
        // Given
        SysParamDefinitionQueryArg queryArg = new SysParamDefinitionQueryArg();
        queryArg.setComponentCode(TEST_COMPONENT_CODE);
        queryArg.setStatus(1);
        queryArg.setOffset(1);
        queryArg.setLimit(5);
        // When
        List<SysParamDefinitionDO> results = sysParamDefinitionDao.queryByArg(queryArg);

        // Then
        Assert.assertFalse(results.isEmpty());
        for (SysParamDefinitionDO param : results) {
            Assert.assertEquals(1, param.getStatus().intValue());
        }
    }

    @Test
    public void testBatchInsert() {
        // Given
        List<SysParamDefinitionDO> params = Arrays.asList(
                createTestParam(101),
                createTestParam(102));

        // When
        sysParamDefinitionDao.batchInsert(params);

        // Then
        SysParamDefinitionQueryArg queryArg = new SysParamDefinitionQueryArg();
        queryArg.setComponentCode(TEST_COMPONENT_CODE);
        queryArg.setCorpCode(TEST_CORP_CODE);
        int count = sysParamDefinitionDao.countByArg(queryArg);
        Assert.assertEquals(12, count); // 10 from setUp + 2 new
    }

    private SysParamDefinitionDO createTestParam(long id) {
        return createTestParam(id, TEST_COMPONENT_CODE, TEST_CORP_CODE);
    }

    private SysParamDefinitionDO createTestParam(long id, String componentCode, String corpCode) {
        SysParamDefinitionDO param = new SysParamDefinitionDO();
        param.setId(id);
        param.setCorpCode(corpCode);
        param.setComponentCode(componentCode);
        param.setParamCode(componentCode + "_TEST_CODE_B" + id);
        param.setParamName("Test Parameter " + id);
        param.setDescription("Test Description " + id);
        param.setDataType("STRING");
        param.setValidationRules("{\"maxLength\": 100}");
        param.setParamValueStatement("Test param value statement " + id);
        param.setIsSensitive(0);
        param.setStatus(1);
        param.setCreateBy("test_user");
        param.setModifyBy("test_user");
        param.setDeleted("F");
        return param;
    }

    // ==================== countByComponentCode 测试
    // ====================

    @Test
    public void testCountSysParamDefinitionByComponentCode_MultipleComponents() {
        // Given
        List<String> componentCodes = Arrays.asList(TEST_COMPONENT_CODE, TEST_COMPONENT_CODE_2, TEST_COMPONENT_CODE_3);
        String corpCode = TEST_CORP_CODE;

        // When
        List<SysParamDefinitionCountByComponentDO> results = sysParamDefinitionDao
                .countByComponentCode(componentCodes, corpCode);

        // Then
        Assert.assertNotNull(results);
        Assert.assertEquals(2, results.size());

        // 验证每个组件的参数数量
        for (SysParamDefinitionCountByComponentDO result : results) {
            if (TEST_COMPONENT_CODE.equals(result.getComponentCode())) {
                Assert.assertEquals(Integer.valueOf(10), result.getSysParamCount());
            } else if (TEST_COMPONENT_CODE_2.equals(result.getComponentCode())) {
                Assert.assertEquals(Integer.valueOf(5), result.getSysParamCount());
            } else {
                Assert.fail("Unexpected component code: " + result.getComponentCode());
            }
        }
    }
}