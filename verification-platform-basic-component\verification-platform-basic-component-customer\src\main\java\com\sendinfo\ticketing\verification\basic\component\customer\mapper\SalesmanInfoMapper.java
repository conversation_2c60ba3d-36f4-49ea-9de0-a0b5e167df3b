package com.sendinfo.ticketing.verification.basic.component.customer.mapper;

import com.sendinfo.ticketing.verification.basic.component.customer.param.SalesmanInfoCreateParam;
import com.sendinfo.ticketing.verification.basic.component.customer.param.SalesmanInfoQueryParam;
import com.sendinfo.ticketing.verification.basic.component.customer.param.SalesmanInfoUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.customer.SalesmanInfo;
import com.sendinfo.ticketing.verification.basic.repository.customer.arg.SalesmanInfoQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.customer.arg.SalesmanInfoUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.customer.dataobject.SalesmanInfoDO;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 业务员信息对象转换映射器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper(
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface SalesmanInfoMapper {

    SalesmanInfoMapper INSTANCE = Mappers.getMapper(SalesmanInfoMapper.class);

    // Param -> DO
    SalesmanInfoDO convert(SalesmanInfoCreateParam createParam);

    // Param -> Repository Arg
    SalesmanInfoQueryArg convert(SalesmanInfoQueryParam queryParam);

    // UpdateParam -> Repository Update Arg
    SalesmanInfoUpdateArg convert(SalesmanInfoUpdateParam updateParam);

    // DO -> Model
    SalesmanInfo convert(SalesmanInfoDO dataObject);
} 