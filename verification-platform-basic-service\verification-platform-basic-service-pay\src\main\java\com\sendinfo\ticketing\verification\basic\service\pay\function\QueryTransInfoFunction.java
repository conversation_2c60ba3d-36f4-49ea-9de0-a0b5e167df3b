package com.sendinfo.ticketing.verification.basic.service.pay.function;

import com.sendinfo.ticketing.verification.basic.api.pay.response.TransInfoPayAwayResponse;
import com.sendinfo.ticketing.verification.basic.component.pay.TransInfoReadComponent;
import com.sendinfo.ticketing.verification.basic.component.pay.converter.TransInfoConverter;
import com.sendinfo.ticketing.verification.basic.model.pay.TransInfo;
import com.sendinfo.ticketing.verification.basic.service.common.enums.CommonAttachmentKey;
import com.sendinfo.ticketing.verification.basic.service.pay.enums.PayAttachmentKey;
import com.sendinfo.ticketing.verification.common.service.stereotype.Function;
import com.sendinfo.ticketing.verification.flow.Hint;
import com.sendinfo.ticketing.verification.flow.Question;
import lombok.Getter;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 查询交易记录函数
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/29 10:53
 */
@Getter
@Function("queryTransInfoFunction")
public class QueryTransInfoFunction {

	private final TransInfoReadComponent transInfoReadComponent;
	private final TransInfoConverter transInfoConverter;

	public QueryTransInfoFunction(TransInfoReadComponent transInfoReadComponent,
	                              TransInfoConverter transInfoConverter) {
		this.transInfoReadComponent = transInfoReadComponent;
		this.transInfoConverter = transInfoConverter;
	}

	public Hint queryTransInfoPayAway(Question<Pair<String, String>> question) {
		Pair<String, String> pair = question.getBody();
		String orderCode = pair.getLeft();
		String corpCode = pair.getRight();

		List<TransInfoPayAwayResponse> payAwayResponseList = Collections.EMPTY_LIST;
		List<TransInfo> transInfoList = transInfoReadComponent.queryTransInfoList(orderCode, corpCode);
		if (!CollectionUtils.isEmpty(transInfoList)) {
			payAwayResponseList = transInfoList.stream().map(transInfo ->
					transInfoConverter.r_m2r(transInfo)).collect(Collectors.toList());
		}

		question.setAttachment(PayAttachmentKey.TRANS_INFO_PAY_AWAY_DATA_LIST_ATTACHMENT_KEY, payAwayResponseList);
		return Hint.gotoSuccess();
	}

	public Hint queryListByTradeCodeAndTransType(Question<Pair<String, Integer>> question) {
		String corpCode = question.getAttachment(CommonAttachmentKey.CORP_CODE);
		Pair<String, Integer> pair = question.getBody();
		List<TransInfo> transInfoList = transInfoReadComponent.queryListByTradeCodeAndTransType(pair.getLeft(), pair.getRight(), corpCode);
		question.setAttachment(PayAttachmentKey.TRANS_INFO_DATA_LIST_ATTACHMENT_KEY, transInfoList);
		return Hint.gotoNext();
	}
}
