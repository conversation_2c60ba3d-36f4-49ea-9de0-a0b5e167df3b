package com.sendinfo.ticketing.verification.basic.service.system;

import com.alibaba.fastjson.JSON;
import com.sendinfo.ticketing.verification.basic.api.system.SysSubsystemReadService;
import com.sendinfo.ticketing.verification.basic.api.system.request.SysSubsystemQueryRequest;
import com.sendinfo.ticketing.verification.basic.model.system.SysSubsystem;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.apache.dubbo.config.bootstrap.DubboBootstrap;

import java.util.List;

/**
 * 子系统管理读取服务客户端测试
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/4 16:20
 */
public class SysSubsystemReadClientTest {

	public static void main(String[] args) {
		// 配置注册中心
		RegistryConfig registryConfig = new RegistryConfig();
		registryConfig.setAddress("nacos://testnacos.sendinfocs.com:8848?namespace=bb13d33c-bda0-4552-91c3-173151d43186");

		// 配置DubboBootstrap
		DubboBootstrap bootstrap = DubboBootstrap.getInstance()
				.application("sys-subsystem-read-client")
				.registry(registryConfig);

		// 创建服务引用
		ReferenceConfig<SysSubsystemReadService> referenceConfig = new ReferenceConfig<>();
		referenceConfig.setInterface(SysSubsystemReadService.class);
		referenceConfig.setVersion("1.0.0");
		referenceConfig.setTimeout(10000);
		referenceConfig.setCheck(false);

		bootstrap.reference(referenceConfig);
		bootstrap.start();

		// 获取服务代理
		SysSubsystemReadService sysSubsystemReadService = referenceConfig.get();

		// 构造请求参数
		SysSubsystemQueryRequest queryRequest = new SysSubsystemQueryRequest();
		queryRequest.setCorpCode("TEST_CORP");
		queryRequest.setUserId(1L);
		queryRequest.setUserAccType("1");
		queryRequest.setSubsystemName("测试子系统");
		queryRequest.setSubsystemCode("TEST_SUBSYSTEM");

		// 调用远程服务
		try {
			ResultModel<List<SysSubsystem>> resultModel = sysSubsystemReadService.querySysSubsystems(queryRequest);
			if (resultModel == null) {
				System.out.println("调用失败：返回结果为null");
				return;
			}
			if (resultModel.isSuccess()) {
				System.out.println("调用成功：");
				System.out.println("返回数据：" + JSON.toJSONString(resultModel.getModel()));
				System.out.println("数据条数：" + (resultModel.getModel() != null ? resultModel.getModel().size() : 0));
			} else {
				System.out.println("调用失败：" + resultModel.getErrorMessage());
				System.out.println("错误代码：" + resultModel.getErrorCode());
			}
		} catch (Exception e) {
			System.err.println("调用异常：" + e.getMessage());
			e.printStackTrace();
		} finally {
			// 清理资源
			bootstrap.stop();
		}
	}
} 