package com.sendinfo.ticketing.verification.basic.api.infra;

import java.util.List;

import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamGroupComponentRelationCreateRequest;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

/**
 * <AUTHOR> 2025-07-29 01:42:03
 */
public interface SysParamGroupComponentRelationCreateService {


    /**
     * 创建系统参数组组件关系（组绑定组件）
     *
     * @param request 创建请求
     * @return 参数ID
     */
    ResultModel<List<Long>> createSysParamGroupComponentRelation(SysParamGroupComponentRelationCreateRequest request);
}
