package com.sendinfo.ticketing.verification.basic.service.pay.impl;

import com.sendinfo.ticketing.verification.basic.api.pay.TransInfoReadService;
import com.sendinfo.ticketing.verification.basic.api.pay.response.TransInfoPayAwayResponse;
import com.sendinfo.ticketing.verification.basic.exception.TicketPlatformBizRuntimeException;
import com.sendinfo.ticketing.verification.basic.model.pay.TransInfo;
import com.sendinfo.ticketing.verification.basic.model.pay.error.PayErrorDef;
import com.sendinfo.ticketing.verification.basic.service.common.enums.CommonAttachmentKey;
import com.sendinfo.ticketing.verification.basic.service.pay.enums.PayAttachmentKey;
import com.sendinfo.ticketing.verification.basic.service.pay.function.QueryTransInfoFunction;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;
import com.sendinfo.ticketing.verification.common.api.result.Results;
import com.sendinfo.ticketing.verification.common.service.support.ApiCommonFailedBiActions;
import com.sendinfo.ticketing.verification.flow.FlowAgentBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 交易记录读取服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/29 10:51
 */
@Slf4j
@Service("transInfoReadService")
public class TransInfoReadServiceImpl implements TransInfoReadService {

    private final FlowAgentBuilder flowAgentBuilder;
    private final QueryTransInfoFunction queryTransInfoFunction;

    public TransInfoReadServiceImpl(FlowAgentBuilder flowAgentBuilder,
                                    QueryTransInfoFunction queryTransInfoFunction) {
        this.flowAgentBuilder = flowAgentBuilder;
        this.queryTransInfoFunction = queryTransInfoFunction;
    }

    @Override
    public ResultModel<List<TransInfoPayAwayResponse>> queryTransInfoPayAway(String orderCode, String corpCode) {
        return flowAgentBuilder.<Pair<String, String>, ResultModel<List<TransInfoPayAwayResponse>>>validateThenChooseBuilder()
                .appendLogicAction(queryTransInfoFunction::queryTransInfoPayAway)
                .withSuccessfulAction(q -> {
                    return Results.success(q.getAttachment(PayAttachmentKey.TRANS_INFO_PAY_AWAY_DATA_LIST_ATTACHMENT_KEY));
                })
                .withFailedBiAction(ApiCommonFailedBiActions::reasonMappingToResult)
                .withExceptionBiAction((q, th) -> {
                    log.error("[TransInfoReadServiceImpl] queryTransInfoPayAway error.question is {}, error is {}", q, th);
                    return Results.fail(PayErrorDef.QUERY_TRANS_INFO_ERROR);
                })
                .rethrowException(TicketPlatformBizRuntimeException.class::isInstance)
                .build()
                .prompt(Pair.of(orderCode, corpCode))
                .getResult();
    }

    @Override
    public ResultModel<List<TransInfo>> queryListByTradeCodeAndTransType(String tradeCode, Integer transType, String corpCode) {
        return flowAgentBuilder.<Pair<String, Integer>, ResultModel<List<TransInfo>>>validateThenChooseBuilder()
                .appendLogicAction(queryTransInfoFunction::queryListByTradeCodeAndTransType)
                .withSuccessfulAction(q -> {
                    List<TransInfo> transInfoList = q.getAttachment(PayAttachmentKey.TRANS_INFO_DATA_LIST_ATTACHMENT_KEY);
                    return Results.success(transInfoList);
                })
                .withFailedBiAction(ApiCommonFailedBiActions::reasonMappingToResult)
                .withExceptionBiAction((q, th) -> {
                    log.error("[TransInfoReadServiceImpl] queryListByTradeCodeAndTransType error.question is {}", q, th);
                    return Results.fail(PayErrorDef.QUERY_TRANS_INFO_ERROR);
                })
                .rethrowException(TicketPlatformBizRuntimeException.class::isInstance)
                .build()
                .prompt(Pair.of(tradeCode, transType), q -> q.setAttachment(CommonAttachmentKey.CORP_CODE, corpCode))
                .getResult();
    }
}
