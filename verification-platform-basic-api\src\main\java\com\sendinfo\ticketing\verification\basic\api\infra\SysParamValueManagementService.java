package com.sendinfo.ticketing.verification.basic.api.infra;

import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamValueUpdateRequest;
import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamValueStatusSwitchRequest;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

/**
 *  系统参数值管理服务
 *
 *  <AUTHOR> 2025-05-20 15:40:00
 */
public interface SysParamValueManagementService {
    /**
     * 更新系统参数值
     *
     * @param request 更新请求
     * @return 更新结果
     */
    ResultModel<Boolean> updateSysParamValue(SysParamValueUpdateRequest request);
} 