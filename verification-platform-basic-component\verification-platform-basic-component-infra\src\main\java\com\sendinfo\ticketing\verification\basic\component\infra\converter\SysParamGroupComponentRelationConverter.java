/**
 * System parameter group component relation converter
 *
 * <AUTHOR> 2025-01-24 15:30:00
 */
package com.sendinfo.ticketing.verification.basic.component.infra.converter;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamGroupComponentRelationCreateRequest;
import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamGroupComponentRelationDeleteRequest;
import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamGroupComponentRelationQueryCondition;
import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamGroupComponentRelationUpdateRequest;
import com.sendinfo.ticketing.verification.basic.component.infra.mapper.SysParamGroupComponentRelationMapper;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamGroupComponentRelationCreateParam;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamGroupComponentRelationDeleteParam;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamGroupComponentRelationQueryParam;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamGroupComponentRelationUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.infra.SysParamGroupComponentRelation;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamGroupComponentRelationDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamGroupComponentRelationQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamGroupComponentRelationUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.SysParamGroupComponentRelationDO;
import com.sendinfo.ticketing.verification.common.api.request.PageRequest;
import com.sendinfo.ticketing.verification.common.component.converter.CreateParam2DoConverter;
import com.sendinfo.ticketing.verification.common.component.converter.CreateReq2ParamConverter;
import com.sendinfo.ticketing.verification.common.component.converter.DeleteParam2ArgConverter;
import com.sendinfo.ticketing.verification.common.component.converter.DeleteReq2ParamConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadDo2ModelConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadPageReq2ParamConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadParam2ArgConverter;
import com.sendinfo.ticketing.verification.common.component.converter.UpdateParam2ArgConverter;
import com.sendinfo.ticketing.verification.common.component.converter.UpdateReq2ParamConverter;

/**
 * 参数分组组件关联转换器，负责不同层级对象间的转换，包括请求对象、参数对象、数据对象和模型对象
 *
 * <AUTHOR> 2025-01-24 15:30:00
 */
@Component("sysParamGroupComponentRelationConverter")
public class SysParamGroupComponentRelationConverter
        implements CreateParam2DoConverter<SysParamGroupComponentRelationCreateParam, SysParamGroupComponentRelationDO>,
        ReadParam2ArgConverter<SysParamGroupComponentRelationQueryParam, SysParamGroupComponentRelationQueryArg>,
        ReadDo2ModelConverter<SysParamGroupComponentRelationDO, SysParamGroupComponentRelation>,
        UpdateParam2ArgConverter<SysParamGroupComponentRelationUpdateParam, SysParamGroupComponentRelationUpdateArg, SysParamGroupComponentRelation>,
        DeleteParam2ArgConverter<SysParamGroupComponentRelationDeleteParam, SysParamGroupComponentRelationDeleteArg>,
        DeleteReq2ParamConverter<SysParamGroupComponentRelationDeleteRequest, SysParamGroupComponentRelationDeleteParam>,
        CreateReq2ParamConverter<SysParamGroupComponentRelationCreateRequest, SysParamGroupComponentRelationCreateParam>,
        UpdateReq2ParamConverter<SysParamGroupComponentRelationUpdateRequest, SysParamGroupComponentRelationUpdateParam>,
        ReadPageReq2ParamConverter<SysParamGroupComponentRelationQueryCondition, SysParamGroupComponentRelationQueryParam> {

    @Override
    public SysParamGroupComponentRelationDO c_p2d(SysParamGroupComponentRelationCreateParam createParam) {
        return SysParamGroupComponentRelationMapper.INSTANCE.convert(createParam);
    }

    @Override
    public List<SysParamGroupComponentRelationDO> c_ps2ds(List<SysParamGroupComponentRelationCreateParam> params) {
        return params.stream()
                .map(SysParamGroupComponentRelationMapper.INSTANCE::convert)
                .collect(Collectors.toList());
    }

    @Override
    public SysParamGroupComponentRelationUpdateArg u_p2a(SysParamGroupComponentRelationUpdateParam param, SysParamGroupComponentRelation currentModel) {
        return SysParamGroupComponentRelationMapper.INSTANCE.convert(param);
    }

    @Override
    public SysParamGroupComponentRelationQueryArg r_p2a(SysParamGroupComponentRelationQueryParam param) {
        return SysParamGroupComponentRelationMapper.INSTANCE.convert(param);
    }

    @Override
    public SysParamGroupComponentRelationDeleteArg d_p2a(SysParamGroupComponentRelationDeleteParam param) {
        return SysParamGroupComponentRelationMapper.INSTANCE.convert(param);
    }

    @Override
    public SysParamGroupComponentRelation r_d2m(SysParamGroupComponentRelationDO dataObject) {
        return SysParamGroupComponentRelationMapper.INSTANCE.convert(dataObject);
    }

    @Override
    public List<SysParamGroupComponentRelation> r_ds2ms(List<SysParamGroupComponentRelationDO> dataObjects) {
        return dataObjects.stream()
                .map(this::r_d2m)
                .collect(Collectors.toList());
    }

    @Override
    public SysParamGroupComponentRelationCreateParam c_r2p(SysParamGroupComponentRelationCreateRequest req) {
        return SysParamGroupComponentRelationMapper.INSTANCE.convert(req);
    }

    @Override
    public SysParamGroupComponentRelationUpdateParam u_r2p(SysParamGroupComponentRelationUpdateRequest req) {
        return SysParamGroupComponentRelationMapper.INSTANCE.convert(req);
    }

    @Override
    public SysParamGroupComponentRelationQueryParam r_pr2p(PageRequest<SysParamGroupComponentRelationQueryCondition> pageReq) {
        SysParamGroupComponentRelationQueryParam queryParam = new SysParamGroupComponentRelationQueryParam();
        SysParamGroupComponentRelationQueryCondition condition = pageReq.getCondition();
        if (condition != null) {
            queryParam = SysParamGroupComponentRelationMapper.INSTANCE.convert(condition);
        }
        queryParam.setStartIndex(pageReq.getStartIndex());
        queryParam.setPageSize(pageReq.getPageSize());
        return queryParam;
    }

    @Override
    public SysParamGroupComponentRelationDeleteParam d_r2p(SysParamGroupComponentRelationDeleteRequest req) {
        return SysParamGroupComponentRelationMapper.INSTANCE.convert(req);
    }
}