package com.sendinfo.ticketing.verification.basic.component.goods.impl;

import com.sendinfo.ticketing.verification.basic.component.goods.TktTicketModelReadComponent;
import com.sendinfo.ticketing.verification.basic.component.goods.converter.TktTicketModelConverter;
import com.sendinfo.ticketing.verification.basic.component.goods.param.TktTicketModelQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TktTicketModel;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TktTicketModelQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dao.TktTicketModelDao;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TktTicketModelDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedCountRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedListRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleRead;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * @Auther: chengjie.li
 * @Date: 2025/6/11 11:25
 */
@Getter
@Component("tktTicketModelReadComponent")
public class TktTicketModelReadComponentImpl implements TktTicketModelReadComponent,
        DaoBasedSingleRead<Long, TktTicketModelQueryParam, TktTicketModel, TktTicketModelDO>,
        DaoBasedCountRead<Long, TktTicketModelQueryParam, TktTicketModel, TktTicketModelQueryArg>,
        DaoBasedListRead<Long, TktTicketModelQueryParam, TktTicketModel, TktTicketModelDO, TktTicketModelQueryArg> {
    private final TktTicketModelDao dao;
    private final TktTicketModelConverter converter;

    public TktTicketModelReadComponentImpl(TktTicketModelDao dao, TktTicketModelConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }

    @Override
    public TktTicketModel queryTicketModelByCode(String ticketCode, String corpCode) {
        return converter.r_d2m(dao.queryTicketModelByCode(ticketCode, corpCode));
    }

    @Override
    public List<TktTicketModel> batchQueryTicketModelByCodes(Set<String> ticketCodes, String corpCode) {
        return converter.r_ds2ms(dao.batchQueryTicketModelByCodes(ticketCodes, corpCode));
    }
}
