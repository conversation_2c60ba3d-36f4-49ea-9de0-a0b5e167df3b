package com.sendinfo.ticketing.verification.basic.api.system.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * 系统菜单角色权限查询请求参数
 * 对应表：sys_roleplugs
 * 字段、注释、注解、命名、风格严格对齐 system/api 层
 * <AUTHOR>
 */
@Getter
@Setter
@ToString(callSuper = true)
public class SysRolePlugsQueryRequest implements Serializable {
    /**
     * 系统角色ID
     */
    private Long sysRoleId;

    /**
     * 系统功能ID
     */
    private Long sysFunctionsId;

    /**
     * 权限类型 1:系统菜单 2：客户端菜单 3:审核权限 4：数据权限 5：售票权限
     */
    private Integer roleModelType;

    /**
     * 操作权限 save,update,delete,view,list,grid
     */
    private String sysOpt;

    /**
     * 子系统ID
     */
    private Integer subsystemId;

    /**
     * 权限级别:1.系统级权限 2.应用及权限
     */
    private Integer roleLevel;

    /**
     * 应用id
     */
    private Integer subApplyId;

    /**
     * 数据权限
     */
    private String dataPermission;

} 