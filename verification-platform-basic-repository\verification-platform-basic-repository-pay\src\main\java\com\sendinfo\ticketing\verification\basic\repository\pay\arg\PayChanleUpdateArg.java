package com.sendinfo.ticketing.verification.basic.repository.pay.arg;

import com.sendinfo.ticketing.verification.common.repository.arg.AbstractUpdateArg;
import com.sendinfo.ticketing.verification.common.repository.arg.StatusUpdater;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 支付渠道更新参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class PayChanleUpdateArg extends AbstractUpdateArg<Long> {

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 渠道名称
     */
    private String chanleName;

    /**
     * 渠道编码
     */
    private String chanleCode;

    /**
     * 支付产品码
     */
    private String payProductCode;

    /**
     * 备注
     */
    private String remark;

    /**
     * 启用标识
     */
    private String useFlag;

    /**
     * 启用标识更新器
     */
    private StatusUpdater<String> useFlagUpdater;

    /**
     * 修改人
     */
    private String modifyBy;
} 