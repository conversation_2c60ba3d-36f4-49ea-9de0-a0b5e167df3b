/**
 *  企业编码工具类，处理企业编码的层级解析
 *
 *  <AUTHOR> 2025-07-29 16:30:00
 */
package com.sendinfo.ticketing.verification.basic.common.util;

import com.sendinfo.ticketing.verification.basic.model.corp.CorpInfo;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 企业编码工具类
 * 
 * 用于处理企业编码的层级解析，支持多级企业编码的父级查找
 * 例如："0001/324123/12312" 可以解析出父级编码 ["0001/324123", "0001"]
 * 
 * <AUTHOR> 2025-07-29 16:30:00
 */
public class CorpCodeUtil {

    /**
     * 企业编码分隔符
     */
    private static final String CORP_CODE_SEPARATOR = "/";

    /**
     * 私有构造函数，防止实例化
     */
    private CorpCodeUtil() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }

    /**
     * 解析企业编码，获取所有父级企业编码
     * 
     * @param corpCode 企业编码，例如："0001/324123/12312"
     * @return 父级企业编码列表，按层级从近到远排序，例如：["0001/324123", "0001"]
     */
    public static List<String> parseParentCorpCodes(String corpCode) {
        if (corpCode == null || corpCode.trim().isEmpty()) {
            return Collections.emptyList();
        }

        String trimmedCorpCode = corpCode.trim();

        // 如果不包含分隔符，说明是顶级企业编码，没有父级
        if (!trimmedCorpCode.contains(CORP_CODE_SEPARATOR)) {
            return Collections.emptyList();
        }

        List<String> parentCorpCodes = new ArrayList<>();
        String[] parts = trimmedCorpCode.split(CORP_CODE_SEPARATOR);

        // 从倒数第二级开始，逐级向上构建父级企业编码
        for (int i = parts.length - 1; i > 0; i--) {
            StringBuilder parentCorpCode = new StringBuilder();
            for (int j = 0; j < i; j++) {
                if (j > 0) {
                    parentCorpCode.append(CORP_CODE_SEPARATOR);
                }
                parentCorpCode.append(parts[j]);
            }
            parentCorpCodes.add(parentCorpCode.toString());
        }

        return parentCorpCodes;
    }

    /**
     * 解析企业编码，获取所有层级的企业编码（包括自身）
     * 
     * @param corpCode 企业编码，例如："0001/324123/12312"
     * @return 所有层级的企业编码列表，按层级从当前到顶级排序，例如：["0001/324123/12312", "0001/324123",
     *         "0001"]
     */
    public static List<String> parseAllLevelCorpCodes(String corpCode) {
        if (corpCode == null || corpCode.trim().isEmpty()) {
            return Collections.emptyList();
        }

        String trimmedCorpCode = corpCode.trim();
        List<String> allLevelCorpCodes = new ArrayList<>();

        // 添加自身
        allLevelCorpCodes.add(trimmedCorpCode);

        // 添加所有父级
        allLevelCorpCodes.addAll(parseParentCorpCodes(trimmedCorpCode));

        return allLevelCorpCodes;
    }

    /**
     * 获取直接父级企业编码
     * 
     * @param corpCode 企业编码，例如："0001/324123/12312"
     * @return 直接父级企业编码，例如："0001/324123"，如果没有父级则返回null
     */
    public static String getDirectParentCorpCode(String corpCode) {
        List<String> parentCorpCodes = parseParentCorpCodes(corpCode);
        return parentCorpCodes.isEmpty() ? null : parentCorpCodes.get(0);
    }

    /**
     * 获取顶级企业编码
     * 
     * @param corpCode 企业编码，例如："0001/324123/12312"
     * @return 顶级企业编码，例如："0001"，如果输入为空或无效则返回null
     */
    public static String getTopLevelCorpCode(String corpCode) {
        if (corpCode == null || corpCode.trim().isEmpty()) {
            return null;
        }

        String trimmedCorpCode = corpCode.trim();

        // 如果不包含分隔符，说明本身就是顶级企业编码
        if (!trimmedCorpCode.contains(CORP_CODE_SEPARATOR)) {
            return trimmedCorpCode;
        }

        String[] parts = trimmedCorpCode.split(CORP_CODE_SEPARATOR);
        return parts.length > 0 ? parts[0] : null;
    }

    /**
     * 判断是否为顶级企业编码
     * 
     * @param corpCode 企业编码
     * @return 如果是顶级企业编码返回true，否则返回false
     */
    public static boolean isTopLevelCorpCode(String corpCode) {
        if (corpCode == null || corpCode.trim().isEmpty()) {
            return false;
        }

        return !corpCode.trim().contains(CORP_CODE_SEPARATOR);
    }

    /**
     * 解析企业编码为CorpInfo对象，包含完整的父级链
     *
     * @param corpCode 企业编码，例如："0001/324123/12312"
     * @return CorpInfo对象，包含完整的父级链结构
     */
    public static CorpInfo parseToCorpInfo(String corpCode) {
        if (corpCode == null || corpCode.trim().isEmpty()) {
            return null;
        }

        String trimmedCorpCode = corpCode.trim();

        // 创建当前企业信息对象
        CorpInfo currentCorpInfo = new CorpInfo();
        currentCorpInfo.setCorpCode(trimmedCorpCode);

        // 获取直接父级企业编码
        String directParentCorpCode = getDirectParentCorpCode(trimmedCorpCode);
        if (directParentCorpCode != null) {
            // 递归创建父级CorpInfo对象
            CorpInfo parentCorpInfo = parseToCorpInfo(directParentCorpCode);
            currentCorpInfo.setParent(parentCorpInfo);
        }

        return currentCorpInfo;
    }

}
