package com.sendinfo.ticketing.verification.basic.repository.system.dao.impl;

import com.sendinfo.ticketing.verification.basic.model.system.SysAuditRole;
import com.sendinfo.ticketing.verification.basic.repository.system.arg.SysAuditRoleDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.system.arg.SysAuditRoleQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.system.arg.SysAuditRoleUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.system.dao.SysAuditRoleDao;
import com.sendinfo.ticketing.verification.basic.repository.system.dataobject.SysAuditRoleDO;
import com.sendinfo.ticketing.verification.repository.jdbc.Statement;
import com.sendinfo.ticketing.verification.repository.jdbc.mybatis.SqlSessionBatchInsertDAO;
import com.sendinfo.ticketing.verification.repository.jdbc.mybatis.SqlSessionCountableDAO;
import com.sendinfo.ticketing.verification.repository.jdbc.mybatis.SqlSessionGenericDAO;
import com.sendinfo.ticketing.verification.repository.jdbc.mybatis.SqlSessionQueryableDAO;
import lombok.Getter;
import org.apache.ibatis.session.SqlSession;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 票型角色数据访问实现类
 *
 * <AUTHOR>
 */
@Getter
@Repository("sysAuditRoleDao")
public class SysAuditRoleDaoImpl implements SysAuditRoleDao,
        SqlSessionGenericDAO<Integer, SysAuditRoleDO, SysAuditRoleUpdateArg, SysAuditRoleDeleteArg>,
        SqlSessionCountableDAO<SysAuditRoleQueryArg>,
        SqlSessionQueryableDAO<SysAuditRoleQueryArg, SysAuditRoleDO>,
        SqlSessionBatchInsertDAO<Integer, SysAuditRoleDO> {

    private final SqlSession sqlSession;
    private final Statement statement;

    public SysAuditRoleDaoImpl(@Qualifier("saasPwSqlSessionTemplate") SqlSessionTemplate sqlSessionTemplate) {
        this.sqlSession = sqlSessionTemplate;
        this.statement = Statement.of(SysAuditRoleDao.class);
    }

    @Override
    public void sort(SysAuditRoleQueryArg queryArg) {
        if (queryArg != null && (queryArg.getSortItems() == null || queryArg.getSortItems().isEmpty())) {
            queryArg.desc("id");
        }
    }

    @Override
    public List<SysAuditRole> querySysAuditRoleByIds(Set<Integer> ids, String corpCode) {
        Map<String, Object> params = new HashMap<>(3);
        params.put("ids", ids);
        params.put("corpCode", corpCode);
        return sqlSession.selectList(statement.get("querySysAuditRoleByIds"), params);
    }
}