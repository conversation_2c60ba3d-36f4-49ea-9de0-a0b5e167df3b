package com.sendinfo.ticketing.verification.basic.component.goods;

import com.sendinfo.ticketing.verification.basic.component.goods.param.ExplainerServiceCategoryQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.ExplainerServiceCategory;
import com.sendinfo.ticketing.verification.common.component.ReadComponent;

import java.util.List;

/**
 * 服务类目读取组件接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ExplainerServiceCategoryReadComponent extends ReadComponent<Long, ExplainerServiceCategoryQueryParam, ExplainerServiceCategory> {

	/**
	 * 查询服务类目列表
	 *
	 * @param queryParam    服务类目查询参数
	 * @return  服务类目集合
	 */
	List<ExplainerServiceCategory> queryServiceCategoryList(ExplainerServiceCategoryQueryParam queryParam);
} 