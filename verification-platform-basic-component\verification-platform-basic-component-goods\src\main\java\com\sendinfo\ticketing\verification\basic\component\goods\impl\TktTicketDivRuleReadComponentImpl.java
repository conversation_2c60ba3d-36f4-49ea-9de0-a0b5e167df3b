package com.sendinfo.ticketing.verification.basic.component.goods.impl;

import com.sendinfo.ticketing.verification.basic.component.goods.TktTicketDivRuleReadComponent;
import com.sendinfo.ticketing.verification.basic.component.goods.converter.TktTicketDivRuleConverter;
import com.sendinfo.ticketing.verification.basic.component.goods.param.TktTicketDivRuleQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TktTicketDivRule;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TktTicketDivRuleQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dao.TktTicketDivRuleDao;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TktTicketDivRuleDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedCountRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedListRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleRead;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * @Auther: chengjie.li
 * @Date: 2025/6/12 13:40
 */
@Getter
@Component("tktTicketDivRuleReadComponent")
public class TktTicketDivRuleReadComponentImpl implements TktTicketDivRuleReadComponent,
        DaoBasedSingleRead<Long, TktTicketDivRuleQueryParam, TktTicketDivRule, TktTicketDivRuleDO>,
        DaoBasedCountRead<Long, TktTicketDivRuleQueryParam, TktTicketDivRule, TktTicketDivRuleQueryArg>,
        DaoBasedListRead<Long, TktTicketDivRuleQueryParam, TktTicketDivRule, TktTicketDivRuleDO, TktTicketDivRuleQueryArg> {

    private final TktTicketDivRuleDao dao;

    private final TktTicketDivRuleConverter converter;


    public TktTicketDivRuleReadComponentImpl(TktTicketDivRuleDao dao, TktTicketDivRuleConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }

    @Override
    public List<TktTicketDivRule> listTicketDivRuleByTicketId(Long ticketId, String corpCode) {
        return converter.r_ds2ms(dao.listTicketDivRuleByTicketId(ticketId, corpCode));
    }

    @Override
    public List<TktTicketDivRule> batchListTicketDivRuleByTicketIds(Set<Long> ticketIds, String corpCode) {
        return converter.r_ds2ms(dao.batchListTicketDivRuleByTicketIds(ticketIds, corpCode));
    }
}
