package com.sendinfo.ticketing.verification.basic.repository.pay.arg;

import com.sendinfo.ticketing.verification.common.repository.arg.AbstractQueryArg;
import com.sendinfo.ticketing.verification.common.repository.arg.Pageable;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 交易记录查询参数
 * 用于封装trans_info表的查询条件，支持租户隔离、分页、排序
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class TransInfoQueryArg extends AbstractQueryArg implements Pageable {
    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 内部交易号
     */
    private String transNo;

    /**
     * 交易类型
     */
    private Integer transType;

    /**
     * 业务类型
     */
    private Integer businessType;

    /**
     * 交易ID
     */
    private Long tradeId;

    /**
     * 客户代码+交易ID号
     */
    private String tradeCode;

    /**
     * 状态： 0:待支付 1:支付未完成 2:未支付 3:已支付
     */
    private String payStatus;

    /**
     * 退单支付交易号
     */
    private String refundPayTransno;

    /**
     * 0：支付宝 1:微信 2:银联3：备用金 9:手功
     */
    private Integer gateway;

    /**
     * 网关交易号（第三方交易号）
     */
    private String gatewayTransNo;

    /**
     * 分页偏移量
     */
    private Integer offset;

    /**
     * 分页大小
     */
    private Integer limit;
} 