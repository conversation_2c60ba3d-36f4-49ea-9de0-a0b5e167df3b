package com.sendinfo.ticketing.verification.basic.service.park.impl;

import com.sendinfo.ticketing.verification.basic.api.park.SysTenementInfoReadService;
import com.sendinfo.ticketing.verification.basic.api.park.request.SysTenementInfoCondition;
import com.sendinfo.ticketing.verification.basic.bootstrap.VerificationPlatformBasicBootstrap;
import com.sendinfo.ticketing.verification.basic.model.park.SysTenementInfo;
import com.sendinfo.ticketing.verification.common.api.request.PageRequest;
import com.sendinfo.ticketing.verification.common.api.result.PageResultModel;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static org.junit.Assert.*;


/**
 * <AUTHOR> Name]
 * @since 2025/6/19 17:15
 **/
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = VerificationPlatformBasicBootstrap.class)
public class SysTenementInfoReadServiceImplTest {

    @Autowired
    private SysTenementInfoReadService sysTenementInfoReadService;

    private String corpCode = "0001";

//    @Test
    public void testSearchSysTenementInfoSuccess() {
        // Arrange
        PageRequest<SysTenementInfoCondition> pageRequest = new PageRequest<>();
        pageRequest.setPageSize(10);
        pageRequest.setCurrentPage(1);
        SysTenementInfoCondition condition = new SysTenementInfoCondition();
        condition.setCorpCode(corpCode);
        pageRequest.setCondition(condition);
        
        // Act
        PageResultModel<SysTenementInfo> result = sysTenementInfoReadService.searchSysTenementInfo(pageRequest);
        
        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
    }


//    @Test
    public void testQuerySysTenementInfoByCorpCodeSuccess() {

        // Act
        ResultModel<List<SysTenementInfo>> result = sysTenementInfoReadService.querySysTenementInfoByCorpCode(corpCode);
        
        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertFalse(result.getModel().isEmpty());
    }

}
