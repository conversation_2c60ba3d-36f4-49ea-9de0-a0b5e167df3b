/**
 * System parameter component definition converter
 *
 * <AUTHOR> 2025-07-21 15:40:00
 */
package com.sendinfo.ticketing.verification.basic.component.infra.converter;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamComponentDefinitionCreateRequest;
import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamComponentDefinitionDeleteRequest;
import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamComponentDefinitionQueryCondition;
import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamComponentDefinitionUpdateRequest;
import com.sendinfo.ticketing.verification.basic.component.infra.mapper.SysParamComponentDefinitionMapper;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamComponentDefinitionCreateParam;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamComponentDefinitionDeleteParam;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamComponentDefinitionQueryParam;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamComponentDefinitionUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.infra.SysParamComponentDefinition;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamComponentDefinitionDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamComponentDefinitionQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamComponentDefinitionUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.SysParamComponentDefinitionDO;
import com.sendinfo.ticketing.verification.common.api.request.PageRequest;
import com.sendinfo.ticketing.verification.common.component.converter.CreateParam2DoConverter;
import com.sendinfo.ticketing.verification.common.component.converter.CreateReq2ParamConverter;
import com.sendinfo.ticketing.verification.common.component.converter.DeleteParam2ArgConverter;
import com.sendinfo.ticketing.verification.common.component.converter.DeleteReq2ParamConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadDo2ModelConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadPageReq2ParamConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadParam2ArgConverter;
import com.sendinfo.ticketing.verification.common.component.converter.UpdateParam2ArgConverter;
import com.sendinfo.ticketing.verification.common.component.converter.UpdateReq2ParamConverter;
import com.sendinfo.ticketing.verification.common.repository.arg.StatusUpdater;

/**
 * 系统参数组件定义转换器，负责不同层级对象间的转换，包括请求对象、参数对象、数据对象和模型对象
 */
@Component("sysParamComponentDefinitionConverter")
public class SysParamComponentDefinitionConverter
        implements CreateParam2DoConverter<SysParamComponentDefinitionCreateParam, SysParamComponentDefinitionDO>,
        ReadParam2ArgConverter<SysParamComponentDefinitionQueryParam, SysParamComponentDefinitionQueryArg>,
        ReadDo2ModelConverter<SysParamComponentDefinitionDO, SysParamComponentDefinition>,
        UpdateParam2ArgConverter<SysParamComponentDefinitionUpdateParam, SysParamComponentDefinitionUpdateArg, SysParamComponentDefinition>,
        DeleteParam2ArgConverter<SysParamComponentDefinitionDeleteParam, SysParamComponentDefinitionDeleteArg>,
        DeleteReq2ParamConverter<SysParamComponentDefinitionDeleteRequest, SysParamComponentDefinitionDeleteParam>,
        CreateReq2ParamConverter<SysParamComponentDefinitionCreateRequest, SysParamComponentDefinitionCreateParam>,
        UpdateReq2ParamConverter<SysParamComponentDefinitionUpdateRequest, SysParamComponentDefinitionUpdateParam>,
        ReadPageReq2ParamConverter<SysParamComponentDefinitionQueryCondition, SysParamComponentDefinitionQueryParam> {

    @Override
    public SysParamComponentDefinitionDO c_p2d(SysParamComponentDefinitionCreateParam createParam) {
        return SysParamComponentDefinitionMapper.INSTANCE.convert(createParam);
    }

    @Override
    public List<SysParamComponentDefinitionDO> c_ps2ds(List<SysParamComponentDefinitionCreateParam> params) {
        return params.stream()
                .map(SysParamComponentDefinitionMapper.INSTANCE::convert)
                .collect(Collectors.toList());
    }

    @Override
    public SysParamComponentDefinitionUpdateArg u_p2a(SysParamComponentDefinitionUpdateParam param,
            SysParamComponentDefinition currentModel) {
        SysParamComponentDefinitionUpdateArg updateArg = SysParamComponentDefinitionMapper.INSTANCE.convert(param);
        Optional.ofNullable(param.getTargetStatus())
                .ifPresent(innerStatus -> updateArg
                        .setStatusUpdater(StatusUpdater.transfer(currentModel.getStatus().getCode(),
                                innerStatus.getCode())));

        return updateArg;
    }

    @Override
    public SysParamComponentDefinitionQueryArg r_p2a(SysParamComponentDefinitionQueryParam param) {
        return SysParamComponentDefinitionMapper.INSTANCE.convert(param);
    }

    @Override
    public SysParamComponentDefinitionDeleteArg d_p2a(SysParamComponentDefinitionDeleteParam param) {
        return SysParamComponentDefinitionMapper.INSTANCE.convert(param);
    }

    @Override
    public SysParamComponentDefinition r_d2m(SysParamComponentDefinitionDO dataObject) {
        return SysParamComponentDefinitionMapper.INSTANCE.convert(dataObject);
    }

    @Override
    public List<SysParamComponentDefinition> r_ds2ms(List<SysParamComponentDefinitionDO> dataObjects) {
        return dataObjects.stream()
                .map(this::r_d2m)
                .collect(Collectors.toList());
    }

    @Override
    public SysParamComponentDefinitionCreateParam c_r2p(SysParamComponentDefinitionCreateRequest req) {
        return SysParamComponentDefinitionMapper.INSTANCE.convert(req);
    }

    @Override
    public SysParamComponentDefinitionUpdateParam u_r2p(SysParamComponentDefinitionUpdateRequest req) {
        return SysParamComponentDefinitionMapper.INSTANCE.convert(req);
    }

    @Override
    public SysParamComponentDefinitionQueryParam r_pr2p(
            PageRequest<SysParamComponentDefinitionQueryCondition> pageReq) {
        SysParamComponentDefinitionQueryParam queryParam = new SysParamComponentDefinitionQueryParam();
        SysParamComponentDefinitionQueryCondition condition = pageReq.getCondition();
        if (condition != null) {
            queryParam = SysParamComponentDefinitionMapper.INSTANCE.convert(condition);
        }
        queryParam.setStartIndex(pageReq.getStartIndex());
        queryParam.setPageSize(pageReq.getPageSize());
        return queryParam;
    }

    @Override
    public SysParamComponentDefinitionDeleteParam d_r2p(SysParamComponentDefinitionDeleteRequest req) {
        return SysParamComponentDefinitionMapper.INSTANCE.convert(req);
    }
}