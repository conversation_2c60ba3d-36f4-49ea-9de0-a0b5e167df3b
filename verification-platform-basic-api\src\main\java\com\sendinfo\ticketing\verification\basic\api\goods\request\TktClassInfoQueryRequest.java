package com.sendinfo.ticketing.verification.basic.api.goods.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Auther: chengjie.li
 * @Date: 2025/6/12 18:01
 */
@Getter
@Setter
@ToString(callSuper = true)
public class TktClassInfoQueryRequest implements Serializable {
    private static final long serialVersionUID = 8027645180437889201L;

    /**
     * 企业编码
     */
    @NotNull
    private String corpCode;

    /**
     * 启用状态
     */
    private String useFlag;
}
