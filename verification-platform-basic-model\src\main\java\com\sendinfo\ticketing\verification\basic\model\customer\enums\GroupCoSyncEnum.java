package com.sendinfo.ticketing.verification.basic.model.customer.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 集团同步状态枚举
 * 定义是否集团同步
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
public enum GroupCoSyncEnum {
    TENANT(0, "租户"),
    GROUP(1, "集团");

    @JsonValue
    private final Integer code;
    private final String description;

    GroupCoSyncEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    // --- 高效反向查找机制 ---
    private static final Map<Integer, GroupCoSyncEnum> CODE_MAP = 
        Arrays.stream(values()).collect(Collectors.toMap(GroupCoSyncEnum::getCode, Function.identity()));

    /**
     * 根据 code 获取枚举实例
     *
     * @param code 集团同步状态编码
     * @return 对应的枚举实例，如果找不到则返回 null
     */
    @JsonCreator
    public static GroupCoSyncEnum ofCode(Integer code) {
        if (code == null) {
            return null;
        }
        return CODE_MAP.get(code);
    }
} 