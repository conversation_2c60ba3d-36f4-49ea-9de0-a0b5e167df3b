package com.sendinfo.ticketing.verification.basic.component.infra.impl;

import lombok.Getter;
import org.springframework.stereotype.Component;

import com.sendinfo.ticketing.verification.basic.component.infra.SysParamComponentDefinitionCreateComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.converter.SysParamComponentDefinitionConverter;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamComponentDefinitionCreateParam;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.SysParamComponentDefinitionDao;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.SysParamComponentDefinitionDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleCreate;

/**
 * 系统参数组件定义创建组件实现
 */
@Component("sysParamComponentDefinitionCreateComponent")
@Getter
public class SysParamComponentDefinitionCreateComponentImpl
        implements SysParamComponentDefinitionCreateComponent,
        DaoBasedSingleCreate<Long, SysParamComponentDefinitionCreateParam, SysParamComponentDefinitionDO> {

    private final SysParamComponentDefinitionDao dao;
    private final SysParamComponentDefinitionConverter converter;

    public SysParamComponentDefinitionCreateComponentImpl(SysParamComponentDefinitionDao dao, SysParamComponentDefinitionConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }
}
