package com.sendinfo.ticketing.verification.basic.api.infra.request;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 组件分类更新请求
 * 
 * <AUTHOR> 2025-07-22 11:45:32
 */
@Getter
@Setter
@ToString
public class FeatureComponentCategoryUpdateRequest implements Serializable {
    private static final long serialVersionUID = -7654321098765432103L;

    /**
     * 主键ID
     */
    @NotNull(message = "ID不能为空")
    private Long id;

    /**
     * 企业编码
     */
    @NotBlank(message = "企业编码不能为空")
    private String corpCode;

    /**
     * 分类名称
     */
    @Size(max = 30, message = "分类名称长度不能超过30")
    private String categoryName;

    /**
     * 排序
     */
    @Min(value = 1, message = "排序不能小于1")
    @Max(value = 999, message = "排序不能超过999")
    private Integer sortNo;

    /**
     * 修改人
     */
    @NotNull(message = "修改人不能为空")
    private String modifyBy;
}
