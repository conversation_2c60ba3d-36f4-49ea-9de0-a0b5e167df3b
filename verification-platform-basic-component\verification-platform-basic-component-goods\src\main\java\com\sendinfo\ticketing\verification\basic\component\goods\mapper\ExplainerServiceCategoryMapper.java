package com.sendinfo.ticketing.verification.basic.component.goods.mapper;

import com.sendinfo.ticketing.verification.basic.api.goods.request.ExplainerServiceCategoryQueryRequest;
import com.sendinfo.ticketing.verification.basic.component.goods.param.ExplainerServiceCategoryCreateParam;
import com.sendinfo.ticketing.verification.basic.component.goods.param.ExplainerServiceCategoryQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.ExplainerServiceCategory;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.ExplainerServiceCategoryQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.ExplainerServiceCategoryDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 服务类目对象转换映射器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface ExplainerServiceCategoryMapper {

    ExplainerServiceCategoryMapper INSTANCE = Mappers.getMapper(ExplainerServiceCategoryMapper.class);

    /**
     * 查询参数转换为查询参数
     *
     * @param queryParam 查询参数
     * @return 查询参数
     */
    @Mapping(target = "useFlag", expression = "java(queryParam.getUseFlag() != null ? queryParam.getUseFlag().getCode() : null)")
    ExplainerServiceCategoryQueryArg convert(ExplainerServiceCategoryQueryParam queryParam);

    /**
     * 数据对象转换为模型对象
     *
     * @param dataObject 数据对象
     * @return 模型对象
     */
    @Mapping(target = "useFlag", expression = "java(com.sendinfo.ticketing.verification.basic.enums.ExplainerUserFlagEnum.ofCode(dataObject.getUseFlag()))")
    ExplainerServiceCategory convert(ExplainerServiceCategoryDO dataObject);

    /**
     * 创建参数转换为数据对象
     *
     * @param createParam 创建参数
     * @return 数据对象
     */
    ExplainerServiceCategoryDO convert(ExplainerServiceCategoryCreateParam createParam);

    /**
     * 查询请求对象转换为查询参数
     *
     * @param queryRequest  服务类目查询请求对象
     * @return  ExplainerServiceCategoryQueryParam
     */
    @Mapping(target = "useFlag", expression = "java(com.sendinfo.ticketing.verification.basic.enums.ExplainerUserFlagEnum.ofCode(queryRequest.getUseFlag()))")
    ExplainerServiceCategoryQueryParam convert(ExplainerServiceCategoryQueryRequest queryRequest);
} 