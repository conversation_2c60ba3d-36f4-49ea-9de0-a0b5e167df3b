package com.sendinfo.ticketing.verification.basic.component.goods.converter;

import com.sendinfo.ticketing.verification.basic.api.goods.request.TktClassInfoQueryRequest;
import com.sendinfo.ticketing.verification.basic.component.goods.mapper.TktClassInfoMapper;
import com.sendinfo.ticketing.verification.basic.component.goods.param.TktClassInfoQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TktClassInfo;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TktClassInfoQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TktClassInfoDO;
import com.sendinfo.ticketing.verification.common.component.converter.ReadDo2ModelConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadParam2ArgConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadReq2ParamConverter;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component("tktClassInfoConverter")
public class TktClassInfoConverter implements
        ReadParam2ArgConverter<TktClassInfoQueryParam, TktClassInfoQueryArg>,
        ReadDo2ModelConverter<TktClassInfoDO, TktClassInfo>,
        ReadReq2ParamConverter<TktClassInfoQueryRequest, TktClassInfoQueryParam> {

    @Override
    public TktClassInfoQueryArg r_p2a(TktClassInfoQueryParam param) {
        return TktClassInfoMapper.INSTANCE.convert(param);
    }


    @Override
    public TktClassInfo r_d2m(TktClassInfoDO dataObject) {
        return TktClassInfoMapper.INSTANCE.convert(dataObject);
    }

    @Override
    public List<TktClassInfo> r_ds2ms(List<TktClassInfoDO> dataObjects) {
        return dataObjects.stream().map(this::r_d2m)
                .collect(Collectors.toList());
    }

    @Override
    public TktClassInfoQueryParam r_r2p(TktClassInfoQueryRequest request) {
        return TktClassInfoMapper.INSTANCE.convert(request);
    }
}