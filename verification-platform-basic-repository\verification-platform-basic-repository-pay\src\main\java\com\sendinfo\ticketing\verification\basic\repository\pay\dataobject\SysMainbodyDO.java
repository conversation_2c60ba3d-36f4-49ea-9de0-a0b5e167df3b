package com.sendinfo.ticketing.verification.basic.repository.pay.dataobject;

import com.sendinfo.ticketing.verification.common.repository.dataobject.AbstractTenantBaseDO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 经营主体数据对象
 * 对应数据库表 sys_mainbody
 * 继承租户基础DO，包含租户隔离字段corpCode
 * 所有字段与表结构保持一致，便于MyBatis自动映射
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class SysMainbodyDO extends AbstractTenantBaseDO<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 经营主体名称
     */
    private String mainbodyName;

    /**
     * 经营主体简称
     */
    private String mainbodyShortName;

    /**
     * 主体商户号
     */
    private String merchantNo;

    /**
     * 联系人
     */
    private String linkMan;

    /**
     * 联系电话
     */
    private String linkTel;

    /**
     * 备注
     */
    private String remark;

    /**
     * 经营主体编号
     */
    private String mainbodyNumber;

    /**
     * 部门ID
     */
    private Long deptId;
} 