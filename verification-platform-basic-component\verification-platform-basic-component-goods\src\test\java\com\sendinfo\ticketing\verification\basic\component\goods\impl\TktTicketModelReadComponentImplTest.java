package com.sendinfo.ticketing.verification.basic.component.goods.impl;

import com.sendinfo.ticketing.verification.basic.component.goods.converter.TktTicketModelConverter;
import com.sendinfo.ticketing.verification.basic.model.goods.TktTicketModel;
import com.sendinfo.ticketing.verification.basic.repository.goods.dao.TktTicketModelDao;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TktTicketModelDO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

/**
 * TktTicketModelReadComponent 单元测试类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RunWith(MockitoJUnitRunner.class)
public class TktTicketModelReadComponentImplTest {

    @Mock
    private TktTicketModelConverter converter;

    @Mock
    private TktTicketModelDao dao;

    @InjectMocks
    private TktTicketModelReadComponentImpl tktTicketModelReadComponent;

    // 测试常量
    private static final String TEST_CORP_CODE = "TEST_CORP_CODE";
    private static final String TEST_TICKET_CODE_1 = "TEST_TICKET_001";
    private static final String TEST_TICKET_CODE_2 = "TEST_TICKET_002";
    private static final Long TEST_TKT_ID_1 = 1L;
    private static final Long TEST_TKT_ID_2 = 2L;
    private static final String TEST_TICKET_NAME_1 = "成人票";
    private static final String TEST_TICKET_NAME_2 = "儿童票";
    private static final String TEST_OUT_TICKET_CODE_1 = "OUT_001";
    private static final String TEST_OUT_TICKET_CODE_2 = "OUT_002";
    private static final String TEST_ENGLINSH_NAME_1 = "Adult Ticket";
    private static final String TEST_ENGLINSH_NAME_2 = "Child Ticket";
    private static final String TEST_BARCODE_TYPE_1 = "YZ";
    private static final String TEST_BARCODE_TYPE_2 = "WZ";
    private static final String TEST_TICKET_KIND_1 = "0";
    private static final String TEST_TICKET_KIND_2 = "1";
    private static final String TEST_TICKET_GROUP_CODE = "GROUP_001";
    private static final String TEST_TICKET_GROUP_NAME = "普通票种";
    private static final String TEST_DAYPART_CODE = "DAY_001";
    private static final String TEST_COMPENSATION_FLAG = "T";
    private static final String TEST_USE_FLAG = "T";
    private static final BigDecimal TEST_PRICE_1 = new BigDecimal("100.00");
    private static final BigDecimal TEST_PRICE_2 = new BigDecimal("50.00");
    private static final String TEST_PINYIN_CODE_1 = "CRP";
    private static final String TEST_PINYIN_CODE_2 = "ETP";
    private static final Integer TEST_SORT_1 = 1;
    private static final Integer TEST_SORT_2 = 2;
    private static final Integer TEST_MIN_USE_PEOPLE = 1;
    private static final Integer TEST_MAX_USE_PEOPLE = 10;
    private static final String TEST_MODIFY_FLAG = "F";
    private static final String TEST_CREATE_BY = "TEST_CREATE_BY";
    private static final String TEST_MODIFY_BY = "TEST_MODIFY_BY";

    private TktTicketModelDO tktTicketModelDO1;
    private TktTicketModelDO tktTicketModelDO2;
    private TktTicketModel tktTicketModel1;
    private TktTicketModel tktTicketModel2;

    @Before
    public void setUp() {
        // 准备第一个测试数据对象 - 成人票
        tktTicketModelDO1 = new TktTicketModelDO();
        tktTicketModelDO1.setCorpCode(TEST_CORP_CODE);
        tktTicketModelDO1.setTktId(TEST_TKT_ID_1);
        tktTicketModelDO1.setTicketName(TEST_TICKET_NAME_1);
        tktTicketModelDO1.setTicketCode(TEST_TICKET_CODE_1);
        tktTicketModelDO1.setOutTicketCode(TEST_OUT_TICKET_CODE_1);
        tktTicketModelDO1.setEnglinshName(TEST_ENGLINSH_NAME_1);
        tktTicketModelDO1.setBarcodeType(TEST_BARCODE_TYPE_1);
        tktTicketModelDO1.setTicketKind(TEST_TICKET_KIND_1);
        tktTicketModelDO1.setTicketGroupCode(TEST_TICKET_GROUP_CODE);
        tktTicketModelDO1.setTicketGroupName(TEST_TICKET_GROUP_NAME);
        tktTicketModelDO1.setDaypartCode(TEST_DAYPART_CODE);
        tktTicketModelDO1.setCompensationFlag(TEST_COMPENSATION_FLAG);
        tktTicketModelDO1.setUseFlag(TEST_USE_FLAG);
        tktTicketModelDO1.setPrice(TEST_PRICE_1);
        tktTicketModelDO1.setPinyinCode(TEST_PINYIN_CODE_1);
        tktTicketModelDO1.setSort(TEST_SORT_1);
        tktTicketModelDO1.setMinUsePeople(TEST_MIN_USE_PEOPLE);
        tktTicketModelDO1.setMaxUsePeople(TEST_MAX_USE_PEOPLE);
        tktTicketModelDO1.setModifyFlag(TEST_MODIFY_FLAG);

        // 准备第二个测试数据对象 - 儿童票
        tktTicketModelDO2 = new TktTicketModelDO();
        tktTicketModelDO2.setCorpCode(TEST_CORP_CODE);
        tktTicketModelDO2.setTktId(TEST_TKT_ID_2);
        tktTicketModelDO2.setTicketName(TEST_TICKET_NAME_2);
        tktTicketModelDO2.setTicketCode(TEST_TICKET_CODE_2);
        tktTicketModelDO2.setOutTicketCode(TEST_OUT_TICKET_CODE_2);
        tktTicketModelDO2.setEnglinshName(TEST_ENGLINSH_NAME_2);
        tktTicketModelDO2.setBarcodeType(TEST_BARCODE_TYPE_2);
        tktTicketModelDO2.setTicketKind(TEST_TICKET_KIND_2);
        tktTicketModelDO2.setTicketGroupCode(TEST_TICKET_GROUP_CODE);
        tktTicketModelDO2.setTicketGroupName(TEST_TICKET_GROUP_NAME);
        tktTicketModelDO2.setDaypartCode(TEST_DAYPART_CODE);
        tktTicketModelDO2.setCompensationFlag(TEST_COMPENSATION_FLAG);
        tktTicketModelDO2.setUseFlag(TEST_USE_FLAG);
        tktTicketModelDO2.setPrice(TEST_PRICE_2);
        tktTicketModelDO2.setPinyinCode(TEST_PINYIN_CODE_2);
        tktTicketModelDO2.setSort(TEST_SORT_2);
        tktTicketModelDO2.setMinUsePeople(TEST_MIN_USE_PEOPLE);
        tktTicketModelDO2.setMaxUsePeople(TEST_MAX_USE_PEOPLE);
        tktTicketModelDO2.setModifyFlag(TEST_MODIFY_FLAG);

        // 准备对应的模型对象 - 成人票
        tktTicketModel1 = new TktTicketModel();
        tktTicketModel1.setCorpCode(TEST_CORP_CODE);
        tktTicketModel1.setTktId(TEST_TKT_ID_1);
        tktTicketModel1.setTicketName(TEST_TICKET_NAME_1);
        tktTicketModel1.setTicketCode(TEST_TICKET_CODE_1);
        tktTicketModel1.setOutTicketCode(TEST_OUT_TICKET_CODE_1);
        tktTicketModel1.setEnglinshName(TEST_ENGLINSH_NAME_1);
        tktTicketModel1.setBarcodeType(TEST_BARCODE_TYPE_1);
        tktTicketModel1.setTicketKind(TEST_TICKET_KIND_1);
        tktTicketModel1.setTicketGroupCode(TEST_TICKET_GROUP_CODE);
        tktTicketModel1.setTicketGroupName(TEST_TICKET_GROUP_NAME);
        tktTicketModel1.setDaypartCode(TEST_DAYPART_CODE);
        tktTicketModel1.setCompensationFlag(TEST_COMPENSATION_FLAG);
        tktTicketModel1.setUseFlag(TEST_USE_FLAG);
        tktTicketModel1.setPrice(TEST_PRICE_1);
        tktTicketModel1.setPinyinCode(TEST_PINYIN_CODE_1);
        tktTicketModel1.setSort(TEST_SORT_1);
        tktTicketModel1.setMinUsePeople(TEST_MIN_USE_PEOPLE);
        tktTicketModel1.setMaxUsePeople(TEST_MAX_USE_PEOPLE);
        tktTicketModel1.setCreateBy(TEST_CREATE_BY);
        tktTicketModel1.setModifyBy(TEST_MODIFY_BY);

        // 准备对应的模型对象 - 儿童票
        tktTicketModel2 = new TktTicketModel();
        tktTicketModel2.setCorpCode(TEST_CORP_CODE);
        tktTicketModel2.setTktId(TEST_TKT_ID_2);
        tktTicketModel2.setTicketName(TEST_TICKET_NAME_2);
        tktTicketModel2.setTicketCode(TEST_TICKET_CODE_2);
        tktTicketModel2.setOutTicketCode(TEST_OUT_TICKET_CODE_2);
        tktTicketModel2.setEnglinshName(TEST_ENGLINSH_NAME_2);
        tktTicketModel2.setBarcodeType(TEST_BARCODE_TYPE_2);
        tktTicketModel2.setTicketKind(TEST_TICKET_KIND_2);
        tktTicketModel2.setTicketGroupCode(TEST_TICKET_GROUP_CODE);
        tktTicketModel2.setTicketGroupName(TEST_TICKET_GROUP_NAME);
        tktTicketModel2.setDaypartCode(TEST_DAYPART_CODE);
        tktTicketModel2.setCompensationFlag(TEST_COMPENSATION_FLAG);
        tktTicketModel2.setUseFlag(TEST_USE_FLAG);
        tktTicketModel2.setPrice(TEST_PRICE_2);
        tktTicketModel2.setPinyinCode(TEST_PINYIN_CODE_2);
        tktTicketModel2.setSort(TEST_SORT_2);
        tktTicketModel2.setMinUsePeople(TEST_MIN_USE_PEOPLE);
        tktTicketModel2.setMaxUsePeople(TEST_MAX_USE_PEOPLE);
        tktTicketModel2.setCreateBy(TEST_CREATE_BY);
        tktTicketModel2.setModifyBy(TEST_MODIFY_BY);
    }

    /**
     * 测试根据票型编码查询票型 - 正常场景
     */
    @Test
    public void testQueryTicketModelByCode_ShouldReturnTktTicketModel() {
        // 1. 准备测试数据
        when(dao.queryTicketModelByCode(TEST_TICKET_CODE_1, TEST_CORP_CODE))
                .thenReturn(tktTicketModelDO1);
        when(converter.r_d2m(tktTicketModelDO1)).thenReturn(tktTicketModel1);

        // 2. 执行测试
        TktTicketModel result = tktTicketModelReadComponent.queryTicketModelByCode(
                TEST_TICKET_CODE_1, TEST_CORP_CODE);

        // 3. 验证结果
        assertNotNull("结果不应为null", result);
        assertEquals("企业编码应该一致", TEST_CORP_CODE, result.getCorpCode());
        assertEquals("票型ID应该一致", TEST_TKT_ID_1, result.getTktId());
        assertEquals("票型名称应该一致", TEST_TICKET_NAME_1, result.getTicketName());
        assertEquals("票型编码应该一致", TEST_TICKET_CODE_1, result.getTicketCode());
        assertEquals("外部票型编码应该一致", TEST_OUT_TICKET_CODE_1, result.getOutTicketCode());
        assertEquals("英文名称应该一致", TEST_ENGLINSH_NAME_1, result.getEnglinshName());
        assertEquals("条码类型应该一致", TEST_BARCODE_TYPE_1, result.getBarcodeType());
        assertEquals("票种应该一致", TEST_TICKET_KIND_1, result.getTicketKind());
        assertEquals("分组编号应该一致", TEST_TICKET_GROUP_CODE, result.getTicketGroupCode());
        assertEquals("分组名称应该一致", TEST_TICKET_GROUP_NAME, result.getTicketGroupName());
        assertEquals("分时库存码应该一致", TEST_DAYPART_CODE, result.getDaypartCode());
        assertEquals("是否补差价应该一致", TEST_COMPENSATION_FLAG, result.getCompensationFlag());
        assertEquals("启用状态应该一致", TEST_USE_FLAG, result.getUseFlag());
        assertEquals("实际销售价格应该一致", TEST_PRICE_1, result.getPrice());
        assertEquals("拼音简码应该一致", TEST_PINYIN_CODE_1, result.getPinyinCode());
        assertEquals("排序应该一致", TEST_SORT_1, result.getSort());
        assertEquals("最小使用人数应该一致", TEST_MIN_USE_PEOPLE, result.getMinUsePeople());
        assertEquals("最大使用人数应该一致", TEST_MAX_USE_PEOPLE, result.getMaxUsePeople());
        assertEquals("创建人应该一致", TEST_CREATE_BY, result.getCreateBy());
        assertEquals("修改人应该一致", TEST_MODIFY_BY, result.getModifyBy());

        // 4. 验证方法调用
        Mockito.verify(dao).queryTicketModelByCode(TEST_TICKET_CODE_1, TEST_CORP_CODE);
        Mockito.verify(converter).r_d2m(tktTicketModelDO1);
    }

    /**
     * 测试根据票型编码查询票型 - 无结果场景
     */
    @Test
    public void testQueryTicketModelByCode_WhenNoResult_ShouldReturnNull() {
        // 1. 准备测试数据
        when(dao.queryTicketModelByCode(TEST_TICKET_CODE_1, TEST_CORP_CODE))
                .thenReturn(null);

        // 2. 执行测试
        TktTicketModel result = tktTicketModelReadComponent.queryTicketModelByCode(
                TEST_TICKET_CODE_1, TEST_CORP_CODE);

        // 3. 验证结果
        assertNull("结果应该为null", result);

        // 4. 验证方法调用
        Mockito.verify(dao).queryTicketModelByCode(TEST_TICKET_CODE_1, TEST_CORP_CODE);
        Mockito.verify(converter).r_d2m(null);
    }

    /**
     * 测试批量查询票型 - 正常场景
     */
    @Test
    public void testBatchQueryTicketModelByCodes_ShouldReturnList() {
        // 1. 准备测试数据
        Set<String> ticketCodes = new HashSet<>(Arrays.asList(TEST_TICKET_CODE_1, TEST_TICKET_CODE_2));
        List<TktTicketModelDO> ticketModelDOList = Arrays.asList(tktTicketModelDO1, tktTicketModelDO2);
        List<TktTicketModel> expectedTicketModelList = Arrays.asList(tktTicketModel1, tktTicketModel2);

        when(dao.batchQueryTicketModelByCodes(ticketCodes, TEST_CORP_CODE))
                .thenReturn(ticketModelDOList);
        when(converter.r_ds2ms(ticketModelDOList)).thenReturn(expectedTicketModelList);

        // 2. 执行测试
        List<TktTicketModel> result = tktTicketModelReadComponent.batchQueryTicketModelByCodes(
                ticketCodes, TEST_CORP_CODE);

        // 3. 验证结果
        assertNotNull("结果不应为null", result);
        assertEquals("结果列表大小应该一致", 2, result.size());
        assertEquals("第一个元素的企业编码应该一致", TEST_CORP_CODE, result.get(0).getCorpCode());
        assertEquals("第一个元素的票型ID应该一致", TEST_TKT_ID_1, result.get(0).getTktId());
        assertEquals("第一个元素的票型名称应该一致", TEST_TICKET_NAME_1, result.get(0).getTicketName());
        assertEquals("第一个元素的票型编码应该一致", TEST_TICKET_CODE_1, result.get(0).getTicketCode());
        assertEquals("第一个元素的条码类型应该一致", TEST_BARCODE_TYPE_1, result.get(0).getBarcodeType());
        assertEquals("第一个元素的票种应该一致", TEST_TICKET_KIND_1, result.get(0).getTicketKind());
        assertEquals("第一个元素的价格应该一致", TEST_PRICE_1, result.get(0).getPrice());

        assertEquals("第二个元素的票型ID应该一致", TEST_TKT_ID_2, result.get(1).getTktId());
        assertEquals("第二个元素的票型名称应该一致", TEST_TICKET_NAME_2, result.get(1).getTicketName());
        assertEquals("第二个元素的票型编码应该一致", TEST_TICKET_CODE_2, result.get(1).getTicketCode());
        assertEquals("第二个元素的条码类型应该一致", TEST_BARCODE_TYPE_2, result.get(1).getBarcodeType());
        assertEquals("第二个元素的票种应该一致", TEST_TICKET_KIND_2, result.get(1).getTicketKind());
        assertEquals("第二个元素的价格应该一致", TEST_PRICE_2, result.get(1).getPrice());

        // 4. 验证方法调用
        Mockito.verify(dao).batchQueryTicketModelByCodes(ticketCodes, TEST_CORP_CODE);
    }

    /**
     * 测试批量查询票型 - 空集合场景
     */
    @Test
    public void testBatchQueryTicketModelByCodes_WhenEmptySet_ShouldReturnEmptyList() {
        // 1. 准备测试数据
        Set<String> emptyTicketCodes = new HashSet<>();
        when(dao.batchQueryTicketModelByCodes(emptyTicketCodes, TEST_CORP_CODE))
                .thenReturn(Collections.emptyList());

        // 2. 执行测试
        List<TktTicketModel> result = tktTicketModelReadComponent.batchQueryTicketModelByCodes(
                emptyTicketCodes, TEST_CORP_CODE);

        // 3. 验证结果
        assertNotNull("结果不应为null", result);
        assertTrue("结果应该是空列表", result.isEmpty());

        // 4. 验证方法调用
        Mockito.verify(dao).batchQueryTicketModelByCodes(emptyTicketCodes, TEST_CORP_CODE);
    }

    /**
     * 测试批量查询票型 - 无结果场景
     */
    @Test
    public void testBatchQueryTicketModelByCodes_WhenNoResult_ShouldReturnEmptyList() {
        // 1. 准备测试数据
        Set<String> ticketCodes = new HashSet<>(Arrays.asList(TEST_TICKET_CODE_1, TEST_TICKET_CODE_2));
        when(dao.batchQueryTicketModelByCodes(ticketCodes, TEST_CORP_CODE))
                .thenReturn(Collections.emptyList());

        // 2. 执行测试
        List<TktTicketModel> result = tktTicketModelReadComponent.batchQueryTicketModelByCodes(
                ticketCodes, TEST_CORP_CODE);

        // 3. 验证结果
        assertNotNull("结果不应为null", result);
        assertTrue("结果应该是空列表", result.isEmpty());

        // 4. 验证方法调用
        Mockito.verify(dao).batchQueryTicketModelByCodes(ticketCodes, TEST_CORP_CODE);
    }
} 