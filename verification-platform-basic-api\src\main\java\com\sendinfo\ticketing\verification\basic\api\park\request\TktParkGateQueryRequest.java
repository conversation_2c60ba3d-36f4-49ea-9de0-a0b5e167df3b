package com.sendinfo.ticketing.verification.basic.api.park.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2025/6/9 9:45
 **/
@Getter
@Setter
@ToString
public class TktParkGateQueryRequest implements Serializable {
    private static final long serialVersionUID = 6377927426766480461L;
    /**
     * 景点通道
     */
    @NotNull
    private Integer gateNo;
    /**
     * 景点ID
     */
    @NotNull
    private Long parkZoneId;
    /**
     * 租户编号
     */
    @NotNull
    private String corpCode;
}
