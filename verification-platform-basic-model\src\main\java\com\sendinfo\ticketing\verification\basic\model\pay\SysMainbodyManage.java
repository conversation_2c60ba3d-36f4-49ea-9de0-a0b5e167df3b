package com.sendinfo.ticketing.verification.basic.model.pay;

import com.sendinfo.ticketing.verification.basic.enums.CommonUseFlagEnum;
import com.sendinfo.ticketing.verification.basic.model.pay.properties.SysMainbodyManageDefinitionPropertyKey;
import com.sendinfo.ticketing.verification.common.model.properties.AbstractProperties;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 经营主体支付配置模型
 * 对应数据库表 sys_mainbody_manage
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class SysMainbodyManage extends AbstractProperties<SysMainbodyManageDefinitionPropertyKey> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 经营主体名称
     */
    private String mainbodyName;

    /**
     * 经营主体简称
     */
    private String mainbodyShortName;

    /**
     * 主体商户号
     */
    private String merchantNo;

    /**
     * 支付应用ID(支付中心分配)
     */
    private String merchantSourceNo;

    /**
     * 支付渠道名称
     */
    private String payChannelName;

    /**
     * 支付渠道编号
     */
    private String payChannelCode;

    /**
     * 商户公钥
     */
    private String paycenterPublicKey;

    /**
     * 商户私钥
     */
    private String mchPrivateKey;

    /**
     * 启用状态：T:启用（默认）,F:不启用
     */
    private CommonUseFlagEnum useFlag;

    /**
     * 联系人
     */
    private String linkMan;

    /**
     * 联系电话
     */
    private String linkTel;

    /**
     * logo
     */
    private String logo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 经营主体ID
     */
    private Long mainbodyId;

    /**
     * 经营主体编号
     */
    private String mainbodyNumber;

    /**
     * 结算主体商户号
     */
    private String seettlementMerchantNo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 修改人
     */
    private String modifyBy;

    /**
     * 删除标志F未删除,T已删除
     */
    private String deleted;
} 