package com.sendinfo.ticketing.verification.basic.component.goods;

import com.sendinfo.ticketing.verification.basic.api.context.TicketSyncContext;
import com.sendinfo.ticketing.verification.basic.component.goods.param.UserTicketRelationUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.goods.UserTicketRelation;
import com.sendinfo.ticketing.verification.common.component.UpdateComponent;

/**
 * <AUTHOR>
 * @since 2025-07-18 16:13
 */
public interface UserTicketRelationUpdateComponent extends UpdateComponent<UserTicketRelationUpdateParam, UserTicketRelation> {

    /**
     * 重新计算用户票型关系,并保存
     *
     * @param context 请求入参
     */
    void refreshRelation(TicketSyncContext context);

    /**
     * 修改了票型信息后,更新人票关系的最后修改时间
     *
     * @param context 请求入参
     */
    void refreshOperateTime(TicketSyncContext context);
}
