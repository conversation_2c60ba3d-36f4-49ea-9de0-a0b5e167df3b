package com.sendinfo.ticketing.verification.basic.component.goods.impl;

import com.sendinfo.ticketing.verification.basic.component.goods.GoodsPropertyReadComponent;
import com.sendinfo.ticketing.verification.basic.component.goods.converter.GoodsPropertyConverter;
import com.sendinfo.ticketing.verification.basic.component.goods.param.GoodsPropertyQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.GoodsProperty;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.GoodsPropertyQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dao.GoodsPropertyDao;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.GoodsPropertyDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedCountRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedListRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleRead;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-07-18 17:29
 */
@Getter
@Component("goodsPropertyReadComponent")
public class GoodsPropertyReadComponentImpl implements GoodsPropertyReadComponent,
        DaoBasedSingleRead<Long, GoodsPropertyQueryParam, GoodsProperty, GoodsPropertyDO>,
        DaoBasedListRead<Long, GoodsPropertyQueryParam, GoodsProperty, GoodsPropertyDO, GoodsPropertyQueryArg>,
        DaoBasedCountRead<Long, GoodsPropertyQueryParam, GoodsProperty, GoodsPropertyQueryArg> {

    private final GoodsPropertyDao dao;
    private final GoodsPropertyConverter converter;

    public GoodsPropertyReadComponentImpl(GoodsPropertyDao dao
            , GoodsPropertyConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }

    @Override
    public List<GoodsProperty> queryByTicketIds(List<Long> ticketIds, String corpCode) {
        List<GoodsPropertyDO> goodsPropertyDOS = dao.queryByTicketIds(ticketIds, corpCode);
        return converter.r_ds2ms(goodsPropertyDOS);
    }
}
