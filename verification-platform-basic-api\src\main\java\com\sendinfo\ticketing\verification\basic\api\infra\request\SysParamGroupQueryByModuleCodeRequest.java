/**
 * 根据模块编码查询系统参数分组请求
 *
 * <AUTHOR> 2025-08-01
 */
package com.sendinfo.ticketing.verification.basic.api.infra.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

@Getter
@Setter
@ToString
public class SysParamGroupQueryByModuleCodeRequest implements Serializable {
    private static final long serialVersionUID = -7654321098765432110L;

    /**
     * 模块编码
     */
    @NotBlank(message = "模块编码不能为空")
    @Size(max = 128, message = "模块编码长度不能超过128")
    private String moduleCode;

    /**
     * 企业编码
     */
    @NotBlank(message = "企业编码不能为空")
    @Size(max = 64, message = "企业编码长度不能超过64")
    private String corpCode;
}
