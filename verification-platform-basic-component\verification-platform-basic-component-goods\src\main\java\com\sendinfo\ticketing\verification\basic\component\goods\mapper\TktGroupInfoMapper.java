package com.sendinfo.ticketing.verification.basic.component.goods.mapper;

import com.sendinfo.ticketing.verification.basic.api.goods.request.TktGroupInfoQueryRequest;
import com.sendinfo.ticketing.verification.basic.component.goods.param.TktGroupInfoQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TktGroupInfo;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TktGroupInfoQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TktGroupInfoDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


@Mapper
public interface TktGroupInfoMapper {
    TktGroupInfoMapper INSTANCE = Mappers.getMapper(TktGroupInfoMapper.class);

    // DO -> Model
    TktGroupInfo convert(TktGroupInfoDO dataObject);

    // QueryParam -> QueryArg
    TktGroupInfoQueryArg convert(TktGroupInfoQueryParam param);

    TktGroupInfoQueryParam convert(TktGroupInfoQueryRequest request);

} 