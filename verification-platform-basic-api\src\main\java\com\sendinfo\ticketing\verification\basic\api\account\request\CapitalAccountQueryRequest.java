package com.sendinfo.ticketing.verification.basic.api.account.request;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 资金账户查询请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
public class CapitalAccountQueryRequest implements Serializable {

    /**
     * 企业编码
     */
    @NotNull(message = "企业编码不能为空")
    private String corpCode;

    /**
     * 账户名称
     */
    private String name;

    /**
     * 账户状态
     */
    private Integer status;
} 