package com.sendinfo.ticketing.verification.basic.service.goods.function;

import com.sendinfo.ticketing.verification.basic.component.goods.TktModelParamReadComponent;
import com.sendinfo.ticketing.verification.basic.model.goods.TktModelParam;
import com.sendinfo.ticketing.verification.common.service.stereotype.Function;
import com.sendinfo.ticketing.verification.flow.Hint;
import com.sendinfo.ticketing.verification.flow.Question;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;

import static com.sendinfo.ticketing.verification.basic.service.goods.enums.GoodsAttachmentKey.TKT_MODEL_PARAM_DATA_LIST_ATTACHMENT_KEY;

/**
 * <AUTHOR>
 * @since 2025/7/28 18:31
 **/
@Function("queryTktModelParamFunction")
public class QueryTktModelParamFunction {

    private final TktModelParamReadComponent tktModelParamReadComponent;

    public QueryTktModelParamFunction(TktModelParamReadComponent tktModelParamReadComponent) {
        this.tktModelParamReadComponent = tktModelParamReadComponent;
    }

    public Hint batchQueryTktModelParam(Question<Pair<List<Long>,String>> question) {
        Pair<List<Long>, String> body = question.getBody();
        List<Long> ids = body.getLeft();
        String corpCode = body.getRight();
        List<TktModelParam> tktModelParams = tktModelParamReadComponent.batchQueryTicketModelParamByIds(ids, corpCode);
        question.setAttachment(TKT_MODEL_PARAM_DATA_LIST_ATTACHMENT_KEY, tktModelParams);
        return Hint.gotoNext();
    }
}
