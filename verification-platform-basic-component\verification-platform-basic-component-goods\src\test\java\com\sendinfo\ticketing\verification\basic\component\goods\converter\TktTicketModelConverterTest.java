package com.sendinfo.ticketing.verification.basic.component.goods.converter;

import com.sendinfo.ticketing.verification.basic.component.goods.param.TktTicketModelQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TktTicketModel;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TktTicketModelQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TktTicketModelDO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

/**
 * @Auther: chengjie.li
 * @Date: 2025/6/18 13:00
 */
@RunWith(MockitoJUnitRunner.class)
public class TktTicketModelConverterTest {

    @InjectMocks
    private TktTicketModelConverter tktTicketModelConverter;

    private static final String TEST_CORP_CODE = "TEST_CORP_001";
    private static final Long TEST_TKT_ID = 1L;
    private static final String TEST_TICKET_NAME = "测试票型";
    private static final String TEST_TICKET_CODE = "TEST_TICKET_001";
    private static final String TEST_OUT_TICKET_CODE = "OUT_TEST_001";
    private static final String TEST_ENGLINSH_NAME = "Test Ticket";
    private static final String TEST_BARCODE_TYPE = "MP";
    private static final String TEST_TICKET_KIND = "0";
    private static final String TEST_TICKET_GROUP_CODE = "GROUP_001";
    private static final String TEST_TICKET_GROUP_NAME = "测试分组";
    private static final String TEST_DAYPART_CODE = "DAY_001";
    private static final String TEST_COMPENSATION_FLAG = "T";
    private static final String TEST_USE_FLAG = "T";
    private static final BigDecimal TEST_PRICE = new BigDecimal("100.00");
    private static final String TEST_PINYIN_CODE = "CSPX";
    private static final Integer TEST_SORT = 1;
    private static final Integer TEST_MIN_USE_PEOPLE = 1;
    private static final Integer TEST_MAX_USE_PEOPLE = 10;
    private static final String TEST_CREATE_BY = "testUser";

    /**
     * 测试目的：验证TktTicketModelDO转换为TktTicketModel的功能
     * 测试步骤：
     * 1. 创建完整的TktTicketModelDO对象
     * 2. 调用r_d2m方法进行转换
     * 3. 验证转换结果的正确性
     */
    @Test
    public void testReadDo2Model_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        TktTicketModelDO tktTicketModelDO = createTktTicketModelDO();

        // 2. 执行转换
        TktTicketModel result = tktTicketModelConverter.r_d2m(tktTicketModelDO);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", result);
        assertEquals("企业编码应该一致", TEST_CORP_CODE, result.getCorpCode());
        assertEquals("票型ID应该一致", TEST_TKT_ID, result.getTktId());
        assertEquals("票型名称应该一致", TEST_TICKET_NAME, result.getTicketName());
        assertEquals("票型编码应该一致", TEST_TICKET_CODE, result.getTicketCode());
        assertEquals("外部票型编码应该一致", TEST_OUT_TICKET_CODE, result.getOutTicketCode());
        assertEquals("英文名称应该一致", TEST_ENGLINSH_NAME, result.getEnglinshName());
        assertEquals("条码类型应该一致", TEST_BARCODE_TYPE, result.getBarcodeType());
        assertEquals("票种应该一致", TEST_TICKET_KIND, result.getTicketKind());
        assertEquals("分组编号应该一致", TEST_TICKET_GROUP_CODE, result.getTicketGroupCode());
        assertEquals("分组名称应该一致", TEST_TICKET_GROUP_NAME, result.getTicketGroupName());
        assertEquals("分时库存码应该一致", TEST_DAYPART_CODE, result.getDaypartCode());
        assertEquals("是否补差价应该一致", TEST_COMPENSATION_FLAG, result.getCompensationFlag());
        assertEquals("启用状态应该一致", TEST_USE_FLAG, result.getUseFlag());
        assertEquals("实际销售价格应该一致", TEST_PRICE, result.getPrice());
        assertEquals("拼音简码应该一致", TEST_PINYIN_CODE, result.getPinyinCode());
        assertEquals("排序应该一致", TEST_SORT, result.getSort());
        assertEquals("最小使用人数应该一致", TEST_MIN_USE_PEOPLE, result.getMinUsePeople());
        assertEquals("最大使用人数应该一致", TEST_MAX_USE_PEOPLE, result.getMaxUsePeople());
        assertEquals("创建人应该一致", TEST_CREATE_BY, result.getCreateBy());
        assertEquals("修改人应该一致", TEST_CREATE_BY, result.getModifyBy());
    }

    /**
     * 测试目的：验证TktTicketModelQueryParam转换为TktTicketModelQueryArg的功能
     * 测试步骤：
     * 1. 创建完整的TktTicketModelQueryParam对象
     * 2. 调用r_p2a方法进行转换
     * 3. 验证转换结果的正确性
     */
    @Test
    public void testReadQueryParam2QueryArg_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        TktTicketModelQueryParam queryParam = createTktTicketModelQueryParam();

        // 2. 执行转换
        TktTicketModelQueryArg queryArg = tktTicketModelConverter.r_p2a(queryParam);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", queryArg);
        assertEquals("企业编码应该一致", TEST_CORP_CODE, queryArg.getCorpCode());
        assertEquals("票型编码应该一致", TEST_TICKET_CODE, queryArg.getTicketCode());
    }

    private TktTicketModelQueryParam createTktTicketModelQueryParam() {
        TktTicketModelQueryParam queryParam = new TktTicketModelQueryParam();
        queryParam.setCorpCode(TEST_CORP_CODE);
        queryParam.setTicketCode(TEST_TICKET_CODE);
        return queryParam;
    }

    private TktTicketModelDO createTktTicketModelDO() {
        TktTicketModelDO ticketModelDO = new TktTicketModelDO();
        ticketModelDO.setCorpCode(TEST_CORP_CODE);
        ticketModelDO.setTktId(TEST_TKT_ID);
        ticketModelDO.setTicketName(TEST_TICKET_NAME);
        ticketModelDO.setTicketCode(TEST_TICKET_CODE);
        ticketModelDO.setOutTicketCode(TEST_OUT_TICKET_CODE);
        ticketModelDO.setEnglinshName(TEST_ENGLINSH_NAME);
        ticketModelDO.setBarcodeType(TEST_BARCODE_TYPE);
        ticketModelDO.setTicketKind(TEST_TICKET_KIND);
        ticketModelDO.setTicketGroupCode(TEST_TICKET_GROUP_CODE);
        ticketModelDO.setTicketGroupName(TEST_TICKET_GROUP_NAME);
        ticketModelDO.setDaypartCode(TEST_DAYPART_CODE);
        ticketModelDO.setCompensationFlag(TEST_COMPENSATION_FLAG);
        ticketModelDO.setUseFlag(TEST_USE_FLAG);
        ticketModelDO.setPrice(TEST_PRICE);
        ticketModelDO.setPinyinCode(TEST_PINYIN_CODE);
        ticketModelDO.setSort(TEST_SORT);
        ticketModelDO.setMinUsePeople(TEST_MIN_USE_PEOPLE);
        ticketModelDO.setMaxUsePeople(TEST_MAX_USE_PEOPLE);
        ticketModelDO.setCreateBy(TEST_CREATE_BY);
        ticketModelDO.setModifyBy(TEST_CREATE_BY);
        return ticketModelDO;
    }
} 