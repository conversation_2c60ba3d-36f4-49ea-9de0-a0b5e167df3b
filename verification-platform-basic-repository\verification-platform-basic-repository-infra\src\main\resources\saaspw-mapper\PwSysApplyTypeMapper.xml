<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sendinfo.ticketing.verification.basic.repository.infra.dao.PwSysApplyTypeDao">

    <!-- ResultMap -->
    <resultMap id="BaseResultMap" type="com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.PwSysApplyTypeDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="corp_code" property="corpCode" jdbcType="VARCHAR"/>
        <result column="subsystem_id" property="subsystemId" jdbcType="INTEGER"/>
        <result column="model_type" property="modelType" jdbcType="CHAR"/>
        <result column="model_code" property="modelCode" jdbcType="VARCHAR"/>
        <result column="model_name" property="modelName" jdbcType="VARCHAR"/>
        <result column="use_flag" property="useFlag" jdbcType="CHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="modify_by" property="modifyBy" jdbcType="VARCHAR"/>
        <result column="deleted" property="deleted" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 表名 -->
    <sql id="tableName">sys_apply_type</sql>

    <!-- Reusable Column List -->
    <sql id="columns">
        `id`,
        `corp_code`,
        `subsystem_id`,
        `model_type`,
        `model_code`,
        `model_name`,
        `use_flag`,
        `create_time`,
        `modify_time`,
        `create_by`,
        `modify_by`,
        `deleted`
    </sql>

    <!-- insert -->
    <insert id="insert" parameterType="com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.PwSysApplyTypeDO"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into <include refid="tableName"/> (
            `corp_code`,`subsystem_id`,`model_type`,`model_code`,`model_name`,`use_flag`
            ,`create_time`,`create_by`, `modify_by`, `deleted`
        )
        values (
            #{corpCode,jdbcType=VARCHAR},
            #{subsystemId,jdbcType=INTEGER},
            #{modelType,jdbcType=CHAR},
            #{modelCode,jdbcType=VARCHAR},
            #{modelName,jdbcType=VARCHAR},
            #{useFlag,jdbcType=CHAR},
            now(),
            #{createBy,jdbcType=VARCHAR},
            #{modifyBy,jdbcType=VARCHAR},
            #{deleted,jdbcType=VARCHAR}
        )
    </insert>

    <!-- queryById -->
    <select id="queryById" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="columns"/>
        from <include refid="tableName"/>
        where `id` = #{id,jdbcType=BIGINT} and `deleted` = 'F'
    </select>

    <!-- updateByArg -->
    <update id="updateByArg" parameterType="com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwSysApplyTypeUpdateArg">
        update <include refid="tableName"/>
        <set>
            <if test="applyTypeName != null">
                `model_name` = #{applyTypeName,jdbcType=VARCHAR},
            </if>
            <if test="applyTypeCode != null">
                `model_code` = #{applyTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="subsystemId != null">
                `subsystem_id` = #{subsystemId,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                `use_flag` = #{status,jdbcType=CHAR},
            </if>
            <if test="sortOrder != null">
                `sort_order` = #{sortOrder,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                `remark` = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="modifyBy != null">
                `modify_by` = #{modifyBy,jdbcType=VARCHAR},
            </if>
            `modify_time` = CURRENT_TIMESTAMP
        </set>
        where `id` = #{id,jdbcType=BIGINT}
        and `deleted` = 'F'
    </update>

    <!-- softDeleteByArg -->
    <update id="softDeleteByArg" parameterType="com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwSysApplyTypeDeleteArg">
        <!-- 该方法不可用，由于rds配置用deleted字段做路由，set delete='T'不被允许 -->
        update <include refid="tableName"/>
        set `modify_by` = #{modifyBy,jdbcType=VARCHAR},
            `modify_time` = now()
        where `id` = #{id,jdbcType=BIGINT}
    </update>

    <!-- deleteByArg (物理删除) -->
    <delete id="deleteByArg" parameterType="com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwSysApplyTypeDeleteArg">
        DELETE FROM <include refid="tableName"/>
        where `id` = #{id,jdbcType=BIGINT}
    </delete>

    <!-- countByArg -->
    <select id="countByArg" resultType="java.lang.Integer" parameterType="com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwSysApplyTypeQueryArg">
        select count(1)
        from <include refid="tableName"/>
        <where>
            and `deleted` = 'F'
            <if test="corpCode != null">
                and `corp_code` = #{corpCode,jdbcType=VARCHAR}
            </if>
            <if test="subsystemId != null">
                and `subsystem_id` = #{subsystemId,jdbcType=INTEGER}
            </if>
            <if test="modelType != null">
                and `model_type` = #{modelType,jdbcType=CHAR}
            </if>
            <if test="modelCode != null">
                and `model_code` = #{modelCode,jdbcType=VARCHAR}
            </if>
            <if test="modelName != null">
                and `model_name` like concat('%', #{modelName,jdbcType=VARCHAR}, '%')
            </if>
        </where>
    </select>

    <!-- queryByArg -->
    <select id="queryByArg" resultMap="BaseResultMap" parameterType="com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwSysApplyTypeQueryArg">
        select
        <include refid="columns"/>
        from <include refid="tableName"/>
        <where>
            and `deleted` = 'F'
            <if test="corpCode != null">
                and `corp_code` = #{corpCode,jdbcType=VARCHAR}
            </if>
            <if test="subsystemId != null">
                and `subsystem_id` = #{subsystemId,jdbcType=INTEGER}
            </if>
            <if test="modelType != null">
                and `model_type` = #{modelType,jdbcType=CHAR}
            </if>
            <if test="modelCode != null">
                and `model_code` = #{modelCode,jdbcType=VARCHAR}
            </if>
            <if test="modelName != null">
                and `model_name` like concat('%', #{modelName,jdbcType=VARCHAR}, '%')
            </if>
        </where>
        <if test="sortItems != null and !sortItems.isEmpty()">
            order by
            <foreach collection="sortItems" separator="," item="sortItem">
                ${sortItem.column} ${sortItem.sortType}
            </foreach>
        </if>
        <if test="offset != null and limit != null">
            limit #{offset}, #{limit}
        </if>
    </select>

    <!-- queryBySubSystemId -->
    <select id="queryBySubSystemId" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
        <include refid="columns"/>
        from <include refid="tableName"/>
        where `use_flag` = 'T'
        and `deleted` = 'F'
        and `subsystem_id` = #{subsystemId,jdbcType=INTEGER}
        order by `id`
    </select>

</mapper>