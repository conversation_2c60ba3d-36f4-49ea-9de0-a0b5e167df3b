/**
 * 选项类型参数值声明模型
 *
 * <AUTHOR> 2025-08-04
 */
package com.sendinfo.ticketing.verification.basic.service.infra.processor.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 选项类型参数值声明模型
 * 对应JSON格式：[{"value":"参数值1","label":"显示文本1","order":2,"default":false}]
 * 适用于RADIO、CHECKBOX、SELECT、MULTI_SELECT类型
 */
@Getter
@Setter
@ToString
public class OptionValueStatement {

    /**
     * 选项列表
     */
    private List<Option> options;

    /**
     * 选项模型
     */
    @Getter
    @Setter
    @ToString
    public static class Option {
        /**
         * 参数值
         */
        private String value;

        /**
         * 显示文本
         */
        private String label;

        /**
         * 排序
         */
        private Integer order;

        /**
         * 是否为默认选项
         */
        private Boolean isDefault;
    }
}
