package com.sendinfo.ticketing.verification.basic.component.infra;

import java.util.List;

import com.sendinfo.ticketing.verification.basic.component.infra.param.PwSysFunctionsQueryParam;
import com.sendinfo.ticketing.verification.basic.model.infra.PwSysFunctions;
import com.sendinfo.ticketing.verification.common.component.ReadComponent;

/**
 * 系统功能模块查询组件
 *
 * <AUTHOR> 2025-07-24
 */
public interface PwSysFunctionsReadComponent extends ReadComponent<Long, PwSysFunctionsQueryParam, PwSysFunctions> {

    /**
     * 根据子系统ID查询功能模块列表
     *
     * @param subsystemId 子系统ID
     * @return 功能模块列表
     */
    List<PwSysFunctions> queryBySubSystemId(Integer subsystemId);

    /**
     * 根据父ID列表查询功能模块列表
     *
     * @param parentIds 父ID列表
     * @return 功能模块列表
     */
    List<PwSysFunctions> queryByParentIds(List<Long> parentIds);

    /**
     * 查询已征用的功能模块列表
     *
     * @param corpCode    公司代码
     * @param subsystemId
     * @return 已征用的功能模块列表
     */
    List<PwSysFunctions> queryHasRequisitionList(String corpCode, Integer subsystemId);
}
