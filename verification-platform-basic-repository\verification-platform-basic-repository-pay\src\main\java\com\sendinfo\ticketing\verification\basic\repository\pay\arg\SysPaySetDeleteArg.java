package com.sendinfo.ticketing.verification.basic.repository.pay.arg;

import com.sendinfo.ticketing.verification.common.repository.arg.AbstractDeleteArg;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 支付标签删除参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class SysPaySetDeleteArg extends AbstractDeleteArg {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 修改人
     */
    private String modifyBy;
} 