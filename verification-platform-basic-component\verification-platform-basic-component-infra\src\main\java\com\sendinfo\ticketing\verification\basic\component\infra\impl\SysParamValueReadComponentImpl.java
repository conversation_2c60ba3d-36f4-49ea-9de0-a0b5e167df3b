package com.sendinfo.ticketing.verification.basic.component.infra.impl;

import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

import com.sendinfo.ticketing.verification.basic.component.infra.SysParamValueReadComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.converter.SysParamValueConverter;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamValueQueryParam;
import com.sendinfo.ticketing.verification.basic.model.infra.SysParamValue;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamValueQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.SysParamValueDao;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.SysParamValueDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedCountRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedPageRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleRead;

/**
 * 系统参数值读取组件实现
 *
 * <AUTHOR> 2025-05-20 15:40:00
 */
@Component("sysParamValueReadComponent")
@Getter
public class SysParamValueReadComponentImpl
        implements SysParamValueReadComponent,
        DaoBasedPageRead<Long, SysParamValueQueryParam, SysParamValue, SysParamValueDO, SysParamValueQueryArg>,
        DaoBasedSingleRead<Long, SysParamValueQueryParam, SysParamValue, SysParamValueDO>,
        DaoBasedCountRead<Long, SysParamValueQueryParam, SysParamValue, SysParamValueQueryArg> {

    private final SysParamValueDao dao;
    private final SysParamValueConverter converter;

    public SysParamValueReadComponentImpl(SysParamValueDao dao, SysParamValueConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }

    @Override
    public SysParamValue queryByParamCodeAndCorpCode(String paramCode, String corpCode) {
        return Optional.ofNullable(dao.queryByParamCodeAndCorpCode(paramCode, corpCode))
                .map(converter::r_d2m)
                .orElse(null);
    }

    @Override
    public List<SysParamValue> queryByParamKeyList(List<String> paramKeyList) {
        List<SysParamValueDO> dataObjects = dao.queryByParamKeyList(paramKeyList);
        return converter.r_ds2ms(dataObjects);
    }

    @Override
    public List<SysParamValue> queryByParamKeyList(String corpCode, List<String> paramKeyList) {
        List<SysParamValueDO> dataObjects = dao.queryByParamKeyList(corpCode, paramKeyList);
        return converter.r_ds2ms(dataObjects);
    }

    @Override
    public SysParamValue queryByParamKeyAndTenantCorpCode(String paramKey, String tenantCorpCode) {
        return Optional.ofNullable(dao.queryByParamKeyAndTenantCorpCode(paramKey, tenantCorpCode))
                .map(converter::r_d2m)
                .orElse(null);
    }

}