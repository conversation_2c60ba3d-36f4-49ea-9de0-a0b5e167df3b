package com.sendinfo.ticketing.verification.basic.service.auth.impl;

import com.sendinfo.ticketing.verification.basic.api.auth.UserSessionDataGetRequest;
import com.sendinfo.ticketing.verification.basic.api.auth.UserSessionDataReadService;
import com.sendinfo.ticketing.verification.basic.exception.VerificationBizRuntimeException;
import com.sendinfo.ticketing.verification.basic.model.auth.UserSessionData;
import com.sendinfo.ticketing.verification.basic.model.auth.error.AuthErrorDef;
import com.sendinfo.ticketing.verification.basic.service.auth.cache.UserSessionDataCacheService;
import com.sendinfo.ticketing.verification.basic.service.auth.jwt.JwtIdentity;
import com.sendinfo.ticketing.verification.basic.service.auth.jwt.JwtTokenParseService;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;
import com.sendinfo.ticketing.verification.common.api.result.Results;
import com.sendinfo.ticketing.verification.common.model.exception.CheckedBizException;
import com.sendinfo.ticketing.verification.common.model.exception.UncheckedBizException;
import com.sendinfo.ticketing.verification.common.service.support.ApiCommonFailedBiActions;
import com.sendinfo.ticketing.verification.flow.FlowAgentBuilder;
import com.sendinfo.ticketing.verification.flow.Hint;
import io.jsonwebtoken.Claims;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import static com.sendinfo.ticketing.verification.basic.service.auth.enums.AuthAttachmentKey.JW_CLAIMS_ATTACHMENT_KEY;
import static com.sendinfo.ticketing.verification.basic.service.auth.enums.AuthAttachmentKey.USER_SESSION_ATTACHMENT_KEY;

/**
 * <AUTHOR>
 * @since 2025-07-23 15:05:13
 */
@Service("userSessionDataReadService")
@Slf4j
public class UserSessionDataReadServiceImpl implements UserSessionDataReadService {
    private final FlowAgentBuilder flowAgentBuilder;
    private final JwtTokenParseService jwtTokenParseService;
    private final UserSessionDataCacheService userSessionDataCacheService;

    public UserSessionDataReadServiceImpl(FlowAgentBuilder flowAgentBuilder, JwtTokenParseService jwtTokenParseService, UserSessionDataCacheService userSessionDataCacheService) {
        this.flowAgentBuilder = flowAgentBuilder;
        this.jwtTokenParseService = jwtTokenParseService;
        this.userSessionDataCacheService = userSessionDataCacheService;
    }

    @Override
    public ResultModel<UserSessionData> getUserSessionDataFromOldPwJwtToken(UserSessionDataGetRequest request) {
        return flowAgentBuilder.<UserSessionDataGetRequest, ResultModel<UserSessionData>>validateThenChooseBuilder().appendLogicAction(q -> {
            Pair<String, Claims> pair = jwtTokenParseService.getClaimsForJwtToken(request.getHeaders(), JwtIdentity.OLD_PW);
            q.setAttachment(JW_CLAIMS_ATTACHMENT_KEY, pair);
            return Hint.gotoNext();
        }).appendLogicAction(q -> {
            Pair<String, Claims> pair = q.getAttachment(JW_CLAIMS_ATTACHMENT_KEY);
            String userId = pair.getValue().getSubject();
            log.info("[UserSessionDataReadServiceImpl] getUserSessionDataFromOldPwJwtToken user id ={}", userId);
            UserSessionData data = userSessionDataCacheService.getUserSessionByUserIdFromOldPw(pair.getKey(), userId, request.getJwtTokenSource());
            q.setAttachment(USER_SESSION_ATTACHMENT_KEY, data);
            log.info("[UserSessionDataReadServiceImpl] getUserSessionDataFromOldPwJwtToken data.userName = {}", data.getName());
            return Hint.gotoNext();
        }).withSuccessfulAction(q -> {
            UserSessionData data = q.getAttachment(USER_SESSION_ATTACHMENT_KEY);
            return Results.success(data);
        }).withFailedBiAction(ApiCommonFailedBiActions::reasonMappingToResult).withExceptionBiAction((q, th) -> {
            log.error("[UserSessionDataReadServiceImpl] getUserSessionDataFromOldPwJwtToken error.question is {}", q, th);
            if (th instanceof UncheckedBizException) {
                UncheckedBizException ae = (UncheckedBizException) th;
                return Results.fail(ae.getErrorCode(), ae.getMessage());
            } else if (th instanceof CheckedBizException) {
                CheckedBizException ae = (CheckedBizException) th;
                return Results.fail(ae.getErrorCode(), ae.getMessage());
            }
            return Results.fail(AuthErrorDef.GET_USER_SESSION_DATA_FROM_OLD_PW_JWT_TOKEN);
        }).rethrowException(throwable -> false).build().prompt(request).getResult();

    }
}
