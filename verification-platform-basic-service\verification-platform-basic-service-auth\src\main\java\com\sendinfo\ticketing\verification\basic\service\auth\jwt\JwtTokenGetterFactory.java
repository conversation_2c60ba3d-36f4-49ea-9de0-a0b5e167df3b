package com.sendinfo.ticketing.verification.basic.service.auth.jwt;

import org.springframework.stereotype.Component;

import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2025-07-23 14:21:33
 */
@Component("jwtTokenGetterFactory")
public class JwtTokenGetterFactory {


    private final Map<JwtIdentity, JwtTokenGetter> id2Getter = new EnumMap<>(JwtIdentity.class);

    public JwtTokenGetterFactory(List<JwtTokenGetter> jwtTokenGetters) {
        jwtTokenGetters.forEach(jwtTokenGetter -> {
            id2Getter.put(jwtTokenGetter.support(), jwtTokenGetter);
        });
    }

    /**
     * 获取token
     *
     * @param headers 请求
     * @return token
     */
    public Optional<String> getJwtToken(Map<String, String> headers, JwtIdentity identity) {
        return id2Getter.get(identity).getJwtToken(headers);
    }

}
