package com.sendinfo.ticketing.verification.basic.api.customer.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * travel_tax信息查询请求对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/16
 */
@Getter
@Setter
@ToString
public class TravelTaxQueryRequest implements Serializable {

    /**
     * 企业编码
     */
    private String corpCode;
    /**
     * 旅行社ID
     */
    private Long travelId;
    /**
     * 实际抬头
     */
    private String taxTitle;
    /**
     * 纳税人识别号
     */
    private String taxNo;
} 