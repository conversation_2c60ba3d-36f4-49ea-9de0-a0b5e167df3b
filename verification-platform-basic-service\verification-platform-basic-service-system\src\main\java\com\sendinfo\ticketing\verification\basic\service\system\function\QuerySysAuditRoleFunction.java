package com.sendinfo.ticketing.verification.basic.service.system.function;

import com.sendinfo.ticketing.verification.basic.api.system.request.SysAuditRoleQueryRequest;
import com.sendinfo.ticketing.verification.basic.component.system.SysAuditRoleReadComponent;
import com.sendinfo.ticketing.verification.basic.component.system.converter.SysAuditRoleConverter;
import com.sendinfo.ticketing.verification.basic.model.system.SysAuditRole;
import com.sendinfo.ticketing.verification.basic.model.system.error.SystemErrorDef;
import com.sendinfo.ticketing.verification.basic.service.system.enums.SystemAttachmentKey;
import com.sendinfo.ticketing.verification.common.service.function.QuerySingleByIdFunction;
import com.sendinfo.ticketing.verification.common.service.stereotype.Function;
import com.sendinfo.ticketing.verification.common.service.support.QuerySingleByIdLogicAction;
import com.sendinfo.ticketing.verification.flow.Hint;
import com.sendinfo.ticketing.verification.flow.Question;
import com.sendinfo.ticketing.verification.flow.function.LogicAction;
import lombok.Getter;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Set;

/**
 * 票型角色查询功能
 * <AUTHOR>
 */
@Getter
@Function("querySysAuditRoleFunction")
public class QuerySysAuditRoleFunction implements QuerySingleByIdFunction<Integer> {

    private final SysAuditRoleReadComponent sysAuditRoleReadComponent;
    private final SysAuditRoleConverter converter;

    private final LogicAction<Integer> querySingleById;

    public QuerySysAuditRoleFunction(SysAuditRoleReadComponent sysAuditRoleReadComponent, SysAuditRoleConverter converter) {
        this.sysAuditRoleReadComponent = sysAuditRoleReadComponent;
        this.converter = converter;
        querySingleById = new QuerySingleByIdLogicAction<>(this.sysAuditRoleReadComponent,
                SystemAttachmentKey.SYS_AUDIT_ROLE_ATTACHMENT_KEY, SystemErrorDef.QUERY_SYS_ROLE_ERROR);
    }

    /**
     * 查询票型角色列表
     *
     * @param question 查询参数
     * @return Hint
     */
    public Hint querySysAuditRoles(Question<SysAuditRoleQueryRequest> question) {
        SysAuditRoleQueryRequest req = question.getBody();
        List<SysAuditRole> sysAuditRoleList = sysAuditRoleReadComponent.querySysAuditRoles(converter.r_r2p(req));
        question.setAttachment(SystemAttachmentKey.SYS_AUDIT_ROLE_DATA_LIST_ATTACHMENT_KEY, sysAuditRoleList);
        
        return Hint.gotoNext();
    }

    public Hint querySysAuditRoleByIds(Question<Pair<Set<Integer>, String>> question) {
        Pair<Set<Integer>, String> pair = question.getBody();
        Set<Integer> ids = pair.getLeft();
        String corpCode = pair.getRight();
        List<SysAuditRole> sysAuditRoleList = sysAuditRoleReadComponent.querySysAuditRoleByIds(ids, corpCode);
        question.setAttachment(SystemAttachmentKey.SYS_AUDIT_ROLE_DATA_LIST_ATTACHMENT_KEY, sysAuditRoleList);

        return Hint.gotoSuccess();
    }
} 