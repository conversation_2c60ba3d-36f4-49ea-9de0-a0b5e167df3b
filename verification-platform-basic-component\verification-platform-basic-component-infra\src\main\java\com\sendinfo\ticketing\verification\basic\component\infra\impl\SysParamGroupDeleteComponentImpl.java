package com.sendinfo.ticketing.verification.basic.component.infra.impl;

import lombok.Getter;
import org.springframework.stereotype.Component;

import com.sendinfo.ticketing.verification.basic.component.infra.SysParamGroupDeleteComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.converter.SysParamGroupConverter;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamGroupDeleteParam;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamGroupDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.SysParamGroupDao;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleDelete;

/**
 * 系统参数分组删除组件实现
 * 
 * <AUTHOR> 2025-07-22 11:45:32
 */
@Component("sysParamGroupDeleteComponent")
@Getter
public class SysParamGroupDeleteComponentImpl
        implements SysParamGroupDeleteComponent,
        DaoBasedSingleDelete<Long, SysParamGroupDeleteParam, SysParamGroupDeleteArg> {

    private final SysParamGroupDao dao;
    private final SysParamGroupConverter converter;

    public SysParamGroupDeleteComponentImpl(SysParamGroupDao dao, SysParamGroupConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }
}
