package com.sendinfo.ticketing.verification.basic.repository.pay.dao;

import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageSubMerchantsDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageSubMerchantsQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageSubMerchantsUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysMainbodyManageSubMerchantsDO;
import com.sendinfo.ticketing.verification.common.repository.BatchInsertDAO;
import com.sendinfo.ticketing.verification.common.repository.CountableDAO;
import com.sendinfo.ticketing.verification.common.repository.GenericDAO;
import com.sendinfo.ticketing.verification.common.repository.QueryableDAO;

/**
 * 经营主体子商户数据访问接口
 * 提供sys_mainbody_manage_sub_merchants表的完整CRUD操作
 * 支持租户隔离、软删除、批量插入等功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface SysMainbodyManageSubMerchantsDao extends
        GenericDAO<Long, SysMainbodyManageSubMerchantsDO, SysMainbodyManageSubMerchantsUpdateArg, SysMainbodyManageSubMerchantsDeleteArg>,
        CountableDAO<SysMainbodyManageSubMerchantsQueryArg>,
        QueryableDAO<SysMainbodyManageSubMerchantsQueryArg, SysMainbodyManageSubMerchantsDO>,
        BatchInsertDAO<Long, SysMainbodyManageSubMerchantsDO> {

} 