package com.sendinfo.ticketing.verification.basic.repository.pay.arg;

import com.sendinfo.ticketing.verification.common.repository.arg.AbstractQueryArg;
import com.sendinfo.ticketing.verification.common.repository.arg.Pageable;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 经营主体支付配置关联子商户表查询参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class SysMainbodyManageMmSubMerchantsQueryArg extends AbstractQueryArg implements Pageable {

    /**
     * 企业编码
     * 用于租户隔离，必填字段
     */
    private String corpCode;

    /**
     * 经营主体支付配置ID
     */
    private Long mainbodyManageId;

    /**
     * 子商户ID
     * 用于查询指定子商户的关联记录
     */
    private Long subMerchantsId;

    /**
     * 子商户名称
     * 用于根据子商户名称进行模糊查询
     */
    private String subMerchantsName;

    /**
     * 子商户号
     * 用于根据商户号进行精确查询
     */
    private String merchantNo;


    /**
     * offset
     */
    private Integer offset;

    /**
     * limit
     */
    private Integer limit;
} 