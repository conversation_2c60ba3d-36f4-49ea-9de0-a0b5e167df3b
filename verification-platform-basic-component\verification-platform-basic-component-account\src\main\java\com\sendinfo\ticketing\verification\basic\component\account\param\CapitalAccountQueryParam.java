package com.sendinfo.ticketing.verification.basic.component.account.param;

import com.sendinfo.ticketing.verification.common.component.param.AbstractQueryParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 资金账户查询参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class CapitalAccountQueryParam extends AbstractQueryParam {
    /**
     * 企业编码
     */
    private String corpCode;
    /**
     * 账户名称
     */
    private String name;
    /**
     * 账户状态
     */
    private Integer status;
    /**
     * 旅行社ID
     */
    private Long travelId;
} 