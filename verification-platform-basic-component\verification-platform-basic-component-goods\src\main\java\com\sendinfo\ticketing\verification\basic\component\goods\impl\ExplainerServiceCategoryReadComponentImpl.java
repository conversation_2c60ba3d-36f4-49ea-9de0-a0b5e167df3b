package com.sendinfo.ticketing.verification.basic.component.goods.impl;

import com.sendinfo.ticketing.verification.basic.component.goods.ExplainerServiceCategoryReadComponent;
import com.sendinfo.ticketing.verification.basic.component.goods.converter.ExplainerServiceCategoryConverter;
import com.sendinfo.ticketing.verification.basic.component.goods.param.ExplainerServiceCategoryQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.ExplainerServiceCategory;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.ExplainerServiceCategoryQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dao.ExplainerServiceCategoryDao;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.ExplainerServiceCategoryDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedCountRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedListRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleRead;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 服务类目读取组件实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("explainerServiceCategoryReadComponent")
@Getter
public class ExplainerServiceCategoryReadComponentImpl implements
        ExplainerServiceCategoryReadComponent,
        DaoBasedSingleRead<Long, ExplainerServiceCategoryQueryParam, ExplainerServiceCategory, ExplainerServiceCategoryDO>,
        DaoBasedListRead<Long, ExplainerServiceCategoryQueryParam, ExplainerServiceCategory, ExplainerServiceCategoryDO, ExplainerServiceCategoryQueryArg>,
        DaoBasedCountRead<Long, ExplainerServiceCategoryQueryParam, ExplainerServiceCategory, ExplainerServiceCategoryQueryArg> {

    private final ExplainerServiceCategoryDao dao;
    private final ExplainerServiceCategoryConverter converter;

    public ExplainerServiceCategoryReadComponentImpl(ExplainerServiceCategoryDao dao, ExplainerServiceCategoryConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }

    @Override
    public List<ExplainerServiceCategory> queryServiceCategoryList(ExplainerServiceCategoryQueryParam queryParam) {
        List<ExplainerServiceCategoryDO> serviceCategoryDOList = dao.queryServiceCategoryList(converter.r_p2a(queryParam));
        return converter.r_ds2ms(serviceCategoryDOList);
    }
}