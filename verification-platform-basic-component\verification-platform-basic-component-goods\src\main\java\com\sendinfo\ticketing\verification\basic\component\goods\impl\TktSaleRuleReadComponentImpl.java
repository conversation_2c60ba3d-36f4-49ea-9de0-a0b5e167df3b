package com.sendinfo.ticketing.verification.basic.component.goods.impl;


import com.sendinfo.ticketing.verification.basic.component.goods.TktSaleRuleReadComponent;
import com.sendinfo.ticketing.verification.basic.component.goods.converter.TktSaleRuleConverter;
import com.sendinfo.ticketing.verification.basic.component.goods.param.TktSaleRuleQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TktSaleRule;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TktSaleRuleQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dao.TktSaleRuleDao;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TktSaleRuleDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedCountRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedListRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleRead;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

@Getter
@Component("tktSaleRuleReadComponent")
public class TktSaleRuleReadComponentImpl implements TktSaleRuleReadComponent,
        DaoBasedSingleRead<Long, TktSaleRuleQueryParam, TktSaleRule, TktSaleRuleDO>,
        DaoBasedCountRead<Long, TktSaleRuleQueryParam, TktSaleRule, TktSaleRuleQueryArg>,
        DaoBasedListRead<Long, TktSaleRuleQueryParam, TktSaleRule, TktSaleRuleDO, TktSaleRuleQueryArg> {

    private final TktSaleRuleDao dao;
    private final TktSaleRuleConverter converter;

    public TktSaleRuleReadComponentImpl(TktSaleRuleDao dao, TktSaleRuleConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }

    @Override
    public TktSaleRule querySaleRuleByTicketId(Long ticketId, String corpCode) {
        return converter.r_d2m(dao.querySaleRuleByTicketId(ticketId, corpCode));
    }

    @Override
    public List<TktSaleRule> batchQuerySaleRuleByTicketIds(Set<Long> ticketIds, String corpCode) {
        return converter.r_ds2ms(dao.batchQuerySaleRuleByTicketIds(ticketIds, corpCode));
    }
}