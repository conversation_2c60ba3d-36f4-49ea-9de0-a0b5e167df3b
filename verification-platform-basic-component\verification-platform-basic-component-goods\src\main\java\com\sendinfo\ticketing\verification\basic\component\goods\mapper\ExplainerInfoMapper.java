package com.sendinfo.ticketing.verification.basic.component.goods.mapper;

import com.sendinfo.ticketing.verification.basic.api.goods.request.ExplainerInfoQueryRequest;
import com.sendinfo.ticketing.verification.basic.component.goods.param.ExplainerInfoQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.ExplainerInfo;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.ExplainerInfoQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.ExplainerInfoDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 讲解人对象转换映射器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface ExplainerInfoMapper {

    ExplainerInfoMapper INSTANCE = Mappers.getMapper(ExplainerInfoMapper.class);

    /**
     * 查询参数转换为查询参数
     *
     * @param queryParam 查询参数
     * @return 查询参数
     */
    @Mapping(target = "status", expression = "java(queryParam.getStatus() != null ? queryParam.getStatus().getCode() : null)")
    @Mapping(target = "useFlag", expression = "java(queryParam.getUseFlag() != null ? queryParam.getUseFlag().getCode() : null)")
    ExplainerInfoQueryArg convert(ExplainerInfoQueryParam queryParam);

    /**
     * 数据对象转换为模型对象
     *
     * @param dataObject 数据对象
     * @return 模型对象
     */
    @Mapping(target = "status", expression = "java(com.sendinfo.ticketing.verification.basic.enums.ExplainerStatusEnum.ofCode(dataObject.getStatus()))")
    @Mapping(target = "useFlag", expression = "java(com.sendinfo.ticketing.verification.basic.enums.ExplainerUserFlagEnum.ofCode(dataObject.getUseFlag()))")
    ExplainerInfo convert(ExplainerInfoDO dataObject);

    /**
     * 查询参数转换为查询参数
     *
     * @param queryRequest  查询参数
     * @return  查询参数
     */
    @Mapping(target = "status", expression = "java(com.sendinfo.ticketing.verification.basic.enums.ExplainerStatusEnum.ofCode(queryRequest.getStatus()))")
    @Mapping(target = "useFlag", expression = "java(com.sendinfo.ticketing.verification.basic.enums.ExplainerUserFlagEnum.ofCode(queryRequest.getUseFlag()))")
    ExplainerInfoQueryParam convert(ExplainerInfoQueryRequest queryRequest);
} 