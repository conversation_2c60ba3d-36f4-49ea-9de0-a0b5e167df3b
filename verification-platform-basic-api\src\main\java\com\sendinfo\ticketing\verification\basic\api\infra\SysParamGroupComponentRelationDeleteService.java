package com.sendinfo.ticketing.verification.basic.api.infra;

import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamGroupComponentRelationCreateRequest;
import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamGroupComponentRelationDeleteRequest;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

/**
 * <AUTHOR> 2025-07-29 01:42:03
 */
public interface SysParamGroupComponentRelationDeleteService {


    /**
     * 删除系统参数组组件关系（组解绑组件）
     *
     * @param request 删除请求
     * @return 参数ID
     */
    ResultModel<Boolean> deleteSysParamGroupComponentRelation(SysParamGroupComponentRelationDeleteRequest request);
}
