package com.sendinfo.ticketing.verification.basic.component.goods.mapper;

import com.sendinfo.ticketing.verification.basic.component.goods.param.TktTicketModelQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TktTicketModel;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TktTicketModelQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TktTicketModelDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Auther: chengjie.li
 * @Date: 2025/6/10 20:30
 */
@Mapper
public interface TktTicketModelMapper {

    TktTicketModelMapper INSTANCE = Mappers.getMapper(TktTicketModelMapper.class);

    TktTicketModel convert(TktTicketModelDO tktTicketModelDO);

    TktTicketModelQueryArg convert(TktTicketModelQueryParam queryParam);
}
