package com.sendinfo.ticketing.verification.basic.repository.pay.dao.impl;

import com.sendinfo.ticketing.verification.basic.repository.pay.arg.TransInfoDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.TransInfoQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.TransInfoUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dao.TransInfoDao;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.TransInfoDO;
import com.sendinfo.ticketing.verification.repository.jdbc.Statement;
import com.sendinfo.ticketing.verification.repository.jdbc.mybatis.*;
import lombok.Getter;
import org.apache.ibatis.session.SqlSession;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 交易记录数据访问实现类
 * 负责trans_info表的数据访问操作，支持租户隔离、分页、批量等功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Repository("transInfoDao")
public class TransInfoDaoImpl implements TransInfoDao,
        SqlSessionGenericDAO<Long, TransInfoDO, TransInfoUpdateArg, TransInfoDeleteArg>,
        SqlSessionCountableDAO<TransInfoQueryArg>,
        SqlSessionQueryableDAO<TransInfoQueryArg, TransInfoDO>,
        SqlSessionBatchInsertDAO<Long, TransInfoDO>,
        SqlSessionDAO {

    private final SqlSession sqlSession;

    private final Statement statement;

    public TransInfoDaoImpl(@Qualifier("saasPwSqlSessionTemplate") SqlSessionTemplate sqlSessionTemplate) {
        this.sqlSession = sqlSessionTemplate;
        this.statement = Statement.of(TransInfoDao.class);
    }

    @Override
    public TransInfoDO queryByTransNo(String transNo, String corpCode) {
        Map<String, Object> params = new HashMap<>();
        params.put("transNo", transNo);
        params.put("corpCode", corpCode);
        return sqlSession.selectOne(statement.get("queryByTransNo"), params);
    }

    @Override
    public TransInfoDO queryByTradeCode(String tradeCode, String corpCode) {
        Map<String, Object> params = new HashMap<>();
        params.put("tradeCode", tradeCode);
        params.put("corpCode", corpCode);
        return sqlSession.selectOne(statement.get("queryByTradeCode"), params);
    }

    @Override
    public List<TransInfoDO> queryListByTradeCode(String tradeCode, String corpCode) {
        Map<String, Object> params = new HashMap<>(3);
        params.put("tradeCode", tradeCode);
        params.put("corpCode", corpCode);
        return sqlSession.selectList(statement.get("queryListByTradeCode"), params);
    }

    @Override
    public List<TransInfoDO> queryListByTradeCodeAndTransType(String tradeCode, Integer transType, String corpCode) {
        Map<String, Object> params = new HashMap<>();
        params.put("tradeCode", tradeCode);
        params.put("transType", transType);
        params.put("corpCode", corpCode);
        return sqlSession.selectList(statement.get("queryListByTradeCodeAndTransType"), params);
    }
}