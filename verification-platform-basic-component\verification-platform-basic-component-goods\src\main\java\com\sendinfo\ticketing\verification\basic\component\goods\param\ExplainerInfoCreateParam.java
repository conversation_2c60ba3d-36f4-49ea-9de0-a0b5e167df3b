package com.sendinfo.ticketing.verification.basic.component.goods.param;

import com.sendinfo.ticketing.verification.common.component.param.AbstractCreateParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 讲解人创建参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class ExplainerInfoCreateParam extends AbstractCreateParam {
    /** 企业编码 */
    private String corpCode;
    /** 服务类别ID */
    private Long serviceCategoryId;
    /** 业态(系统字典) */
    private String businessType;
    /** 讲解人工号 */
    private String explainerNo;
    /** 讲解人名称 */
    private String explainerName;
    /** 导游证 */
    private String guideCard;
    /** 身份证 */
    private String idCard;
    /** 手机号 */
    private String phone;
    /** 职工状态(票务字典) */
    private String status;
    /** 地区编号 */
    private String areaCode;
    /** 地区名称 */
    private String areaName;
    /** 地址 */
    private String address;
    /** 备注 */
    private String remark;
    /** 禁启用状态 */
    private String useFlag;
    /**
     * 创建人
     */
    private String createBy;
} 