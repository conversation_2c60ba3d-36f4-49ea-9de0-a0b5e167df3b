/**
 * System parameter component definition create request
 *
 * <AUTHOR> 2025-07-21 15:40:00
 */
package com.sendinfo.ticketing.verification.basic.api.infra.request;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 系统参数组件定义创建请求
 */
@Getter
@Setter
@ToString
public class SysParamComponentDefinitionCreateRequest implements Serializable {
    private static final long serialVersionUID = -7654321098765432103L;

    /**
     * 企业编码
     */
    @NotBlank(message = "企业编码不能为空")
    private String corpCode;

    /**
     * 组件分类Id，infra_feature_component_category主键id
     */
    @NotNull(message = "组件分类ID不能为空")
    private Long categoryId;

    /**
     * 组件编码
     */
    @Size(max = 64, message = "组件编码长度不能超过64")
    @Pattern(regexp = "^[a-zA-Z0-9_]+$", message = "组件编码只能包含字母、数字和下划线")
    private String componentCode;

    /**
     * 组件名称
     */
    @NotBlank(message = "组件名称不能为空")
    @Size(max = 128, message = "组件名称长度不能超过128")
    private String componentName;

    /**
     * 状态：1-启用，0-禁用
     */
    @NotNull(message = "状态不能为空")
    private Integer status;

    /**
     * 组件描述
     */
    @Size(max = 256, message = "组件描述长度不能超过256")
    private String description;

    /**
     * 排序
     */
    @NotNull(message = "排序不能为空")
    @Min(value = 1, message = "排序不能小于1")
    @Max(value = 999, message = "排序不能大于999")
    private Integer sortNo;

    /**
     * 创建人
     */
    @NotBlank(message = "创建人不能为空")
    private String createBy;
}