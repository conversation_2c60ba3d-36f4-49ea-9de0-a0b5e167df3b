package com.sendinfo.ticketing.verification.basic.api.infra.request;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 参数命名空间查询条件
 */
@Getter
@Setter
@ToString
public class SysParamNamespaceQueryCondition implements Serializable {
    private static final long serialVersionUID = -4876543210123456790L;

    /**
     * 企业编码
     */
    @NotBlank(message = "企业编码不能为空")
    private String corpCode;

    /**
     * 功能模块编码(菜单编码)
     */
    private String moduleCode;

    /**
     * 命名空间编码
     */
    private String namespaceCode;

    /**
     * 组件分类Id，infra_feature_component_category主键id
     */
    private Long categoryId;

    /**
     * 状态：1-启用，0-禁用
     */
    private Integer status;
}