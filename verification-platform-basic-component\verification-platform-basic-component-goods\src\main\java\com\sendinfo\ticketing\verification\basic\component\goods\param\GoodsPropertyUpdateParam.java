package com.sendinfo.ticketing.verification.basic.component.goods.param;

import com.sendinfo.ticketing.verification.basic.enums.TicketPropertyType;
import com.sendinfo.ticketing.verification.common.component.param.AbstractUpdateParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2025-07-18 16:15
 */
@Getter
@Setter
@ToString(callSuper = true)
public class GoodsPropertyUpdateParam extends AbstractUpdateParam<Long> {

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 操作人
     */
    private String modifyBy;

    /**
     * 商品表主键
     */
    private Long goodsPk;

    /**
     * 商品编码
     */
    private String goodsCode;

    /**
     * 业态编码: TODO
     * SCEN-景区门票, HOTL-酒店, ACARD-年卡,
     * FOOD-餐饮美食, THTR-剧场演出, BATH-温泉,
     * WINT-滑雪, CRSE-游轮, RENT-租车,
     * PARK-乐园, SHOP-标准店
     */
    private String domainCode;

    /**
     * 扩展数据类型: 1-基础信息,2-销售规则,3-退款规则,4-打印规则
     */
    private TicketPropertyType type;

    /**
     * 类型表主键
     */
    private Long typePk;

    /**
     * 属性key
     */
    private String key;

    /**
     * 属性值value
     */
    private String value;

    /**
     * 属性描述
     */
    private String description;

    /**
     * 原表修改数据时间
     */
    private Date operateTime;
}
