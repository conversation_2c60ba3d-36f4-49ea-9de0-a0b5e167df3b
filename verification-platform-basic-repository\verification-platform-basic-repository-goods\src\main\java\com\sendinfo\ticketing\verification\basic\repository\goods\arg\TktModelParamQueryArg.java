package com.sendinfo.ticketing.verification.basic.repository.goods.arg;

import com.sendinfo.ticketing.verification.common.repository.arg.AbstractQueryArg;
import com.sendinfo.ticketing.verification.common.repository.arg.Pageable;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
@Builder
public class TktModelParamQueryArg extends AbstractQueryArg implements Pageable {
    private String corpCode;
    private String title;
    private String modelType;
    private Integer modelKind;
    private String useFlag;
    private String modelCode;

    /**
     * offset
     */
    private Integer offset;
    /**
     * limit
     */
    private Integer limit;
}
