package com.sendinfo.ticketing.verification.basic.api.goods;

import com.sendinfo.ticketing.verification.basic.api.goods.request.GoodsPropertyDeleteRequest;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

/**
 * <AUTHOR>
 * @since 2025-07-18 17:38
 */
public interface GoodsPropertyDeleteService {

    /**
     * 按请求逻辑删除数据
     *
     * @param request 删除参数请求对象
     * @return 保存的对象
     */
    ResultModel<Long> softDelete(GoodsPropertyDeleteRequest request);

    /**
     * 按请求物理删除数据
     *
     * @param request 删除参数请求对象
     * @return 保存的对象
     */
    ResultModel<Long> delete(GoodsPropertyDeleteRequest request);
}
