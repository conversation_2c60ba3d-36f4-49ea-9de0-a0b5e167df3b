package com.sendinfo.ticketing.verification.basic.component.infra.impl;

import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

import com.sendinfo.ticketing.verification.basic.component.infra.SysParamDefinitionReadComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.converter.SysParamDefinitionConverter;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamDefinitionQueryParam;
import com.sendinfo.ticketing.verification.basic.model.infra.SysParamDefinition;
import com.sendinfo.ticketing.verification.basic.model.infra.SysParamDefinitionCountByComponent;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamDefinitionQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.SysParamDefinitionDao;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.SysParamDefinitionCountByComponentDO;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.SysParamDefinitionDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedCountRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedPageRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleRead;

@Component("sysParamDefinitionReadComponent")
@Getter
public class SysParamDefinitionReadComponentImpl
        implements SysParamDefinitionReadComponent,
        DaoBasedPageRead<Long, SysParamDefinitionQueryParam, SysParamDefinition, SysParamDefinitionDO, SysParamDefinitionQueryArg>,
        DaoBasedSingleRead<Long, SysParamDefinitionQueryParam, SysParamDefinition, SysParamDefinitionDO>,
        DaoBasedCountRead<Long, SysParamDefinitionQueryParam, SysParamDefinition, SysParamDefinitionQueryArg> {

    private final SysParamDefinitionDao dao;
    private final SysParamDefinitionConverter converter;

    public SysParamDefinitionReadComponentImpl(SysParamDefinitionDao dao, SysParamDefinitionConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }

    @Override
    public SysParamDefinition queryByParamCode(String paramCode, String corpCode) {
        return Optional.ofNullable(dao.queryByParamCode(paramCode, corpCode))
                .map(converter::r_d2m)
                .orElse(null);
    }

    @Override
    public List<SysParamDefinition> queryByComponentCode(String componentCode) {
        List<SysParamDefinitionDO> dataObjects = dao.queryByComponentCode(componentCode);
        return converter.r_ds2ms(dataObjects);
    }

    @Override
    public List<SysParamDefinitionCountByComponent> countByComponentCode(List<String> componentCodes, String corpCode) {
        List<SysParamDefinitionCountByComponentDO> definitionCountByComponentDOS = dao
                .countByComponentCode(componentCodes, corpCode);
        return converter.convertCountByComponentList(definitionCountByComponentDOS);
    }

    @Override
    public List<SysParamDefinition> queryByComponentCodeList(List<String> componentCodeList) {
        List<SysParamDefinitionDO> dataObjects = dao.queryByComponentCodeList(componentCodeList);
        return converter.r_ds2ms(dataObjects);
    }
}