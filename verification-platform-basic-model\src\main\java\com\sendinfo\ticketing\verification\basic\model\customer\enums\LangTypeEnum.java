package com.sendinfo.ticketing.verification.basic.model.customer.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 语种类型枚举
 * 定义导游语种类型
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
public enum LangTypeEnum {
    CHINESE("0", "汉语"),
    RESERVED("1", "保留"),
    ENGLISH("2", "英语"),
    KOREAN("3", "韩语"),
    FRENCH("4", "法语"),
    JAPANESE("5", "日语"),
    GERMAN("6", "德语"),
    OTHER("7", "其他");

    @JsonValue
    private final String code;
    private final String description;

    LangTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    // --- 高效反向查找机制 ---
    private static final Map<String, LangTypeEnum> CODE_MAP = 
        Arrays.stream(values()).collect(Collectors.toMap(LangTypeEnum::getCode, Function.identity()));

    /**
     * 根据 code 获取枚举实例
     *
     * @param code 语种类型编码
     * @return 对应的枚举实例，如果找不到则返回 null
     */
    @JsonCreator
    public static LangTypeEnum ofCode(String code) {
        if (code == null) {
            return null;
        }
        return CODE_MAP.get(code);
    }
} 