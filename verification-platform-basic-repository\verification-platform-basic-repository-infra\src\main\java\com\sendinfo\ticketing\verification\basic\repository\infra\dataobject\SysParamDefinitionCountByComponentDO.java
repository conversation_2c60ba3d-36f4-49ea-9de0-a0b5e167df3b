/**
 * 系统参数定义按组件编码统计结果
 *
 * <AUTHOR> 2023-07-25
 */
package com.sendinfo.ticketing.verification.basic.repository.infra.dataobject;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 系统参数定义按组件编码统计结果数据对象
 */
@Getter
@Setter
@ToString
public class SysParamDefinitionCountByComponentDO {

    /**
     * 组件编码
     */
    private String componentCode;

    /**
     * 系统参数数量
     */
    private Integer sysParamCount;
}
