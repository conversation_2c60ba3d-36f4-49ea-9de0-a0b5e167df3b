package com.sendinfo.ticketing.verification.basic.repository.common.sharding;

import java.util.Random;

/**
 * <AUTHOR>
 * @since 2025/7/20 16:23
 **/
public class ShardingIdGeneratorUtil {

    private static final Random RANDOM = new Random();

    // 机器唯一标识（0 ~ 1023）
    // 减少nodeId位数，增加时间戳位数
    private static final long nodeId;
    private static final long nodeIdBits = 5L;  // 从10位减到5位 (0-31)
    private static final long maxSequence = ~(-1L << 12); // 4095 (12位)

    private static long lastTimestamp = -1L;
    private static long lastSequence = 0L;

    static {
        nodeId = initNodeId();
    }

    private static long initNodeId() {
        return new Random().nextInt(32); // 0 ~ 31
    }

    public static synchronized Long generateIdByCorpCode(String corpCode) {
        if (corpCode == null || corpCode.isEmpty()) {
            throw new IllegalArgumentException("corpCode 不能为空");
        }

        // 根据 corpCode 生成分片位
        int hc = corpCode.hashCode();
        int shardingBitByCorpCode = ((hc ^ (hc >>> 16)) & 0x7fffffff) % 32;

        long timestamp = System.currentTimeMillis();

        if (timestamp < lastTimestamp) {
            throw new RuntimeException("时钟回拨");
        }

        if (timestamp == lastTimestamp) {
            lastSequence = (lastSequence + 1) & maxSequence;
            if (lastSequence == 0) {
                timestamp = tilNextMillis(lastTimestamp);
            }
        } else {
            lastSequence = 0;
        }

        lastTimestamp = timestamp;

        // 时间戳偏移（从 2020-01-01 开始）
        long timestampOffset = timestamp - 1577836800000L; // 2020-01-01 UTC

        // 重新分配位数：
        // 5 bits 分片位 (58-62)
        // 34 bits 时间戳 (24-57) 可支持~544年
        // 5 bits 节点ID (19-23)
        // 12 bits 序列号 (0-11)
        // Calculate ID components
        long shardingPart = ((long) shardingBitByCorpCode & 0x1F) << 58;  // 5 bits at 58-62
        long timestampPart = (timestampOffset & 0x3FFFFFFFFL) << 24;      // 34 bits at 24-57
        long nodePart = (nodeId & 0x1F) << 19;                            // 5 bits at 19-23
        long sequencePart = lastSequence & 0xFFF;                          // 12 bits at 0-11
        
        // Combine parts
        long id = shardingPart | timestampPart | nodePart | sequencePart;
        
        // Verify ID is within BIGINT UNSIGNED range (0 to 18446744073709551615)
        if (id < 0) {
            throw new RuntimeException("Generated negative ID: " + id);
        }
        
        return id;
    }

    private static long tilNextMillis(long lastTimestamp) {
        long timestamp = System.currentTimeMillis();
        while (timestamp <= lastTimestamp) {
            timestamp = System.currentTimeMillis();
        }
        return timestamp;
    }

    public static void main(String[] args) {
    	System.out.println(generateIdByCorpCode("0001"));
    	System.out.println(generateIdByCorpCode("0001"));
    	System.out.println(generateIdByCorpCode("0001"));
    	System.out.println(generateIdByCorpCode("0001"));
    	System.out.println(generateIdByCorpCode("0001"));
    	System.out.println(generateIdByCorpCode("0001"));
    	System.out.println(generateIdByCorpCode("0001"));
    	System.out.println(generateIdByCorpCode("0001"));
    	System.out.println(generateIdByCorpCode("0001"));
    	System.out.println(generateIdByCorpCode("0001"));
    	System.out.println(generateIdByCorpCode("0001"));
    }
}
