package com.sendinfo.ticketing.verification.basic.component.customer.impl;

import com.sendinfo.ticketing.verification.basic.component.customer.TravelInfoReadComponent;
import com.sendinfo.ticketing.verification.basic.component.customer.converter.TravelInfoConverter;
import com.sendinfo.ticketing.verification.basic.component.customer.param.TravelInfoDeptQueryParam;
import com.sendinfo.ticketing.verification.basic.component.customer.param.TravelInfoQueryParam;
import com.sendinfo.ticketing.verification.basic.component.customer.param.TravelInfoRoleQueryParam;
import com.sendinfo.ticketing.verification.basic.model.customer.TravelInfo;
import com.sendinfo.ticketing.verification.basic.repository.customer.arg.TravelInfoQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.customer.dao.TravelInfoDao;
import com.sendinfo.ticketing.verification.basic.repository.customer.dataobject.TravelInfoDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedCountRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedListRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleRead;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * 导游读取组件实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("travelInfoReadComponent")
@Getter
public class TravelInfoReadComponentImpl implements
        TravelInfoReadComponent,
        DaoBasedSingleRead<Long, TravelInfoQueryParam, TravelInfo, TravelInfoDO>,
        DaoBasedListRead<Long, TravelInfoQueryParam, TravelInfo, TravelInfoDO, TravelInfoQueryArg>,
        DaoBasedCountRead<Long, TravelInfoQueryParam, TravelInfo, TravelInfoQueryArg> {

    private final TravelInfoDao dao;
    private final TravelInfoConverter converter;

    public TravelInfoReadComponentImpl(TravelInfoDao dao, TravelInfoConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }

    @Override
    public List<TravelInfo> batchQueryByIds(Set<Long> ids) {
        return converter.r_ds2ms(dao.batchQueryByIds(ids));
    }

    @Override
    public List<TravelInfo> queryTravelInfoList(TravelInfoQueryParam queryParam) {
        return converter.r_ds2ms(dao.queryByArg(converter.r_p2a(queryParam)));
    }

    @Override
    public List<TravelInfo> queryRoleTravelInfoList(TravelInfoRoleQueryParam queryParam) {
        return converter.r_ds2ms(dao.queryRoleTravelInfoList(converter.r_p2a(queryParam)));
    }

    @Override
    public List<TravelInfo> queryDeptTravelInfoList(TravelInfoDeptQueryParam queryParam) {
        return converter.r_ds2ms(dao.queryDeptTravelInfoList(converter.r_p2a(queryParam)));
    }
}