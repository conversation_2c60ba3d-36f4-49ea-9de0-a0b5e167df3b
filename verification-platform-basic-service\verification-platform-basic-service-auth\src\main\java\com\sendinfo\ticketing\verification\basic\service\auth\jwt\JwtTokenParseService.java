package com.sendinfo.ticketing.verification.basic.service.auth.jwt;

import io.jsonwebtoken.Claims;
import org.apache.commons.lang3.tuple.Pair;

import java.util.Map;


/**
 * <AUTHOR>
 * @since 2025-07-23 13:20:43
 */
public interface JwtTokenParseService {

    /**
     * 获取 claims
     *
     * @param headers  头部
     * @param identity 标签
     * @return claims
     */
    Pair<String, Claims> getClaimsForJwtToken(Map<String, String> headers, JwtIdentity identity);
}
