package com.sendinfo.ticketing.verification.basic.model.customer.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 审核状态枚举
 * 定义导游审核状态
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
public enum AuditStatusEnum {
    UNAUDITED("0", "未审核"),
    AUDITED("1", "已审核");

    @JsonValue
    private final String code;
    private final String description;

    AuditStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    // --- 高效反向查找机制 ---
    private static final Map<String, AuditStatusEnum> CODE_MAP = 
        Arrays.stream(values()).collect(Collectors.toMap(AuditStatusEnum::getCode, Function.identity()));

    /**
     * 根据 code 获取枚举实例
     *
     * @param code 审核状态编码
     * @return 对应的枚举实例，如果找不到则返回 null
     */
    @JsonCreator
    public static AuditStatusEnum ofCode(String code) {
        if (code == null) {
            return null;
        }
        return CODE_MAP.get(code);
    }
} 