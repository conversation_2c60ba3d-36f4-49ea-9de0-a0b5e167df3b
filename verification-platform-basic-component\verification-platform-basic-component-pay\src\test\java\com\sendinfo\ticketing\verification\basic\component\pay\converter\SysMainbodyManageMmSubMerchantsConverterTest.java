package com.sendinfo.ticketing.verification.basic.component.pay.converter;

import com.sendinfo.ticketing.verification.basic.component.pay.param.SysMainbodyManageMmSubMerchantsCreateParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.SysMainbodyManageMmSubMerchantsQueryParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.SysMainbodyManageMmSubMerchantsUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.pay.SysMainbodyManageMmSubMerchants;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageMmSubMerchantsQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageMmSubMerchantsUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysMainbodyManageMmSubMerchantsDO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;

/**
 * SysMainbodyManageMmSubMerchantsConverter单元测试
 * 测试经营主体支付配置子商户关联表转换器的各种转换功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RunWith(MockitoJUnitRunner.class)
public class SysMainbodyManageMmSubMerchantsConverterTest {

    @InjectMocks
    private SysMainbodyManageMmSubMerchantsConverter sysMainbodyManageMmSubMerchantsConverter;

    // 测试常量
    private static final Long TEST_ID = 1L;
    private static final Long TEST_MAINBODY_MANAGE_ID = 100L;
    private static final Long TEST_SUB_MERCHANTS_ID = 200L;
    private static final String TEST_SUB_MERCHANTS_NAME = "测试子商户";
    private static final String TEST_MERCHANT_NO = "SUB001";
    private static final String TEST_CREATE_BY = "testUser";
    private static final String TEST_MODIFY_BY = "testUser";

    /**
     * 测试目的：验证SysMainbodyManageMmSubMerchantsCreateParam转换为SysMainbodyManageMmSubMerchantsDO的功能
     * 测试步骤：
     * 1. 创建完整的SysMainbodyManageMmSubMerchantsCreateParam对象
     * 2. 调用c_p2d方法进行转换
     * 3. 验证转换结果的正确性
     */
    @Test
    public void testCreateParam2Do_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        SysMainbodyManageMmSubMerchantsCreateParam createParam = createSysMainbodyManageMmSubMerchantsCreateParam();

        // 2. 执行转换
        SysMainbodyManageMmSubMerchantsDO result = sysMainbodyManageMmSubMerchantsConverter.c_p2d(createParam);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", result);
        assertEquals("经营主体管理ID应该一致", TEST_MAINBODY_MANAGE_ID, result.getMainbodyManageId());
        assertEquals("子商户ID应该一致", TEST_SUB_MERCHANTS_ID, result.getSubMerchantsId());
        assertEquals("子商户名称应该一致", TEST_SUB_MERCHANTS_NAME, result.getSubMerchantsName());
        assertEquals("子商户号应该一致", TEST_MERCHANT_NO, result.getMerchantNo());
        assertEquals("创建人应该一致", TEST_CREATE_BY, result.getCreateBy());
    }

    /**
     * 测试目的：验证SysMainbodyManageMmSubMerchantsCreateParam为null时的处理
     */
    @Test
    public void testCreateParam2Do_WithNullInput_ShouldReturnNull() {
        // 1. 执行转换
        SysMainbodyManageMmSubMerchantsDO result = sysMainbodyManageMmSubMerchantsConverter.c_p2d(null);

        // 2. 验证结果
        assertNull("输入为null时应该返回null", result);
    }

    /**
     * 测试目的：验证SysMainbodyManageMmSubMerchantsDO转换为SysMainbodyManageMmSubMerchants的功能
     * 测试步骤：
     * 1. 创建完整的SysMainbodyManageMmSubMerchantsDO对象
     * 2. 调用r_d2m方法进行转换
     * 3. 验证转换结果的正确性
     */
    @Test
    public void testReadDo2Model_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        SysMainbodyManageMmSubMerchantsDO sysMainbodyManageMmSubMerchantsDO = createSysMainbodyManageMmSubMerchantsDO();

        // 2. 执行转换
        SysMainbodyManageMmSubMerchants result = sysMainbodyManageMmSubMerchantsConverter.r_d2m(sysMainbodyManageMmSubMerchantsDO);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", result);
        assertEquals("ID应该一致", TEST_ID, result.getId());
        assertEquals("经营主体管理ID应该一致", TEST_MAINBODY_MANAGE_ID, result.getMainbodyManageId());
        assertEquals("子商户ID应该一致", TEST_SUB_MERCHANTS_ID, result.getSubMerchantsId());
        assertEquals("子商户名称应该一致", TEST_SUB_MERCHANTS_NAME, result.getSubMerchantsName());
        assertEquals("子商户号应该一致", TEST_MERCHANT_NO, result.getMerchantNo());
        assertEquals("创建人应该一致", TEST_CREATE_BY, result.getCreateBy());
        assertEquals("修改人应该一致", TEST_MODIFY_BY, result.getModifyBy());
    }

    /**
     * 测试目的：验证SysMainbodyManageMmSubMerchantsDO为null时的处理
     */
    @Test
    public void testReadDo2Model_WithNullInput_ShouldReturnNull() {
        // 1. 执行转换
        SysMainbodyManageMmSubMerchants result = sysMainbodyManageMmSubMerchantsConverter.r_d2m(null);

        // 2. 验证结果
        assertNull("输入为null时应该返回null", result);
    }

    /**
     * 测试目的：验证List<SysMainbodyManageMmSubMerchantsDO>转换为List<SysMainbodyManageMmSubMerchants>的功能
     * 测试步骤：
     * 1. 创建包含多个SysMainbodyManageMmSubMerchantsDO的列表
     * 2. 调用r_ds2ms方法进行转换
     * 3. 验证转换结果的正确性
     */
    @Test
    public void testReadDoList2ModelList_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        List<SysMainbodyManageMmSubMerchantsDO> sysMainbodyManageMmSubMerchantsDOList = Arrays.asList(
                createSysMainbodyManageMmSubMerchantsDO(),
                createSysMainbodyManageMmSubMerchantsDO()
        );

        // 2. 执行转换
        List<SysMainbodyManageMmSubMerchants> result = sysMainbodyManageMmSubMerchantsConverter.r_ds2ms(sysMainbodyManageMmSubMerchantsDOList);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", result);
        assertEquals("列表大小应该一致", 2, result.size());

        // 验证第一个元素
        SysMainbodyManageMmSubMerchants firstResult = result.get(0);
        assertEquals("第一个元素的ID应该一致", TEST_ID, firstResult.getId());
        assertEquals("第一个元素的经营主体管理ID应该一致", TEST_MAINBODY_MANAGE_ID, firstResult.getMainbodyManageId());
        assertEquals("第一个元素的子商户ID应该一致", TEST_SUB_MERCHANTS_ID, firstResult.getSubMerchantsId());
        assertEquals("第一个元素的子商户名称应该一致", TEST_SUB_MERCHANTS_NAME, firstResult.getSubMerchantsName());
        assertEquals("第一个元素的子商户号应该一致", TEST_MERCHANT_NO, firstResult.getMerchantNo());

        // 验证第二个元素
        SysMainbodyManageMmSubMerchants secondResult = result.get(1);
        assertEquals("第二个元素的ID应该一致", TEST_ID, secondResult.getId());
        assertEquals("第二个元素的经营主体管理ID应该一致", TEST_MAINBODY_MANAGE_ID, secondResult.getMainbodyManageId());
        assertEquals("第二个元素的子商户ID应该一致", TEST_SUB_MERCHANTS_ID, secondResult.getSubMerchantsId());
        assertEquals("第二个元素的子商户名称应该一致", TEST_SUB_MERCHANTS_NAME, secondResult.getSubMerchantsName());
        assertEquals("第二个元素的子商户号应该一致", TEST_MERCHANT_NO, secondResult.getMerchantNo());
    }

    /**
     * 测试目的：验证List<SysMainbodyManageMmSubMerchantsDO>为null时的处理
     */
    @Test
    public void testReadDoList2ModelList_WithNullInput_ShouldReturnNull() {
        // 1. 执行转换
        List<SysMainbodyManageMmSubMerchants> result = sysMainbodyManageMmSubMerchantsConverter.r_ds2ms(null);

        // 2. 验证结果
        assertNull("输入为null时应该返回null", result);
    }

    /**
     * 测试目的：验证空列表的处理
     */
    @Test
    public void testReadDoList2ModelList_WithEmptyList_ShouldReturnEmptyList() {
        // 1. 创建空列表
        List<SysMainbodyManageMmSubMerchantsDO> emptyList = Arrays.asList();

        // 2. 执行转换
        List<SysMainbodyManageMmSubMerchants> result = sysMainbodyManageMmSubMerchantsConverter.r_ds2ms(emptyList);

        // 3. 验证结果
        assertNotNull("结果不应为null", result);
        assertTrue("结果应该是空列表", result.isEmpty());
    }

    /**
     * 测试目的：验证SysMainbodyManageMmSubMerchantsQueryParam转换为SysMainbodyManageMmSubMerchantsQueryArg的功能
     * 测试步骤：
     * 1. 创建完整的SysMainbodyManageMmSubMerchantsQueryParam对象
     * 2. 调用r_p2a方法进行转换
     * 3. 验证转换结果的正确性
     */
    @Test
    public void testReadQueryParam2QueryArg_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        SysMainbodyManageMmSubMerchantsQueryParam queryParam = createSysMainbodyManageMmSubMerchantsQueryParam();

        // 2. 执行转换
        SysMainbodyManageMmSubMerchantsQueryArg result = sysMainbodyManageMmSubMerchantsConverter.r_p2a(queryParam);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", result);
        assertEquals("经营主体管理ID应该一致", TEST_MAINBODY_MANAGE_ID, result.getMainbodyManageId());
        assertEquals("子商户ID应该一致", TEST_SUB_MERCHANTS_ID, result.getSubMerchantsId());
        assertEquals("子商户名称应该一致", TEST_SUB_MERCHANTS_NAME, result.getSubMerchantsName());
        assertEquals("子商户号应该一致", TEST_MERCHANT_NO, result.getMerchantNo());
    }

    /**
     * 测试目的：验证SysMainbodyManageMmSubMerchantsQueryParam为null时的处理
     */
    @Test
    public void testReadQueryParam2QueryArg_WithNullInput_ShouldReturnNull() {
        // 1. 执行转换
        SysMainbodyManageMmSubMerchantsQueryArg result = sysMainbodyManageMmSubMerchantsConverter.r_p2a(null);

        // 2. 验证结果
        assertNull("输入为null时应该返回null", result);
    }

    /**
     * 测试目的：验证SysMainbodyManageMmSubMerchantsUpdateParam转换为SysMainbodyManageMmSubMerchantsUpdateArg的功能
     * 测试步骤：
     * 1. 创建完整的SysMainbodyManageMmSubMerchantsUpdateParam对象
     * 2. 调用u_p2a方法进行转换
     * 3. 验证转换结果的正确性
     */
    @Test
    public void testUpdateParam2UpdateArg_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        SysMainbodyManageMmSubMerchantsUpdateParam updateParam = createSysMainbodyManageMmSubMerchantsUpdateParam();
        SysMainbodyManageMmSubMerchants currentModel = createSysMainbodyManageMmSubMerchants();

        // 2. 执行转换
        SysMainbodyManageMmSubMerchantsUpdateArg result = sysMainbodyManageMmSubMerchantsConverter.u_p2a(updateParam, currentModel);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", result);
        assertEquals("经营主体管理ID应该一致", TEST_MAINBODY_MANAGE_ID, result.getMainbodyManageId());
        assertEquals("子商户ID应该一致", TEST_SUB_MERCHANTS_ID, result.getSubMerchantsId());
        assertEquals("子商户名称应该一致", TEST_SUB_MERCHANTS_NAME, result.getSubMerchantsName());
        assertEquals("子商户号应该一致", TEST_MERCHANT_NO, result.getMerchantNo());
        assertEquals("修改人应该一致", TEST_MODIFY_BY, result.getModifyBy());
    }

    /**
     * 测试目的：验证SysMainbodyManageMmSubMerchantsUpdateParam为null时的处理
     */
    @Test
    public void testUpdateParam2UpdateArg_WithNullInput_ShouldReturnNull() {
        // 1. 创建当前模型
        SysMainbodyManageMmSubMerchants currentModel = createSysMainbodyManageMmSubMerchants();

        // 2. 执行转换
        SysMainbodyManageMmSubMerchantsUpdateArg result = sysMainbodyManageMmSubMerchantsConverter.u_p2a(null, currentModel);

        // 3. 验证结果
        assertNull("输入为null时应该返回null", result);
    }

    // ==================== 测试数据构建方法 ====================

    /**
     * 创建SysMainbodyManageMmSubMerchantsCreateParam测试数据
     */
    private SysMainbodyManageMmSubMerchantsCreateParam createSysMainbodyManageMmSubMerchantsCreateParam() {
        SysMainbodyManageMmSubMerchantsCreateParam createParam = new SysMainbodyManageMmSubMerchantsCreateParam();
        createParam.setMainbodyManageId(TEST_MAINBODY_MANAGE_ID);
        createParam.setSubMerchantsId(TEST_SUB_MERCHANTS_ID);
        createParam.setSubMerchantsName(TEST_SUB_MERCHANTS_NAME);
        createParam.setMerchantNo(TEST_MERCHANT_NO);
        createParam.setCreateBy(TEST_CREATE_BY);
        createParam.setModifyBy(TEST_MODIFY_BY);
        return createParam;
    }

    /**
     * 创建SysMainbodyManageMmSubMerchantsDO测试数据
     */
    private SysMainbodyManageMmSubMerchantsDO createSysMainbodyManageMmSubMerchantsDO() {
        SysMainbodyManageMmSubMerchantsDO sysMainbodyManageMmSubMerchantsDO = new SysMainbodyManageMmSubMerchantsDO();
        sysMainbodyManageMmSubMerchantsDO.setId(TEST_ID);
        sysMainbodyManageMmSubMerchantsDO.setMainbodyManageId(TEST_MAINBODY_MANAGE_ID);
        sysMainbodyManageMmSubMerchantsDO.setSubMerchantsId(TEST_SUB_MERCHANTS_ID);
        sysMainbodyManageMmSubMerchantsDO.setSubMerchantsName(TEST_SUB_MERCHANTS_NAME);
        sysMainbodyManageMmSubMerchantsDO.setMerchantNo(TEST_MERCHANT_NO);
        sysMainbodyManageMmSubMerchantsDO.setCreateBy(TEST_CREATE_BY);
        sysMainbodyManageMmSubMerchantsDO.setModifyBy(TEST_MODIFY_BY);
        return sysMainbodyManageMmSubMerchantsDO;
    }

    /**
     * 创建SysMainbodyManageMmSubMerchantsQueryParam测试数据
     */
    private SysMainbodyManageMmSubMerchantsQueryParam createSysMainbodyManageMmSubMerchantsQueryParam() {
        SysMainbodyManageMmSubMerchantsQueryParam queryParam = new SysMainbodyManageMmSubMerchantsQueryParam();
        queryParam.setMainbodyManageId(TEST_MAINBODY_MANAGE_ID);
        queryParam.setSubMerchantsId(TEST_SUB_MERCHANTS_ID);
        queryParam.setSubMerchantsName(TEST_SUB_MERCHANTS_NAME);
        queryParam.setMerchantNo(TEST_MERCHANT_NO);
        return queryParam;
    }

    /**
     * 创建SysMainbodyManageMmSubMerchantsUpdateParam测试数据
     */
    private SysMainbodyManageMmSubMerchantsUpdateParam createSysMainbodyManageMmSubMerchantsUpdateParam() {
        SysMainbodyManageMmSubMerchantsUpdateParam updateParam = new SysMainbodyManageMmSubMerchantsUpdateParam();
        updateParam.setMainbodyManageId(TEST_MAINBODY_MANAGE_ID);
        updateParam.setSubMerchantsId(TEST_SUB_MERCHANTS_ID);
        updateParam.setSubMerchantsName(TEST_SUB_MERCHANTS_NAME);
        updateParam.setMerchantNo(TEST_MERCHANT_NO);
        updateParam.setModifyBy(TEST_MODIFY_BY);
        return updateParam;
    }

    /**
     * 创建SysMainbodyManageMmSubMerchants测试数据
     */
    private SysMainbodyManageMmSubMerchants createSysMainbodyManageMmSubMerchants() {
        SysMainbodyManageMmSubMerchants sysMainbodyManageMmSubMerchants = new SysMainbodyManageMmSubMerchants();
        sysMainbodyManageMmSubMerchants.setId(TEST_ID);
        sysMainbodyManageMmSubMerchants.setMainbodyManageId(TEST_MAINBODY_MANAGE_ID);
        sysMainbodyManageMmSubMerchants.setSubMerchantsId(TEST_SUB_MERCHANTS_ID);
        sysMainbodyManageMmSubMerchants.setSubMerchantsName(TEST_SUB_MERCHANTS_NAME);
        sysMainbodyManageMmSubMerchants.setMerchantNo(TEST_MERCHANT_NO);
        sysMainbodyManageMmSubMerchants.setCreateBy(TEST_CREATE_BY);
        sysMainbodyManageMmSubMerchants.setModifyBy(TEST_MODIFY_BY);
        return sysMainbodyManageMmSubMerchants;
    }

} 