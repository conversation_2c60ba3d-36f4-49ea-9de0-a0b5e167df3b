package com.sendinfo.ticketing.verification.basic.component.infra.converter;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamValueCreateRequest;
import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamValueDeleteRequest;
import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamValueQueryCondition;
import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamValueUpdateRequest;
import com.sendinfo.ticketing.verification.basic.component.infra.mapper.SysParamValueMapper;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamValueCreateParam;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamValueDeleteParam;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamValueQueryParam;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamValueUpdateParam;
import com.sendinfo.ticketing.verification.basic.enums.CommonStatusEnum;
import com.sendinfo.ticketing.verification.basic.model.infra.SysParamValue;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamValueDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamValueQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamValueUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.SysParamValueDO;
import com.sendinfo.ticketing.verification.common.api.request.PageRequest;
import com.sendinfo.ticketing.verification.common.component.converter.CreateParam2DoConverter;
import com.sendinfo.ticketing.verification.common.component.converter.CreateReq2ParamConverter;
import com.sendinfo.ticketing.verification.common.component.converter.DeleteParam2ArgConverter;
import com.sendinfo.ticketing.verification.common.component.converter.DeleteReq2ParamConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadDo2ModelConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadPageReq2ParamConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadParam2ArgConverter;
import com.sendinfo.ticketing.verification.common.component.converter.UpdateParam2ArgConverter;
import com.sendinfo.ticketing.verification.common.component.converter.UpdateReq2ParamConverter;
import com.sendinfo.ticketing.verification.common.repository.arg.StatusUpdater;

/**
 *  系统参数值转换器
 *
 *  <AUTHOR> 2025-05-20 15:40:00
 */
@Component("sysParamValueConverter")
public class SysParamValueConverter
        implements CreateParam2DoConverter<SysParamValueCreateParam, SysParamValueDO>,
        ReadParam2ArgConverter<SysParamValueQueryParam, SysParamValueQueryArg>,
        ReadDo2ModelConverter<SysParamValueDO, SysParamValue>,
        UpdateParam2ArgConverter<SysParamValueUpdateParam, SysParamValueUpdateArg, SysParamValue>,
        DeleteParam2ArgConverter<SysParamValueDeleteParam, SysParamValueDeleteArg>,
        DeleteReq2ParamConverter<SysParamValueDeleteRequest, SysParamValueDeleteParam>,
        CreateReq2ParamConverter<SysParamValueCreateRequest, SysParamValueCreateParam>,
        UpdateReq2ParamConverter<SysParamValueUpdateRequest, SysParamValueUpdateParam>,
        ReadPageReq2ParamConverter<SysParamValueQueryCondition, SysParamValueQueryParam> {

    @Override
    public SysParamValueDO c_p2d(SysParamValueCreateParam createParam) {
        return SysParamValueMapper.INSTANCE.convert(createParam);
    }

    @Override
    public List<SysParamValueDO> c_ps2ds(List<SysParamValueCreateParam> params) {
        return params.stream()
                .map(SysParamValueMapper.INSTANCE::convert)
                .collect(Collectors.toList());
    }

    @Override
    public SysParamValueUpdateArg u_p2a(SysParamValueUpdateParam param, SysParamValue currentModel) {
        SysParamValueUpdateArg updateArg = SysParamValueMapper.INSTANCE.convert(param);
        Optional.ofNullable(param.getTargetStatus())
                .ifPresent(innerStatus -> updateArg.setStatusUpdater(StatusUpdater.transfer(currentModel.getStatus().getCode(), innerStatus.getCode())));

        return updateArg;
    }

    @Override
    public SysParamValueQueryArg r_p2a(SysParamValueQueryParam param) {
        return SysParamValueMapper.INSTANCE.convert(param);
    }

    @Override
    public SysParamValueDeleteArg d_p2a(SysParamValueDeleteParam param) {
        return SysParamValueMapper.INSTANCE.convert(param);
    }

    @Override
    public SysParamValue r_d2m(SysParamValueDO dataObject) {
        return SysParamValueMapper.INSTANCE.convert(dataObject);
    }

    @Override
    public List<SysParamValue> r_ds2ms(List<SysParamValueDO> dataObjects) {
        return dataObjects.stream()
                .map(this::r_d2m)
                .collect(Collectors.toList());
    }

    @Override
    public SysParamValueCreateParam c_r2p(SysParamValueCreateRequest req) {
        return SysParamValueMapper.INSTANCE.convert(req);
    }

    @Override
    public SysParamValueUpdateParam u_r2p(SysParamValueUpdateRequest req) {
        return SysParamValueMapper.INSTANCE.convert(req);
    }

    @Override
    public SysParamValueQueryParam r_pr2p(PageRequest<SysParamValueQueryCondition> pageReq) {
        SysParamValueQueryParam queryParam = new SysParamValueQueryParam();
        SysParamValueQueryCondition condition = pageReq.getCondition();
        if (condition != null) {
            queryParam.setParamCode(condition.getParamCode());
            queryParam.setCorpCode(condition.getCorpCode());
            queryParam.setStatus(CommonStatusEnum.of(condition.getStatus()));
        }
        queryParam.setStartIndex(pageReq.getStartIndex());
        queryParam.setPageSize(pageReq.getPageSize());
        return queryParam;
    }

    @Override
    public SysParamValueDeleteParam d_r2p(SysParamValueDeleteRequest req) {
        return SysParamValueMapper.INSTANCE.convert(req);
    }
} 