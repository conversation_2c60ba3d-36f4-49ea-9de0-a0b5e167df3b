package com.sendinfo.ticketing.verification.basic.repository.pay.arg;

import com.sendinfo.ticketing.verification.common.repository.arg.AbstractDeleteArg;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 交易记录删除参数
 * 用于封装trans_info表的删除条件，支持租户隔离和软删除
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class TransInfoDeleteArg extends AbstractDeleteArg {
    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 修改人
     */
    private String modifyBy;
} 