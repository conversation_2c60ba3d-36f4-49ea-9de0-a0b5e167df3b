package com.sendinfo.ticketing.verification.basic.api.infra.request;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

import com.sendinfo.ticketing.verification.basic.enums.CommonStatusEnum;

/**
 * 系统参数分组查询条件
 * 
 * <AUTHOR> 2025-07-22 11:45:32
 */
@Getter
@Setter
@ToString
public class SysParamGroupQueryCondition implements Serializable {
    private static final long serialVersionUID = -4876543210123456790L;

    /**
     * 企业编码
     */
    @NotBlank(message = "企业编码不能为空")
    private String corpCode;

    /**
     * 功能模块编码(菜单编码)
     */
    @Size(max = 128, message = "功能模块编码长度不能超过128")
    private String moduleCode;

    /**
     * 分组编码
     */
    @Size(max = 64, message = "分组编码长度不能超过64")
    private String groupCode;

    /**
     * 分组名称(模糊查询)
     */
    @Size(max = 128, message = "分组名称长度不能超过128")
    private String groupName;

    /**
     * 模块类型：1表示前端；2表示后台
     */
    private Integer moduleType;
}
