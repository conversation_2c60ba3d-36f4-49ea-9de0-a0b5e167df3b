package com.sendinfo.ticketing.verification.basic.api.system.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Set;

/**
 * 通过业务角色ID查询角色业务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18 10:49
 */
@Getter
@Setter
@ToString
public class RoleBusinessRoleIdsQueryRequest implements Serializable {

	/**
	 * 企业码
	 */
	@NotNull(message = "企业码不能为空")
	private String corpCode;

	/**
	 * 业务类型，travel：旅行社
	 */
	private String businessType;

	/**
	 * 角色ID列表
	 */
	private Set<Long> roleIds;
}
