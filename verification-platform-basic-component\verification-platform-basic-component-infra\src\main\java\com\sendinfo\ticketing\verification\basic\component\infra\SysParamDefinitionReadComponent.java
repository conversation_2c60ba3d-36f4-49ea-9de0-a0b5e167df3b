package com.sendinfo.ticketing.verification.basic.component.infra;

import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamDefinitionQueryParam;
import com.sendinfo.ticketing.verification.basic.model.infra.SysParamDefinition;
import com.sendinfo.ticketing.verification.basic.model.infra.SysParamDefinitionCountByComponent;
import com.sendinfo.ticketing.verification.common.component.ReadComponent;

import java.util.List;

public interface SysParamDefinitionReadComponent
        extends ReadComponent<Long, SysParamDefinitionQueryParam, SysParamDefinition> {

    /**
     * 根据参数编码查询参数定义
     *
     * @param paramCode
     * @param corpCode
     * @return
     */
    SysParamDefinition queryByParamCode(String paramCode, String corpCode);

    /**
     * 根据组件编码查询参数定义列表
     * 
     * @param componentCode 组件编码
     * @return 参数定义列表
     */
    List<SysParamDefinition> queryByComponentCode(String componentCode);

    /**
     * 根据组件编码查询参数定义数量
     *
     * @param componentCodes 组件编码列表
     * @param corpCode       企业编码
     * @return 参数定义数量列表
     */
    List<SysParamDefinitionCountByComponent> countByComponentCode(List<String> componentCodes, String corpCode);

    /**
     * 根据组件编码列表查询参数定义列表
     *
     * @param componentCodeList 组件编码列表
     * @return 参数定义列表
     */
    List<SysParamDefinition> queryByComponentCodeList(List<String> componentCodeList);

}