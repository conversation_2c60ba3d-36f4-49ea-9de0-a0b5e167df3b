package com.sendinfo.ticketing.verification.basic.api.park.request;

import lombok.Data;

import java.io.Serializable;

/**
 * 售票窗口查询条件
 * <AUTHOR>
 */
@Data
public class TktPosQueryCondition implements Serializable {
    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 园区ID
     */
    private Long parkId;

    /**
     * 自助机标识（0:否，1：是）
     */
    private String ssmFlag;

    /**
     * 售票员ID
     */
    private Long sellerId;

    /**
     * 窗口编号
     */
    private String posCode;

    /**
     * 窗口名称
     */
    private String posName;

    /**
     * 窗口MAC
     */
    private String posMac;

    /**
     * 终端号
     */
    private String tid;

    /**
     * 终端类型 1：pc端 2：移动端
     */
    private Integer terminalType;

    /**
     * 部门ID
     */
    private Long deptId;
} 