package com.sendinfo.ticketing.verification.basic.repository.pay.dao;

import com.alibaba.nacos.api.exception.NacosException;
import com.sendinfo.ticketing.verification.basic.repository.common.config.SaasPwMybatisConfig;
import com.sendinfo.ticketing.verification.basic.repository.common.sharding.DynamicDataSourceMappingConfigPullStorage;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.PayChanleDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.PayChanleQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.PayChanleUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dao.impl.PayChanleDaoImpl;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.PayChanleDO;
import com.sendinfo.ticketing.verification.common.repository.arg.StatusUpdater;
import org.junit.Test;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.AbstractTransactionalJUnit4SpringContextTests;

import java.util.List;
import java.util.Random;

import static org.junit.Assert.*;

/**
 * 支付渠道数据访问层单元测试
 * <p>
 * 本测试类覆盖了PayChanleDao的所有方法，包括：
 * 1. 插入操作测试
 * 2. 批量插入操作测试
 * 3. 根据ID查询测试
 * 4. 条件查询测试
 * 5. 计数查询测试
 * 6. 更新操作测试
 * 7. 软删除操作测试
 * 8. 分页查询测试
 * <p>
 * 测试目的：验证PayChanleDao的所有方法都能正常工作，确保数据访问层的正确性
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootTest(classes = PayChanleDaoTest.Config.class)
@EnableAutoConfiguration
@TestPropertySource(locations = "classpath:test-application.properties")
public class PayChanleDaoTest extends AbstractTransactionalJUnit4SpringContextTests {

    @Autowired
    private PayChanleDao payChanleDao;

    /**
     * 测试用的企业编码
     */
    private static final String TEST_CORP_CODE = "TEST_CORP_001";

    /**
     * 测试用户
     */
    private static final String TEST_USER = "test_user";

    /**
     * 测试产品ID
     */
    private static final Long TEST_PRODUCT_ID = 1L;

    /**
     * 测试配置类
     * 配置MyBatis和DAO Bean
     */
    @Configuration
    @Import(SaasPwMybatisConfig.class)
    static class Config {

        @Bean
        public PayChanleDao payChanleDao(@Qualifier(SaasPwMybatisConfig.BEAN_NAME_SQL_SESSION_TEMPLATE) SqlSessionTemplate sqlSessionTemplate) {
            return new PayChanleDaoImpl(sqlSessionTemplate);
        }

        @Bean
        DynamicDataSourceMappingConfigPullStorage dynamicDataSourceMappingConfigPullStorage(@Value("${nacos.config.server-addr}") String serverAddr,
                                                                                            @Value("${nacos.config.namespace}") String namespace,
                                                                                            @Value("${verification-platform-basic.nacos.tenant-datasource-mapping.data-id:saas-pw-datasource-sharding-mapping.json}") String dataId,
                                                                                            @Value("${verification-platform-basic.nacos.tenant-datasource-mapping.group:DEFAULT_GROUP}") String group,
                                                                                            @Value("${verification-platform-basic.nacos.tenant-datasource-mapping.pulled.timeout:3000}") Integer timeout) throws NacosException {
            return new DynamicDataSourceMappingConfigPullStorage(serverAddr, namespace, dataId, group, timeout);
        }
    }

    /**
     * 测试插入操作
     * <p>
     * 测试步骤：
     * 1. 创建测试支付渠道数据
     * 2. 执行插入操作
     * 3. 验证主键ID被自动生成
     * 4. 通过ID查询验证记录确实被插入
     * 5. 验证插入的数据字段值正确
     */
    @Test
    public void testInsert() {
        // 1. 创建测试支付渠道数据
        PayChanleDO payChanleDO = createTestDO();

        // 2. 执行插入操作
        payChanleDao.insert(payChanleDO);

        // 3. 验证主键ID被自动生成
        assertNotNull("插入后主键ID应该被自动生成", payChanleDO.getId());

        // 4. 通过ID查询验证记录确实被插入
        PayChanleDO insertedPayChanle = payChanleDao.queryById(payChanleDO.getId());
        assertNotNull("插入后应能通过ID查询到记录", insertedPayChanle);
        assertEquals("产品ID应该一致", payChanleDO.getProductId(), insertedPayChanle.getProductId());
        assertEquals("渠道名称应该一致", payChanleDO.getChanleName(), insertedPayChanle.getChanleName());
        assertEquals("企业编码应该一致", TEST_CORP_CODE, insertedPayChanle.getCorpCode());
    }

    /**
     * 测试批量插入操作
     * <p>
     * 测试步骤：
     * 1. 创建多个测试支付渠道数据
     * 2. 执行批量插入操作
     * 3. 验证所有记录的主键ID都被自动生成
     * 4. 通过ID查询验证所有记录都被正确插入
     */
    @Test
    public void testBatchInsert() {
        // 1. 创建多个测试支付渠道数据
        List<PayChanleDO> payChanleList = new java.util.ArrayList<>();
        for (int i = 0; i < 3; i++) {
            PayChanleDO payChanleDO = createTestDO();
            payChanleList.add(payChanleDO);
        }

        // 2. 执行批量插入操作
        payChanleDao.batchInsert(payChanleList);

        // 3. 查询插入数据
        PayChanleQueryArg queryArg = new PayChanleQueryArg();
        queryArg.setCorpCode(TEST_CORP_CODE);
        List<PayChanleDO> payChanleDOList = payChanleDao.queryByArg(queryArg);

        // 4. 通过ID查询验证所有记录都被正确插入
        for (PayChanleDO payChanleDO : payChanleDOList) {
            assertNotNull("每个记录都应该生成ID", payChanleDO.getId());
        }
    }

    /**
     * 测试根据ID查询操作
     * <p>
     * 测试步骤：
     * 1. 插入测试记录
     * 2. 使用插入记录的ID进行查询
     * 3. 验证查询结果正确
     */
    @Test
    public void testQueryById() {
        // 1. 插入测试记录
        PayChanleDO payChanleDO = createTestDO();
        payChanleDao.insert(payChanleDO);

        // 2. 使用插入记录的ID进行查询
        PayChanleDO queriedPayChanle = payChanleDao.queryById(payChanleDO.getId());

        // 3. 验证查询结果正确
        assertNotNull("应该能查询到记录", queriedPayChanle);
        assertEquals("ID应该一致", payChanleDO.getId(), queriedPayChanle.getId());
        assertEquals("产品ID应该一致", payChanleDO.getProductId(), queriedPayChanle.getProductId());
        assertEquals("渠道名称应该一致", payChanleDO.getChanleName(), queriedPayChanle.getChanleName());
    }

    /**
     * 测试更新操作
     * <p>
     * 测试步骤：
     * 1. 插入测试记录
     * 2. 创建更新参数并设置新的值
     * 3. 执行更新操作
     * 4. 验证更新结果
     * 5. 查询更新后的记录验证字段值已更新
     */
    @Test
    public void testUpdateByArg() {
        // 1. 插入测试记录
        PayChanleDO payChanleDO = createTestDO();
        payChanleDao.insert(payChanleDO);

        // 2. 创建更新参数并设置新的值
        PayChanleUpdateArg updateArg = new PayChanleUpdateArg();
        updateArg.setId(payChanleDO.getId());
        updateArg.setCorpCode(payChanleDO.getCorpCode());
        updateArg.setChanleName("更新后的渠道名称");
        updateArg.setRemark("更新后的备注");
        updateArg.setModifyBy(TEST_USER);

        // 3. 执行更新操作
        int updateCount = payChanleDao.updateByArg(updateArg);

        // 4. 验证更新结果
        assertEquals("应该更新1条记录", 1, updateCount);

        // 5. 查询更新后的记录验证字段值已更新
        PayChanleDO updatedPayChanle = payChanleDao.queryById(payChanleDO.getId());
        assertEquals("渠道名称应该已更新", "更新后的渠道名称", updatedPayChanle.getChanleName());
        assertEquals("备注应该已更新", "更新后的备注", updatedPayChanle.getRemark());
    }

    /**
     * 测试带状态更新器的更新操作
     * <p>
     * 测试步骤：
     * 1. 插入测试记录（设置为未启用状态）
     * 2. 创建更新参数，使用状态更新器
     * 3. 执行更新操作
     * 4. 验证更新结果
     * 5. 查询更新后的记录验证状态已更新
     */
    @Test
    public void testUpdateWithStatusUpdater() {
        // 1. 插入测试记录（设置为未启用状态）
        PayChanleDO payChanleDO = createTestDO();
        payChanleDO.setUseFlag("F"); // 未启用状态
        payChanleDao.insert(payChanleDO);

        // 2. 创建更新参数，使用状态更新器
        PayChanleUpdateArg updateArg = new PayChanleUpdateArg();
        updateArg.setId(payChanleDO.getId());
        updateArg.setCorpCode(payChanleDO.getCorpCode());
        updateArg.setUseFlagUpdater(new StatusUpdater<>("F", "T"));
        updateArg.setModifyBy(TEST_USER);

        // 3. 执行更新操作
        int updateCount = payChanleDao.updateByArg(updateArg);

        // 4. 验证更新结果
        assertEquals("应该更新1条记录", 1, updateCount);

        // 5. 查询更新后的记录验证状态已更新
        PayChanleDO updatedPayChanle = payChanleDao.queryById(payChanleDO.getId());
        assertEquals("启用标识应该已更新为T", "T", updatedPayChanle.getUseFlag());

        // 6. 测试错误的状态更新（当前状态不匹配）
        updateArg.setUseFlagUpdater(new StatusUpdater<>("F", "T"));
        int wrongUpdateCount = payChanleDao.updateByArg(updateArg);
        assertEquals("状态不匹配时应该更新0条记录", 0, wrongUpdateCount);
    }

    /**
     * 测试软删除操作
     * <p>
     * 测试步骤：
     * 1. 插入测试记录
     * 2. 验证记录存在且未删除
     * 3. 创建删除参数并执行软删除
     * 4. 验证删除结果
     * 5. 查询记录验证已被软删除
     */
    @Test
    public void testSoftDeleteByArg() {
        // 1. 插入测试记录
        PayChanleDO payChanleDO = createTestDO();
        payChanleDao.insert(payChanleDO);
        assertNotNull("插入后主键ID应该被自动生成", payChanleDO.getId());

        // 2. 验证记录存在且未删除
        PayChanleDO insertedPayChanle = payChanleDao.queryById(payChanleDO.getId());
        assertNotNull("删除前应能查询到记录", insertedPayChanle);
        assertEquals("删除标志应该为F", "F", insertedPayChanle.getDeleted());

        // 3. 创建删除参数并执行软删除
        PayChanleDeleteArg deleteArg = new PayChanleDeleteArg();
        deleteArg.setId(payChanleDO.getId());
        deleteArg.setCorpCode(payChanleDO.getCorpCode());
        deleteArg.setModifyBy(TEST_USER);
        payChanleDao.softDeleteByArg(deleteArg);

        // 4. 验证删除结果
        PayChanleDO deletedPayChanle = payChanleDao.queryById(payChanleDO.getId());
        assertNull("软删除后应该查询不到记录", deletedPayChanle);
    }

    /**
     * 测试分页查询操作
     * <p>
     * 测试步骤：
     * 1. 插入测试记录
     * 2. 创建分页查询参数
     * 3. 执行分页查询
     * 4. 验证分页结果
     */
    @Test
    public void testPageableQuery() {
        // 1. 插入测试记录
        PayChanleDO payChanleDO = createTestDO();
        payChanleDao.insert(payChanleDO);

        // 2. 创建分页查询参数
        PayChanleQueryArg queryArg = new PayChanleQueryArg();
        queryArg.setCorpCode(TEST_CORP_CODE);
        queryArg.setOffset(0);
        queryArg.setLimit(10);

        // 3. 执行分页查询
        List<PayChanleDO> firstPageResults = payChanleDao.queryByArg(queryArg);
        assertNotNull("分页查询结果不应为null", firstPageResults);
        assertTrue("分页查询结果应该包含记录", firstPageResults.size() > 0);

        // 4. 测试第二页查询
        queryArg.setOffset(10);
        List<PayChanleDO> secondPageResults = payChanleDao.queryByArg(queryArg);
        assertNotNull("第二页查询结果不应为null", secondPageResults);
    }

    /**
     * 测试条件查询操作
     * <p>
     * 测试步骤：
     * 1. 插入多个测试记录（不同状态）
     * 2. 创建条件查询参数
     * 3. 执行条件查询
     * 4. 验证查询结果
     */
    @Test
    public void testQueryByArg() {
        // 1. 插入多个测试记录（不同状态）
        PayChanleDO payChanleDO1 = createTestDO();
        payChanleDO1.setUseFlag("T");
        payChanleDao.insert(payChanleDO1);

        PayChanleDO payChanleDO2 = createTestDO();
        payChanleDO2.setUseFlag("F");
        payChanleDao.insert(payChanleDO2);

        // 2. 创建条件查询参数
        PayChanleQueryArg queryArg = new PayChanleQueryArg();
        queryArg.setCorpCode(TEST_CORP_CODE);
        queryArg.setUseFlag("T");

        // 3. 执行条件查询
        List<PayChanleDO> resultList = payChanleDao.queryByArg(queryArg);

        // 4. 验证查询结果
        assertNotNull("查询结果不应为null", resultList);
        assertTrue("查询结果应该包含记录", resultList.size() > 0);
        for (PayChanleDO payChanle : resultList) {
            assertEquals("所有查询结果的使用标志应该为T", "T", payChanle.getUseFlag());
        }
    }

    /**
     * 测试计数查询操作
     * <p>
     * 测试步骤：
     * 1. 插入多个测试记录
     * 2. 创建计数查询参数
     * 3. 执行计数查询
     * 4. 验证计数结果
     */
    @Test
    public void testCountByArg() {
        // 1. 插入多个测试记录
        PayChanleDO payChanleDO1 = createTestDO();
        payChanleDO1.setUseFlag("T");
        payChanleDao.insert(payChanleDO1);

        PayChanleDO payChanleDO2 = createTestDO();
        payChanleDO2.setUseFlag("T");
        payChanleDao.insert(payChanleDO2);

        // 2. 创建计数查询参数
        PayChanleQueryArg queryArg = new PayChanleQueryArg();
        queryArg.setCorpCode(TEST_CORP_CODE);
        queryArg.setUseFlag("T");

        // 3. 执行计数查询
        int count = payChanleDao.countByArg(queryArg);

        // 4. 验证计数结果
        assertTrue("计数结果应该大于0", count > 0);
    }

    /**
     * 测试根据ID删除操作
     * <p>
     * 测试步骤：
     * 1. 插入测试记录
     * 2. 验证记录存在
     * 3. 执行根据ID删除操作
     * 4. 验证删除结果
     */
    @Test
    public void testDeleteById() {
        // 1. 插入测试记录
        PayChanleDO payChanleDO = createTestDO();
        payChanleDao.insert(payChanleDO);

        // 2. 验证记录存在
        PayChanleDO insertedPayChanle = payChanleDao.queryById(payChanleDO.getId());
        assertNotNull("删除前应能查询到记录", insertedPayChanle);

        // 3. 执行根据ID删除操作
        payChanleDao.deleteById(payChanleDO.getId());

        // 4. 验证删除结果
        PayChanleDO deletedPayChanle = payChanleDao.queryById(payChanleDO.getId());
        assertNull("删除后应该查询不到记录", deletedPayChanle);
    }

    /**
     * 创建测试用的PayChanleDO对象
     * <p>
     * 创建步骤：
     * 1. 创建PayChanleDO实例
     * 2. 设置所有必要字段的值
     * 3. 使用随机数确保测试数据的唯一性
     * 4. 返回配置好的测试对象
     *
     * @return 配置好的测试PayChanleDO对象
     */
    private PayChanleDO createTestDO() {
        PayChanleDO payChanleDO = new PayChanleDO();

        // 生成随机数确保测试数据唯一性
        Random random = new Random();
        int randomNum = random.nextInt(10000);

        payChanleDO.setCorpCode(TEST_CORP_CODE);
        payChanleDO.setProductId(TEST_PRODUCT_ID);
        payChanleDO.setChanleName("测试渠道" + randomNum);
        payChanleDO.setChanleCode("TEST_CHANLE_" + randomNum);
        payChanleDO.setPayProductCode("TEST_PAY_PRODUCT_" + randomNum);
        payChanleDO.setRemark("测试备注" + randomNum);
        payChanleDO.setUseFlag("T");
        payChanleDO.setCreateBy(TEST_USER);
        payChanleDO.setModifyBy(TEST_USER);
        payChanleDO.setDeleted("F");

        return payChanleDO;
    }
} 