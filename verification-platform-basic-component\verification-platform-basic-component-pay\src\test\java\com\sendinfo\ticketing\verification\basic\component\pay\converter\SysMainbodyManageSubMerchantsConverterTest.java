package com.sendinfo.ticketing.verification.basic.component.pay.converter;

import com.sendinfo.ticketing.verification.basic.component.pay.param.SysMainbodyManageSubMerchantsCreateParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.SysMainbodyManageSubMerchantsQueryParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.SysMainbodyManageSubMerchantsUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.pay.SysMainbodyManageSubMerchants;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageSubMerchantsQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageSubMerchantsUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysMainbodyManageSubMerchantsDO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;

/**
 * SysMainbodyManageSubMerchantsConverter单元测试
 * 测试经营主体子商户转换器的各种转换功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RunWith(MockitoJUnitRunner.class)
public class SysMainbodyManageSubMerchantsConverterTest {

    @InjectMocks
    private SysMainbodyManageSubMerchantsConverter converter;

    // 测试常量
    private static final Long TEST_ID = 1L;
    private static final String TEST_CORP_CODE = "CORP001";
    private static final String TEST_SUB_MERCHANTS_NAME = "子商户A";
    private static final String TEST_MERCHANT_NO = "MNO001";
    private static final String TEST_CREATE_BY = "testUser";
    private static final String TEST_MODIFY_BY = "testUser";

    @Test
    public void testCreateParam2Do_ShouldConvertSuccessfully() {
        SysMainbodyManageSubMerchantsCreateParam createParam = createCreateParam();
        SysMainbodyManageSubMerchantsDO result = converter.c_p2d(createParam);
        assertNotNull(result);
        assertEquals(TEST_CORP_CODE, result.getCorpCode());
        assertEquals(TEST_SUB_MERCHANTS_NAME, result.getSubMerchantsName());
        assertEquals(TEST_MERCHANT_NO, result.getMerchantNo());
        assertEquals(TEST_CREATE_BY, result.getCreateBy());
    }

    @Test
    public void testCreateParam2Do_WithNullInput_ShouldReturnNull() {
        assertNull(converter.c_p2d(null));
    }

    @Test
    public void testReadDo2Model_ShouldConvertSuccessfully() {
        SysMainbodyManageSubMerchantsDO dataObject = createDO();
        SysMainbodyManageSubMerchants result = converter.r_d2m(dataObject);
        assertNotNull(result);
        assertEquals(TEST_ID, result.getId());
        assertEquals(TEST_CORP_CODE, result.getCorpCode());
        assertEquals(TEST_SUB_MERCHANTS_NAME, result.getSubMerchantsName());
        assertEquals(TEST_MERCHANT_NO, result.getMerchantNo());
        assertEquals(TEST_CREATE_BY, result.getCreateBy());
        assertEquals(TEST_MODIFY_BY, result.getModifyBy());
    }

    @Test
    public void testReadDo2Model_WithNullInput_ShouldReturnNull() {
        assertNull(converter.r_d2m(null));
    }

    @Test
    public void testReadDoList2ModelList_ShouldConvertSuccessfully() {
        List<SysMainbodyManageSubMerchantsDO> doList = Arrays.asList(createDO(), createDO());
        List<SysMainbodyManageSubMerchants> result = converter.r_ds2ms(doList);
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(TEST_ID, result.get(0).getId());
        assertEquals(TEST_SUB_MERCHANTS_NAME, result.get(0).getSubMerchantsName());
        assertEquals(TEST_ID, result.get(1).getId());
        assertEquals(TEST_SUB_MERCHANTS_NAME, result.get(1).getSubMerchantsName());
    }

    @Test
    public void testReadDoList2ModelList_WithNullInput_ShouldReturnNull() {
        assertNull(converter.r_ds2ms(null));
    }

    @Test
    public void testReadDoList2ModelList_WithEmptyList_ShouldReturnEmptyList() {
        List<SysMainbodyManageSubMerchantsDO> emptyList = Arrays.asList();
        List<SysMainbodyManageSubMerchants> result = converter.r_ds2ms(emptyList);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testReadQueryParam2QueryArg_ShouldConvertSuccessfully() {
        SysMainbodyManageSubMerchantsQueryParam queryParam = createQueryParam();
        SysMainbodyManageSubMerchantsQueryArg result = converter.r_p2a(queryParam);
        assertNotNull(result);
        assertEquals(TEST_CORP_CODE, result.getCorpCode());
        assertEquals(TEST_SUB_MERCHANTS_NAME, result.getSubMerchantsName());
        assertEquals(TEST_MERCHANT_NO, result.getMerchantNo());
    }

    @Test
    public void testReadQueryParam2QueryArg_WithNullInput_ShouldReturnNull() {
        assertNull(converter.r_p2a(null));
    }

    @Test
    public void testUpdateParam2UpdateArg_ShouldConvertSuccessfully() {
        SysMainbodyManageSubMerchantsUpdateParam updateParam = createUpdateParam();
        SysMainbodyManageSubMerchants currentModel = createModel();
        SysMainbodyManageSubMerchantsUpdateArg result = converter.u_p2a(updateParam, currentModel);
        assertNotNull(result);
        assertEquals(TEST_CORP_CODE, result.getCorpCode());
        assertEquals(TEST_SUB_MERCHANTS_NAME, result.getSubMerchantsName());
        assertEquals(TEST_MERCHANT_NO, result.getMerchantNo());
        assertEquals(TEST_MODIFY_BY, result.getModifyBy());
    }

    @Test
    public void testUpdateParam2UpdateArg_WithNullInput_ShouldReturnNull() {
        SysMainbodyManageSubMerchants currentModel = createModel();
        assertNull(converter.u_p2a(null, currentModel));
    }

    // ==================== 测试数据构建方法 ====================
    private SysMainbodyManageSubMerchantsCreateParam createCreateParam() {
        SysMainbodyManageSubMerchantsCreateParam param = new SysMainbodyManageSubMerchantsCreateParam();
        param.setCorpCode(TEST_CORP_CODE);
        param.setSubMerchantsName(TEST_SUB_MERCHANTS_NAME);
        param.setMerchantNo(TEST_MERCHANT_NO);
        param.setCreateBy(TEST_CREATE_BY);
        param.setModifyBy(TEST_MODIFY_BY);
        return param;
    }

    private SysMainbodyManageSubMerchantsDO createDO() {
        SysMainbodyManageSubMerchantsDO dataObject = new SysMainbodyManageSubMerchantsDO();
        dataObject.setId(TEST_ID);
        dataObject.setCorpCode(TEST_CORP_CODE);
        dataObject.setSubMerchantsName(TEST_SUB_MERCHANTS_NAME);
        dataObject.setMerchantNo(TEST_MERCHANT_NO);
        dataObject.setCreateBy(TEST_CREATE_BY);
        dataObject.setModifyBy(TEST_MODIFY_BY);
        return dataObject;
    }

    private SysMainbodyManageSubMerchantsQueryParam createQueryParam() {
        SysMainbodyManageSubMerchantsQueryParam param = new SysMainbodyManageSubMerchantsQueryParam();
        param.setCorpCode(TEST_CORP_CODE);
        param.setSubMerchantsName(TEST_SUB_MERCHANTS_NAME);
        param.setMerchantNo(TEST_MERCHANT_NO);
        return param;
    }

    private SysMainbodyManageSubMerchantsUpdateParam createUpdateParam() {
        SysMainbodyManageSubMerchantsUpdateParam param = new SysMainbodyManageSubMerchantsUpdateParam();
        param.setCorpCode(TEST_CORP_CODE);
        param.setSubMerchantsName(TEST_SUB_MERCHANTS_NAME);
        param.setMerchantNo(TEST_MERCHANT_NO);
        param.setModifyBy(TEST_MODIFY_BY);
        return param;
    }

    private SysMainbodyManageSubMerchants createModel() {
        SysMainbodyManageSubMerchants model = new SysMainbodyManageSubMerchants();
        model.setId(TEST_ID);
        model.setCorpCode(TEST_CORP_CODE);
        model.setSubMerchantsName(TEST_SUB_MERCHANTS_NAME);
        model.setMerchantNo(TEST_MERCHANT_NO);
        model.setCreateBy(TEST_CREATE_BY);
        model.setModifyBy(TEST_MODIFY_BY);
        return model;
    }
} 