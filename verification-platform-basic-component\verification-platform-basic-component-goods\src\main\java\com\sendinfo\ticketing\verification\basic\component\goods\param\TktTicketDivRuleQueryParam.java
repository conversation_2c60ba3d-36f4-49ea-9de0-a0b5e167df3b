package com.sendinfo.ticketing.verification.basic.component.goods.param;

import com.sendinfo.ticketing.verification.common.component.param.AbstractQueryParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
public class TktTicketDivRuleQueryParam extends AbstractQueryParam {
    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 票型ID
     */
    private Long ticketId;

    /**
     * 票型编号
     */
    private String ticketCode;

    /**
     * 是否景点组 F:无组（默认） T:有组
     */
    private String groupFlag;

    /**
     * 景点ID或景点组id
     */
    private Integer parkId;

    /**
     * 景区名称
     */
    private String parkName;

    /**
     * 数据字典： 门票、车票、船票
     */
    private String ticketType;

    /**
     * 分成方式 0：固定金额 1：比例
     */
    private String divMethod;

    /**
     * 启用标志:0不启用（默认），1:启用
     */
    private Short useFlag;

    /**
     * 是否必定分成点:F不是必分成点（默认），T:比分成点
     */
    private String divMustFlag;

    /**
     * 是否盈余分成点:F不是（默认），T:是
     */
    private String divCampsurFlag;

    /**
     * 旅代分成方式 0：固定金额， 1：比例
     */
    private String travelDivMethod;

    /**
     * 分成点经营类型
     */
    private String divBusinessType;

    /**
     * 是否进入旅代分成
     */
    private String intoTravelDiv;

    /**
     * 分成扩展
     */
    private Long extendParkId;
} 