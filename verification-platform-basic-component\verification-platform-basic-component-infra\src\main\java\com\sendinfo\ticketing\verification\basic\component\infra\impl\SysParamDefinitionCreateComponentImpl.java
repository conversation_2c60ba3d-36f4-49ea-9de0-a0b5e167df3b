package com.sendinfo.ticketing.verification.basic.component.infra.impl;

import lombok.Getter;
import org.springframework.stereotype.Component;

import com.sendinfo.ticketing.verification.basic.component.infra.SysParamDefinitionCreateComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.converter.SysParamDefinitionConverter;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamDefinitionCreateParam;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.SysParamDefinitionDao;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.SysParamDefinitionDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleCreate;

@Component("sysParamDefinitionCreateComponent")
@Getter
public class SysParamDefinitionCreateComponentImpl
        implements SysParamDefinitionCreateComponent,
        DaoBasedSingleCreate<Long, SysParamDefinitionCreateParam, SysParamDefinitionDO> {

    private final SysParamDefinitionDao dao;
    private final SysParamDefinitionConverter converter;

    public SysParamDefinitionCreateComponentImpl(SysParamDefinitionDao dao, SysParamDefinitionConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }
} 