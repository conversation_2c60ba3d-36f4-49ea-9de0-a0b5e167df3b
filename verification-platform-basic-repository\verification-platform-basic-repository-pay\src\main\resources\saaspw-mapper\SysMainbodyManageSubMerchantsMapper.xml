<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sendinfo.ticketing.verification.basic.repository.pay.dao.SysMainbodyManageSubMerchantsDao">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysMainbodyManageSubMerchantsDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="CORP_CODE" property="corpCode" jdbcType="VARCHAR"/>
        <result column="SUB_MERCHANTS_NAME" property="subMerchantsName" jdbcType="VARCHAR"/>
        <result column="MERCHANT_NO" property="merchantNo" jdbcType="VARCHAR"/>
        <result column="CREATE_BY" property="createBy" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="MODIFY_BY" property="modifyBy" jdbcType="VARCHAR"/>
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="DELETED" property="deleted" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 表名 -->
    <sql id="tableName">sys_mainbody_manage_sub_merchants</sql>

    <!-- 所有列 -->
    <sql id="allColumns">
        id, CORP_CODE, SUB_MERCHANTS_NAME, MERCHANT_NO, CREATE_BY, CREATE_TIME, MODIFY_BY, MODIFY_TIME, DELETED
    </sql>

    <!-- 插入列 -->
    <sql id="insertColumns">
        CORP_CODE, SUB_MERCHANTS_NAME, MERCHANT_NO, CREATE_BY, MODIFY_BY, DELETED, CREATE_TIME, MODIFY_TIME
    </sql>

    <!-- 插入操作 -->
    <insert id="insert" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysMainbodyManageSubMerchantsDO" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO <include refid="tableName"/> (<include refid="insertColumns"/>)
        VALUES (
            #{corpCode,jdbcType=VARCHAR}, #{subMerchantsName,jdbcType=VARCHAR}, #{merchantNo,jdbcType=VARCHAR},
            #{createBy,jdbcType=VARCHAR}, #{modifyBy,jdbcType=VARCHAR}, #{deleted,jdbcType=VARCHAR},
            NOW(), NOW()
        )
    </insert>

    <!-- 根据ID查询 -->
    <select id="queryById" resultMap="BaseResultMap" parameterType="map">
        SELECT <include refid="allColumns"/>
        FROM <include refid="tableName"/>
        WHERE id = #{id,jdbcType=BIGINT}
          AND deleted = 'F'
    </select>

    <!-- 根据参数更新 -->
    <update id="updateByArg" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageSubMerchantsUpdateArg">
        UPDATE <include refid="tableName"/>
        <set>
            <if test="subMerchantsName != null and subMerchantsName != ''">SUB_MERCHANTS_NAME = #{subMerchantsName,jdbcType=VARCHAR},</if>
            <if test="merchantNo != null and merchantNo != ''">MERCHANT_NO = #{merchantNo,jdbcType=VARCHAR},</if>
            <if test="modifyBy != null and modifyBy != ''">MODIFY_BY = #{modifyBy,jdbcType=VARCHAR},</if>
            MODIFY_TIME = NOW()
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
          AND CORP_CODE = #{corpCode,jdbcType=VARCHAR}
          AND deleted = 'F'
    </update>

    <!-- 软删除 -->
    <update id="softDeleteByArg" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageSubMerchantsDeleteArg">
        UPDATE <include refid="tableName"/>
        SET deleted = 'T',
            <choose>
                <when test="modifyBy != null and modifyBy != ''">MODIFY_BY = #{modifyBy,jdbcType=VARCHAR},</when>
                <otherwise>MODIFY_BY = 'SYSTEM_SDA',</otherwise>
            </choose>
            MODIFY_TIME = NOW()
        WHERE CORP_CODE = #{corpCode,jdbcType=VARCHAR}
          AND deleted = 'F'
          <if test="id != null">AND id = #{id,jdbcType=BIGINT}</if>
    </update>

    <!-- 根据ID删除 -->
    <update id="deleteById" parameterType="java.lang.Long">
        UPDATE <include refid="tableName"/>
        SET deleted = 'T',
            MODIFY_BY = 'SYSTEM_DBI',
            MODIFY_TIME = NOW()
        WHERE id = #{id,jdbcType=BIGINT}
          AND deleted = 'F'
    </update>

    <!-- 计数查询 -->
    <select id="countByArg" resultType="int" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageSubMerchantsQueryArg">
        SELECT COUNT(*)
        FROM <include refid="tableName"/>
        <where>
            CORP_CODE = #{corpCode,jdbcType=VARCHAR}
            AND deleted = 'F'
            <if test="subMerchantsName != null and subMerchantsName != ''">AND SUB_MERCHANTS_NAME LIKE CONCAT('%', #{subMerchantsName,jdbcType=VARCHAR}, '%')</if>
            <if test="merchantNo != null and merchantNo != ''">AND MERCHANT_NO = #{merchantNo,jdbcType=VARCHAR}</if>
        </where>
    </select>

    <!-- 条件查询 -->
    <select id="queryByArg" resultMap="BaseResultMap" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageSubMerchantsQueryArg">
        SELECT <include refid="allColumns"/>
        FROM <include refid="tableName"/>
        <where>
            CORP_CODE = #{corpCode,jdbcType=VARCHAR}
            AND deleted = 'F'
            <if test="subMerchantsName != null and subMerchantsName != ''">AND SUB_MERCHANTS_NAME LIKE CONCAT('%', #{subMerchantsName,jdbcType=VARCHAR}, '%')</if>
            <if test="merchantNo != null and merchantNo != ''">AND MERCHANT_NO = #{merchantNo,jdbcType=VARCHAR}</if>
        </where>
        ORDER BY id DESC
        <if test="limit != null and offset != null">
            LIMIT #{offset,jdbcType=INTEGER}, #{limit,jdbcType=INTEGER}
        </if>
    </select>

    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO <include refid="tableName"/> (<include refid="insertColumns"/>)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.corpCode,jdbcType=VARCHAR}, #{item.subMerchantsName,jdbcType=VARCHAR}, #{item.merchantNo,jdbcType=VARCHAR},
                #{item.createBy,jdbcType=VARCHAR}, #{item.modifyBy,jdbcType=VARCHAR}, #{item.deleted,jdbcType=VARCHAR},
                NOW(), NOW()
            )
        </foreach>
    </insert>
</mapper> 