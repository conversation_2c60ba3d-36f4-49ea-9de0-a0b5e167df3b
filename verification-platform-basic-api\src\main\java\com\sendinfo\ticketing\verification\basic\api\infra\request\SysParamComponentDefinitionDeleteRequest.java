/**
 * System parameter component definition delete request
 *
 * <AUTHOR> 2025-07-21 15:40:00
 */
package com.sendinfo.ticketing.verification.basic.api.infra.request;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 系统参数组件定义删除请求
 */
@Getter
@Setter
@ToString
public class SysParamComponentDefinitionDeleteRequest implements Serializable {
    private static final long serialVersionUID = -7654321098765432106L;

    /**
     * 企业编码
     */
    @NotBlank(message = "企业编码不能为空")
    private String corpCode;

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空")
    private Long id;

    /**
     * 修改人
     */
    @NotBlank(message = "修改人不能为空")
    private String modifyBy;
}