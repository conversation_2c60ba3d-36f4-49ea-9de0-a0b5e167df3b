package com.sendinfo.ticketing.verification.basic.component.customer.impl;

import com.sendinfo.ticketing.verification.basic.component.customer.TravelTaxReadComponent;
import com.sendinfo.ticketing.verification.basic.component.customer.converter.TravelTaxConverter;
import com.sendinfo.ticketing.verification.basic.component.customer.param.TravelTaxQueryParam;
import com.sendinfo.ticketing.verification.basic.model.customer.TravelTax;
import com.sendinfo.ticketing.verification.basic.repository.customer.arg.TravelTaxQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.customer.dao.TravelTaxDao;
import com.sendinfo.ticketing.verification.basic.repository.customer.dataobject.TravelTaxDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedCountRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedListRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleRead;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * travel_tax读取组件实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("travelTaxReadComponent")
@Getter
public class TravelTaxReadComponentImpl implements
        TravelTaxReadComponent,
        DaoBasedSingleRead<Long, TravelTaxQueryParam, TravelTax, TravelTaxDO>,
        DaoBasedListRead<Long, TravelTaxQueryParam, TravelTax, TravelTaxDO, TravelTaxQueryArg>,
        DaoBasedCountRead<Long, TravelTaxQueryParam, TravelTax, TravelTaxQueryArg> {

    private final TravelTaxDao dao;
    private final TravelTaxConverter converter;

    public TravelTaxReadComponentImpl(TravelTaxDao dao, TravelTaxConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }

    @Override
    public List<TravelTax> batchQueryByIds(Set<Long> ids) {
        return converter.r_ds2ms(dao.batchQueryByIds(ids));
    }

    @Override
    public List<TravelTax> queryTravelTaxList(TravelTaxQueryParam queryParam) {
        return converter.r_ds2ms(dao.queryByArg(converter.r_p2a(queryParam)));
    }
} 