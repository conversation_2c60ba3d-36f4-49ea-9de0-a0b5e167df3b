<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sendinfo.ticketing.verification.basic.repository.system.dao.SysAuditRoleDao">

    <!--
        票型角色表 sys_audit_role 的ResultMap
        映射DO对象所有字段，包括逻辑删除等通用字段
    -->
    <resultMap id="BaseResultMap" type="com.sendinfo.ticketing.verification.basic.repository.system.dataobject.SysAuditRoleDO">
        <id column="ID" property="id" jdbcType="INTEGER"/>
        <result column="CORP_CODE" property="corpCode" jdbcType="VARCHAR"/>
        <result column="ROLE_NAME" property="roleName" jdbcType="VARCHAR"/>
        <result column="ROLE_TYPE" property="roleType" jdbcType="INTEGER"/>
        <result column="SUBSYSTEM_ID" property="subsystemId" jdbcType="INTEGER"/>
        <result column="USE_FLAG" property="useFlag" jdbcType="CHAR"/>
        <result column="REMARK" property="remark" jdbcType="VARCHAR"/>
        <result column="CREATE_BY" property="createBy" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="MODIFY_BY" property="modifyBy" jdbcType="VARCHAR"/>
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="DELETED" property="deleted" jdbcType="CHAR"/>
    </resultMap>

    <!-- 可复用的所有列 -->
    <sql id="Base_Column_List">
        ID, CORP_CODE, ROLE_NAME, ROLE_TYPE, SUBSYSTEM_ID, USE_FLAG, REMARK, CREATE_BY, CREATE_TIME, MODIFY_BY, MODIFY_TIME, DELETED
    </sql>

    <!-- 表名 -->
    <sql id="tableName">sys_audit_role</sql>

    <!-- 查询条件 -->
    <sql id="Query_Where_Clause">
        <where>
            <if test="corpCode != null and corpCode != ''">
                AND CORP_CODE = #{corpCode,jdbcType=VARCHAR}
            </if>
            <if test="roleName != null and roleName != ''">
                AND ROLE_NAME = #{roleName,jdbcType=VARCHAR}
            </if>
            <if test="roleType != null">
                AND ROLE_TYPE = #{roleType,jdbcType=INTEGER}
            </if>
            <if test="subsystemId != null">
                AND SUBSYSTEM_ID = #{subsystemId,jdbcType=INTEGER}
            </if>
            <if test="useFlag != null and useFlag != ''">
                AND USE_FLAG = #{useFlag,jdbcType=CHAR}
            </if>
            AND DELETED = 'F'
        </where>
    </sql>

    <!-- ========== 基础DAO方法SQL ========== -->

    <!-- 按主键查询 -->
    <select id="queryById" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM <include refid="tableName"/>
        WHERE ID = #{id,jdbcType=INTEGER}
        AND DELETED = 'F'
    </select>

    <!-- 条件查询 -->
    <select id="queryByArg" parameterType="com.sendinfo.ticketing.verification.basic.repository.system.arg.SysAuditRoleQueryArg"
            resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM <include refid="tableName"/>
        <include refid="Query_Where_Clause"/>
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <!-- 条件计数 -->
    <select id="countByArg" parameterType="com.sendinfo.ticketing.verification.basic.repository.system.arg.SysAuditRoleQueryArg"
            resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM <include refid="tableName"/>
        <include refid="Query_Where_Clause"/>
    </select>

    <!-- 插入记录 -->
    <insert id="insert" parameterType="com.sendinfo.ticketing.verification.basic.repository.system.dataobject.SysAuditRoleDO"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="tableName"/> (
            CORP_CODE, ROLE_NAME, ROLE_TYPE, SUBSYSTEM_ID, USE_FLAG, REMARK, CREATE_BY, CREATE_TIME, MODIFY_BY, MODIFY_TIME, DELETED
        )
        VALUES (
            #{corpCode,jdbcType=VARCHAR}, #{roleName,jdbcType=VARCHAR}, #{roleType,jdbcType=INTEGER}, #{subsystemId,jdbcType=INTEGER},
            #{useFlag,jdbcType=CHAR}, #{remark,jdbcType=VARCHAR},
            #{createBy,jdbcType=VARCHAR}, NOW(), #{modifyBy,jdbcType=VARCHAR}, NOW(), #{deleted,jdbcType=CHAR}
        )
    </insert>

    <!-- 更新记录 -->
    <update id="updateByArg" parameterType="com.sendinfo.ticketing.verification.basic.repository.system.arg.SysAuditRoleUpdateArg">
        UPDATE <include refid="tableName"/>
        <set>
            <if test="corpCode != null and corpCode != ''">
                CORP_CODE = #{corpCode,jdbcType=VARCHAR},
            </if>
            <if test="roleName != null and roleName != ''">
                ROLE_NAME = #{roleName,jdbcType=VARCHAR},
            </if>
            <if test="roleType != null">
                ROLE_TYPE = #{roleType,jdbcType=INTEGER},
            </if>
            <if test="subsystemId != null">
                SUBSYSTEM_ID = #{subsystemId,jdbcType=INTEGER},
            </if>
            <if test="useFlag != null and useFlag != ''">
                USE_FLAG = #{useFlag,jdbcType=CHAR},
            </if>
            <if test="remark != null and remark != ''">
                REMARK = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="modifyBy != null and modifyBy != ''">
                MODIFY_BY = #{modifyBy,jdbcType=VARCHAR},
            </if>
            MODIFY_TIME = NOW()
        </set>
        WHERE ID = #{id,jdbcType=INTEGER}
        AND DELETED = 'F'
    </update>

    <!-- 逻辑删除 -->
    <update id="softDeleteByArg" parameterType="com.sendinfo.ticketing.verification.basic.repository.system.arg.SysAuditRoleDeleteArg">
        UPDATE <include refid="tableName"/>
        SET deleted = 'T',
            MODIFY_BY = #{modifyBy,jdbcType=VARCHAR},
            MODIFY_TIME = NOW()
        WHERE ID = #{id,jdbcType=INTEGER}
        AND CORP_CODE = #{corpCode,jdbcType=VARCHAR}
        AND deleted = 'F'
    </update>

    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="tableName"/> (
            CORP_CODE, ROLE_NAME, ROLE_TYPE, SUBSYSTEM_ID, USE_FLAG, REMARK, CREATE_BY, CREATE_TIME, MODIFY_BY, MODIFY_TIME, DELETED
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.corpCode,jdbcType=VARCHAR}, #{item.roleName,jdbcType=VARCHAR}, #{item.roleType,jdbcType=INTEGER}, #{item.subsystemId,jdbcType=INTEGER},
                #{item.useFlag,jdbcType=CHAR}, #{item.remark,jdbcType=VARCHAR},
                #{item.createBy,jdbcType=VARCHAR}, NOW(), #{item.modifyBy,jdbcType=VARCHAR}, NOW(), #{item.deleted,jdbcType=CHAR}
            )
        </foreach>
    </insert>

    <!-- 按主键查询 -->
    <select id="querySysAuditRoleByIds" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM <include refid="tableName"/>
        WHERE corp_code = #{corpCode,jdbcType=VARCHAR}
        AND id IN
        <foreach item="item" collection="ids" separator="," open="(" close=")" index="">
            #{item,jdbcType=INTEGER}
        </foreach>
        AND DELETED = 'F'
    </select>

</mapper> 