package com.sendinfo.ticketing.verification.basic.component.infra.impl;

import lombok.Getter;
import org.springframework.stereotype.Component;

import com.sendinfo.ticketing.verification.basic.component.infra.SysParamGroupCreateComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.converter.SysParamGroupConverter;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamGroupCreateParam;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.SysParamGroupDao;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.SysParamGroupDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleCreate;

/**
 * 系统参数分组创建组件实现
 * 
 * <AUTHOR> 2025-07-22 11:45:32
 */
@Component("sysParamGroupCreateComponent")
@Getter
public class SysParamGroupCreateComponentImpl
        implements SysParamGroupCreateComponent,
        DaoBasedSingleCreate<Long, SysParamGroupCreateParam, SysParamGroupDO> {

    private final SysParamGroupDao dao;
    private final SysParamGroupConverter converter;

    public SysParamGroupCreateComponentImpl(SysParamGroupDao dao, SysParamGroupConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }
}
