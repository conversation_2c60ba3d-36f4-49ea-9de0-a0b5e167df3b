package com.sendinfo.ticketing.verification.basic.api.goods.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2025-7-24 17:34
 */
@Getter
@Setter
@ToString(callSuper = true)
public class UserTicketRelationQueryRequest implements Serializable {

    private static final long serialVersionUID = -2495129052300685380L;

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 用户ID
     */
    @NotNull(message = "查询的用户ID不能为空")
    private Long userId;

    /**
     * 客户端上次同步的时间,首次不用传
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date operateTime;
}
