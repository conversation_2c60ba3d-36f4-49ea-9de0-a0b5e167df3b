package com.sendinfo.ticketing.verification.basic.component.pay;

import com.sendinfo.ticketing.verification.basic.component.pay.param.SysMainbodyManageMmSubMerchantsQueryParam;
import com.sendinfo.ticketing.verification.basic.model.pay.SysMainbodyManageMmSubMerchants;
import com.sendinfo.ticketing.verification.common.component.ReadComponent;

/**
 * 经营主体支付配置子商户关联表读取组件接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface SysMainbodyManageMmSubMerchantsReadComponent extends ReadComponent<Long, SysMainbodyManageMmSubMerchantsQueryParam, SysMainbodyManageMmSubMerchants> {

} 