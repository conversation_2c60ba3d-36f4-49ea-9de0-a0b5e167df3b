package com.sendinfo.ticketing.verification.basic.model.pay;

import com.sendinfo.ticketing.verification.basic.model.pay.properties.SysMainbodyManageMmSubMerchantsDefinitionPropertyKey;
import com.sendinfo.ticketing.verification.common.model.properties.AbstractProperties;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 经营主体支付配置子商户关联表模型对象
 * 用于业务逻辑层和数据展示层之间的数据传输
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class SysMainbodyManageMmSubMerchants extends AbstractProperties<SysMainbodyManageMmSubMerchantsDefinitionPropertyKey> implements Serializable {

    private static final long serialVersionUID = 4593495408938569641L;
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 经营主体管理ID
     * 关联经营主体管理表的主键
     */
    private Long mainbodyManageId;

    /**
     * 子商户ID
     * 关联子商户表的主键
     */
    private Long subMerchantsId;

    /**
     * 子商户名称
     * 子商户的显示名称
     */
    private String subMerchantsName;

    /**
     * 子商户号
     * 子商户的唯一标识号
     */
    private String merchantNo;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String modifyBy;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 删除标志F未删除,T已删除
     */
    private String deleted;
} 