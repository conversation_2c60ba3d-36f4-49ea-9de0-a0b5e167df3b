package com.sendinfo.ticketing.verification.basic.component.goods;

import com.sendinfo.ticketing.verification.basic.component.goods.param.GoodsPropertyCreateParam;
import com.sendinfo.ticketing.verification.basic.api.context.TicketSyncContext;
import com.sendinfo.ticketing.verification.basic.exception.VerificationBizException;
import com.sendinfo.ticketing.verification.common.component.CreateComponent;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-07-18 16:13
 */
public interface GoodsPropertyCreateComponent extends CreateComponent<GoodsPropertyCreateParam, Long> {
    /**
     * 批量 保存
     *
     * @param paramList
     * @return
     */
    Long batchInsert(List<GoodsPropertyCreateParam> paramList);

    /**
     * binlog数据保存
     *
     * @param context binlog同步过来的数据组装context
     */
    void saveForTicketSync(TicketSyncContext context) throws VerificationBizException;
}
