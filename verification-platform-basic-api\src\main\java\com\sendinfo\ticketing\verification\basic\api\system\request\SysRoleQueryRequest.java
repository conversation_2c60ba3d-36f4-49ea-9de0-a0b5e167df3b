package com.sendinfo.ticketing.verification.basic.api.system.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * 系统角色查询请求参数
 * <AUTHOR>
 */
@Getter
@Setter
@ToString(callSuper = true)
public class SysRoleQueryRequest implements Serializable {

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 账户类型 0:系统管理员 1:景区管理员 2:分销商 3:推广员
     */
    private String accType;

    /**
     * 启用状态 F:停用 T:启动
     */
    private String useFlag;

    /**
     * 所属部门ID
     */
    private Integer deptParentId;

    /**
     * 所属部门
     */
    private String deptLevel;
} 