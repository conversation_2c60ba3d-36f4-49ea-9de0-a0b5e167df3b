package com.sendinfo.ticketing.verification.basic.repository.goods.dao.impl;

import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TicketCalendarPriceDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TicketCalendarPriceQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TicketCalendarPriceUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dao.TicketCalendarPriceDao;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TicketCalendarPriceDO;
import com.sendinfo.ticketing.verification.repository.jdbc.Statement;
import com.sendinfo.ticketing.verification.repository.jdbc.mybatis.SqlSessionBatchInsertDAO;
import com.sendinfo.ticketing.verification.repository.jdbc.mybatis.SqlSessionCountableDAO;
import com.sendinfo.ticketing.verification.repository.jdbc.mybatis.SqlSessionGenericDAO;
import com.sendinfo.ticketing.verification.repository.jdbc.mybatis.SqlSessionQueryableDAO;
import lombok.Getter;
import org.apache.ibatis.session.SqlSession;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
@Repository("ticketCalendarPriceDao")
public class TicketCalendarPriceDaoImpl implements TicketCalendarPriceDao,
        SqlSessionGenericDAO<Long, TicketCalendarPriceDO, TicketCalendarPriceUpdateArg, TicketCalendarPriceDeleteArg>,
        SqlSessionCountableDAO<TicketCalendarPriceQueryArg>,
        SqlSessionQueryableDAO<TicketCalendarPriceQueryArg, TicketCalendarPriceDO>,
        SqlSessionBatchInsertDAO<Long, TicketCalendarPriceDO> {

    private final SqlSession sqlSession;
    private final Statement statement;

    public TicketCalendarPriceDaoImpl(SqlSessionTemplate sqlSessionTemplate) {
        this.sqlSession = sqlSessionTemplate;
        this.statement = Statement.of(TicketCalendarPriceDao.class);
    }

    @Override
    public TicketCalendarPriceDO queryByTicketId(Long ticketId, String corpCode) {
        Map<String, Object> params = new HashMap<>();
        params.put("ticketId", ticketId);
        params.put("corpCode", corpCode);
        return sqlSession.selectOne(statement.get("queryByTicketId"), params);
    }

    @Override
    public List<TicketCalendarPriceDO> batchQueryByTicketIds(List<Long> ticketIds, String corpCode) {
        Map<String, Object> params = new HashMap<>();
        params.put("ticketIds", ticketIds);
        params.put("corpCode", corpCode);
        return sqlSession.selectList(statement.get("batchQueryByTicketIds"), params);
    }

    @Override
    public void sort(TicketCalendarPriceQueryArg queryArg) {
        if (queryArg != null && queryArg.getSortItems().isEmpty()) {
            queryArg.desc("id");
        }
    }
}
