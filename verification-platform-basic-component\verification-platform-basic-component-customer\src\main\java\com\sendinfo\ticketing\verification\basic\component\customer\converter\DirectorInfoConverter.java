package com.sendinfo.ticketing.verification.basic.component.customer.converter;

import com.sendinfo.ticketing.verification.basic.component.customer.mapper.DirectorInfoMapper;
import com.sendinfo.ticketing.verification.basic.component.customer.param.DirectorInfoCreateParam;
import com.sendinfo.ticketing.verification.basic.component.customer.param.DirectorInfoQueryParam;
import com.sendinfo.ticketing.verification.basic.component.customer.param.DirectorInfoUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.customer.DirectorInfo;
import com.sendinfo.ticketing.verification.basic.repository.customer.arg.DirectorInfoQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.customer.arg.DirectorInfoUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.customer.dataobject.DirectorInfoDO;
import com.sendinfo.ticketing.verification.common.component.converter.CreateParam2DoConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadDo2ModelConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadParam2ArgConverter;
import com.sendinfo.ticketing.verification.common.component.converter.UpdateParam2ArgConverter;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 主管信息转换器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("directorInfoConverter")
public class DirectorInfoConverter implements
        CreateParam2DoConverter<DirectorInfoCreateParam, DirectorInfoDO>,
        ReadDo2ModelConverter<DirectorInfoDO, DirectorInfo>,
        ReadParam2ArgConverter<DirectorInfoQueryParam, DirectorInfoQueryArg>,
        UpdateParam2ArgConverter<DirectorInfoUpdateParam, DirectorInfoUpdateArg, DirectorInfo> {

    private final DirectorInfoMapper mapper = DirectorInfoMapper.INSTANCE;

    @Override
    public DirectorInfoDO c_p2d(DirectorInfoCreateParam createParam) {
        if (createParam == null) {
            return null;
        }
        return mapper.convert(createParam);
    }

    @Override
    public DirectorInfo r_d2m(DirectorInfoDO dataObject) {
        if (dataObject == null) {
            return null;
        }
        return mapper.convert(dataObject);
    }

    @Override
    public List<DirectorInfo> r_ds2ms(List<DirectorInfoDO> dataObjects) {
        if (dataObjects == null) {
            return null;
        }
        return dataObjects.stream().map(this::r_d2m).collect(Collectors.toList());
    }

    @Override
    public DirectorInfoQueryArg r_p2a(DirectorInfoQueryParam queryParam) {
        if (queryParam == null) {
            return null;
        }
        return mapper.convert(queryParam);
    }

    @Override
    public DirectorInfoUpdateArg u_p2a(DirectorInfoUpdateParam updateParam, DirectorInfo currentModel) {
        if (updateParam == null) {
            return null;
        }
        return mapper.convert(updateParam);
    }
} 