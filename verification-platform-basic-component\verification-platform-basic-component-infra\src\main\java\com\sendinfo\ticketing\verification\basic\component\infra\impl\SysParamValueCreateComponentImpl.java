package com.sendinfo.ticketing.verification.basic.component.infra.impl;

import java.util.List;

import lombok.Getter;
import org.springframework.stereotype.Component;

import com.sendinfo.ticketing.verification.basic.component.infra.SysParamValueCreateComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.converter.SysParamValueConverter;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamValueCreateParam;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.SysParamValueDao;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.SysParamValueDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedBatchCreate;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleCreate;

/**
 * 系统参数值创建组件实现
 *
 * <AUTHOR> 2025-05-20 15:40:00
 */
@Component("sysParamValueCreateComponent")
@Getter
public class SysParamValueCreateComponentImpl
        implements SysParamValueCreateComponent,
        DaoBasedSingleCreate<Long, SysParamValueCreateParam, SysParamValueDO>,
        DaoBasedBatchCreate<Long, SysParamValueCreateParam, SysParamValueDO> {

    private final SysParamValueDao dao;
    private final SysParamValueConverter converter;

    public SysParamValueCreateComponentImpl(SysParamValueDao dao, SysParamValueConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }
}