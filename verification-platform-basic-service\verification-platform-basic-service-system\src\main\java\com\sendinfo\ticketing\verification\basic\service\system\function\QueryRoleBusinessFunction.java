package com.sendinfo.ticketing.verification.basic.service.system.function;

import com.sendinfo.ticketing.verification.basic.api.system.request.RoleBusinessQueryRequest;
import com.sendinfo.ticketing.verification.basic.api.system.request.RoleBusinessRoleIdsQueryRequest;
import com.sendinfo.ticketing.verification.basic.component.system.RoleBusinessReadComponent;
import com.sendinfo.ticketing.verification.basic.component.system.converter.RoleBusinessConverter;
import com.sendinfo.ticketing.verification.basic.model.system.RoleBusiness;
import com.sendinfo.ticketing.verification.basic.model.system.error.SystemErrorDef;
import com.sendinfo.ticketing.verification.basic.service.system.enums.SystemAttachmentKey;
import com.sendinfo.ticketing.verification.common.service.function.QuerySingleByIdFunction;
import com.sendinfo.ticketing.verification.common.service.stereotype.Function;
import com.sendinfo.ticketing.verification.common.service.support.QuerySingleByIdLogicAction;
import com.sendinfo.ticketing.verification.flow.Hint;
import com.sendinfo.ticketing.verification.flow.Question;
import com.sendinfo.ticketing.verification.flow.function.LogicAction;
import lombok.Getter;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Set;

/**
 * 角色业务权限查询功能
 * 对应表：role_business
 *
 * <AUTHOR>
 */
@Getter
@Function("queryRoleBusinessFunction")
public class QueryRoleBusinessFunction implements QuerySingleByIdFunction<Long> {

    private final RoleBusinessReadComponent roleBusinessReadComponent;
    private final RoleBusinessConverter converter;
    private final LogicAction<Long> querySingleById;

    public QueryRoleBusinessFunction(RoleBusinessReadComponent roleBusinessReadComponent,
                                     RoleBusinessConverter converter) {
        this.roleBusinessReadComponent = roleBusinessReadComponent;
        this.converter = converter;
        this.querySingleById = new QuerySingleByIdLogicAction<>(roleBusinessReadComponent,
                SystemAttachmentKey.ROLE_BUSINESS_ATTACHMENT_KEY, SystemErrorDef.QUERY_ROLE_BUSINESS_ERROR);
    }

    /**
     * 查询角色业务权限列表
     *
     * @param question 查询参数
     * @return Hint
     */
    public Hint queryList(Question<RoleBusinessQueryRequest> question) {
        RoleBusinessQueryRequest request = question.getBody();
        List<RoleBusiness> list = roleBusinessReadComponent.queryList(converter.r_r2p(request));
        question.setAttachment(SystemAttachmentKey.ROLE_BUSINESS_DATA_LIST_ATTACHMENT_KEY, list);
        return Hint.gotoNext();
    }

    /**
     * 查询角色业务权限列表
     *
     * @param question  查询参数
     * @return  Hint
     */
    public Hint queryByRoleIds(Question<RoleBusinessRoleIdsQueryRequest> question) {
        RoleBusinessRoleIdsQueryRequest request = question.getBody();
        String businessType = request.getBusinessType();
        String corpCode = request.getCorpCode();
        Set<Long> roleIds = request.getRoleIds();
        List<RoleBusiness> list = roleBusinessReadComponent.queryByRoleIds(businessType, roleIds, corpCode);
        question.setAttachment(SystemAttachmentKey.ROLE_BUSINESS_DATA_LIST_ATTACHMENT_KEY, list);

        return Hint.gotoNext();
    }
} 