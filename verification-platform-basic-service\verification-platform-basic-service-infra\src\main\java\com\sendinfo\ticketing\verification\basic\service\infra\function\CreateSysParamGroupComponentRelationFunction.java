/**
 * 系统参数分组组件关联创建功能
 *
 * <AUTHOR> 2025-07-29 01:42:03
 */
package com.sendinfo.ticketing.verification.basic.service.infra.function;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import org.springframework.transaction.annotation.Transactional;

import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamGroupComponentRelationCreateRequest;
import com.sendinfo.ticketing.verification.basic.common.constant.StringConstants;
import com.sendinfo.ticketing.verification.basic.common.util.CorpCodeUtil;
import com.sendinfo.ticketing.verification.basic.component.infra.SysParamDefinitionReadComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.SysParamGroupComponentRelationCreateComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.SysParamGroupReadComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.SysParamValueCreateComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.converter.SysParamGroupComponentRelationConverter;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamGroupComponentRelationCreateParam;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamValueCreateParam;
import com.sendinfo.ticketing.verification.basic.enums.CommonStatusEnum;
import com.sendinfo.ticketing.verification.basic.exception.VerificationBizRuntimeException;
import com.sendinfo.ticketing.verification.basic.model.infra.SysParamComponentDefinition;
import com.sendinfo.ticketing.verification.basic.model.infra.SysParamDefinition;
import com.sendinfo.ticketing.verification.basic.model.infra.SysParamGroup;
import com.sendinfo.ticketing.verification.basic.model.infra.error.InfraErrorDef;
import com.sendinfo.ticketing.verification.basic.service.infra.enums.SysParamComponentDefinitionAttachmentKey;
import com.sendinfo.ticketing.verification.basic.service.infra.enums.SysParamGroupAttachmentKey;
import com.sendinfo.ticketing.verification.basic.service.infra.processor.SysParamValueStatementProcessor;
import com.sendinfo.ticketing.verification.basic.service.infra.processor.factory.SysParamValueStatementProcessorFactory;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;
import com.sendinfo.ticketing.verification.common.api.result.Results;
import com.sendinfo.ticketing.verification.common.service.function.CreateSingleByRequestFunction;
import com.sendinfo.ticketing.verification.common.service.stereotype.Function;
import com.sendinfo.ticketing.verification.common.service.support.CreateSingleByRequestSuccessfulAction;
import com.sendinfo.ticketing.verification.flow.Question;
import com.sendinfo.ticketing.verification.flow.function.SuccessfulAction;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 系统参数分组组件关联创建功能
 *
 * <AUTHOR> 2025-07-29 01:42:03
 */
@Getter
@Function("createSysParamGroupComponentRelationFunction")
@Slf4j
public class CreateSysParamGroupComponentRelationFunction implements
        CreateSingleByRequestFunction<SysParamGroupComponentRelationCreateRequest, Long> {

    private final SuccessfulAction<SysParamGroupComponentRelationCreateRequest, ResultModel<Long>> createSingleByRequest;

    private final SysParamDefinitionReadComponent sysParamDefinitionReadComponent;
    private final SysParamGroupReadComponent sysParamGroupReadComponent;
    private final SysParamGroupComponentRelationCreateComponent sysParamGroupComponentRelationCreateComponent;
    private final SysParamGroupComponentRelationConverter sysParamGroupComponentRelationConverter;
    private final SysParamValueStatementProcessorFactory sysParamValueStatementProcessorFactory;

    public CreateSysParamGroupComponentRelationFunction(
            SysParamGroupComponentRelationCreateComponent createComponent,
            SysParamGroupComponentRelationConverter converter,
            SysParamDefinitionReadComponent sysParamDefinitionReadComponent,
            SysParamGroupReadComponent sysParamGroupReadComponent,
            SysParamValueCreateComponent sysParamValueCreateComponent,
            SysParamValueStatementProcessorFactory sysParamValueStatementProcessorFactory) {

        this.createSingleByRequest = new CreateSingleByRequestSuccessfulAction<>(
                createComponent, converter::c_r2p);
        this.sysParamDefinitionReadComponent = sysParamDefinitionReadComponent;
        this.sysParamGroupReadComponent = sysParamGroupReadComponent;
        this.sysParamGroupComponentRelationCreateComponent = createComponent;
        this.sysParamGroupComponentRelationConverter = converter;
        this.sysParamValueStatementProcessorFactory = sysParamValueStatementProcessorFactory;
    }


    public ResultModel<List<Long>> createWithSysParamValues(Question<SysParamGroupComponentRelationCreateRequest> question) {
        SysParamGroupComponentRelationCreateRequest request = question.getBody();
        SysParamGroup sysParamGroup = question.getAttachment(SysParamGroupAttachmentKey.SYS_PARAM_GROUP_ATTACHMENT_KEY);
        Map<String, SysParamComponentDefinition> componentCode2EntityMap =
                question.getAttachment(SysParamComponentDefinitionAttachmentKey.SYS_PARAM_COMPONENT_DEFINITION_CODE_2_ENTITY_MAP_ATTACHMENT_KEY);
        log.info("[CreateSysParamGroupComponentRelationFunction] 开始创建参数分组组件关联并批量创建系统参数值, " +
                        "groupCode: {}, componentCodeList: {}",
                request.getGroupCode(),
                request.getComponentCodeList());

        List<Long> relationIds = new ArrayList<>();

        List<SysParamGroupComponentRelationCreateParam> relationCreateParamList = new ArrayList<>();
        List<SysParamValueCreateParam> sysParamValueCreateParamList = new ArrayList<>();

        for (Map.Entry<String, SysParamComponentDefinition> componentDefinitionEntry :
                componentCode2EntityMap.entrySet()) {
            String componentCode = componentDefinitionEntry.getKey();
            SysParamComponentDefinition componentDefinition = componentDefinitionEntry.getValue();
            SysParamGroupComponentRelationCreateParam relationCreateParam = buildRelationCreateParam(request,
                    componentCode);

            relationCreateParamList.add(relationCreateParam);

            // 为当前组件批量创建系统参数值
            sysParamValueCreateParamList.addAll(
                    buildSysParamValueCreateParamList(sysParamGroup,
                            componentDefinition,
                            request.getCorpCode(),
                            request.getCreateBy()));
        }
        sysParamGroupComponentRelationCreateComponent.batchCreateRelationAndSysParamDefaultValue(relationCreateParamList,
                sysParamValueCreateParamList);

        log.info("[CreateSysParamGroupComponentRelationFunction] 成功创建参数分组组件关联并批量初始化系统参数值, relationIds: {}",
                relationIds);
        return Results.success(relationIds);
    }

    /**
     * 构建参数分组与组件关联创建参数
     *
     * @param originalRequest 原始请求
     * @param componentCode   单个组件编码
     * @return 参数分组与组件关联创建参数
     */
    private SysParamGroupComponentRelationCreateParam buildRelationCreateParam(
            SysParamGroupComponentRelationCreateRequest originalRequest, String componentCode) {
        SysParamGroupComponentRelationCreateParam param = new SysParamGroupComponentRelationCreateParam();
        param.setCorpCode(originalRequest.getCorpCode());
        param.setGroupCode(originalRequest.getGroupCode());
        param.setComponentCode(componentCode);
        param.setCreateBy(originalRequest.getCreateBy());
        return param;
    }


    private List<SysParamValueCreateParam> buildSysParamValueCreateParamList(SysParamGroup sysParamGroup,
                                                   SysParamComponentDefinition componentDefinition,
                                                   String corpCode, String createBy) {


        if (!CorpCodeUtil.isTopLevelCorpCode(corpCode)) {
            throw new IllegalArgumentException("corpCode不是超级租户，暂不支持此功能 corpCode=" + corpCode);
        }
        // 根据组件code查询系统参数定义列表
        List<SysParamDefinition> paramDefinitions =
                sysParamDefinitionReadComponent.queryByComponentCode(componentDefinition.getComponentCode());
        if (paramDefinitions == null || paramDefinitions.isEmpty()) {
            log.info("[CreateSysParamGroupComponentRelationFunction] 组件{} 没有参数定义，跳过",
                    componentDefinition.getComponentCode());
            return Collections.EMPTY_LIST;
        }
        String moduleCode = sysParamGroup.getModuleCode();

        // 遍历定义列表，创建参数值创建参数列表
        List<SysParamValueCreateParam> sysParamValueCreateParams = new ArrayList<>();
        for (SysParamDefinition definition : paramDefinitions) {
            // 根据数据类型获取处理器并计算默认值
            SysParamValueStatementProcessor processor = sysParamValueStatementProcessorFactory.getProcessor(definition.getDataType());
            String defaultValue = "";
            if (processor != null) {
                defaultValue = processor.parseAndGetDefaultSysParamValue(definition.getParamValueStatement());
            } else {
                throw new IllegalArgumentException("不支持的数据类型 " + definition.getDataType());
            }
            // 构建参数Key: moduleCode.componentCode.paramCode
            String paramKey =
                    moduleCode
                    + StringConstants.DOT_SEPARATOR
                    + componentDefinition.getComponentCode()
                    + StringConstants.DOT_SEPARATOR
                    + definition.getParamCode();

            // 创建参数值创建参数
            SysParamValueCreateParam createParam = new SysParamValueCreateParam();
            createParam.setModuleCode(moduleCode);
            createParam.setComponentCode(componentDefinition.getComponentCode());
            createParam.setGroupCode(sysParamGroup.getGroupCode());
            createParam.setParamKey(paramKey);
            createParam.setParamCode(definition.getParamCode());
            createParam.setCorpCode(corpCode);
            createParam.setParamValue(defaultValue);// 全局默认值
            createParam.setTenantCorpCode(corpCode);// 理论上是超级租户
            createParam.setStatus(CommonStatusEnum.ENABLE);
            createParam.setCreateBy(createBy);
            createParam.setModifyBy(createBy);

            sysParamValueCreateParams.add(createParam);
        }
        return sysParamValueCreateParams;
    }
}
