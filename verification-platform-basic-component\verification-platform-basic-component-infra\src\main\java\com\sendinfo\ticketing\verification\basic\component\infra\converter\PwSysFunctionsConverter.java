package com.sendinfo.ticketing.verification.basic.component.infra.converter;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

import com.sendinfo.ticketing.verification.basic.api.infra.request.PwSysFunctionsQueryCondition;
import com.sendinfo.ticketing.verification.basic.component.infra.mapper.PwSysFunctionsMapper;
import com.sendinfo.ticketing.verification.basic.component.infra.param.PwSysFunctionsQueryParam;
import com.sendinfo.ticketing.verification.basic.component.infra.param.PwSysFunctionsUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.infra.PwSysFunctions;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwSysFunctionsQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwSysFunctionsUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.PwSysFunctionsDO;
import com.sendinfo.ticketing.verification.common.api.request.PageRequest;
import com.sendinfo.ticketing.verification.common.component.converter.ReadDo2ModelConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadPageReq2ParamConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadParam2ArgConverter;
import com.sendinfo.ticketing.verification.common.component.converter.UpdateParam2ArgConverter;

/**
 * 系统功能模块转换器
 *
 * <AUTHOR> 2025-07-24
 */
@Component("pwSysFunctionsConverter")
public class PwSysFunctionsConverter implements
        ReadParam2ArgConverter<PwSysFunctionsQueryParam, PwSysFunctionsQueryArg>,
        ReadPageReq2ParamConverter<PwSysFunctionsQueryCondition, PwSysFunctionsQueryParam>,
        ReadDo2ModelConverter<PwSysFunctionsDO, PwSysFunctions>,
        UpdateParam2ArgConverter<PwSysFunctionsUpdateParam, PwSysFunctionsUpdateArg, PwSysFunctions> {

    @Override
    public PwSysFunctions r_d2m(PwSysFunctionsDO dataObject) {
        return PwSysFunctionsMapper.INSTANCE.convert(dataObject);
    }

    @Override
    public PwSysFunctionsQueryArg r_p2a(PwSysFunctionsQueryParam param) {
        return PwSysFunctionsMapper.INSTANCE.convert(param);
    }

    @Override
    public PwSysFunctionsUpdateArg u_p2a(PwSysFunctionsUpdateParam param, PwSysFunctions currentModel) {
        return PwSysFunctionsMapper.INSTANCE.convert(param);
    }

    @Override
    public PwSysFunctionsQueryParam r_pr2p(PageRequest<PwSysFunctionsQueryCondition> pageRequest) {
        PwSysFunctionsQueryCondition condition = pageRequest.getCondition();
        PwSysFunctionsQueryParam param = new PwSysFunctionsQueryParam();

        if (condition != null) {
            param = PwSysFunctionsMapper.INSTANCE.convert(condition);
        }

        param.setPageSize(pageRequest.getPageSize());
        param.setStartIndex((pageRequest.getCurrentPage() - 1) * pageRequest.getPageSize());

        return param;
    }

    /**
     * DO列表转Model列表
     */
    public List<PwSysFunctions> r_ds2ms(List<PwSysFunctionsDO> dataObjects) {
        if (dataObjects == null) {
            return null;
        }
        return dataObjects.stream().map(this::r_d2m).collect(Collectors.toList());
    }
}
