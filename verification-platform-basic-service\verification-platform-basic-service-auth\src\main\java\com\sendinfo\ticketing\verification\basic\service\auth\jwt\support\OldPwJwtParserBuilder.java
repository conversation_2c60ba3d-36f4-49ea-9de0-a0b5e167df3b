package com.sendinfo.ticketing.verification.basic.service.auth.jwt.support;

import com.sendinfo.ticketing.verification.basic.exception.VerificationBizRuntimeException;
import com.sendinfo.ticketing.verification.basic.model.auth.error.AuthErrorDef;
import com.sendinfo.ticketing.verification.basic.service.auth.config.OldPwJwtConfigurationProperties;
import com.sendinfo.ticketing.verification.basic.service.auth.jwt.JwtIdentity;
import com.sendinfo.ticketing.verification.basic.service.auth.jwt.JwtParserFactory;
import io.jsonwebtoken.JwtParser;
import io.jsonwebtoken.Jwts;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * <AUTHOR>
 * @since 2025-07-23 13:26:48
 */
@Component("oldPwJwtParserBuilder")
@Slf4j
public class OldPwJwtParserBuilder implements JwtParserFactory.JwtParserBuilder {

    private final OldPwJwtConfigurationProperties properties;


    public OldPwJwtParserBuilder(OldPwJwtConfigurationProperties oldPwJwtConfigurationProperties) {
        this.properties = oldPwJwtConfigurationProperties;
    }

    @Override
    public JwtIdentity supportedIdentity() {
        return JwtIdentity.OLD_PW;
    }

    @Override
    public JwtParser build() {
        try {
            JwtParser jwtParser = Jwts.parserBuilder()
                    .setSigningKey(properties.getSigningKey())
                    .build();
            if (log.isInfoEnabled()) {
                log.info("JWT Parser initialized successfully with issuer validation and signing key.");
            }
            return jwtParser;
        } catch (IllegalStateException e) {
            log.error("Failed to initialize JwtTokenValidateManager - Signing key unavailable?: {}", e.getMessage());
            throw new VerificationBizRuntimeException(AuthErrorDef.JWT_INIT_ERROR, e);
        }
    }
}
