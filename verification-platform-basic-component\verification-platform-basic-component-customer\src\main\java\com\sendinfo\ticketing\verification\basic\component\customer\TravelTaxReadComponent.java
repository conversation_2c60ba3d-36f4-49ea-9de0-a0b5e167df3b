package com.sendinfo.ticketing.verification.basic.component.customer;

import com.sendinfo.ticketing.verification.basic.component.customer.param.TravelTaxQueryParam;
import com.sendinfo.ticketing.verification.basic.model.customer.TravelTax;
import com.sendinfo.ticketing.verification.common.component.ReadComponent;

import java.util.List;
import java.util.Set;

/**
 * travel_tax信息读取组件接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface TravelTaxReadComponent extends ReadComponent<Long, TravelTaxQueryParam, TravelTax> {

    /**
     * 批量查询travel_tax信息
     *
     * @param ids 主键集合
     * @return travel_tax信息列表
     */
    List<TravelTax> batchQueryByIds(Set<Long> ids);

    /**
     * 查询travel_tax信息列表
     *
     * @param queryParam 查询参数
     * @return travel_tax信息列表
     */
    List<TravelTax> queryTravelTaxList(TravelTaxQueryParam queryParam);
} 