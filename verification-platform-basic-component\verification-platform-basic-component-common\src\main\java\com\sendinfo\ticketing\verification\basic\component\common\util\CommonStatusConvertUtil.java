package com.sendinfo.ticketing.verification.basic.component.common.util;

import java.util.Optional;

import com.sendinfo.ticketing.verification.basic.enums.CommonStatusEnum;


/**
 * <AUTHOR> 2025-06-30 09:41:31
 */
public class CommonStatusConvertUtil {

    private CommonStatusConvertUtil() {
    }


    public static CommonStatusEnum convert(Integer status) {
        return Optional.ofNullable(status)
                .map(CommonStatusEnum::of)
                .orElse(null);
    }


    public static Integer convert(CommonStatusEnum status) {
        return Optional.ofNullable(status)
                .map(CommonStatusEnum::getCode)
                .orElse(null);
    }

}
