package com.sendinfo.ticketing.verification.basic.api.goods.request;

import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 打印设置查询条件
 * <AUTHOR>
 */
@Data
public class TktPrintQueryCondition implements Serializable {
    private static final long serialVersionUID = -4876543210123456789L;
    
    /**
     * 票据ID
     */
    private Long ticketId;

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 状态：1-启用，0-禁用
     */
    private Integer status;
} 