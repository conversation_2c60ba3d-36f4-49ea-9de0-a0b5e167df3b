package com.sendinfo.ticketing.verification.basic.repository.system.dao;

import com.sendinfo.ticketing.verification.basic.model.system.SysAuditRole;
import com.sendinfo.ticketing.verification.basic.repository.system.arg.SysAuditRoleDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.system.arg.SysAuditRoleQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.system.arg.SysAuditRoleUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.system.dataobject.SysAuditRoleDO;
import com.sendinfo.ticketing.verification.common.repository.BatchInsertDAO;
import com.sendinfo.ticketing.verification.common.repository.CountableDAO;
import com.sendinfo.ticketing.verification.common.repository.GenericDAO;
import com.sendinfo.ticketing.verification.common.repository.QueryableDAO;

import java.util.List;
import java.util.Set;

/**
 * 票型角色数据访问接口
 *
 * <AUTHOR>
 */
public interface SysAuditRoleDao extends
        GenericDAO<Integer, SysAuditRoleDO, SysAuditRoleUpdateArg, SysAuditRoleDeleteArg>,
        CountableDAO<SysAuditRoleQueryArg>,
        QueryableDAO<SysAuditRoleQueryArg, SysAuditRoleDO>,
        BatchInsertDAO<Integer, SysAuditRoleDO> {

	/**
	 * 根据ID集合查询角色信息
	 *
	 * @param ids       id集合
	 * @param corpCode  企业编码
	 * @return  List<SysAuditRole>
	 */
	List<SysAuditRole> querySysAuditRoleByIds(Set<Integer> ids, String corpCode);
} 