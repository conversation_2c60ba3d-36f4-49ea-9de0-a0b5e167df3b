package com.sendinfo.ticketing.verification.basic.component.infra.impl;

import lombok.Getter;
import org.springframework.stereotype.Component;

import com.sendinfo.ticketing.verification.basic.component.infra.SysParamGroupManagementComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.converter.SysParamGroupConverter;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamGroupUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.infra.SysParamGroup;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamGroupUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.SysParamGroupDao;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleUpdate;

/**
 * 系统参数分组管理组件实现
 * 
 * <AUTHOR> 2025-07-22 11:45:32
 */
@Component("sysParamGroupManagementComponent")
@Getter
public class SysParamGroupManagementComponentImpl
        implements SysParamGroupManagementComponent,
        DaoBasedSingleUpdate<SysParamGroupUpdateParam, SysParamGroupUpdateArg, SysParamGroup> {

    private final SysParamGroupDao dao;
    private final SysParamGroupConverter converter;

    public SysParamGroupManagementComponentImpl(SysParamGroupDao dao, SysParamGroupConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }
}
