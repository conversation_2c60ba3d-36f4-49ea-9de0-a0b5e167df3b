package com.sendinfo.ticketing.verification.basic.repository.pay.dataobject;

import com.sendinfo.ticketing.verification.common.repository.dataobject.AbstractTenantBaseDO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 支付标签数据对象
 * 对应数据库表 sys_pay_set
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class SysPaySetDO extends AbstractTenantBaseDO<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 支付标签名称
     */
    private String paylableName;

    /**
     * 支付平台编码
     */
    private String payTypeCode;

    /**
     * 支付标签图标
     */
    private String paylableIcon;

    /**
     * 启用状态
     */
    private String useFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 售票员默认收款方式
     */
    private String payDefault;

    /**
     * 主体ID
     */
    private Long mainbodyId;

    /**
     * 支付类型（主扫，被扫，现金）
     */
    private String payType;

    /**
     * 自定义支付名称
     */
    private String payName;

    /**
     * 迁移数据
     */
    private String transferParam;
} 