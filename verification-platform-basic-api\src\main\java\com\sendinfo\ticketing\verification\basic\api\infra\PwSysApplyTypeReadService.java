package com.sendinfo.ticketing.verification.basic.api.infra;

import java.util.List;

import com.sendinfo.ticketing.verification.basic.model.infra.PwSysApplyType;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

/**
 * 系统应用类型读取服务接口
 * 
 * <AUTHOR> 2025-07-24 15:40:00
 */
public interface PwSysApplyTypeReadService {

    /**
     * 根据子系统ID查询应用类型列表
     * 只返回启用且未删除的记录
     * 
     * @param subsystemId 子系统ID
     * @return 应用类型列表
     */
    ResultModel<List<PwSysApplyType>> queryBySubSystemId(Integer subsystemId);

}