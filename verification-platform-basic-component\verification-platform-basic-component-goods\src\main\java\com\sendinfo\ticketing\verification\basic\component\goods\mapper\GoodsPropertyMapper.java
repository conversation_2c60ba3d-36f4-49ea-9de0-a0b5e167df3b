package com.sendinfo.ticketing.verification.basic.component.goods.mapper;

import com.sendinfo.ticketing.verification.basic.api.goods.request.GoodsPropertyCreateRequest;
import com.sendinfo.ticketing.verification.basic.api.goods.request.GoodsPropertyDeleteRequest;
import com.sendinfo.ticketing.verification.basic.api.goods.request.GoodsPropertyQueryRequest;
import com.sendinfo.ticketing.verification.basic.api.goods.request.GoodsPropertyUpdateRequest;
import com.sendinfo.ticketing.verification.basic.component.goods.param.GoodsPropertyCreateParam;
import com.sendinfo.ticketing.verification.basic.component.goods.param.GoodsPropertyDeleteParam;
import com.sendinfo.ticketing.verification.basic.component.goods.param.GoodsPropertyQueryParam;
import com.sendinfo.ticketing.verification.basic.component.goods.param.GoodsPropertyUpdateParam;
import com.sendinfo.ticketing.verification.basic.enums.TicketPropertyType;
import com.sendinfo.ticketing.verification.basic.model.goods.GoodsProperty;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.GoodsPropertyDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.GoodsPropertyQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.GoodsPropertyUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.GoodsPropertyDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2025-07-18 16:16
 */
@Mapper(imports = {
        TicketPropertyType.class
})
public interface GoodsPropertyMapper {

    GoodsPropertyMapper INSTANCE = Mappers.getMapper(GoodsPropertyMapper.class);

    /**
     * 数据对象转化为API对象
     *
     * @param dataObject DO对象
     * @return api对象
     */
    @Mapping(target = "type", expression = "java(TicketPropertyType.of(dataObject.getType()))")
    GoodsProperty convert(GoodsPropertyDO dataObject);

    /**
     * 创建参数对象转化为数据对象
     *
     * @param param 创建参数对象
     * @return 数据对象
     */
    @Mapping(target = "type", expression = "java(param.getType() == null ? null : param.getType().getCode())")
    GoodsPropertyDO convert(GoodsPropertyCreateParam param);

    /**
     * 删除参数对象转化为数据删除对象
     *
     * @param param 删除参数对象
     * @return 数据删除对象
     */
    @Mapping(target = "type", expression = "java(param.getType() == null ? null : param.getType().getCode())")
    GoodsPropertyDeleteArg convert(GoodsPropertyDeleteParam param);

    /**
     * 查询参数对象转化为数据查询对象
     *
     * @param param 查询参数对象
     * @return 数据查询对象
     */
    @Mapping(target = "type", expression = "java(param.getType() == null ? null : param.getType().getCode())")
    GoodsPropertyQueryArg convert(GoodsPropertyQueryParam param);

    /**
     * 修改参数对象转化为数据修改对象
     *
     * @param param 修改参数对象
     * @return 数据修改对象
     */
    @Mapping(target = "type", expression = "java(param.getType() == null ? null : param.getType().getCode())")
    GoodsPropertyUpdateArg convert(GoodsPropertyUpdateParam param);

    /**
     * 创建请求对象转化为创建参数对象
     *
     * @param request 创建请求对象
     * @return 创建参数对象
     */
    @Mapping(target = "type", expression = "java(TicketPropertyType.of(request.getType()))")
    GoodsPropertyCreateParam convert(GoodsPropertyCreateRequest request);

    /**
     * 创建请求对象转化为创建参数对象
     *
     * @param request 删除请求对象
     * @return 删除参数对象
     */
    @Mapping(target = "type", expression = "java(TicketPropertyType.of(request.getType()))")
    GoodsPropertyDeleteParam convert(GoodsPropertyDeleteRequest request);

    /**
     * 创建请求对象转化为创建参数对象
     *
     * @param request 查询请求对象
     * @return 查询参数对象
     */
    @Mapping(target = "type", expression = "java(TicketPropertyType.of(request.getType()))")
    GoodsPropertyQueryParam convert(GoodsPropertyQueryRequest request);

    /**
     * 创建请求对象转化为创建参数对象
     *
     * @param request 修改请求对象
     * @return 修改参数对象
     */
    @Mapping(target = "type", expression = "java(TicketPropertyType.of(request.getType()))")
    GoodsPropertyUpdateParam convert(GoodsPropertyUpdateRequest request);

}
