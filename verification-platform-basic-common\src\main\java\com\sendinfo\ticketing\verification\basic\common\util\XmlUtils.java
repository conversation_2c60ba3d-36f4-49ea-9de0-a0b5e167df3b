package com.sendinfo.ticketing.verification.basic.common.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import com.sendinfo.ticketing.verification.basic.common.log.VerificationPlatformBasicLoggers;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025-01-24 16:14
 */
public class XmlUtils {
    private static final XmlMapper xmlMapper;

    static {
        xmlMapper = new XmlMapper();
        // 配置通用设置
        xmlMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        xmlMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        xmlMapper.registerModule(new JavaTimeModule());
    }

    /**
     * XML 字符串转换为 Map
     */
    public static Map<String, Object> xmlToMap(String xml) {
        try {
            return xmlMapper.readValue(xml, new TypeReference<Map<String, Object>>() {});
        } catch (IOException e) {
             VerificationPlatformBasicLoggers.APPLICATION_LOGGER.error("Failed to parse XML to Map", e);
            throw new RuntimeException("XML parsing failed", e);
        }
    }

    /**
     * XML 字符串转换为指定类型对象
     */
    public static <T> T xmlToObject(String xml, Class<T> clazz) {
        try {
            return xmlMapper.readValue(xml, clazz);
        } catch (IOException e) {
            VerificationPlatformBasicLoggers.APPLICATION_LOGGER.error("Failed to parse XML to Object", e);
            throw new RuntimeException("XML parsing failed", e);
        }
    }

    /**
     * 对象转换为 XML 字符串
     */
    public static String objectToXml(Object obj) {
        try {
            return xmlMapper.writeValueAsString(obj);
        } catch (IOException e) {
            VerificationPlatformBasicLoggers.APPLICATION_LOGGER.error("Failed to convert Object to XML", e);
            throw new RuntimeException("XML conversion failed", e);
        }
    }

    /**
     * XML 字符串转换为复杂类型（如带泛型的类型）
     */
    public static <T> T xmlToObject(String xml, TypeReference<T> typeReference) {
        try {
            return xmlMapper.readValue(xml, typeReference);
        } catch (IOException e) {
            VerificationPlatformBasicLoggers.APPLICATION_LOGGER.error("Failed to parse XML to Complex Object", e);
            throw new RuntimeException("XML parsing failed", e);
        }
    }
}