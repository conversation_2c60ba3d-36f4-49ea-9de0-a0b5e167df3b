package com.sendinfo.ticketing.verification.basic.service.system;

import com.alibaba.fastjson.JSON;
import com.sendinfo.ticketing.verification.basic.api.system.SysAuditRoleReadService;
import com.sendinfo.ticketing.verification.basic.api.system.request.SysAuditRoleQueryRequest;
import com.sendinfo.ticketing.verification.basic.model.system.SysAuditRole;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.apache.dubbo.config.bootstrap.DubboBootstrap;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 票型角色读取服务客户端测试
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/4 16:20
 */
public class SysAuditRoleReadClientTest {

	public static void main(String[] args) {
		// 配置注册中心
		RegistryConfig registryConfig = new RegistryConfig();
		registryConfig.setAddress("nacos://testnacos.sendinfocs.com:8848?namespace=bb13d33c-bda0-4552-91c3-173151d43186");

		// 配置DubboBootstrap
		DubboBootstrap bootstrap = DubboBootstrap.getInstance()
				.application("sys-audit-role-read-client")
				.registry(registryConfig);

		// 创建服务引用
		ReferenceConfig<SysAuditRoleReadService> referenceConfig = new ReferenceConfig<>();
		referenceConfig.setInterface(SysAuditRoleReadService.class);
		referenceConfig.setVersion("1.0.0");
		referenceConfig.setTimeout(10000);
		referenceConfig.setCheck(false);

		bootstrap.reference(referenceConfig);
		bootstrap.start();

		// 获取服务代理
		SysAuditRoleReadService sysAuditRoleReadService = referenceConfig.get();

		try {
			// 测试查询票型角色列表
			System.out.println("=== 测试查询票型角色列表 ===");
			SysAuditRoleQueryRequest queryRequest = new SysAuditRoleQueryRequest();
			queryRequest.setCorpCode("TEST_CORP");
			queryRequest.setRoleName("测试角色");
			queryRequest.setRoleType(1);
			queryRequest.setSubsystemId(1);
			queryRequest.setUseFlag("T");
			ResultModel<List<SysAuditRole>> listResult = sysAuditRoleReadService.querySysAuditRoles(queryRequest);
			if (listResult != null && listResult.isSuccess()) {
				System.out.println("列表查询成功，数据条数：" + (listResult.getModel() != null ? listResult.getModel().size() : 0));
				System.out.println("返回数据：" + JSON.toJSONString(listResult.getModel()));
			} else {
				System.out.println("列表查询失败：" + (listResult != null ? listResult.getErrorMessage() : "返回null"));
			}

			// 测试根据ID集合查询票型角色列表
			System.out.println("\n=== 测试根据ID集合查询票型角色列表 ===");
			Set<Integer> ids = new HashSet<>();
			ids.add(1);
			ids.add(2);
			ids.add(3);
			ResultModel<List<SysAuditRole>> batchResult = sysAuditRoleReadService.querySysAuditRoleByIds(ids, "TEST_CORP");
			if (batchResult != null && batchResult.isSuccess()) {
				System.out.println("批量查询成功，数据条数：" + (batchResult.getModel() != null ? batchResult.getModel().size() : 0));
				System.out.println("返回数据：" + JSON.toJSONString(batchResult.getModel()));
			} else {
				System.out.println("批量查询失败：" + (batchResult != null ? batchResult.getErrorMessage() : "返回null"));
			}

		} catch (Exception e) {
			System.err.println("调用异常：" + e.getMessage());
			e.printStackTrace();
		} finally {
			// 清理资源
			bootstrap.stop();
		}
	}
} 