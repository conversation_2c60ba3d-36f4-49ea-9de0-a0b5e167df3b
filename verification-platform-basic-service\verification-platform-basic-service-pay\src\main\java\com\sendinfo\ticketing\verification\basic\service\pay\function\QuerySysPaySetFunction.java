package com.sendinfo.ticketing.verification.basic.service.pay.function;

import com.sendinfo.ticketing.verification.basic.api.pay.request.SysPaySetEnableQueryRequest;
import com.sendinfo.ticketing.verification.basic.component.pay.SysPaySetReadComponent;
import com.sendinfo.ticketing.verification.basic.model.pay.SysPaySet;
import com.sendinfo.ticketing.verification.basic.service.pay.enums.PayAttachmentKey;
import com.sendinfo.ticketing.verification.common.service.stereotype.Function;
import com.sendinfo.ticketing.verification.flow.Hint;
import com.sendinfo.ticketing.verification.flow.Question;
import lombok.Getter;

import java.util.List;

/**
 * @Auther: chengjie.li
 * @Date: 2025/6/13 14:52
 */
@Getter
@Function("querySysPaySetFunction")
public class QuerySysPaySetFunction {

    private final SysPaySetReadComponent sysPaySetReadComponent;

    public QuerySysPaySetFunction(SysPaySetReadComponent sysPaySetReadComponent) {
        this.sysPaySetReadComponent = sysPaySetReadComponent;
    }

    public Hint queryEnableListByPayTypeAndId(Question<SysPaySetEnableQueryRequest> question) {
        SysPaySetEnableQueryRequest request = question.getBody();
        List<SysPaySet> sysPaySetList = sysPaySetReadComponent.queryEnableListByPayTypeAndId(request.getPayType(), request.getIdSet(), request.getCorpCode());
        question.setAttachment(PayAttachmentKey.SYS_PAY_SET_DATA_LIST_ATTACHMENT_KEY, sysPaySetList);
        return Hint.gotoNext();
    }
}
