package com.sendinfo.ticketing.verification.basic.repository.system.arg;

import com.sendinfo.ticketing.verification.common.repository.arg.AbstractQueryArg;
import com.sendinfo.ticketing.verification.common.repository.arg.Pageable;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 票型角色查询参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class SysAuditRoleQueryArg extends AbstractQueryArg implements Pageable {

    /**
     * 企业码
     */
    private String corpCode;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 权限类型
     */
    private Integer roleType;

    /**
     * 子系统ID
     */
    private Integer subsystemId;

    /**
     * 启用状态
     */
    private String useFlag;

    /**
     * 分页偏移量
     */
    private Integer offset;

    /**
     * 分页大小
     */
    private Integer limit;
} 