/**
 * System parameter component definition read service
 *
 * <AUTHOR> 2025-07-21 15:40:00
 */
package com.sendinfo.ticketing.verification.basic.api.infra;

import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamComponentDefinitionQueryCondition;
import com.sendinfo.ticketing.verification.basic.model.infra.SysParamComponentDefinition;
import com.sendinfo.ticketing.verification.common.api.request.PageRequest;
import com.sendinfo.ticketing.verification.common.api.result.PageResultModel;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

/**
 * 系统参数组件定义查询服务
 */
public interface SysParamComponentDefinitionReadService {

    /**
     * 分页查询系统参数组件定义
     *
     * @param pageRequest 分页查询条件
     * @return 分页结果
     */
    PageResultModel<SysParamComponentDefinition> queryPageList(PageRequest<SysParamComponentDefinitionQueryCondition> pageRequest);

    /**
     * 根据ID查询系统参数组件定义
     *
     * @param id 组件定义ID
     * @return 组件定义信息
     */
    ResultModel<SysParamComponentDefinition> queryById(Long id);

    /**
     * 根据组件编码和企业编码查询系统参数组件定义
     *
     * @param componentCode 组件编码
     * @return 组件定义信息
     */
    ResultModel<SysParamComponentDefinition> queryByComponentCode(String componentCode);
}