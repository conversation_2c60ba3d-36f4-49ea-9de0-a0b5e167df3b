package com.sendinfo.ticketing.verification.basic.repository.pay.dao;

import com.sendinfo.ticketing.verification.basic.repository.pay.arg.PayChanleDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.PayChanleQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.PayChanleUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.PayChanleDO;
import com.sendinfo.ticketing.verification.common.repository.BatchInsertDAO;
import com.sendinfo.ticketing.verification.common.repository.CountableDAO;
import com.sendinfo.ticketing.verification.common.repository.GenericDAO;
import com.sendinfo.ticketing.verification.common.repository.QueryableDAO;

/**
 * 支付渠道数据访问接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface PayChanleDao extends
        GenericDAO<Long, PayChanleDO, PayChanleUpdateArg, PayChanleDeleteArg>,
        CountableDAO<PayChanleQueryArg>,
        QueryableDAO<PayChanleQueryArg, PayChanleDO>,
        BatchInsertDAO<Long, PayChanleDO> {

} 