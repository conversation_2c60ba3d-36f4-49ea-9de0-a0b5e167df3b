package com.sendinfo.ticketing.verification.basic.api.infra;

import com.sendinfo.ticketing.verification.basic.api.infra.request.PwSysFunctionsRequisitionListQueryRequest;
import com.sendinfo.ticketing.verification.basic.model.infra.PwSysFunctions;
import com.sendinfo.ticketing.verification.common.api.result.PageResultModel;
import com.sendinfo.ticketing.verification.common.api.request.PageRequest;
import com.sendinfo.ticketing.verification.basic.api.infra.request.PwSysFunctionsQueryCondition;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import java.util.List;

/**
 * 系统功能模块查询服务
 * 
 * <AUTHOR> 2025-07-24
 */
public interface PwSysFunctionsReadService {

    /**
     * 分页查询系统功能模块
     *
     * @param pageRequest 分页查询条件
     * @return 分页结果
     */
    PageResultModel<PwSysFunctions> queryTreePageList(PageRequest<PwSysFunctionsQueryCondition> pageRequest);

    /**
     * 查询被征用的功能模块列表
     *
     * @param request 查询条件
     * @return 功能模块列表
     */
    ResultModel<List<PwSysFunctions>> queryHasRequisitionList(PwSysFunctionsRequisitionListQueryRequest request);
}
