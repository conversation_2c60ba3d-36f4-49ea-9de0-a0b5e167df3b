package com.sendinfo.ticketing.verification.basic.model.pay;

import com.sendinfo.ticketing.verification.basic.enums.CommonUseFlagEnum;
import com.sendinfo.ticketing.verification.basic.model.pay.enums.PayDefaultEnum;
import com.sendinfo.ticketing.verification.basic.model.pay.enums.PayTypeEnum;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 支付标签模型对象
 * 用于业务逻辑层和数据展示层之间的数据传输
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@ToString(callSuper = true)
public class SysPaySet implements Serializable {

    private static final long serialVersionUID = 257339388950208989L;
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 支付标签名称
     */
    private String paylableName;

    /**
     * 支付平台编码
     */
    private String payTypeCode;

    /**
     * 支付标签图标
     */
    private String paylableIcon;

    /**
     * 启用状态
     */
    private CommonUseFlagEnum useFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 售票员默认收款方式
     */
    private PayDefaultEnum payDefault;

    /**
     * 主体ID
     */
    private Long mainbodyId;

    /**
     * 支付类型（主扫，被扫，现金）
     */
    private PayTypeEnum payType;

    /**
     * 自定义支付名称
     */
    private String payName;

    /**
     * 迁移数据
     */
    private String transferParam;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String modifyBy;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 删除标志F未删除,T已删除
     */
    private String deleted;
} 