package com.sendinfo.ticketing.verification.basic.api.goods;

import com.sendinfo.ticketing.verification.basic.api.goods.request.TktSaleRuleBatchQueryByTicketIdsRequest;
import com.sendinfo.ticketing.verification.basic.api.goods.request.TktSaleRuleQueryByTicketIdRequest;
import com.sendinfo.ticketing.verification.basic.model.goods.TktSaleRule;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import java.util.List;

/**
 * @Auther: chengjie.li
 * @Date: 2025/6/11 18:05
 */
public interface TktSaleRuleReadService {
    /**
     * 根据票型ID查询售票规则
     *
     * @param request 请求
     * @return 售票规则
     */
    ResultModel<TktSaleRule> querySaleRuleByTicketId(TktSaleRuleQueryByTicketIdRequest request);

    /**
     * 根据票型ID查询售票规则
     *
     * @param request 请求
     * @return 售票规则
     */
    ResultModel<List<TktSaleRule>> batchQuerySaleRuleByTicketIds(TktSaleRuleBatchQueryByTicketIdsRequest request);
}
