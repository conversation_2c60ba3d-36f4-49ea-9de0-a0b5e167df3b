package com.sendinfo.ticketing.verification.basic.api.config.request;

import com.sendinfo.ticketing.verification.basic.enums.CommonStatusEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2025/6/2 16:03
 **/
@Getter
@Setter
@ToString
public class TenantTesbConfigUpdateRequest implements Serializable {
    private static final long serialVersionUID = -137860583113350853L;

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 配置值
     */
    private String configValue;

    /**
     * 配置名称
     */
    private String configName;

    /**
     * 配置父ID
     */
    private Long configParentId;

    /**
     * 目标状态
     */
    private CommonStatusEnum targetStatus;

    /**
     * 状态：1-启用，0-禁用
     *
     * @see CommonStatusEnum
     */
    private Integer status;

    /**
     * 排序
     */
    private Integer sortNum;

    /**
     * 描述
     */
    private String description;

    /**
     * 修改人
     */
    private String modifyBy;
}
