package com.sendinfo.ticketing.verification.basic.api.goods.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class TktRefundRuleQueryRequest implements Serializable {

    /**
     * 企业编码
     */
    @NotEmpty
    private String corpCode;

    /**
     * 票型ID
     */
    @NotNull
    private Long ticketId;
} 