package com.sendinfo.ticketing.verification.basic.model.user;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 当前用户信息
 *
 * <AUTHOR>
 * @since 2025-02-11 14:10
 */
@Getter
@Setter
@ToString
public class CurrentUserInfo implements Serializable {
    private static final long serialVersionUID = 6989024993928838021L;
    /**
     * 用户名
     */
    @NotNull(message = "用户名不能为空")
    private String userName;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 租户信息
     */
    @NotNull(message = "租户信息不能为空")
    private String corpCode = "0001";
    /**
     * 来源组织系统编码
     */
    private String organizationSystemCode;

    /**
     * 用户ID
     */
    private Long userId;
}
