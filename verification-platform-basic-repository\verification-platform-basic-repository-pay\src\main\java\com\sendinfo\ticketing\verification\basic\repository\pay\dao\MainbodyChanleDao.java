package com.sendinfo.ticketing.verification.basic.repository.pay.dao;

import com.sendinfo.ticketing.verification.basic.repository.pay.arg.MainbodyChanleDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.MainbodyChanleQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.MainbodyChanleUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.MainbodyChanleDO;
import com.sendinfo.ticketing.verification.common.repository.BatchInsertDAO;
import com.sendinfo.ticketing.verification.common.repository.CountableDAO;
import com.sendinfo.ticketing.verification.common.repository.GenericDAO;
import com.sendinfo.ticketing.verification.common.repository.QueryableDAO;

/**
 * 商户渠道配置数据访问接口
 * 提供mainbody_chanle表的完整CRUD操作
 * 支持租户隔离、软删除、批量插入等功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface MainbodyChanleDao extends
        GenericDAO<Long, MainbodyChanleDO, MainbodyChanleUpdateArg, MainbodyChanleDeleteArg>,
        CountableDAO<MainbodyChanleQueryArg>,
        QueryableDAO<MainbodyChanleQueryArg, MainbodyChanleDO>,
        BatchInsertDAO<Long, MainbodyChanleDO> {
} 