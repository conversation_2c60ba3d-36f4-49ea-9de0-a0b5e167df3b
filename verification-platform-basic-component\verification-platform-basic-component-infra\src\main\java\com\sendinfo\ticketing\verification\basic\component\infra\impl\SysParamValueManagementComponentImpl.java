package com.sendinfo.ticketing.verification.basic.component.infra.impl;

import lombok.Getter;
import org.springframework.stereotype.Component;

import com.sendinfo.ticketing.verification.basic.component.infra.SysParamValueManagementComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.SysParamValueReadComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.converter.SysParamValueConverter;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamValueUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.infra.SysParamValue;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamValueUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.SysParamValueDao;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleUpdate;

/**
 *  系统参数值管理组件实现
 *
 *  <AUTHOR> 2025-05-20 15:40:00
 */
@Component("sysParamValueManagementComponent")
@Getter
public class SysParamValueManagementComponentImpl
        implements SysParamValueManagementComponent,
        DaoBasedSingleUpdate<SysParamValueUpdateParam, SysParamValueUpdateArg, SysParamValue> {

    private final SysParamValueDao dao;
    private final SysParamValueConverter converter;
    private final SysParamValueReadComponent readComponent;

    public SysParamValueManagementComponentImpl(
            SysParamValueDao dao,
            SysParamValueConverter converter, 
            SysParamValueReadComponent readComponent) {
        this.dao = dao;
        this.converter = converter;
        this.readComponent = readComponent;
    }
} 