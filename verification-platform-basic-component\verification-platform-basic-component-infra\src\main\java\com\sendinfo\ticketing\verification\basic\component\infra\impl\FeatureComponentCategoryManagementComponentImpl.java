package com.sendinfo.ticketing.verification.basic.component.infra.impl;

import lombok.Getter;
import org.springframework.stereotype.Component;

import com.sendinfo.ticketing.verification.basic.component.infra.FeatureComponentCategoryManagementComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.converter.FeatureComponentCategoryConverter;
import com.sendinfo.ticketing.verification.basic.component.infra.param.FeatureComponentCategoryUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.infra.FeatureComponentCategory;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.FeatureComponentCategoryUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.FeatureComponentCategoryDao;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleUpdate;

/**
 * 组件分类管理组件实现
 * 
 * <AUTHOR> 2025-07-22 11:45:32
 */
@Component("featureComponentCategoryManagementComponent")
@Getter
public class FeatureComponentCategoryManagementComponentImpl
        implements FeatureComponentCategoryManagementComponent,
        DaoBasedSingleUpdate<FeatureComponentCategoryUpdateParam, FeatureComponentCategoryUpdateArg, FeatureComponentCategory> {

    private final FeatureComponentCategoryDao dao;
    private final FeatureComponentCategoryConverter converter;

    public FeatureComponentCategoryManagementComponentImpl(FeatureComponentCategoryDao dao, FeatureComponentCategoryConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }
}
