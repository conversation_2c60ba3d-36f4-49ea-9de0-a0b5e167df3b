package com.sendinfo.ticketing.verification.basic.repository.pay.arg;

import com.sendinfo.ticketing.verification.common.repository.arg.AbstractQueryArg;
import com.sendinfo.ticketing.verification.common.repository.arg.Pageable;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 经营主体子商户查询参数
 * 用于封装sys_mainbody_manage_sub_merchants表的查询条件，支持分页、排序、租户隔离
 * 包含所有可查询字段和分页参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class SysMainbodyManageSubMerchantsQueryArg extends AbstractQueryArg implements Pageable {
    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 子商户名称（模糊查询）
     */
    private String subMerchantsName;

    /**
     * 子商户号（精确查询）
     */
    private String merchantNo;

    /**
     * 分页偏移量
     */
    private Integer offset;

    /**
     * 分页大小
     */
    private Integer limit;
} 