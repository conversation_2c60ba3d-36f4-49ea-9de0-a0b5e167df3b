package com.sendinfo.ticketing.verification.basic.repository.pay.dao;

import com.sendinfo.ticketing.verification.basic.repository.pay.arg.TransInfoDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.TransInfoQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.TransInfoUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.TransInfoDO;
import com.sendinfo.ticketing.verification.common.repository.BatchInsertDAO;
import com.sendinfo.ticketing.verification.common.repository.CountableDAO;
import com.sendinfo.ticketing.verification.common.repository.GenericDAO;
import com.sendinfo.ticketing.verification.common.repository.QueryableDAO;

import java.util.List;

/**
 * 交易记录数据访问接口
 * 负责trans_info表的数据访问操作，支持租户隔离、分页、批量等功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface TransInfoDao extends
        GenericDAO<Long, TransInfoDO, TransInfoUpdateArg, TransInfoDeleteArg>,
        CountableDAO<TransInfoQueryArg>,
        QueryableDAO<TransInfoQueryArg, TransInfoDO>,
        BatchInsertDAO<Long, TransInfoDO> {

    /**
     * 根据内部交易号查询交易记录
     *
     * @param transNo  内部交易号
     * @param corpCode 企业编码
     * @return 交易记录
     */
    TransInfoDO queryByTransNo(String transNo, String corpCode);

    /**
     * 根据订单号查询交易记录
     *
     * @param tradeCode 订单号
     * @param corpCode  企业编码
     * @return 交易记录
     */
    TransInfoDO queryByTradeCode(String tradeCode, String corpCode);

    /**
     * 根据订单号查询交易记录
     *
     * @param tradeCode 订单号
     * @param corpCode  企业编码
     * @return 列表
     */
    List<TransInfoDO> queryListByTradeCode(String tradeCode, String corpCode);


    /**
     * 根据订单号和交易类型查询
     *
     * @param tradeCode 订单号
     * @param transType 交易类型
     * @param corpCode  企业编码
     * @return 交易记录
     */
    List<TransInfoDO> queryListByTradeCodeAndTransType(String tradeCode, Integer transType, String corpCode);
}