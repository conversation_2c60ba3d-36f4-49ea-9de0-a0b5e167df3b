package com.sendinfo.ticketing.verification.basic.component.goods;


import com.sendinfo.ticketing.verification.basic.component.goods.param.TktTicketDivRuleQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TktTicketDivRule;
import com.sendinfo.ticketing.verification.common.component.ReadComponent;

import java.util.List;
import java.util.Set;

public interface TktTicketDivRuleReadComponent extends ReadComponent<Long, TktTicketDivRuleQueryParam, TktTicketDivRule> {

    /**
     * 根据票型ID查询分成规则
     *
     * @param ticketId 票型ID
     * @param corpCode 企业编码
     * @return 分成规则列表
     */
    List<TktTicketDivRule> listTicketDivRuleByTicketId(Long ticketId, String corpCode);

    /**
     * 根据票型ID查询分成规则
     *
     * @param ticketIds 票型ID集合
     * @param corpCode  企业编码
     * @return 分成规则列表
     */
    List<TktTicketDivRule> batchListTicketDivRuleByTicketIds(Set<Long> ticketIds, String corpCode);
}