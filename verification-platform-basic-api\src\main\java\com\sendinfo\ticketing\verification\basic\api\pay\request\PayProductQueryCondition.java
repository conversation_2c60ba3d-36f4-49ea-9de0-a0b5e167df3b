package com.sendinfo.ticketing.verification.basic.api.pay.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * 支付产品查询条件参数
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString(callSuper = true)
public class PayProductQueryCondition implements Serializable {

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 支付产品名称
     */
    private String productName;

    /**
     * 支付产品码
     */
    private String productCode;
} 