/**
 * System parameter group component relation update request
 *
 * <AUTHOR> 2025-01-24 15:30:00
 */
package com.sendinfo.ticketing.verification.basic.api.infra.request;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 系统参数分组组件关联更新请求
 */
@Getter
@Setter
@ToString
public class SysParamGroupComponentRelationUpdateRequest implements Serializable {
    private static final long serialVersionUID = -7654321098765432108L;

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空")
    private Long id;

    /**
     * 企业编码
     */
    @Size(max = 64, message = "企业编码长度不能超过64")
    private String corpCode;

    /**
     * 分组编码
     */
    @Size(max = 64, message = "分组编码长度不能超过64")
    private String groupCode;

    /**
     * 组件编码
     */
    @Size(max = 64, message = "组件编码长度不能超过64")
    private String componentCode;

    /**
     * 修改人
     */
    @NotBlank(message = "修改人不能为空")
    private String modifyBy;
}