package com.sendinfo.ticketing.verification.basic.api.goods.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Set;

/**
 * @Auther: chengjie.li
 * @Date: 2025/6/12 11:50
 */
@Getter
@Setter
@ToString(callSuper = true)
public class TktTicketDivRuleBatchQueryByTicketIdsRequest implements Serializable {

    private static final long serialVersionUID = -485675332049779183L;

    /**
     * 票型ID
     */
    @NotEmpty
    private Set<Long> ticketIds;

    /**
     * 企业编码
     */
    @NotNull
    private String corpCode;
}
