package com.sendinfo.ticketing.verification.basic.service.pay.impl;

import com.sendinfo.ticketing.verification.basic.api.pay.request.TktSellerPayAwayEnableQueryRequest;
import com.sendinfo.ticketing.verification.basic.api.pay.TktSellerPayAwayReadService;
import com.sendinfo.ticketing.verification.basic.exception.TicketPlatformBizRuntimeException;
import com.sendinfo.ticketing.verification.basic.model.pay.TktSellerPayaway;
import com.sendinfo.ticketing.verification.basic.model.pay.error.PayErrorDef;
import com.sendinfo.ticketing.verification.basic.service.pay.enums.PayAttachmentKey;
import com.sendinfo.ticketing.verification.basic.service.pay.function.QueryTktSellerPayAwayFunction;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;
import com.sendinfo.ticketing.verification.common.api.result.Results;
import com.sendinfo.ticketing.verification.common.service.support.ApiCommonFailedBiActions;
import com.sendinfo.ticketing.verification.flow.FlowAgentBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Auther: chengjie.li
 * @Date: 2025/7/25 14:18
 */
@Service("tktSellerPayAwayReadService")
@Slf4j
public class TktSellerPayAwayReadServiceImpl implements TktSellerPayAwayReadService {


    private final FlowAgentBuilder flowAgentBuilder;

    private final QueryTktSellerPayAwayFunction queryTktSellerPayAwayFunction;

    public TktSellerPayAwayReadServiceImpl(FlowAgentBuilder flowAgentBuilder,
                                           QueryTktSellerPayAwayFunction queryTktSellerPayAwayFunction) {
        this.flowAgentBuilder = flowAgentBuilder;
        this.queryTktSellerPayAwayFunction = queryTktSellerPayAwayFunction;
    }

    @Override
    public ResultModel<List<TktSellerPayaway>> queryEnableSellerPayAwayList(TktSellerPayAwayEnableQueryRequest request) {
        return flowAgentBuilder.<TktSellerPayAwayEnableQueryRequest, ResultModel<List<TktSellerPayaway>>>validateThenChooseBuilder()
                .appendLogicAction(queryTktSellerPayAwayFunction::queryEnableSellerPayAwayList)
                .withSuccessfulAction(q -> {
                    List<TktSellerPayaway> sellerPayawayList = q.getAttachment(PayAttachmentKey.TKT_SELLER_PAY_AWAY_DATA_LIST_ATTACHMENT_KEY);
                    return Results.success(sellerPayawayList);
                })
                .withFailedBiAction(ApiCommonFailedBiActions::reasonMappingToResult)
                .withExceptionBiAction((q, th) -> {
                    log.error("[TktSellerPayAwayReadServiceImpl] queryEnableSellerPayAwayList error.question is {}", q, th);
                    return Results.fail(PayErrorDef.QUERY_TKT_SELLER_PAY_AWAY_ERROR);
                })
                .rethrowException(TicketPlatformBizRuntimeException.class::isInstance)
                .build()
                .prompt(request)
                .getResult();
    }
}
