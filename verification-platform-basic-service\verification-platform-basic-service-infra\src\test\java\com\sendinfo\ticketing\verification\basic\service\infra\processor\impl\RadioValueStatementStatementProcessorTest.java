/**
 * RADIO类型参数值声明处理器测试
 *
 * <AUTHOR> 2025-08-04
 */
package com.sendinfo.ticketing.verification.basic.service.infra.processor.impl;

import com.sendinfo.ticketing.verification.basic.enums.SysParamDataType;
import com.sendinfo.ticketing.verification.basic.exception.VerificationBizRuntimeException;
import com.sendinfo.ticketing.verification.basic.model.infra.error.InfraErrorDef;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class RadioValueStatementStatementProcessorTest {

    private RadioValueStatementStatementProcessor processor;

    @Before
    public void setUp() {
        processor = new RadioValueStatementStatementProcessor();
    }

    @Test
    public void testGetDataType() {
        assertEquals(SysParamDataType.RADIO, processor.getDataType());
    }

    // ========== validateStatement 测试 ==========

    @Test
    public void testValidateStatement_成功校验() {
        String validStatement = "{\"options\":[" +
                "{\"value\":\"male\",\"label\":\"男\",\"isDefault\":true}," +
                "{\"value\":\"female\",\"label\":\"女\",\"isDefault\":false}" +
                "]}";

        // 不应该抛出异常
        processor.validateStatement(validStatement);
    }

    @Test
    public void testValidateStatement_成功校验_单个选项() {
        String validStatement = "{\"options\":[" +
                "{\"value\":\"yes\",\"label\":\"是\",\"isDefault\":true}" +
                "]}";

        // 不应该抛出异常
        processor.validateStatement(validStatement);
    }

    @Test
    public void testValidateStatement_成功校验_多个选项() {
        String validStatement = "{\"options\":[" +
                "{\"value\":\"option1\",\"label\":\"选项1\",\"isDefault\":false}," +
                "{\"value\":\"option2\",\"label\":\"选项2\",\"isDefault\":true}," +
                "{\"value\":\"option3\",\"label\":\"选项3\",\"isDefault\":false}" +
                "]}";

        // 不应该抛出异常
        processor.validateStatement(validStatement);
    }

    @Test(expected = VerificationBizRuntimeException.class)
    public void testValidateStatement_参数为null() {
        processor.validateStatement(null);
    }

    @Test(expected = VerificationBizRuntimeException.class)
    public void testValidateStatement_参数为空字符串() {
        processor.validateStatement("");
    }

    @Test(expected = VerificationBizRuntimeException.class)
    public void testValidateStatement_JSON格式错误() {
        String invalidJson = "{\"options\":[{\"value\":\"option1\"}";
        processor.validateStatement(invalidJson);
    }

    @Test(expected = VerificationBizRuntimeException.class)
    public void testValidateStatement_options为空数组() {
        String statement = "{\"options\":[]}";
        processor.validateStatement(statement);
    }

    @Test(expected = VerificationBizRuntimeException.class)
    public void testValidateStatement_没有默认选项() {
        String statement = "{\"options\":[" +
                "{\"value\":\"option1\",\"label\":\"选项1\",\"isDefault\":false}," +
                "{\"value\":\"option2\",\"label\":\"选项2\",\"isDefault\":false}" +
                "]}";
        processor.validateStatement(statement);
    }

    @Test(expected = VerificationBizRuntimeException.class)
    public void testValidateStatement_多个默认选项() {
        String statement = "{\"options\":[" +
                "{\"value\":\"option1\",\"label\":\"选项1\",\"isDefault\":true}," +
                "{\"value\":\"option2\",\"label\":\"选项2\",\"isDefault\":true}" +
                "]}";
        processor.validateStatement(statement);
    }

    @Test(expected = VerificationBizRuntimeException.class)
    public void testValidateStatement_选项value为空() {
        String statement = "{\"options\":[" +
                "{\"value\":\"\",\"label\":\"选项1\",\"isDefault\":true}" +
                "]}";
        processor.validateStatement(statement);
    }

    @Test
    public void testValidateStatement_异常信息验证() {
        try {
            processor.validateStatement(null);
            fail("应该抛出异常");
        } catch (VerificationBizRuntimeException e) {
            assertEquals(InfraErrorDef.SYSPARAM_VALUE_STATEMENT_VALIDATION_ERROR.code(), e.getErrorCode());
            assertTrue(e.getMessage().contains("RADIO类型参数值声明不能为空"));
        }
    }

    // ========== parseAndGetDefaultSysParamValue 测试 ==========

    @Test
    public void testParseAndGetDefaultSysParamValue_成功解析() {
        String statement = "{\"options\":[" +
                "{\"value\":\"male\",\"label\":\"男\",\"isDefault\":true}," +
                "{\"value\":\"female\",\"label\":\"女\",\"isDefault\":false}" +
                "]}";

        String result = processor.parseAndGetDefaultSysParamValue(statement);
        assertEquals("male", result);
    }


    // ========== 综合测试 ==========

    @Test
    public void testValidateAndParse_完整流程() {
        String statement = "{\"options\":[" +
                "{\"value\":\"yes\",\"label\":\"是\",\"isDefault\":true}," +
                "{\"value\":\"no\",\"label\":\"否\",\"isDefault\":false}" +
                "]}";

        // 先校验
        processor.validateStatement(statement);

        // 再解析
        String result = processor.parseAndGetDefaultSysParamValue(statement);
        assertEquals("yes", result);
    }

    @Test
    public void testValidateAndParse_复杂场景() {
        String statement = "{\"options\":[" +
                "{\"value\":\"high\",\"label\":\"高优先级\",\"isDefault\":false}," +
                "{\"value\":\"medium\",\"label\":\"中优先级\",\"isDefault\":true}," +
                "{\"value\":\"low\",\"label\":\"低优先级\",\"isDefault\":false}" +
                "]}";

        // 先校验
        processor.validateStatement(statement);

        // 再解析
        String result = processor.parseAndGetDefaultSysParamValue(statement);
        assertEquals("medium", result);
    }

    // ========== 业务场景测试 ==========

    @Test
    public void testRadio_性别选择() {
        String statement = "{\"options\":[" +
                "{\"value\":\"M\",\"label\":\"男性\",\"isDefault\":true}," +
                "{\"value\":\"F\",\"label\":\"女性\",\"isDefault\":false}" +
                "]}";

        processor.validateStatement(statement);
        String result = processor.parseAndGetDefaultSysParamValue(statement);
        assertEquals("M", result);
    }

    @Test
    public void testRadio_是否选择() {
        String statement = "{\"options\":[" +
                "{\"value\":\"Y\",\"label\":\"是\",\"isDefault\":false}," +
                "{\"value\":\"N\",\"label\":\"否\",\"isDefault\":true}" +
                "]}";

        processor.validateStatement(statement);
        String result = processor.parseAndGetDefaultSysParamValue(statement);
        assertEquals("N", result);
    }

    @Test
    public void testRadio_状态选择() {
        String statement = "{\"options\":[" +
                "{\"value\":\"ACTIVE\",\"label\":\"激活\",\"isDefault\":true}," +
                "{\"value\":\"INACTIVE\",\"label\":\"未激活\",\"isDefault\":false}," +
                "{\"value\":\"SUSPENDED\",\"label\":\"暂停\",\"isDefault\":false}" +
                "]}";

        processor.validateStatement(statement);
        String result = processor.parseAndGetDefaultSysParamValue(statement);
        assertEquals("ACTIVE", result);
    }
}
