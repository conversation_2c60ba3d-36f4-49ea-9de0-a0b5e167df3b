package com.sendinfo.ticketing.verification.basic.component.customer;

import com.sendinfo.ticketing.verification.basic.component.customer.param.TravelInfoDeptQueryParam;
import com.sendinfo.ticketing.verification.basic.component.customer.param.TravelInfoQueryParam;
import com.sendinfo.ticketing.verification.basic.component.customer.param.TravelInfoRoleQueryParam;
import com.sendinfo.ticketing.verification.basic.model.customer.TravelInfo;
import com.sendinfo.ticketing.verification.common.component.ReadComponent;

import java.util.List;
import java.util.Set;

/**
 * 旅行社信息读取组件接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface TravelInfoReadComponent extends ReadComponent<Long, TravelInfoQueryParam, TravelInfo> {

    /**
     * 批量查询旅行社信息
     *
     * @param ids 主键集合
     * @return 旅行社信息
     */
    List<TravelInfo> batchQueryByIds(Set<Long> ids);

    /**
     * 查询旅行社信息列表
     *
     * @param queryParam
     * @return  旅行社信息
     */
    List<TravelInfo> queryTravelInfoList(TravelInfoQueryParam queryParam);

    /**
     * 查询角色旅行社信息列表
     *
     * @param queryParam
     * @return  旅行社信息
     */
    List<TravelInfo> queryRoleTravelInfoList(TravelInfoRoleQueryParam queryParam);

    /**
     * 查询组织机构旅行社信息列表
     *
     * @param queryParam
     * @return  旅行社信息
     */
    List<TravelInfo> queryDeptTravelInfoList(TravelInfoDeptQueryParam queryParam);
}