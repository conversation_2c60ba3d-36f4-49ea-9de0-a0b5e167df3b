package com.sendinfo.ticketing.verification.basic.api.config.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2025/6/2 17:06
 **/
@Getter
@Setter
@ToString
public class TenantTesbConfigGetRequest implements Serializable {
    private static final long serialVersionUID = 6206319883528129920L;
    /**
     * 配置编号
     */
    private String configCode;
    /**
     * 租户编号
     */
    @NotNull(message = "租户编号不能为空")
    private String corpCode;
    /**
     * 是否包含子节点
     */
    private boolean withChildren = false;

}
