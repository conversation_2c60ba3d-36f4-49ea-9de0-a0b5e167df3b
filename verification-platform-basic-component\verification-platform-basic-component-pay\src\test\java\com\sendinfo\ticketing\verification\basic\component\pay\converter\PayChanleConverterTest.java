package com.sendinfo.ticketing.verification.basic.component.pay.converter;

import com.sendinfo.ticketing.verification.basic.component.pay.param.PayChanleCreateParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.PayChanleQueryParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.PayChanleUpdateParam;
import com.sendinfo.ticketing.verification.basic.enums.CommonUseFlagEnum;
import com.sendinfo.ticketing.verification.basic.model.pay.PayChanle;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.PayChanleQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.PayChanleUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.PayChanleDO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;

/**
 * PayChanleConverter单元测试
 * 测试支付渠道转换器的各种转换功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RunWith(MockitoJUnitRunner.class)
public class PayChanleConverterTest {

    @InjectMocks
    private PayChanleConverter payChanleConverter;

    // 测试常量
    private static final String TEST_CORP_CODE = "TEST_CORP_001";
    private static final Long TEST_ID = 1L;
    private static final Long TEST_PRODUCT_ID = 100L;
    private static final String TEST_CHANLE_NAME = "建行支付";
    private static final String TEST_CHANLE_CODE = "CCB_PAY_001";
    private static final String TEST_PAY_PRODUCT_CODE = "PAY_PRODUCT_001";
    private static final String TEST_REMARK = "这是一个测试支付渠道";
    private static final String TEST_CREATE_BY = "testUser";
    private static final String TEST_MODIFY_BY = "testUser";

    /**
     * 测试目的：验证PayChanleCreateParam转换为PayChanleDO的功能
     * 测试步骤：
     * 1. 创建完整的PayChanleCreateParam对象
     * 2. 调用c_p2d方法进行转换
     * 3. 验证转换结果的正确性
     */
    @Test
    public void testCreateParam2Do_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        PayChanleCreateParam createParam = createPayChanleCreateParam();

        // 2. 执行转换
        PayChanleDO result = payChanleConverter.c_p2d(createParam);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", result);
        assertEquals("企业编码应该一致", TEST_CORP_CODE, result.getCorpCode());
        assertEquals("产品ID应该一致", TEST_PRODUCT_ID, result.getProductId());
        assertEquals("渠道名称应该一致", TEST_CHANLE_NAME, result.getChanleName());
        assertEquals("渠道编码应该一致", TEST_CHANLE_CODE, result.getChanleCode());
        assertEquals("支付产品码应该一致", TEST_PAY_PRODUCT_CODE, result.getPayProductCode());
        assertEquals("启用状态应该一致", CommonUseFlagEnum.ENABLED.getCode(), result.getUseFlag());
        assertEquals("备注应该一致", TEST_REMARK, result.getRemark());
        assertEquals("创建人应该一致", TEST_CREATE_BY, result.getCreateBy());
    }

    /**
     * 测试目的：验证PayChanleCreateParam为null时的处理
     */
    @Test
    public void testCreateParam2Do_WithNullInput_ShouldReturnNull() {
        // 1. 执行转换
        PayChanleDO result = payChanleConverter.c_p2d(null);

        // 2. 验证结果
        assertNull("输入为null时应该返回null", result);
    }

    /**
     * 测试目的：验证PayChanleDO转换为PayChanle的功能
     * 测试步骤：
     * 1. 创建完整的PayChanleDO对象
     * 2. 调用r_d2m方法进行转换
     * 3. 验证转换结果的正确性
     */
    @Test
    public void testReadDo2Model_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        PayChanleDO payChanleDO = createPayChanleDO();

        // 2. 执行转换
        PayChanle result = payChanleConverter.r_d2m(payChanleDO);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", result);
        assertEquals("ID应该一致", TEST_ID, result.getId());
        assertEquals("企业编码应该一致", TEST_CORP_CODE, result.getCorpCode());
        assertEquals("产品ID应该一致", TEST_PRODUCT_ID, result.getProductId());
        assertEquals("渠道名称应该一致", TEST_CHANLE_NAME, result.getChanleName());
        assertEquals("渠道编码应该一致", TEST_CHANLE_CODE, result.getChanleCode());
        assertEquals("支付产品码应该一致", TEST_PAY_PRODUCT_CODE, result.getPayProductCode());
        assertEquals("启用状态应该一致", CommonUseFlagEnum.ENABLED, result.getUseFlag());
        assertEquals("备注应该一致", TEST_REMARK, result.getRemark());
    }

    /**
     * 测试目的：验证PayChanleDO为null时的处理
     */
    @Test
    public void testReadDo2Model_WithNullInput_ShouldReturnNull() {
        // 1. 执行转换
        PayChanle result = payChanleConverter.r_d2m(null);

        // 2. 验证结果
        assertNull("输入为null时应该返回null", result);
    }

    /**
     * 测试目的：验证List<PayChanleDO>转换为List<PayChanle>的功能
     * 测试步骤：
     * 1. 创建包含多个PayChanleDO的列表
     * 2. 调用r_ds2ms方法进行转换
     * 3. 验证转换结果的正确性
     */
    @Test
    public void testReadDoList2ModelList_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        List<PayChanleDO> payChanleDOList = Arrays.asList(
                createPayChanleDO(),
                createPayChanleDO()
        );

        // 2. 执行转换
        List<PayChanle> result = payChanleConverter.r_ds2ms(payChanleDOList);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", result);
        assertEquals("列表大小应该一致", 2, result.size());

        // 验证第一个元素
        PayChanle firstResult = result.get(0);
        assertEquals("第一个元素的ID应该一致", TEST_ID, firstResult.getId());
        assertEquals("第一个元素的企业编码应该一致", TEST_CORP_CODE, firstResult.getCorpCode());
        assertEquals("第一个元素的产品ID应该一致", TEST_PRODUCT_ID, firstResult.getProductId());
        assertEquals("第一个元素的渠道名称应该一致", TEST_CHANLE_NAME, firstResult.getChanleName());
        assertEquals("第一个元素的渠道编码应该一致", TEST_CHANLE_CODE, firstResult.getChanleCode());
        assertEquals("第一个元素的支付产品码应该一致", TEST_PAY_PRODUCT_CODE, firstResult.getPayProductCode());
        assertEquals("第一个元素的启用状态应该一致", CommonUseFlagEnum.ENABLED, firstResult.getUseFlag());
        assertEquals("第一个元素的备注应该一致", TEST_REMARK, firstResult.getRemark());

        // 验证第二个元素
        PayChanle secondResult = result.get(1);
        assertEquals("第二个元素的ID应该一致", TEST_ID, secondResult.getId());
        assertEquals("第二个元素的企业编码应该一致", TEST_CORP_CODE, secondResult.getCorpCode());
        assertEquals("第二个元素的产品ID应该一致", TEST_PRODUCT_ID, secondResult.getProductId());
        assertEquals("第二个元素的渠道名称应该一致", TEST_CHANLE_NAME, secondResult.getChanleName());
        assertEquals("第二个元素的渠道编码应该一致", TEST_CHANLE_CODE, secondResult.getChanleCode());
        assertEquals("第二个元素的支付产品码应该一致", TEST_PAY_PRODUCT_CODE, secondResult.getPayProductCode());
        assertEquals("第二个元素的启用状态应该一致", CommonUseFlagEnum.ENABLED, secondResult.getUseFlag());
        assertEquals("第二个元素的备注应该一致", TEST_REMARK, secondResult.getRemark());
    }

    /**
     * 测试目的：验证List<PayChanleDO>为null时的处理
     */
    @Test
    public void testReadDoList2ModelList_WithNullInput_ShouldReturnNull() {
        // 1. 执行转换
        List<PayChanle> result = payChanleConverter.r_ds2ms(null);

        // 2. 验证结果
        assertNull("输入为null时应该返回null", result);
    }

    /**
     * 测试目的：验证空列表的处理
     */
    @Test
    public void testReadDoList2ModelList_WithEmptyList_ShouldReturnEmptyList() {
        // 1. 创建空列表
        List<PayChanleDO> emptyList = Arrays.asList();

        // 2. 执行转换
        List<PayChanle> result = payChanleConverter.r_ds2ms(emptyList);

        // 3. 验证结果
        assertNotNull("结果不应为null", result);
        assertTrue("结果应该是空列表", result.isEmpty());
    }

    /**
     * 测试目的：验证PayChanleQueryParam转换为PayChanleQueryArg的功能
     * 测试步骤：
     * 1. 创建完整的PayChanleQueryParam对象
     * 2. 调用r_p2a方法进行转换
     * 3. 验证转换结果的正确性
     */
    @Test
    public void testReadQueryParam2QueryArg_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        PayChanleQueryParam queryParam = createPayChanleQueryParam();

        // 2. 执行转换
        PayChanleQueryArg result = payChanleConverter.r_p2a(queryParam);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", result);
        assertEquals("企业编码应该一致", TEST_CORP_CODE, result.getCorpCode());
        assertEquals("产品ID应该一致", TEST_PRODUCT_ID, result.getProductId());
        assertEquals("渠道名称应该一致", TEST_CHANLE_NAME, result.getChanleName());
        assertEquals("渠道编码应该一致", TEST_CHANLE_CODE, result.getChanleCode());
        assertEquals("支付产品码应该一致", TEST_PAY_PRODUCT_CODE, result.getPayProductCode());
        assertEquals("启用状态应该一致", CommonUseFlagEnum.ENABLED.getCode(), result.getUseFlag());
    }

    /**
     * 测试目的：验证PayChanleQueryParam为null时的处理
     */
    @Test
    public void testReadQueryParam2QueryArg_WithNullInput_ShouldReturnNull() {
        // 1. 执行转换
        PayChanleQueryArg result = payChanleConverter.r_p2a(null);

        // 2. 验证结果
        assertNull("输入为null时应该返回null", result);
    }

    /**
     * 测试目的：验证PayChanleUpdateParam转换为PayChanleUpdateArg的功能
     * 测试步骤：
     * 1. 创建完整的PayChanleUpdateParam对象
     * 2. 调用u_p2a方法进行转换
     * 3. 验证转换结果的正确性
     */
    @Test
    public void testUpdateParam2UpdateArg_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        PayChanleUpdateParam updateParam = createPayChanleUpdateParam();
        PayChanle currentModel = createPayChanle();

        // 2. 执行转换
        PayChanleUpdateArg result = payChanleConverter.u_p2a(updateParam, currentModel);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", result);
        assertEquals("企业编码应该一致", TEST_CORP_CODE, result.getCorpCode());
        assertEquals("产品ID应该一致", TEST_PRODUCT_ID, result.getProductId());
        assertEquals("渠道名称应该一致", TEST_CHANLE_NAME, result.getChanleName());
        assertEquals("渠道编码应该一致", TEST_CHANLE_CODE, result.getChanleCode());
        assertEquals("支付产品码应该一致", TEST_PAY_PRODUCT_CODE, result.getPayProductCode());
        assertEquals("启用状态应该一致", CommonUseFlagEnum.ENABLED.getCode(), result.getUseFlag());
        assertEquals("备注应该一致", TEST_REMARK, result.getRemark());
        assertEquals("修改人应该一致", TEST_MODIFY_BY, result.getModifyBy());
    }

    /**
     * 测试目的：验证PayChanleUpdateParam为null时的处理
     */
    @Test
    public void testUpdateParam2UpdateArg_WithNullInput_ShouldReturnNull() {
        // 1. 创建当前模型
        PayChanle currentModel = createPayChanle();

        // 2. 执行转换
        PayChanleUpdateArg result = payChanleConverter.u_p2a(null, currentModel);

        // 3. 验证结果
        assertNull("输入为null时应该返回null", result);
    }

    // ==================== 测试数据构建方法 ====================

    /**
     * 创建PayChanleCreateParam测试数据
     */
    private PayChanleCreateParam createPayChanleCreateParam() {
        PayChanleCreateParam createParam = new PayChanleCreateParam();
        createParam.setCorpCode(TEST_CORP_CODE);
        createParam.setProductId(TEST_PRODUCT_ID);
        createParam.setChanleName(TEST_CHANLE_NAME);
        createParam.setChanleCode(TEST_CHANLE_CODE);
        createParam.setPayProductCode(TEST_PAY_PRODUCT_CODE);
        createParam.setUseFlag(CommonUseFlagEnum.ENABLED);
        createParam.setRemark(TEST_REMARK);
        createParam.setCreateBy(TEST_CREATE_BY);
        return createParam;
    }

    /**
     * 创建PayChanleDO测试数据
     */
    private PayChanleDO createPayChanleDO() {
        PayChanleDO payChanleDO = new PayChanleDO();
        payChanleDO.setId(TEST_ID);
        payChanleDO.setCorpCode(TEST_CORP_CODE);
        payChanleDO.setProductId(TEST_PRODUCT_ID);
        payChanleDO.setChanleName(TEST_CHANLE_NAME);
        payChanleDO.setChanleCode(TEST_CHANLE_CODE);
        payChanleDO.setPayProductCode(TEST_PAY_PRODUCT_CODE);
        payChanleDO.setUseFlag(CommonUseFlagEnum.ENABLED.getCode());
        payChanleDO.setRemark(TEST_REMARK);
        return payChanleDO;
    }

    /**
     * 创建PayChanleQueryParam测试数据
     */
    private PayChanleQueryParam createPayChanleQueryParam() {
        PayChanleQueryParam queryParam = new PayChanleQueryParam();
        queryParam.setCorpCode(TEST_CORP_CODE);
        queryParam.setProductId(TEST_PRODUCT_ID);
        queryParam.setChanleName(TEST_CHANLE_NAME);
        queryParam.setChanleCode(TEST_CHANLE_CODE);
        queryParam.setPayProductCode(TEST_PAY_PRODUCT_CODE);
        queryParam.setUseFlag(CommonUseFlagEnum.ENABLED);
        return queryParam;
    }

    /**
     * 创建PayChanleUpdateParam测试数据
     */
    private PayChanleUpdateParam createPayChanleUpdateParam() {
        PayChanleUpdateParam updateParam = new PayChanleUpdateParam();
        updateParam.setCorpCode(TEST_CORP_CODE);
        updateParam.setProductId(TEST_PRODUCT_ID);
        updateParam.setChanleName(TEST_CHANLE_NAME);
        updateParam.setChanleCode(TEST_CHANLE_CODE);
        updateParam.setPayProductCode(TEST_PAY_PRODUCT_CODE);
        updateParam.setUseFlag(CommonUseFlagEnum.ENABLED);
        updateParam.setRemark(TEST_REMARK);
        updateParam.setModifyBy(TEST_MODIFY_BY);
        return updateParam;
    }

    /**
     * 创建PayChanle测试数据
     */
    private PayChanle createPayChanle() {
        PayChanle payChanle = new PayChanle();
        payChanle.setId(TEST_ID);
        payChanle.setCorpCode(TEST_CORP_CODE);
        payChanle.setProductId(TEST_PRODUCT_ID);
        payChanle.setChanleName(TEST_CHANLE_NAME);
        payChanle.setChanleCode(TEST_CHANLE_CODE);
        payChanle.setPayProductCode(TEST_PAY_PRODUCT_CODE);
        payChanle.setUseFlag(CommonUseFlagEnum.ENABLED);
        payChanle.setRemark(TEST_REMARK);
        return payChanle;
    }

} 