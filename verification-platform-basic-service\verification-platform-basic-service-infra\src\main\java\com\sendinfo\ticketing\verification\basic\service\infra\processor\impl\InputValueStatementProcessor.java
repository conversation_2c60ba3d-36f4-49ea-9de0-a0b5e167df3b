/**
 * INPUT类型参数值声明处理器
 *
 * <AUTHOR> 2025-08-04
 */
package com.sendinfo.ticketing.verification.basic.service.infra.processor.impl;

import com.alibaba.fastjson.JSON;
import com.sendinfo.ticketing.verification.basic.enums.SysParamDataType;
import com.sendinfo.ticketing.verification.basic.exception.VerificationBizRuntimeException;
import com.sendinfo.ticketing.verification.basic.model.infra.error.InfraErrorDef;
import com.sendinfo.ticketing.verification.basic.service.infra.processor.SysParamValueStatementProcessor;
import com.sendinfo.ticketing.verification.basic.service.infra.processor.model.InputValueStatement;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * INPUT类型参数值声明处理器
 * 处理格式：{"defaultValue":"text文本"}
 */
@Slf4j
@Component("inputValueStatementProcessor")
public class InputValueStatementProcessor implements SysParamValueStatementProcessor {

    @Override
    public SysParamDataType getDataType() {
        return SysParamDataType.INPUT;
    }

    @Override
    public void validateStatement(String paramValueStatement) {
        if (StringUtils.isBlank(paramValueStatement)) {
            throw new VerificationBizRuntimeException(InfraErrorDef.SYSPARAM_VALUE_STATEMENT_VALIDATION_ERROR,
                    "INPUT类型参数值声明不能为空");
        }

        try {
            InputValueStatement statement = JSON.parseObject(paramValueStatement, InputValueStatement.class);

            // 校验defaultValue是否为空
            if (StringUtils.isBlank(statement.getDefaultValue())) {
                throw new VerificationBizRuntimeException(InfraErrorDef.SYSPARAM_VALUE_STATEMENT_VALIDATION_ERROR,
                        "INPUT类型参数值声明中defaultValue不能为空");
            }

        } catch (VerificationBizRuntimeException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            throw new VerificationBizRuntimeException(InfraErrorDef.SYSPARAM_VALUE_STATEMENT_VALIDATION_ERROR,
                    "INPUT类型参数值声明格式错误: " + paramValueStatement, e);
        }
    }

    @Override
    public String parseAndGetDefaultSysParamValue(String paramValueStatement) {
        InputValueStatement statement = JSON.parseObject(paramValueStatement, InputValueStatement.class);
        return statement.getDefaultValue();
    }
}
