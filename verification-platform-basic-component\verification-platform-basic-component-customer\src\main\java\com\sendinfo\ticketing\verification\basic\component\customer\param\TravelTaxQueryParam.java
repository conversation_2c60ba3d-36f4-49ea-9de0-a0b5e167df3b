package com.sendinfo.ticketing.verification.basic.component.customer.param;

import com.sendinfo.ticketing.verification.common.component.param.AbstractQueryParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * travel_tax信息查询参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class TravelTaxQueryParam extends AbstractQueryParam {
    /**
     * 企业编码
     */
    private String corpCode;
    /**
     * 旅行社ID
     */
    private Long travelId;
    /**
     * 实际抬头
     */
    private String taxTitle;
    /**
     * 纳税人识别号
     */
    private String taxNo;
} 