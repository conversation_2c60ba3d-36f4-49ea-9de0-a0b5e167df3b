package com.sendinfo.ticketing.verification.basic.component.account.param;

import com.sendinfo.ticketing.verification.common.component.param.AbstractDeleteParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 资金账户删除参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString
public class CapitalAccountDeleteParam extends AbstractDeleteParam {
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 企业编码
     */
    private String corpCode;
    /**
     * 删除人
     */
    private String modifyBy;
} 