package com.sendinfo.ticketing.verification.basic.repository.pay.dao;

import com.alibaba.nacos.api.exception.NacosException;
import com.sendinfo.ticketing.verification.basic.repository.common.config.SaasPwMybatisConfig;
import com.sendinfo.ticketing.verification.basic.repository.common.sharding.DynamicDataSourceMappingConfigPullStorage;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.TransInfoQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.TransInfoUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dao.impl.TransInfoDaoImpl;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.TransInfoDO;
import org.junit.Test;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.AbstractTransactionalJUnit4SpringContextTests;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Random;

import static org.junit.Assert.*;

/**
 * 交易记录数据访问层单元测试
 * <p>
 * 本测试类覆盖了TransInfoDao的所有方法，包括：
 * 1. 插入操作测试
 * 2. 批量插入操作测试
 * 3. 根据ID查询测试
 * 4. 条件查询测试
 * 5. 计数查询测试
 * 6. 更新操作测试
 * 7. 软删除操作测试
 * 8. 分页查询测试
 * 9. 自定义查询方法测试
 * <p>
 * 测试目的：验证TransInfoDao的所有方法都能正常工作，确保数据访问层的正确性
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootTest(classes = TransInfoDaoTest.Config.class)
@EnableAutoConfiguration
@TestPropertySource(locations = "classpath:test-application.properties")
public class TransInfoDaoTest extends AbstractTransactionalJUnit4SpringContextTests {

    @Autowired
    private TransInfoDao transInfoDao;

    /**
     * 测试用的企业编码
     */
    private static final String TEST_CORP_CODE = "TEST_CORP_001";

    /**
     * 测试用户
     */
    private static final String TEST_USER = "test_user";

    /**
     * 测试配置类
     * 配置MyBatis和DAO Bean
     */
    @Configuration
    @Import(SaasPwMybatisConfig.class)
    static class Config {

        @Bean
        public TransInfoDao transInfoDao(@Qualifier(SaasPwMybatisConfig.BEAN_NAME_SQL_SESSION_TEMPLATE) SqlSessionTemplate sqlSessionTemplate) {
            return new TransInfoDaoImpl(sqlSessionTemplate);
        }

        @Bean
        DynamicDataSourceMappingConfigPullStorage dynamicDataSourceMappingConfigPullStorage(@Value("${nacos.config.server-addr}") String serverAddr,
                                                                                            @Value("${nacos.config.namespace}") String namespace,
                                                                                            @Value("${verification-platform-basic.nacos.tenant-datasource-mapping.data-id:saas-pw-datasource-sharding-mapping.json}") String dataId,
                                                                                            @Value("${verification-platform-basic.nacos.tenant-datasource-mapping.group:DEFAULT_GROUP}") String group,
                                                                                            @Value("${verification-platform-basic.nacos.tenant-datasource-mapping.pulled.timeout:3000}") Integer timeout) throws NacosException {
            return new DynamicDataSourceMappingConfigPullStorage(serverAddr, namespace, dataId, group, timeout);
        }
    }

    /**
     * 测试插入操作
     * <p>
     * 测试步骤：
     * 1. 创建测试交易记录数据
     * 2. 执行插入操作
     * 3. 验证主键ID被自动生成
     * 4. 通过ID查询验证记录确实被插入
     * 5. 验证插入的数据字段值正确
     */
    @Test
    public void testInsert() {
        // 1. 创建测试交易记录数据
        TransInfoDO transInfoDO = createTestDO();

        // 2. 执行插入操作
        transInfoDao.insert(transInfoDO);

        // 3. 验证主键ID被自动生成
        assertNotNull("插入后主键ID应该被自动生成", transInfoDO.getId());

        // 4. 通过ID查询验证记录确实被插入
        TransInfoDO insertedTransInfo = transInfoDao.queryById(transInfoDO.getId());
        assertNotNull("插入后应能通过ID查询到记录", insertedTransInfo);
        assertEquals("内部交易号应该一致", transInfoDO.getTransNo(), insertedTransInfo.getTransNo());
        assertEquals("企业编码应该一致", TEST_CORP_CODE, insertedTransInfo.getCorpCode());
        assertEquals("交易类型应该一致", transInfoDO.getTransType(), insertedTransInfo.getTransType());
        assertEquals("交易ID应该一致", transInfoDO.getTradeId(), insertedTransInfo.getTradeId());
    }

    /**
     * 测试批量插入操作
     * <p>
     * 测试步骤：
     * 1. 创建多个测试交易记录数据
     * 2. 执行批量插入操作
     * 3. 验证所有记录的主键ID都被自动生成
     * 4. 通过ID查询验证所有记录都被正确插入
     */
    @Test
    public void testBatchInsert() {
        // 1. 创建多个测试交易记录数据
        List<TransInfoDO> transInfoList = new java.util.ArrayList<>();
        for (int i = 0; i < 3; i++) {
            TransInfoDO transInfoDO = createTestDO();
            transInfoList.add(transInfoDO);
        }

        // 2. 执行批量插入操作
        transInfoDao.batchInsert(transInfoList);

        // 3. 查询插入数据
        TransInfoQueryArg queryArg = new TransInfoQueryArg();
        queryArg.setCorpCode(TEST_CORP_CODE);
        List<TransInfoDO> transInfoDOList = transInfoDao.queryByArg(queryArg);

        // 4. 通过ID查询验证所有记录都被正确插入
        for (TransInfoDO transInfoDO : transInfoDOList) {
            assertNotNull("每个记录都应该生成ID", transInfoDO.getId());
        }
    }

    /**
     * 测试根据ID查询操作
     * <p>
     * 测试步骤：
     * 1. 插入测试记录
     * 2. 使用插入记录的ID进行查询
     * 3. 验证查询结果正确
     */
    @Test
    public void testQueryById() {
        // 1. 插入测试记录
        TransInfoDO transInfoDO = createTestDO();
        transInfoDao.insert(transInfoDO);

        // 2. 使用插入记录的ID进行查询
        TransInfoDO queriedTransInfo = transInfoDao.queryById(transInfoDO.getId());

        // 3. 验证查询结果正确
        assertNotNull("应该能查询到记录", queriedTransInfo);
        assertEquals("ID应该一致", transInfoDO.getId(), queriedTransInfo.getId());
        assertEquals("内部交易号应该一致", transInfoDO.getTransNo(), queriedTransInfo.getTransNo());
        assertEquals("企业编码应该一致", TEST_CORP_CODE, queriedTransInfo.getCorpCode());
    }

    /**
     * 测试基本的更新功能
     * <p>
     * 目的：验证 updateByArg 方法的基本更新功能
     * 步骤：
     * 1. 插入一条测试记录
     * 2. 创建更新参数，修改部分字段
     * 3. 执行更新操作
     * 4. 查询更新后的记录，验证字段值已正确更新
     */
    @Test
    public void testUpdateByArg() {
        // 1. 插入测试记录
        TransInfoDO transInfoDO = createTestDO();
        transInfoDao.insert(transInfoDO);

        // 2. 创建更新参数
        TransInfoUpdateArg updateArg = new TransInfoUpdateArg();
        updateArg.setId(transInfoDO.getId());
        updateArg.setCorpCode(TEST_CORP_CODE);
        updateArg.setPayAway("更新后的支付方式");
        updateArg.setPayStatus("3");
        updateArg.setModifyBy("testUpdater");

        // 3. 执行更新操作
        int updateCount = transInfoDao.updateByArg(updateArg);
        assertEquals("应该更新1条记录", 1, updateCount);

        // 4. 验证更新结果
        TransInfoDO updatedTransInfo = transInfoDao.queryById(transInfoDO.getId());
        assertEquals("支付方式应该被更新", "更新后的支付方式", updatedTransInfo.getPayAway());
        assertEquals("交易状态应该被更新", "3", updatedTransInfo.getPayStatus());
    }

    /**
     * 测试分页查询功能
     * <p>
     * 测试步骤：
     * 1. 插入多条测试记录
     * 2. 使用分页参数进行查询
     * 3. 验证分页结果正确
     * 4. 验证计数功能
     */
    @Test
    public void testPageableQuery() {
        // 1. 插入3条测试记录
        for (int i = 1; i <= 3; i++) {
            TransInfoDO transInfoDO = createTestDO();
            transInfoDao.insert(transInfoDO);
        }

        // 2. 使用分页参数查询第一页（2条记录）
        TransInfoQueryArg queryArg = new TransInfoQueryArg();
        queryArg.setCorpCode(TEST_CORP_CODE);
        queryArg.setOffset(0);
        queryArg.setLimit(2);

        List<TransInfoDO> firstPageResults = transInfoDao.queryByArg(queryArg);
        assertEquals("第一页应该返回2条记录", 2, firstPageResults.size());

        // 3. 查询第二页（1条记录）
        queryArg.setOffset(2);
        queryArg.setLimit(2);
        List<TransInfoDO> secondPageResults = transInfoDao.queryByArg(queryArg);
        assertEquals("第二页应该返回1条记录", 1, secondPageResults.size());

        // 4. 验证计数功能
        queryArg.setOffset(null);
        queryArg.setLimit(null);
        int totalCount = transInfoDao.countByArg(queryArg);
        assertEquals("总计数应该是3", 3, totalCount);
    }

    /**
     * 测试条件查询功能
     * <p>
     * 测试步骤：
     * 1. 插入多个测试记录
     * 2. 创建查询条件
     * 3. 执行条件查询
     * 4. 验证查询结果符合条件
     */
    @Test
    public void testQueryByArg() {
        // 1. 插入多个测试记录
        TransInfoDO transInfoDO1 = createTestDO();
        transInfoDO1.setPayStatus("3");
        transInfoDao.insert(transInfoDO1);

        TransInfoDO transInfoDO2 = createTestDO();
        transInfoDO2.setPayStatus("2");
        transInfoDao.insert(transInfoDO2);

        // 2. 创建查询条件
        TransInfoQueryArg queryArg = new TransInfoQueryArg();
        queryArg.setCorpCode(TEST_CORP_CODE);
        queryArg.setPayStatus("3");

        // 3. 执行条件查询
        List<TransInfoDO> resultList = transInfoDao.queryByArg(queryArg);

        // 4. 验证查询结果符合条件
        assertNotNull("查询结果不应为null", resultList);
        assertTrue("应该查询到记录", resultList.size() > 0);
        for (TransInfoDO transInfo : resultList) {
            assertEquals("企业编码应该一致", TEST_CORP_CODE, transInfo.getCorpCode());
            assertEquals("交易状态应该一致", "3", transInfo.getPayStatus());
        }
    }

    /**
     * 测试计数查询操作
     * <p>
     * 测试步骤：
     * 1. 插入多个测试记录
     * 2. 创建查询条件
     * 3. 执行计数查询
     * 4. 验证计数结果正确
     */
    @Test
    public void testCountByArg() {
        // 1. 插入多个测试记录
        TransInfoDO transInfoDO1 = createTestDO();
        transInfoDO1.setPayStatus("3");
        transInfoDao.insert(transInfoDO1);

        TransInfoDO transInfoDO2 = createTestDO();
        transInfoDO2.setPayStatus("3");
        transInfoDao.insert(transInfoDO2);

        // 2. 创建查询条件
        TransInfoQueryArg queryArg = new TransInfoQueryArg();
        queryArg.setCorpCode(TEST_CORP_CODE);
        queryArg.setPayStatus("3");

        // 3. 执行计数查询
        int count = transInfoDao.countByArg(queryArg);

        // 4. 验证计数结果正确
        assertTrue("计数结果应该大于0", count >= 2);
    }

    /**
     * 测试根据ID删除操作
     * <p>
     * 测试步骤：
     * 1. 插入测试记录
     * 2. 执行根据ID删除操作
     * 3. 验证记录被删除
     */
    @Test
    public void testDeleteById() {
        // 1. 插入测试记录
        TransInfoDO transInfoDO = createTestDO();
        transInfoDao.insert(transInfoDO);

        // 2. 执行根据ID删除操作
        transInfoDao.deleteById(transInfoDO.getId());

        // 3. 验证记录被删除
        TransInfoDO deletedTransInfo = transInfoDao.queryById(transInfoDO.getId());
        assertNull("删除后通过ID查询应该返回null", deletedTransInfo);
    }

    /**
     * 测试根据内部交易号查询交易记录 - 有效参数场景
     * <p>
     * 测试步骤：
     * 1. 插入测试交易记录数据
     * 2. 使用插入记录的内部交易号进行查询
     * 3. 验证查询结果正确
     */
    @Test
    public void testQueryByTransNo_WithValidParams() {
        // 1. 插入测试交易记录数据
        TransInfoDO transInfoDO = createTestDO();
        transInfoDao.insert(transInfoDO);

        // 2. 使用插入记录的内部交易号进行查询
        TransInfoDO queriedTransInfo = transInfoDao.queryByTransNo(transInfoDO.getTransNo(), TEST_CORP_CODE);

        // 3. 验证查询结果正确
        assertNotNull("应该能查询到记录", queriedTransInfo);
        assertEquals("内部交易号应该一致", transInfoDO.getTransNo(), queriedTransInfo.getTransNo());
        assertEquals("企业编码应该一致", TEST_CORP_CODE, queriedTransInfo.getCorpCode());
        assertEquals("交易类型应该一致", transInfoDO.getTransType(), queriedTransInfo.getTransType());
    }

    /**
     * 测试根据内部交易号查询交易记录 - 无匹配结果场景
     * <p>
     * 测试步骤：
     * 1. 插入测试交易记录数据
     * 2. 使用不存在的内部交易号进行查询
     * 3. 验证返回null
     */
    @Test
    public void testQueryByTransNo_WithNoMatch() {
        // 1. 插入测试交易记录数据
        TransInfoDO transInfoDO = createTestDO();
        transInfoDao.insert(transInfoDO);

        // 2. 使用不存在的内部交易号进行查询
        TransInfoDO result = transInfoDao.queryByTransNo("NON_EXISTENT_TRANS_NO", TEST_CORP_CODE);

        // 3. 验证返回null
        assertNull("不存在的内部交易号应该返回null", result);
    }

    /**
     * 测试根据交易代码查询交易记录 - 有效参数场景
     * <p>
     * 测试步骤：
     * 1. 插入测试交易记录数据
     * 2. 使用插入记录的交易代码进行查询
     * 3. 验证查询结果正确
     */
    @Test
    public void testQueryByTradeCode_WithValidParams() {
        // 1. 插入测试交易记录数据
        TransInfoDO transInfoDO = createTestDO();
        transInfoDao.insert(transInfoDO);

        // 2. 使用插入记录的交易代码进行查询
        TransInfoDO queriedTransInfo = transInfoDao.queryByTradeCode(transInfoDO.getTradeCode(), TEST_CORP_CODE);

        // 3. 验证查询结果正确
        assertNotNull("应该能查询到记录", queriedTransInfo);
        assertEquals("交易代码应该一致", transInfoDO.getTradeCode(), queriedTransInfo.getTradeCode());
        assertEquals("企业编码应该一致", TEST_CORP_CODE, queriedTransInfo.getCorpCode());
        assertEquals("内部交易号应该一致", transInfoDO.getTransNo(), queriedTransInfo.getTransNo());
    }

    /**
     * 测试根据交易代码查询交易记录 - 无匹配结果场景
     * <p>
     * 测试步骤：
     * 1. 插入测试交易记录数据
     * 2. 使用不存在的交易代码进行查询
     * 3. 验证返回null
     */
    @Test
    public void testQueryByTradeCode_WithNoMatch() {
        // 1. 插入测试交易记录数据
        TransInfoDO transInfoDO = createTestDO();
        transInfoDao.insert(transInfoDO);

        // 2. 使用不存在的交易代码进行查询
        TransInfoDO result = transInfoDao.queryByTradeCode("NON_EXISTENT_TRADE_CODE", TEST_CORP_CODE);

        // 3. 验证返回null
        assertNull("不存在的交易代码应该返回null", result);
    }

    @Test
    public void testQueryListByTradeCode() {
        // 1. 插入测试交易记录数据
        TransInfoDO transInfoDO = createTestDO();
        transInfoDao.insert(transInfoDO);

        // 2. 使用插入记录的交易代码进行查询
        List<TransInfoDO> queriedTransInfo = transInfoDao.queryListByTradeCode(transInfoDO.getTradeCode(), TEST_CORP_CODE);

        // 3. 验证查询结果正确
        assertNotNull("应该能查询到记录", queriedTransInfo);
    }

    /**
     * 测试根据交易代码查询交易记录 - 有效参数场景
     * <p>
     * 测试步骤：
     * 1. 插入测试交易记录数据
     * 2. 使用插入记录的交易代码进行查询
     * 3. 验证查询结果正确
     */
    @Test
    public void testQueryListByTradeCodeAndTransType_WithValidParams() {
        // 1. 插入测试交易记录数据
        TransInfoDO transInfoDO = createTestDO();
        transInfoDao.insert(transInfoDO);

        // 2. 使用插入记录的交易代码进行查询
        List<TransInfoDO> queriedTransInfo = transInfoDao.queryListByTradeCodeAndTransType(transInfoDO.getTradeCode(), 0, TEST_CORP_CODE);

        // 3. 验证查询结果正确
        assertTrue("应该能查询到记录", !CollectionUtils.isEmpty(queriedTransInfo));
        assertEquals("交易代码应该一致", transInfoDO.getTradeCode(), queriedTransInfo.get(0).getTradeCode());
        assertEquals("企业编码应该一致", TEST_CORP_CODE, queriedTransInfo.get(0).getCorpCode());
        assertEquals("内部交易号应该一致", transInfoDO.getTransNo(), queriedTransInfo.get(0).getTransNo());
    }

    /**
     * 创建测试数据对象
     *
     * @return 测试用的TransInfoDO对象
     */
    private TransInfoDO createTestDO() {
        TransInfoDO transInfoDO = new TransInfoDO();

        transInfoDO.setCorpCode(TEST_CORP_CODE);
        transInfoDO.setTransNo("TEST_TRANS_" + System.currentTimeMillis() + "_" + new Random().nextInt(1000));
        transInfoDO.setTransType(0);
        transInfoDO.setTradeId(99L);
        transInfoDO.setTradeCode("TRADE_" + System.currentTimeMillis());
        transInfoDO.setPayAway("支付宝扫码付");
        transInfoDO.setGateway(0);
        transInfoDO.setGatewayTransNo("GATEWAY_" + System.currentTimeMillis());
        transInfoDO.setBusinessType(0);
        transInfoDO.setPayAmount(new BigDecimal("100.00"));
        transInfoDO.setLoginId(100L);
        transInfoDO.setLoginName("test_operator");
        transInfoDO.setPayStatus("0");
        transInfoDO.setReportTime(new Date());
        transInfoDO.setPayTime(new Date());
        transInfoDO.setReportInfo("测试报文");
        transInfoDO.setPayTypeView("支付宝");
        transInfoDO.setPayTotal(new BigDecimal("100.00"));
        transInfoDO.setRefundPayTransno("");
        transInfoDO.setPayType("alipay");
        transInfoDO.setMainbodyPayid(1L);
        transInfoDO.setPayInfo("支付信息");
        transInfoDO.setExtendParamJson("{}");
        transInfoDO.setTid("TID001");
        transInfoDO.setPayIntegralSum(0);
        transInfoDO.setPayIntegralTotal(new BigDecimal("0.00"));
        transInfoDO.setOtherTransNo("");
        transInfoDO.setSettlement("F");
        transInfoDO.setCreateBy(TEST_USER);
        transInfoDO.setModifyBy(TEST_USER);
        transInfoDO.setDeleted("F");

        return transInfoDO;
    }
} 