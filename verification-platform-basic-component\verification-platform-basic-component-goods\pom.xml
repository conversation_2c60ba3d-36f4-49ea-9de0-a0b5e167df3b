<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.sendinfo.ticketing</groupId>
        <artifactId>verification-platform-basic-component</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>verification-platform-basic-component-goods</artifactId>
    <name>verification-platform-basic-component-goods</name>
    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sendinfo.ticketing</groupId>
            <artifactId>verification-platform-basic-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sendinfo.ticketing</groupId>
            <artifactId>verification-common-component</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sendinfo.ticketing</groupId>
            <artifactId>verification-platform-basic-repository-goods</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.sendinfo.ticketing</groupId>
            <artifactId>verification-platform-basic-repository-system</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sendinfo.paas</groupId>
            <artifactId>sendinfo-paas-cache-client-api</artifactId>
        </dependency>
    </dependencies>
</project>
