
# =================================================================================
# Git Attributes for a Standard Java Project (Updated)
# =================================================================================

# 策略一: 设置默认行为
* text=auto

# 策略二: 明确指定文本文件
# 源码文件
*.java           text
*.scala          text
*.kt             text
*.groovy         text
*.sql            text

# 构建和配置文件
*.xml            text
*.gradle         text
*.properties     text
*.yml            text
*.yaml           text
*.json           text
*.conf           text
*.sh             text

# 文档和脚本
*.md             text
*.txt            text
*.adoc           text
*.html           text
*.css            text
*.js             text

# Git相关文件
.gitignore       text
.gitattributes   text

# 策略三: 明确指定需要特定换行符的文件
*.bat            text eol=crlf
*.cmd            text eol=crlf
*.sh             text eol=lf

# 策略四: 明确指定二进制文件
# 编译产物
*.class          binary
*.jar            binary
*.war            binary
*.ear            binary

# 密钥和证书
*.jks            binary
*.p12            binary
*.keystore       binary

# 常见图片格式
*.png            binary
*.jpg            binary
*.jpeg           binary
*.gif            binary
*.ico            binary

# 压缩文件
*.zip            binary
*.gz             binary
*.tar            binary

# 文档和其他格式
*.pdf            binary
*.doc            binary
*.docx           binary
*.xls            binary
*.xlsx           binary
*.ppt            binary
*.pptx           binary

# 字体文件
*.ttf            binary
*.eot            binary
*.woff           binary
*.woff2          binary
