package com.sendinfo.ticketing.verification.basic.component.system;

import com.sendinfo.ticketing.verification.basic.component.system.param.SysAuditRoleQueryParam;
import com.sendinfo.ticketing.verification.basic.model.system.SysAuditRole;
import com.sendinfo.ticketing.verification.common.component.ReadComponent;

import java.util.List;
import java.util.Set;

/**
 * 票型角色读取组件
 * <AUTHOR>
 */
public interface SysAuditRoleReadComponent extends ReadComponent<Integer, SysAuditRoleQueryParam, SysAuditRole> {

	/**
	 * 查询票型角色
	 * @param queryParam 查询参数
	 * @return 票型角色列表
	 */
	List<SysAuditRole> querySysAuditRoles(SysAuditRoleQueryParam queryParam);

	/**
	 * 查询票型角色
	 * @param ids ids集合
	 * @param corpCode 企业编码
	 * @return 票型角色列表
	 */
	List<SysAuditRole> querySysAuditRoleByIds(Set<Integer> ids, String corpCode);
} 