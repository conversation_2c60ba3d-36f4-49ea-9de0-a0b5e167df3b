package com.sendinfo.ticketing.verification.basic.api.customer.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Set;

/**
 * 旅行社信息组织机构查询请求对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/17 14:55
 */
@Getter
@Setter
@ToString
public class TravelInfoDeptQueryRequest extends TravelInfoQueryRequest implements Serializable {

	/**
	 * 组织机构ID集合
	 */
	private Set<String> deptParentIds;
}
