package com.sendinfo.ticketing.verification.basic.component.goods.param;

import com.sendinfo.ticketing.verification.basic.enums.ExplainerUserFlagEnum;
import com.sendinfo.ticketing.verification.common.component.param.AbstractQueryParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 服务类目查询参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class ExplainerServiceCategoryQueryParam extends AbstractQueryParam {

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 业态(系统字典)
     */
    private String businessType;

    /**
     * 服务编码
     */
    private String categoryNo;

    /**
     * 服务类型名称
     */
    private String categoryName;

    /**
     * 状态F禁用 T启用
     */
    private ExplainerUserFlagEnum useFlag;
} 