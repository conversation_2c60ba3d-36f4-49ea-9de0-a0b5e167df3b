package com.sendinfo.ticketing.verification.basic.repository.pay.arg;

import com.sendinfo.ticketing.verification.common.repository.arg.AbstractUpdateArg;
import com.sendinfo.ticketing.verification.common.repository.arg.StatusUpdater;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 交易记录更新参数
 * 用于封装trans_info表的更新条件和目标字段，支持租户隔离
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class TransInfoUpdateArg extends AbstractUpdateArg<Long> {
    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 支付方式中文说明（如支付宝扫码付）
     */
    private String payAway;

    /**
     * 网关交易号（第三方交易号）
     */
    private String gatewayTransNo;

    /**
     * 状态： 0:待支付 1:支付未完成 2:未支付 3:已支付
     */
    private String payStatus;

    /**
     * 状态更新器
     */
    private StatusUpdater<String> payStatusUpdater;

    /**
     * 报告时间
     */
    private Date reportTime;

    /**
     * 实际支付时间
     */
    private Date payTime;

    /**
     * 交易报文
     */
    private String reportInfo;

    /**
     * 页面展示支付方式
     */
    private String payTypeView;

    /**
     * 退单支付交易号
     */
    private String refundPayTransno;

    /**
     * 子支付方式
     */
    private String payType;

    /**
     * 主体支付ID
     */
    private Long mainbodyPayid;

    /**
     * 付款信息
     */
    private String payInfo;

    /**
     * 扩展参数
     */
    private String extendParamJson;

    /**
     * 终端号
     */
    private String tid;

    /**
     * 积分数量
     */
    private Integer payIntegralSum;

    /**
     * 积分支付抵扣金额
     */
    private BigDecimal payIntegralTotal;

    /**
     * 其他交易号
     */
    private String otherTransNo;

    /**
     * 结算标识T结算F未结算
     */
    private String settlement;

    /**
     * 结算标识更新器
     */
    private StatusUpdater<String> settlementUpdater;

    /**
     * 修改人
     */
    private String modifyBy;
} 