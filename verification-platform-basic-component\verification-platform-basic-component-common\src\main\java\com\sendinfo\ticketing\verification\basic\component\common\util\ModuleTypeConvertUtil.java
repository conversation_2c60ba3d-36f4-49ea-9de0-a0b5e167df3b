package com.sendinfo.ticketing.verification.basic.component.common.util;

import java.util.Optional;

import com.sendinfo.ticketing.verification.basic.enums.ModuleTypeEnum;

/**
 * 模块类型转换工具类
 *
 * <AUTHOR> 2025-07-28
 */
public class ModuleTypeConvertUtil {

    private ModuleTypeConvertUtil() {
    }

    /**
     * 将整数转换为模块类型枚举
     *
     * @param moduleType 模块类型整数值
     * @return 模块类型枚举
     */
    public static ModuleTypeEnum convert(Integer moduleType) {
        return Optional.ofNullable(moduleType)
                .map(ModuleTypeEnum::of)
                .orElse(null);
    }

    /**
     * 将模块类型枚举转换为整数
     *
     * @param moduleType 模块类型枚举
     * @return 模块类型整数值
     */
    public static Integer convert(ModuleTypeEnum moduleType) {
        return Optional.ofNullable(moduleType)
                .map(ModuleTypeEnum::getCode)
                .orElse(null);
    }
}
