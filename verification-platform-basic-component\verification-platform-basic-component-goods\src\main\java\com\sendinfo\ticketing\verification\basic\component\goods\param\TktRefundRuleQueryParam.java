package com.sendinfo.ticketing.verification.basic.component.goods.param;

import com.sendinfo.ticketing.verification.common.component.param.AbstractPageQueryParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString(callSuper = true)
public class TktRefundRuleQueryParam extends AbstractPageQueryParam {
    /**
     * 企业编码
     */
    private String corpCode;
    
    /**
     * 票型ID
     */
    private Long ticketId;
    
    /**
     * 是否允许退单 0:不可退（默认） 1：检票开始前可退 2：未过期均可退
     */
    private Integer refundFlag;
    
    /**
     * 退票是否审核：T:审核 F:不审核
     */
    private String auditFlag;
    
    /**
     * 退票是否收手续费：T:收取 F:不收取
     */
    private String refundFeeFlag;
    
    /**
     * 过期票是否退票 ：T:可退 F:不可退
     */
    private String overRefundFlag;
    
    /**
     * 已检票是否可退 ：T:可退 F:不可退
     */
    private String checkedRefundFlag;
    
    /**
     * 是否支持按检票时间段不审核退票 ：T:是 F:否
     */
    private String unauditTimeFlag;
    
    /**
     * 团购票退票限制 T:是 F：否
     */
    private String teamRefundLimit;
    
    /**
     * 超过分时时段是否可退  T可退  F不可退
     */
    private String exceedAppointmentRefundFlag;
    
    /**
     * 0是不开启 1是开启
     */
    private Integer refundParkFlag;
    
    /**
     * 多人一票是否支持部分退， 0： 表示不支持， 1 表示支持
     */
    private Integer refundTypeFlag;
} 