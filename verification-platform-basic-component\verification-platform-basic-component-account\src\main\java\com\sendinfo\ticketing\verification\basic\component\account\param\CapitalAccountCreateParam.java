package com.sendinfo.ticketing.verification.basic.component.account.param;

import com.sendinfo.ticketing.verification.common.component.param.AbstractCreateParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 资金账户创建参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString
public class CapitalAccountCreateParam extends AbstractCreateParam {
    /**
     * 企业编码
     */
    private String corpCode;
    /**
     * 旅行社ID
     */
    private Long travelId;
    /**
     * 账户名称
     */
    private String name;
    /**
     * 账户余额
     */
    private BigDecimal leftValue;
    /**
     * 备用金支付密码（预留备用）
     */
    private String capitalPass;
    /**
     * 总信用额度（预留备用）
     */
    private BigDecimal creditValue;
    /**
     * 可用信用额度（预留备用）
     */
    private BigDecimal creditLeftValue;
    /**
     * 最近充值时间
     */
    private Date lastRechargingTime;
    /**
     * 账户状态 0:停用 1:启用 2:未激活
     */
    private Integer status;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 删除标志 F:未删除, T:已删除
     */
    private String deleted;
    /**
     * 迁移数据
     */
    private String transferParam;
} 