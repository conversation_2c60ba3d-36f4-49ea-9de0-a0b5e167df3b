package com.sendinfo.ticketing.verification.basic.repository.goods.dataobject;

import com.sendinfo.ticketing.verification.common.repository.dataobject.AbstractTenantBaseDO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
public class TktModelParamDO extends AbstractTenantBaseDO<Long> {
    private static final long serialVersionUID = 1L;

    private String title;
    private String modelType;
    private Integer modelKind;
    private String backImg;
    private String pageContent;
    private String hnPrintContent;
    private String printContent;
    private Integer width;
    private Integer height;
    private Integer printDirection;
    private String pageInfo;
    private String useFlag;
    private Integer roleApply;
    private String modelCode;
    private String continuousPrint;
    private String transferParam;
}
