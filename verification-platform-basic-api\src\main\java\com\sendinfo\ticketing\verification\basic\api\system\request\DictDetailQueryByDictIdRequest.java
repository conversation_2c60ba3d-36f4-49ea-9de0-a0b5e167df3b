package com.sendinfo.ticketing.verification.basic.api.system.request;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Auther: chengjie.li
 * @Date: 2025/6/23 16:52
 */
@Getter
@Setter
public class DictDetailQueryByDictIdRequest implements Serializable {
    private static final long serialVersionUID = 1882721437800071124L;

    /**
     * 字典ID
     */
    @NotNull(message = "字典ID不能为空")
    private Long dictId;

    /**
     * 企业编码
     */
    @NotNull(message = "企业编码不能为空")
    private String corpCode;
}
