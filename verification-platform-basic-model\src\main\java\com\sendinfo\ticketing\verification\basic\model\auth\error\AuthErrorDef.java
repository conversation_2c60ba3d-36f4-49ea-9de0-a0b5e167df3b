package com.sendinfo.ticketing.verification.basic.model.auth.error;

import com.sendinfo.ticketing.verification.basic.model.auth.domain.AuthDomainDefinition;
import com.sendinfo.ticketing.verification.common.model.domain.DomainDefinition;
import com.sendinfo.ticketing.verification.common.model.error.ErrorDefinition;
import com.sendinfo.ticketing.verification.common.model.service.ServiceDefinition;

import static com.sendinfo.ticketing.verification.common.model.enums.ProductCode.TICKET_PLATFORM;

/**
 * <AUTHOR>
 * @since 2025-07-23 13:40:31
 */
public enum AuthErrorDef implements ErrorDefinition {
    JWT_INIT_ERROR(TICKET_PLATFORM, AuthDomainDefinition.AUTH, "001", "ticket-platform.auth.jwt-init-error"),
    HTTP_HEADER_JWT_TOKEN_NOT_EXIST(TICKET_PLATFORM, AuthDomainDefinition.AUTH, "002", "ticket-platform.auth.http-header-jwt-token-not-exist"),
    JWT_TOKEN_EXPIRED(TICKET_PLATFORM, AuthDomainDefinition.AUTH, "003", "ticket-platform.auth.jwt-token-expired"),
    JWT_TOKEN_PARSE_ERROR(TICKET_PLATFORM, AuthDomainDefinition.AUTH, "004", "ticket-platform.auth.jwt-token-parse-error"),
    CACHE_JWT_TOKEN_NOT_EXIST(TICKET_PLATFORM, AuthDomainDefinition.AUTH, "005", "ticket-platform.auth.cache-jwt-token-not-exist"),
    JWT_INVALID(TICKET_PLATFORM, AuthDomainDefinition.AUTH, "005", "ticket-platform.auth.jwt-invalid"),
    GET_USER_SESSION_DATA_FROM_OLD_PW_JWT_TOKEN(TICKET_PLATFORM, AuthDomainDefinition.AUTH, "006", "ticket-platform.auth.get-user-session-data-from-old-pw-jwt-token"),
    ;

    private final String code;
    private final String resourceKey;

    /**
     * 构造函数
     *
     * @param serverDefinition           服务定义
     * @param domainDefinition           领域定义
     * @param functionCode               功能码
     * @param resourceBundlerResourceKey 国际化资源key
     */
    AuthErrorDef(ServiceDefinition serverDefinition, DomainDefinition domainDefinition, String functionCode,
                 String resourceBundlerResourceKey) {
        this.code = String.join("-", serverDefinition.code(), domainDefinition.code(), functionCode);
        this.resourceKey = resourceBundlerResourceKey;
    }

    @Override
    public String code() {
        return code;
    }

    @Override
    public String resourceKey() {
        return resourceKey;
    }
}