package com.sendinfo.ticketing.verification.basic.repository.pay.arg;

import com.sendinfo.ticketing.verification.common.repository.arg.AbstractQueryArg;
import com.sendinfo.ticketing.verification.common.repository.arg.Pageable;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 售票员收款方式查询参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class TktSellerPayawayQueryArg extends AbstractQueryArg implements Pageable {
    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 售票员
     */
    private String seller;

    /**
     * 账户ID
     */
    private Long accId;

    /**
     * 售票模式：1:正常出票 2:预售票 3:电子商务票 4:手工票补录 5:剧院售票6：自助机7:扫码入园
     */
    private Integer saleModel;

    /**
     * 客户类型（1：散客 2：团队...）
     */
    private Integer clientType;

    /**
     * 支付方式
     */
    private String payAway;

    /**
     * 启用状态：T:启用（默认）,F:不启用
     */
    private String useFlag;

    /**
     * 支付ID
     */
    private Long payId;

    /**
     * offset
     */
    private Integer offset;

    /**
     * limit
     */
    private Integer limit;
} 