package com.sendinfo.ticketing.verification.basic.component.infra;

import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamValueQueryParam;
import com.sendinfo.ticketing.verification.basic.model.infra.SysParamValue;
import com.sendinfo.ticketing.verification.common.component.ReadComponent;

import java.util.List;

/**
 * 系统参数值读取组件
 *
 * <AUTHOR> 2025-05-20 15:40:00
 */
public interface SysParamValueReadComponent extends ReadComponent<Long, SysParamValueQueryParam, SysParamValue> {

    /**
     * 根据参数编码和企业编码查询参数值
     *
     * @param paramCode 参数编码
     * @param corpCode  企业编码
     * @return 参数值
     */
    SysParamValue queryByParamCodeAndCorpCode(String paramCode, String corpCode);

    /**
     * 根据参数Key列表查询参数值列表
     *
     * @param paramKeyList 参数Key列表
     * @return 参数值列表
     */
    List<SysParamValue> queryByParamKeyList(List<String> paramKeyList);

    /**
     * 根据参数Key列表和企业编码查询参数值列表
     *
     * @param corpCode     企业编码
     * @param paramKeyList 参数Key列表
     * @return 参数值列表
     */
    List<SysParamValue> queryByParamKeyList(String corpCode, List<String> paramKeyList);

    /**
     * 根据参数Key和租户企业编码查询参数值
     *
     * @param paramKey       参数Key
     * @param tenantCorpCode 租户企业编码
     * @return 参数值
     */
    SysParamValue queryByParamKeyAndTenantCorpCode(String paramKey, String tenantCorpCode);

}