package com.sendinfo.ticketing.verification.basic.component.goods.converter;

import com.sendinfo.ticketing.verification.basic.api.goods.request.TktTicketTypeQueryRequest;
import com.sendinfo.ticketing.verification.basic.component.goods.mapper.TktTicketTypeMapper;
import com.sendinfo.ticketing.verification.basic.component.goods.param.TktTicketTypeQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TktTicketType;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TktTicketTypeQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TktTicketTypeDO;
import com.sendinfo.ticketing.verification.common.component.converter.ReadDo2ModelConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadParam2ArgConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadReq2ParamConverter;
import org.springframework.stereotype.Component;

/**
 * 门票类型转换器
 *
 * <AUTHOR>
 */
@Component("tktTicketTypeConverter")
public class TktTicketTypeConverter implements
        ReadParam2ArgConverter<TktTicketTypeQueryParam, TktTicketTypeQueryArg>,
        ReadDo2ModelConverter<TktTicketTypeDO, TktTicketType>,
        ReadReq2ParamConverter<TktTicketTypeQueryRequest, TktTicketTypeQueryParam> {

    @Override
    public TktTicketType r_d2m(TktTicketTypeDO TktTicketTypeDO) {
        return TktTicketTypeMapper.INSTANCE.convert(TktTicketTypeDO);
    }

    @Override
    public TktTicketTypeQueryArg r_p2a(TktTicketTypeQueryParam queryParam) {
        return TktTicketTypeMapper.INSTANCE.convert(queryParam);
    }

    @Override
    public TktTicketTypeQueryParam r_r2p(TktTicketTypeQueryRequest request) {
        return TktTicketTypeMapper.INSTANCE.convert(request);
    }
} 