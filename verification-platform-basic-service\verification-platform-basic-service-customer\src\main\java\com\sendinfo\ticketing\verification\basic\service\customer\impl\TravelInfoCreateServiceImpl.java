package com.sendinfo.ticketing.verification.basic.service.customer.impl;

import com.sendinfo.ticketing.verification.basic.api.customer.request.TravelAssemblyCreateRequest;
import com.sendinfo.ticketing.verification.basic.api.customer.TravelInfoCreateService;
import com.sendinfo.ticketing.verification.basic.exception.TicketPlatformBizRuntimeException;
import com.sendinfo.ticketing.verification.basic.model.customer.TravelInfo;
import com.sendinfo.ticketing.verification.basic.model.customer.error.CustomerErrorDef;
import com.sendinfo.ticketing.verification.basic.service.customer.enums.CustomerAttachmentKey;
import com.sendinfo.ticketing.verification.basic.service.customer.function.CreateTravelInfoFunction;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;
import com.sendinfo.ticketing.verification.common.api.result.Results;
import com.sendinfo.ticketing.verification.common.service.support.ApiCommonFailedBiActions;
import com.sendinfo.ticketing.verification.flow.FlowAgentBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 旅行社信息创建服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/22 17:04
 */
@Slf4j
@Service("travelInfoCreateService")
public class TravelInfoCreateServiceImpl implements TravelInfoCreateService {

	private final FlowAgentBuilder flowAgentBuilder;
	private CreateTravelInfoFunction createTravelInfoFunction;

	public TravelInfoCreateServiceImpl(FlowAgentBuilder flowAgentBuilder,
	                                   CreateTravelInfoFunction createTravelInfoFunction) {
		this.flowAgentBuilder = flowAgentBuilder;
		this.createTravelInfoFunction = createTravelInfoFunction;
	}

	@Override
	public ResultModel<TravelInfo> createTravelInfo(TravelAssemblyCreateRequest travelAssemblyCreateRequest) {
		return flowAgentBuilder.<TravelAssemblyCreateRequest, ResultModel<TravelInfo>>validateThenChooseBuilder()
				.appendLogicAction(createTravelInfoFunction::createTravelInfo)
				.withSuccessfulAction(q -> Results.success(q.getAttachment(CustomerAttachmentKey.TRAVEL_TAX_ATTACHMENT_KEY)))
				.withFailedBiAction(ApiCommonFailedBiActions::reasonMappingToResult)
				.withExceptionBiAction((q, th) -> {
					log.error("[TravelInfoCreateServiceImpl] createTravelInfo error.question is {}, error is {}", q, th);
					return Results.fail(CustomerErrorDef.QUERY_TRAVEL_INFO_ERROR);
				})
				.rethrowException(TicketPlatformBizRuntimeException.class::isInstance)
				.build()
				.prompt(travelAssemblyCreateRequest)
				.getResult();
	}
}
