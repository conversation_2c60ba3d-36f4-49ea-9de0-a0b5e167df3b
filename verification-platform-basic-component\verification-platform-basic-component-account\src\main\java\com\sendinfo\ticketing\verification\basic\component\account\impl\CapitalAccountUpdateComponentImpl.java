package com.sendinfo.ticketing.verification.basic.component.account.impl;

import com.sendinfo.ticketing.verification.basic.component.account.CapitalAccountUpdateComponent;
import com.sendinfo.ticketing.verification.basic.component.account.converter.CapitalAccountConverter;
import com.sendinfo.ticketing.verification.basic.component.account.param.CapitalAccountUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.account.CapitalAccount;
import com.sendinfo.ticketing.verification.basic.repository.account.arg.CapitalAccountUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.account.dao.CapitalAccountDao;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleUpdate;
import lombok.Getter;
import org.springframework.stereotype.Component;

/**
 * 资金账户更新组件实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Component("capitalAccountUpdateComponent")
public class CapitalAccountUpdateComponentImpl implements CapitalAccountUpdateComponent,
        DaoBasedSingleUpdate<CapitalAccountUpdateParam, CapitalAccountUpdateArg, CapitalAccount> {
    private final CapitalAccountDao dao;
    private final CapitalAccountConverter converter;

    public CapitalAccountUpdateComponentImpl(CapitalAccountDao dao, CapitalAccountConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }
} 