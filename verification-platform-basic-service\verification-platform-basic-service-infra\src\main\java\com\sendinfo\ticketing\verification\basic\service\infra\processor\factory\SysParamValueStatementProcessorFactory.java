/**
 * 系统参数值声明处理器工厂类
 * 基于 BizCodeCenter 设计，采用处理器自标识的方式
 *
 * <AUTHOR> 2025-08-04
 */
package com.sendinfo.ticketing.verification.basic.service.infra.processor.factory;

import com.sendinfo.ticketing.verification.basic.enums.SysParamDataType;
import com.sendinfo.ticketing.verification.basic.service.infra.processor.SysParamValueStatementProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * 系统参数值声明处理器工厂类
 *
 */
@Slf4j
@Component("sysParamValueStatementProcessorFactory")
public class SysParamValueStatementProcessorFactory {

    private final Map<SysParamDataType, SysParamValueStatementProcessor> processorMap = new HashMap<>();
    private final ApplicationContext applicationContext;

    public SysParamValueStatementProcessorFactory(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    /**
     * 初始化处理器映射
     * 自动发现 Spring 容器中的所有处理器实现并注册
     */
    @PostConstruct
    public void init() {
        log.info("[SysParamValueStatementProcessorFactory] start init...");
        
        // 获取 Spring 容器中所有的处理器实现
        Map<String, SysParamValueStatementProcessor> processorBeans = 
            applicationContext.getBeansOfType(SysParamValueStatementProcessor.class);
        
        // 遍历所有处理器，通过 getDataType() 方法获取其处理的数据类型
        for (Map.Entry<String, SysParamValueStatementProcessor> entry : processorBeans.entrySet()) {
            String beanName = entry.getKey();
            SysParamValueStatementProcessor processor = entry.getValue();
            
            try {
                SysParamDataType dataType = processor.getDataType();
                if (dataType != null) {
                    // 检查是否有重复的数据类型
                    if (processorMap.containsKey(dataType)) {
                        throw new IllegalArgumentException("Duplicate processor dataType:" + dataType);
                    }
                    processorMap.put(dataType, processor);
                    log.info("[SysParamValueStatementProcessorFactory] register processor: {} -> {} (Bean: {})",
                            dataType, processor.getClass().getSimpleName(), beanName);
                } else {
                    log.warn("[SysParamValueStatementProcessorFactory] processor {} return dataType null, skip...",
                            beanName);
                }
            } catch (Exception e) {
                log.error("[SysParamValueStatementProcessorFactory] register processor {} error happen", beanName, e);
                throw e;
            }
        }
        
        log.info("[SysParamValueStatementProcessorFactory] init finish, register processor count:{}",
                processorMap.size());
    }

    /**
     * 根据数据类型获取对应的处理器
     *
     * @param dataType 系统参数数据类型
     * @return 对应的处理器实现
     * @throws IllegalArgumentException 当数据类型为 null 或找不到对应处理器时抛出
     */
    public SysParamValueStatementProcessor getProcessor(SysParamDataType dataType) {
        if (dataType == null) {
            throw new IllegalArgumentException("[SysParamValueStatementProcessorFactory] dataType couldn't be null.");
        }
        
        SysParamValueStatementProcessor processor = processorMap.get(dataType);
        if (processor == null) {
            throw new IllegalArgumentException("couldn't find processor, dataType:"+ dataType);
        }
        
        return processor;
    }
}
