package com.sendinfo.ticketing.verification.basic.api.infra.request;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 *  系统参数值状态切换请求
 *
 *  <AUTHOR> 2025-05-20 15:40:00
 */
@Getter
@Setter
@ToString
public class SysParamValueStatusSwitchRequest implements Serializable {
    private static final long serialVersionUID = -7654321098765432105L;

    /**
     * 主键ID
     */
    @NotNull(message = "ID不能为空")
    private Long id;

    /**
     * 状态：1-启用，0-禁用
     */
    @NotNull(message = "状态不能为空")
    private Integer status;

    /**
     * 修改人
     */
    private String modifyBy;
} 