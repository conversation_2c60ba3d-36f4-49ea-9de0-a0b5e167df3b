package com.sendinfo.ticketing.verification.basic.repository.pay.arg;

import com.sendinfo.ticketing.verification.common.repository.arg.AbstractQueryArg;
import com.sendinfo.ticketing.verification.common.repository.arg.Pageable;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 支付渠道查询参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class PayChanleQueryArg extends AbstractQueryArg implements Pageable {
    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 渠道编码
     */
    private String chanleCode;

    /**
     * 渠道名称
     */
    private String chanleName;

    /**
     * 支付产品码
     */
    private String payProductCode;

    /**
     * 启用标识
     */
    private String useFlag;

    /**
     * 分页偏移量
     */
    private Integer offset;

    /**
     * 分页大小
     */
    private Integer limit;
} 