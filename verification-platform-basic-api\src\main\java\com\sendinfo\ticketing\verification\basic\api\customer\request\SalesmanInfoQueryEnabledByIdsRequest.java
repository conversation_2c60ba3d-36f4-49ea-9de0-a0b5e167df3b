package com.sendinfo.ticketing.verification.basic.api.customer.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Set;

/**
 * 业务员信息批量查询启用状态请求参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString
public class SalesmanInfoQueryEnabledByIdsRequest implements Serializable {


    private static final long serialVersionUID = 951571161258454100L;
    /**
     * 业务员ID集合
     */
    @NotEmpty(message = "业务员ID集合不能为空")
    private Set<Long> ids;

    /**
     * 企业编码
     */
    @NotNull(message = "企业编码不能为空")
    private String corpCode;
} 