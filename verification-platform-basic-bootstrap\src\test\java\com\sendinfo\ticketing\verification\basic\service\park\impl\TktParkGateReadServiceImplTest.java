package com.sendinfo.ticketing.verification.basic.service.park.impl;

import com.sendinfo.ticketing.verification.basic.api.park.TktParkGateReadService;
import com.sendinfo.ticketing.verification.basic.api.park.request.TktParkGateQueryCondition;
import com.sendinfo.ticketing.verification.basic.api.park.request.TktParkGateQueryRequest;
import com.sendinfo.ticketing.verification.basic.bootstrap.VerificationPlatformBasicBootstrap;
import com.sendinfo.ticketing.verification.basic.model.park.TktParkGate;
import com.sendinfo.ticketing.verification.common.api.request.PageRequest;
import com.sendinfo.ticketing.verification.common.api.result.PageResultModel;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static org.junit.Assert.*;

/**
 * <AUTHOR> Name]
 * @since 2025/6/19 17:15
 **/
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = VerificationPlatformBasicBootstrap.class)
public class TktParkGateReadServiceImplTest {

    @Autowired
    private TktParkGateReadService tktParkGateReadService;

//    @Test
    public void testSearchParkGateSuccess() {
        // Arrange
        PageRequest<TktParkGateQueryCondition> pageRequest = new PageRequest<>();
        pageRequest.setPageSize(10);
        pageRequest.setCurrentPage(1);
        TktParkGateQueryCondition condition = new TktParkGateQueryCondition();
        condition.setCorpCode("0001");
        pageRequest.setCondition(condition);
        
        // Act
        PageResultModel<TktParkGate> result = tktParkGateReadService.searchParkGate(pageRequest);
        
        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertFalse(result.getModelList().isEmpty());
    }

//    @Test
    public void testQueryParkGateListSuccess() {
        // Arrange
        Long parkZoneId = 51L;
        String corpCode = "0001";
        
        // Act
        ResultModel<List<TktParkGate>> result = tktParkGateReadService.queryParkGateList(parkZoneId, corpCode);
        
        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertFalse(result.getModel().isEmpty());
    }

//    @Test
    public void testQueryParkGateSuccess() {
        // Arrange
        Long parkGateId = 22L;
        String corpCode = "0001";
        
        // Act
        ResultModel<TktParkGate> result = tktParkGateReadService.queryParkGate(parkGateId, corpCode);
        
        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getModel());
    }

//    @Test
    public void testQueryParkGateByGateNoSuccess() {
        // Arrange
        TktParkGateQueryRequest request = new TktParkGateQueryRequest();
        request.setGateNo(7);
        request.setParkZoneId(51L);
        request.setCorpCode("0001");
        
        // Act
        ResultModel<TktParkGate> result = tktParkGateReadService.queryParkGateByGateNo(request);
        
        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getModel());
    }
}
