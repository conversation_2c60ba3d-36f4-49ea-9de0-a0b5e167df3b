package com.sendinfo.ticketing.verification.basic.component.goods.param;

import com.sendinfo.ticketing.verification.common.component.param.AbstractQueryParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString(callSuper = true)
public class TktSaleRuleQueryParam extends AbstractQueryParam {
    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 票型ID
     */
    private Long ticketId;

    /**
     * 销售模式
     */
    private String saleModel;

    /**
     * 销售渠道
     */
    private String saleChannel;

    /**
     * 是否限制购买数量
     */
    private String saleLimit;

    /**
     * 游客购票限制
     */
    private String buyLimitFlag;

    /**
     * 游客年龄限制
     */
    private String ageLimitFlag;

    /**
     * 是否必须取票
     */
    private String pickupFlag;

    /**
     * 票价是否可修改
     */
    private String priceModifyFlag;

    /**
     * 同步类型
     */
    private String syncType;

    /**
     * 星期售票限制
     */
    private String weekLimitFlag;

    /**
     * 售票时段限制
     */
    private String sessionLimitFlag;

    /**
     * 保险标志
     */
    private String insuranceFlag;

    /**
     * 是否日历价格
     */
    private String dayPriceFlag;

    /**
     * 是否对接一卡通
     */
    private String joinSmartCardFlag;

    /**
     * 采集证件照
     */
    private Integer collectCardImg;

    /**
     * 销售渠道标识
     */
    private String saleBillSource;
} 