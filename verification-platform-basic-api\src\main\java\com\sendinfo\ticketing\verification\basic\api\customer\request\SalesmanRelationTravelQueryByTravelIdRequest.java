package com.sendinfo.ticketing.verification.basic.api.customer.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 业务员关联客户按客户ID查询请求参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString
public class SalesmanRelationTravelQueryByTravelIdRequest implements Serializable {

    private static final long serialVersionUID = 6234567890123456789L;

    /**
     * 客户ID
     */
    @NotNull(message = "客户ID不能为空")
    private Long travelId;

    /**
     * 企业编码
     */
    @NotNull(message = "企业编码不能为空")
    private String corpCode;
} 