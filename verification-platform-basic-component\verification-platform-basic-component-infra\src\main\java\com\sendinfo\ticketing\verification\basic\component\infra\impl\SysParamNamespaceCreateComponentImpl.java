package com.sendinfo.ticketing.verification.basic.component.infra.impl;

import lombok.Getter;
import org.springframework.stereotype.Component;

import com.sendinfo.ticketing.verification.basic.component.infra.SysParamNamespaceCreateComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.converter.SysParamNamespaceConverter;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamNamespaceCreateParam;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.SysParamNamespaceDao;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.SysParamNamespaceDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleCreate;

/**
 * 参数命名空间创建组件实现类，负责创建参数命名空间的业务逻辑
 *
 * <AUTHOR> 2025-07-21 15:30:00
 */
@Component("sysParamNamespaceCreateComponent")
@Getter
public class SysParamNamespaceCreateComponentImpl
        implements SysParamNamespaceCreateComponent,
        DaoBasedSingleCreate<Long, SysParamNamespaceCreateParam, SysParamNamespaceDO> {

    private final SysParamNamespaceDao dao;
    private final SysParamNamespaceConverter converter;

    public SysParamNamespaceCreateComponentImpl(SysParamNamespaceDao dao, SysParamNamespaceConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }
}