<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sendinfo.ticketing.verification.basic.repository.pay.dao.SysMainbodyDao">

    <resultMap id="BaseResultMap" type="com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysMainbodyDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="CORP_CODE" property="corpCode" jdbcType="VARCHAR"/>
        <result column="MAINBODY_NAME" property="mainbodyName" jdbcType="VARCHAR"/>
        <result column="MAINBODY_SHORT_NAME" property="mainbodyShortName" jdbcType="VARCHAR"/>
        <result column="MERCHANT_NO" property="merchantNo" jdbcType="VARCHAR"/>
        <result column="LINK_MAN" property="linkMan" jdbcType="VARCHAR"/>
        <result column="link_tel" property="linkTel" jdbcType="VARCHAR"/>
        <result column="REMARK" property="remark" jdbcType="VARCHAR"/>
        <result column="MAINBODY_NUMBER" property="mainbodyNumber" jdbcType="VARCHAR"/>
        <result column="DEPT_ID" property="deptId" jdbcType="BIGINT"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="CREATE_BY" property="createBy" jdbcType="VARCHAR"/>
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="MODIFY_BY" property="modifyBy" jdbcType="VARCHAR"/>
        <result column="DELETED" property="deleted" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="tableName">sys_mainbody</sql>

    <sql id="allColumns">
        id, CORP_CODE, MAINBODY_NAME, MAINBODY_SHORT_NAME, MERCHANT_NO, LINK_MAN, link_tel, REMARK,
        MAINBODY_NUMBER, DEPT_ID, CREATE_TIME, CREATE_BY, MODIFY_TIME, MODIFY_BY, DELETED
    </sql>

    <sql id="insertColumns">
        CORP_CODE, MAINBODY_NAME, MAINBODY_SHORT_NAME, MERCHANT_NO, LINK_MAN, link_tel, REMARK,
        MAINBODY_NUMBER, DEPT_ID, CREATE_BY, MODIFY_BY, DELETED,
        CREATE_TIME, MODIFY_TIME
    </sql>

    <insert id="insert" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysMainbodyDO" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO <include refid="tableName"/> (<include refid="insertColumns"/>)
        VALUES (
            #{corpCode,jdbcType=VARCHAR}, #{mainbodyName,jdbcType=VARCHAR}, #{mainbodyShortName,jdbcType=VARCHAR},
            #{merchantNo,jdbcType=VARCHAR}, #{linkMan,jdbcType=VARCHAR}, #{linkTel,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
            #{mainbodyNumber,jdbcType=VARCHAR}, #{deptId,jdbcType=BIGINT}, #{createBy,jdbcType=VARCHAR}, #{modifyBy,jdbcType=VARCHAR}, #{deleted,jdbcType=VARCHAR},
            NOW(), NOW()
        )
    </insert>

    <select id="queryById" resultMap="BaseResultMap" parameterType="map">
        SELECT <include refid="allColumns"/>
        FROM <include refid="tableName"/>
        WHERE id = #{id,jdbcType=BIGINT}
          AND deleted = 'F'
    </select>

    <update id="updateByArg" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyUpdateArg">
        UPDATE <include refid="tableName"/>
        <set>
            <if test="mainbodyName != null and mainbodyName != ''">MAINBODY_NAME = #{mainbodyName,jdbcType=VARCHAR},</if>
            <if test="mainbodyShortName != null and mainbodyShortName != ''">MAINBODY_SHORT_NAME = #{mainbodyShortName,jdbcType=VARCHAR},</if>
            <if test="merchantNo != null and merchantNo != ''">MERCHANT_NO = #{merchantNo,jdbcType=VARCHAR},</if>
            <if test="linkMan != null and linkMan != ''">LINK_MAN = #{linkMan,jdbcType=VARCHAR},</if>
            <if test="linkTel != null and linkTel != ''">link_tel = #{linkTel,jdbcType=VARCHAR},</if>
            <if test="remark != null and remark != ''">REMARK = #{remark,jdbcType=VARCHAR},</if>
            <if test="mainbodyNumber != null and mainbodyNumber != ''">MAINBODY_NUMBER = #{mainbodyNumber,jdbcType=VARCHAR},</if>
            <if test="deptId != null">DEPT_ID = #{deptId,jdbcType=BIGINT},</if>
            <if test="modifyBy != null and modifyBy != ''">MODIFY_BY = #{modifyBy,jdbcType=VARCHAR},</if>
            MODIFY_TIME = NOW()
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
          AND CORP_CODE = #{corpCode,jdbcType=VARCHAR}
          AND deleted = 'F'
    </update>

    <update id="softDeleteByArg" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyDeleteArg">
        UPDATE <include refid="tableName"/>
        SET deleted = 'T',
            <choose>
                <when test="modifyBy != null and modifyBy != ''">MODIFY_BY = #{modifyBy,jdbcType=VARCHAR},</when>
                <otherwise>MODIFY_BY = 'SYSTEM_SDA',</otherwise>
            </choose>
            MODIFY_TIME = NOW()
        WHERE CORP_CODE = #{corpCode,jdbcType=VARCHAR}
          AND deleted = 'F'
          <if test="id != null">AND id = #{id,jdbcType=BIGINT}</if>
    </update>

    <update id="deleteById" parameterType="java.lang.Long">
        UPDATE <include refid="tableName"/>
        SET deleted = 'T',
            MODIFY_BY = 'SYSTEM_DBI',
            MODIFY_TIME = NOW()
        WHERE id = #{id,jdbcType=BIGINT}
          AND deleted = 'F'
    </update>

    <select id="countByArg" resultType="int" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyQueryArg">
        SELECT COUNT(*)
        FROM <include refid="tableName"/>
        <where>
            CORP_CODE = #{corpCode,jdbcType=VARCHAR}
            AND deleted = 'F'
            <if test="mainbodyName != null and mainbodyName != ''">AND MAINBODY_NAME LIKE CONCAT('%', #{mainbodyName,jdbcType=VARCHAR}, '%')</if>
            <if test="mainbodyShortName != null and mainbodyShortName != ''">AND MAINBODY_SHORT_NAME LIKE CONCAT('%', #{mainbodyShortName,jdbcType=VARCHAR}, '%')</if>
            <if test="merchantNo != null and merchantNo != ''">AND MERCHANT_NO = #{merchantNo,jdbcType=VARCHAR}</if>
            <if test="linkMan != null and linkMan != ''">AND LINK_MAN LIKE CONCAT('%', #{linkMan,jdbcType=VARCHAR}, '%')</if>
            <if test="linkTel != null and linkTel != ''">AND link_tel = #{linkTel,jdbcType=VARCHAR}</if>
            <if test="remark != null and remark != ''">AND REMARK LIKE CONCAT('%', #{remark,jdbcType=VARCHAR}, '%')</if>
            <if test="mainbodyNumber != null and mainbodyNumber != ''">AND MAINBODY_NUMBER = #{mainbodyNumber,jdbcType=VARCHAR}</if>
            <if test="deptId != null">AND DEPT_ID = #{deptId,jdbcType=BIGINT}</if>
        </where>
    </select>

    <select id="queryByArg" resultMap="BaseResultMap" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyQueryArg">
        SELECT <include refid="allColumns"/>
        FROM <include refid="tableName"/>
        <where>
            CORP_CODE = #{corpCode,jdbcType=VARCHAR}
            AND deleted = 'F'
            <if test="mainbodyName != null and mainbodyName != ''">AND MAINBODY_NAME LIKE CONCAT('%', #{mainbodyName,jdbcType=VARCHAR}, '%')</if>
            <if test="mainbodyShortName != null and mainbodyShortName != ''">AND MAINBODY_SHORT_NAME LIKE CONCAT('%', #{mainbodyShortName,jdbcType=VARCHAR}, '%')</if>
            <if test="merchantNo != null and merchantNo != ''">AND MERCHANT_NO = #{merchantNo,jdbcType=VARCHAR}</if>
            <if test="linkMan != null and linkMan != ''">AND LINK_MAN LIKE CONCAT('%', #{linkMan,jdbcType=VARCHAR}, '%')</if>
            <if test="linkTel != null and linkTel != ''">AND link_tel = #{linkTel,jdbcType=VARCHAR}</if>
            <if test="remark != null and remark != ''">AND REMARK LIKE CONCAT('%', #{remark,jdbcType=VARCHAR}, '%')</if>
            <if test="mainbodyNumber != null and mainbodyNumber != ''">AND MAINBODY_NUMBER = #{mainbodyNumber,jdbcType=VARCHAR}</if>
            <if test="deptId != null">AND DEPT_ID = #{deptId,jdbcType=BIGINT}</if>
        </where>
        ORDER BY id DESC
        <if test="limit != null and offset != null">
            LIMIT #{offset,jdbcType=INTEGER}, #{limit,jdbcType=INTEGER}
        </if>
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO <include refid="tableName"/> (<include refid="insertColumns"/>)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.corpCode,jdbcType=VARCHAR}, #{item.mainbodyName,jdbcType=VARCHAR}, #{item.mainbodyShortName,jdbcType=VARCHAR},
                #{item.merchantNo,jdbcType=VARCHAR}, #{item.linkMan,jdbcType=VARCHAR}, #{item.linkTel,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR},
                #{item.mainbodyNumber,jdbcType=VARCHAR}, #{item.deptId,jdbcType=BIGINT}, #{item.createBy,jdbcType=VARCHAR}, #{item.modifyBy,jdbcType=VARCHAR}, #{item.deleted,jdbcType=VARCHAR},
                NOW(), NOW()
            )
        </foreach>
    </insert>
</mapper> 