/**
 *  系统参数值读取组件实现测试
 *
 *  <AUTHOR> 2025-07-29 15:30:00
 */
package com.sendinfo.ticketing.verification.basic.component.infra.impl;

import com.sendinfo.ticketing.verification.basic.component.infra.converter.SysParamValueConverter;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamValueQueryParam;
import com.sendinfo.ticketing.verification.basic.model.infra.SysParamValue;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.SysParamValueDao;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.SysParamValueDO;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamValueQueryArg;
import com.sendinfo.ticketing.verification.basic.enums.CommonStatusEnum;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.*;

/**
 * 系统参数值查询组件实现测试
 *
 * <AUTHOR> 2025-07-29 15:30:00
 */
@RunWith(MockitoJUnitRunner.class)
public class SysParamValueReadComponentImplTest {

    @Mock
    private SysParamValueDao dao;

    private SysParamValueConverter converter;
    private SysParamValueReadComponentImpl component;

    // 测试常量
    private static final String TEST_CORP_CODE = "TEST_CORP";
    private static final String TEST_MODULE_CODE = "TEST_MODULE";
    private static final String TEST_COMPONENT_CODE = "TEST_COMPONENT";
    private static final String TEST_GROUP_CODE = "TEST_GROUP";
    private static final String TEST_PARAM_CODE = "TEST_PARAM";
    private static final String TEST_PARAM_KEY = "TEST_MODULE.TEST_COMPONENT.TEST_PARAM";
    private static final String TEST_PARAM_VALUE = "Test Value";
    private static final String TEST_TENANT_CORP_CODE = "0001";
    private static final String TEST_CREATE_BY = "test_user";
    private static final String TEST_MODIFY_BY = "test_user";

    private static final Long TEST_ID = 1L;

    @Before
    public void setUp() {
        converter = new SysParamValueConverter();
        component = new SysParamValueReadComponentImpl(dao, converter);
    }

    @Test
    public void testQueryByParamCodeAndCorpCode_Success() {
        // Given
        String paramCode = TEST_PARAM_CODE;
        String corpCode = TEST_CORP_CODE;

        SysParamValueDO mockDO = createTestSysParamValueDO();
        when(dao.queryByParamCodeAndCorpCode(paramCode, corpCode)).thenReturn(mockDO);

        // When
        SysParamValue result = component.queryByParamCodeAndCorpCode(paramCode, corpCode);

        // Then
        Assert.assertNotNull(result);
        Assert.assertEquals(TEST_ID, result.getId());
        Assert.assertEquals(TEST_CORP_CODE, result.getCorpCode());
        Assert.assertEquals(TEST_PARAM_CODE, result.getParamCode());
        Assert.assertEquals(TEST_PARAM_VALUE, result.getParamValue());

        verify(dao, times(1)).queryByParamCodeAndCorpCode(paramCode, corpCode);
    }

    @Test
    public void testQueryByParamCodeAndCorpCode_NotFound() {
        // Given
        String paramCode = "NON_EXISTENT_PARAM";
        String corpCode = TEST_CORP_CODE;

        when(dao.queryByParamCodeAndCorpCode(paramCode, corpCode)).thenReturn(null);

        // When
        SysParamValue result = component.queryByParamCodeAndCorpCode(paramCode, corpCode);

        // Then
        Assert.assertNull(result);
        verify(dao, times(1)).queryByParamCodeAndCorpCode(paramCode, corpCode);
    }

    @Test
    public void testQueryByParamKeyList_Success() {
        // Given
        List<String> paramKeyList = Arrays.asList(
                TEST_PARAM_KEY + "_1",
                TEST_PARAM_KEY + "_2",
                TEST_PARAM_KEY + "_3");

        List<SysParamValueDO> mockDOList = Arrays.asList(
                createTestSysParamValueDO(1L, TEST_PARAM_KEY + "_1"),
                createTestSysParamValueDO(2L, TEST_PARAM_KEY + "_2"),
                createTestSysParamValueDO(3L, TEST_PARAM_KEY + "_3"));

        when(dao.queryByParamKeyList(paramKeyList)).thenReturn(mockDOList);

        // When
        List<SysParamValue> results = component.queryByParamKeyList(paramKeyList);

        // Then
        Assert.assertNotNull(results);
        Assert.assertEquals(3, results.size());

        for (int i = 0; i < results.size(); i++) {
            SysParamValue result = results.get(i);
            Assert.assertEquals(Long.valueOf(i + 1), result.getId());
            Assert.assertEquals(TEST_PARAM_KEY + "_" + (i + 1), result.getParamKey());
            Assert.assertEquals(TEST_CORP_CODE, result.getCorpCode());
        }

        verify(dao, times(1)).queryByParamKeyList(paramKeyList);
    }

    @Test
    public void testQueryByParamKeyList_EmptyList() {
        // Given
        List<String> emptyParamKeyList = Collections.emptyList();
        List<SysParamValueDO> emptyDOList = Collections.emptyList();

        when(dao.queryByParamKeyList(emptyParamKeyList)).thenReturn(emptyDOList);

        // When
        List<SysParamValue> results = component.queryByParamKeyList(emptyParamKeyList);

        // Then
        Assert.assertNotNull(results);
        Assert.assertTrue(results.isEmpty());

        verify(dao, times(1)).queryByParamKeyList(emptyParamKeyList);
    }

    @Test
    public void testQueryByParamKeyList_NotFound() {
        // Given
        List<String> nonExistentParamKeyList = Arrays.asList(
                "NON_EXISTENT.PARAM.KEY1",
                "NON_EXISTENT.PARAM.KEY2");
        List<SysParamValueDO> emptyDOList = Collections.emptyList();

        when(dao.queryByParamKeyList(nonExistentParamKeyList)).thenReturn(emptyDOList);

        // When
        List<SysParamValue> results = component.queryByParamKeyList(nonExistentParamKeyList);

        // Then
        Assert.assertNotNull(results);
        Assert.assertTrue(results.isEmpty());

        verify(dao, times(1)).queryByParamKeyList(nonExistentParamKeyList);
    }

    @Test
    public void testQueryByParamKeyList_SingleItem() {
        // Given
        List<String> singleParamKeyList = Arrays.asList(TEST_PARAM_KEY);
        List<SysParamValueDO> singleDOList = Arrays.asList(createTestSysParamValueDO());

        when(dao.queryByParamKeyList(singleParamKeyList)).thenReturn(singleDOList);

        // When
        List<SysParamValue> results = component.queryByParamKeyList(singleParamKeyList);

        // Then
        Assert.assertNotNull(results);
        Assert.assertEquals(1, results.size());

        SysParamValue result = results.get(0);
        Assert.assertEquals(TEST_ID, result.getId());
        Assert.assertEquals(TEST_PARAM_KEY, result.getParamKey());
        Assert.assertEquals(TEST_CORP_CODE, result.getCorpCode());

        verify(dao, times(1)).queryByParamKeyList(singleParamKeyList);
    }

    @Test
    public void testQueryByParamKeyAndTenantCorpCode_Success() {
        // Given
        String paramKey = TEST_PARAM_KEY;
        String tenantCorpCode = TEST_TENANT_CORP_CODE;

        SysParamValueDO mockDO = createTestSysParamValueDO();
        when(dao.queryByParamKeyAndTenantCorpCode(paramKey, tenantCorpCode)).thenReturn(mockDO);

        // When
        SysParamValue result = component.queryByParamKeyAndTenantCorpCode(paramKey, tenantCorpCode);

        // Then
        Assert.assertNotNull(result);
        Assert.assertEquals(TEST_ID, result.getId());
        Assert.assertEquals(TEST_CORP_CODE, result.getCorpCode());
        Assert.assertEquals(TEST_PARAM_KEY, result.getParamKey());
        Assert.assertEquals(TEST_PARAM_VALUE, result.getParamValue());
        Assert.assertEquals(TEST_TENANT_CORP_CODE, result.getTenantCorpCode());

        verify(dao, times(1)).queryByParamKeyAndTenantCorpCode(paramKey, tenantCorpCode);
    }

    @Test
    public void testQueryByParamKeyAndTenantCorpCode_NotFound() {
        // Given
        String nonExistentParamKey = "NON_EXISTENT.PARAM.KEY";
        String tenantCorpCode = TEST_TENANT_CORP_CODE;

        when(dao.queryByParamKeyAndTenantCorpCode(nonExistentParamKey, tenantCorpCode)).thenReturn(null);

        // When
        SysParamValue result = component.queryByParamKeyAndTenantCorpCode(nonExistentParamKey, tenantCorpCode);

        // Then
        Assert.assertNull(result);
        verify(dao, times(1)).queryByParamKeyAndTenantCorpCode(nonExistentParamKey, tenantCorpCode);
    }

    @Test
    public void testQueryByParamKeyAndTenantCorpCode_WrongTenantCorpCode() {
        // Given
        String paramKey = TEST_PARAM_KEY;
        String wrongTenantCorpCode = "WRONG_TENANT_CORP_CODE";

        when(dao.queryByParamKeyAndTenantCorpCode(paramKey, wrongTenantCorpCode)).thenReturn(null);

        // When
        SysParamValue result = component.queryByParamKeyAndTenantCorpCode(paramKey, wrongTenantCorpCode);

        // Then
        Assert.assertNull(result);
        verify(dao, times(1)).queryByParamKeyAndTenantCorpCode(paramKey, wrongTenantCorpCode);
    }

    @Test
    public void testQueryByParamKeyListWithCorpCode() {
        // Given
        String corpCode = TEST_CORP_CODE;
        List<String> paramKeyList = Arrays.asList(TEST_PARAM_KEY, "TEST_MODULE.TEST_COMPONENT.TEST_PARAM_2");
        List<SysParamValueDO> mockDOList = Arrays.asList(
                createTestSysParamValueDO(1L, TEST_PARAM_KEY),
                createTestSysParamValueDO(2L, "TEST_MODULE.TEST_COMPONENT.TEST_PARAM_2"));

        when(dao.queryByParamKeyList(corpCode, paramKeyList)).thenReturn(mockDOList);

        // When
        List<SysParamValue> result = component.queryByParamKeyList(corpCode, paramKeyList);

        // Then
        Assert.assertNotNull(result);
        Assert.assertEquals(2, result.size());
        Assert.assertEquals(TEST_PARAM_KEY, result.get(0).getParamKey());
        Assert.assertEquals("TEST_MODULE.TEST_COMPONENT.TEST_PARAM_2", result.get(1).getParamKey());

        verify(dao, times(1)).queryByParamKeyList(corpCode, paramKeyList);
    }

    @Test
    public void testQueryByParamKeyListWithCorpCode_EmptyList() {
        // Given
        String corpCode = TEST_CORP_CODE;
        List<String> emptyList = Collections.emptyList();

        when(dao.queryByParamKeyList(corpCode, emptyList)).thenReturn(Collections.emptyList());

        // When
        List<SysParamValue> result = component.queryByParamKeyList(corpCode, emptyList);

        // Then
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isEmpty());

        verify(dao, times(1)).queryByParamKeyList(corpCode, emptyList);
    }

    @Test
    public void testQueryByParamKeyListWithCorpCode_NullList() {
        // Given
        String corpCode = TEST_CORP_CODE;
        List<String> nullList = null;

        when(dao.queryByParamKeyList(corpCode, nullList)).thenReturn(Collections.emptyList());

        // When
        List<SysParamValue> result = component.queryByParamKeyList(corpCode, nullList);

        // Then
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isEmpty());

        verify(dao, times(1)).queryByParamKeyList(corpCode, nullList);
    }

    private SysParamValueDO createTestSysParamValueDO() {
        return createTestSysParamValueDO(TEST_ID, TEST_PARAM_KEY);
    }

    private SysParamValueDO createTestSysParamValueDO(Long id, String paramKey) {
        SysParamValueDO paramValueDO = new SysParamValueDO();
        paramValueDO.setId(id);
        paramValueDO.setCorpCode(TEST_CORP_CODE);
        paramValueDO.setModuleCode(TEST_MODULE_CODE);
        paramValueDO.setComponentCode(TEST_COMPONENT_CODE);
        paramValueDO.setGroupCode(TEST_GROUP_CODE);
        paramValueDO.setParamKey(paramKey);
        paramValueDO.setParamCode(TEST_PARAM_CODE);
        paramValueDO.setParamValue(TEST_PARAM_VALUE);
        paramValueDO.setTenantCorpCode(TEST_TENANT_CORP_CODE);
        paramValueDO.setStatus(1);
        paramValueDO.setCreateBy(TEST_CREATE_BY);
        paramValueDO.setModifyBy(TEST_MODIFY_BY);
        paramValueDO.setDeleted("F");
        return paramValueDO;
    }
}
