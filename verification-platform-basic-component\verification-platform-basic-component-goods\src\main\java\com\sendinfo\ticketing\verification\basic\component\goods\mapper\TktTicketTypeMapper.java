package com.sendinfo.ticketing.verification.basic.component.goods.mapper;

import com.sendinfo.ticketing.verification.basic.api.goods.request.TktTicketTypeQueryRequest;
import com.sendinfo.ticketing.verification.basic.component.goods.param.TktTicketTypeQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TktTicketType;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TktTicketTypeQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TktTicketTypeDO;
import org.mapstruct.factory.Mappers;

/**
 * 门票类型映射器
 *
 * <AUTHOR>
 */
public interface TktTicketTypeMapper {

    TktTicketTypeMapper INSTANCE = Mappers.getMapper(TktTicketTypeMapper.class);

    /**
     * convert TktTicketTypeDO to TktTicketType
     *
     * @param tktTicketTypeDO
     * @return
     */
    TktTicketType convert(TktTicketTypeDO tktTicketTypeDO);

    /**
     * convert TktTicketTypeQueryRequest to TktTicketTypeQueryParam
     *
     * @param condition
     * @return
     */
    TktTicketTypeQueryParam convert(TktTicketTypeQueryRequest condition);

    /**
     * convert TktTicketTypeQueryParam to TktTicketTypeQueryArg
     *
     * @param queryParam
     * @return
     */
    TktTicketTypeQueryArg convert(TktTicketTypeQueryParam queryParam);
} 