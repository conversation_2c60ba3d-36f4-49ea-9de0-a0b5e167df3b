package com.sendinfo.ticketing.verification.basic.api.goods;

import com.sendinfo.ticketing.verification.basic.api.goods.request.TktTicketModelQueryByCodeRequest;
import com.sendinfo.ticketing.verification.basic.api.goods.request.TktTicketModelBatchQueryByCodesRequest;
import com.sendinfo.ticketing.verification.basic.model.goods.TktTicketModel;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import java.util.List;

/**
 * @Auther: chengjie.li
 * @Date: 2025/6/10 20:40
 */
public interface TktTicketModelReadService {

    /**
     * 根据票型编码查询票型信息
     *
     * @param request 请求
     * @return 票型信息
     */
    ResultModel<TktTicketModel> queryTicketModelByCode(TktTicketModelQueryByCodeRequest request);

    /**
     * 根据票型编码查询票型信息
     *
     * @param request 请求
     * @return 票型信息列表
     */
    ResultModel<List<TktTicketModel>> batchQueryTicketModelByCodes(TktTicketModelBatchQueryByCodesRequest request);
}
