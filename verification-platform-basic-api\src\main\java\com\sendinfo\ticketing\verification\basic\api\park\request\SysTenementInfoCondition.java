package com.sendinfo.ticketing.verification.basic.api.park.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2025/6/8 16:22
 **/
@Getter
@Setter
@ToString(callSuper = true)
public class SysTenementInfoCondition implements Serializable {
    private static final long serialVersionUID = 3061322668805977175L;
    @NotNull(message = "corpCode不能为空")
    /** 租户标识，必填，确保租户数据隔离 */
    private String corpCode;
    /** 企业名称 */
    private String corpName;

    /** 企业联系人 */
    private String linkName;

    /** 联系电话 */
    private String linkPhone;

    /** 企业logo */
    private String logo;

    /** 企业介绍 */
    private String description;

    /** 版本号 */
    private String version;

    /** 数据源模式 0-共享数据源；1-独立数据源 */
    private String dataSourcesModel;

    /** 地址 */
    private String address;

    /** 租户状态 */
    private String useFlag;

    /** 供应商企业码 */
    private String sellCorpCode;

    /** TESB企业编码 */
    private String tesbCorpCode;

    /** 用户中心主体ID */
    private Integer subjectId;

    /** 用户中心主体编码 */
    private String subjectCode;

    /** 产品类型 1：产品。2应用 */
    private String productType;

    /** 来源 */
    private String orderSource;

    /** TESB appID */
    private String tesbAppId;

    /** TESB 秘钥 */
    private String tesbAppSecret;

    /** 地址代码 */
    private String addresCode;

    /** 是否使用离线售检退系统 */
    private String hasOffline;

    /** 是否开启预发环境 */
    private String advancePre;

    /** 支付中心编码 */
    private String corePayCode;

    /** 租户状态 */
    private String tenementStatus;

    /** 研发负责人 */
    private String programmer;

    /** 工程负责人 */
    private String maintainer;

    /** 加密标志 */
    private String encryptFlag;

    /** 在线标志 */
    private String onlineFlag;

    /** 审核状态 */
    private Integer auditStatus;

    /** 流量预估 */
    private String trafficEstimate;

    /** 工程负责人电话 */
    private String maintainerTel;

    /** 研发负责人电话 */
    private String programmerTel;

    /** 扩展信息 */
    private String extendInfo;

    /** 删除审核状态 */
    private Integer deleteAuditStatus;

    /** 是否集团 0租户 1集团 */
    private Integer isGroupCoType;

    /** 云餐饮租户编码----二消集团使用 */
    private String ycyTenementCode;

    /** 云pos租户编码----二消集团使用 */
    private String posTenementCode;
}
