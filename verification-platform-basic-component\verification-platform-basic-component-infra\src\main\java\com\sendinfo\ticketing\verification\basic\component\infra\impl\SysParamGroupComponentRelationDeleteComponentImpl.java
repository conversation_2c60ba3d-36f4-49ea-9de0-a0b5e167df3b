/**
 * System parameter group component relation delete component implementation
 *
 * <AUTHOR> 2025-01-24 15:30:00
 */
package com.sendinfo.ticketing.verification.basic.component.infra.impl;

import lombok.Getter;
import org.springframework.stereotype.Component;

import com.sendinfo.ticketing.verification.basic.component.infra.SysParamGroupComponentRelationDeleteComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.converter.SysParamGroupComponentRelationConverter;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamGroupComponentRelationDeleteParam;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamGroupComponentRelationDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.SysParamGroupComponentRelationDao;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleDelete;

/**
 * 参数分组组件关联删除组件实现类，负责删除参数分组组件关联的业务逻辑
 *
 * <AUTHOR> 2025-01-24 15:30:00
 */
@Component("sysParamGroupComponentRelationDeleteComponent")
@Getter
public class SysParamGroupComponentRelationDeleteComponentImpl
        implements SysParamGroupComponentRelationDeleteComponent,
        DaoBasedSingleDelete<Long, SysParamGroupComponentRelationDeleteParam, SysParamGroupComponentRelationDeleteArg> {

    private final SysParamGroupComponentRelationDao dao;
    private final SysParamGroupComponentRelationConverter converter;

    public SysParamGroupComponentRelationDeleteComponentImpl(
            SysParamGroupComponentRelationDao dao,
            SysParamGroupComponentRelationConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }
}