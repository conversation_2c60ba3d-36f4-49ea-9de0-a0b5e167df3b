package com.sendinfo.ticketing.verification.basic.component.goods.converter;

import com.sendinfo.ticketing.verification.basic.component.goods.mapper.TktSaleRuleMapper;
import com.sendinfo.ticketing.verification.basic.component.goods.param.TktSaleRuleQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TktSaleRule;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TktSaleRuleQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TktSaleRuleDO;
import com.sendinfo.ticketing.verification.common.component.converter.ReadDo2ModelConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadParam2ArgConverter;
import org.springframework.stereotype.Component;

@Component("tktSaleRuleConverter")
public class TktSaleRuleConverter implements
        ReadParam2ArgConverter<TktSaleRuleQueryParam, TktSaleRuleQueryArg>,
        ReadDo2ModelConverter<TktSaleRuleDO, TktSaleRule> {

    @Override
    public TktSaleRuleQueryArg r_p2a(TktSaleRuleQueryParam param) {
        return TktSaleRuleMapper.INSTANCE.convert(param);
    }


    @Override
    public TktSaleRule r_d2m(TktSaleRuleDO dataObject) {
        return TktSaleRuleMapper.INSTANCE.convert(dataObject);
    }
}