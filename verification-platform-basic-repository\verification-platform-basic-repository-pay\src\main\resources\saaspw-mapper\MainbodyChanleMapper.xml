<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sendinfo.ticketing.verification.basic.repository.pay.dao.MainbodyChanleDao">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.MainbodyChanleDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="CORP_CODE" property="corpCode" jdbcType="VARCHAR"/>
        <result column="PAY_CHANLE_ID" property="payChanleId" jdbcType="BIGINT"/>
        <result column="MAINBODY_ID" property="mainbodyId" jdbcType="BIGINT"/>
        <result column="MERCHANT_ID" property="merchantId" jdbcType="BIGINT"/>
        <result column="REMARK" property="remark" jdbcType="VARCHAR"/>
        <result column="CREATE_BY" property="createBy" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="MODIFY_BY" property="modifyBy" jdbcType="VARCHAR"/>
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="DELETED" property="deleted" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 表名 -->
    <sql id="tableName">mainbody_chanle</sql>

    <!-- 所有列 -->
    <sql id="allColumns">
        id, CORP_CODE, PAY_CHANLE_ID, MAINBODY_ID, MERCHANT_ID, REMARK, CREATE_BY, CREATE_TIME, MODIFY_BY, MODIFY_TIME, DELETED
    </sql>

    <!-- 插入列 -->
    <sql id="insertColumns">
        CORP_CODE, PAY_CHANLE_ID, MAINBODY_ID, MERCHANT_ID, REMARK, CREATE_BY, MODIFY_BY, DELETED, CREATE_TIME, MODIFY_TIME
    </sql>

    <!-- 插入操作 -->
    <insert id="insert" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.MainbodyChanleDO" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO <include refid="tableName"/> (<include refid="insertColumns"/>)
        VALUES (
            #{corpCode,jdbcType=VARCHAR}, #{payChanleId,jdbcType=BIGINT}, #{mainbodyId,jdbcType=BIGINT}, #{merchantId,jdbcType=BIGINT}, #{remark,jdbcType=VARCHAR},
            #{createBy,jdbcType=VARCHAR}, #{modifyBy,jdbcType=VARCHAR}, #{deleted,jdbcType=VARCHAR},
            NOW(), NOW()
        )
    </insert>

    <!-- 根据ID查询 -->
    <select id="queryById" resultMap="BaseResultMap" parameterType="map">
        SELECT <include refid="allColumns"/>
        FROM <include refid="tableName"/>
        WHERE id = #{id,jdbcType=BIGINT}
          AND deleted = 'F'
    </select>

    <!-- 根据参数更新 -->
    <update id="updateByArg" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.arg.MainbodyChanleUpdateArg">
        UPDATE <include refid="tableName"/>
        <set>
            <if test="payChanleId != null">PAY_CHANLE_ID = #{payChanleId,jdbcType=BIGINT},</if>
            <if test="mainbodyId != null">MAINBODY_ID = #{mainbodyId,jdbcType=BIGINT},</if>
            <if test="merchantId != null">MERCHANT_ID = #{merchantId,jdbcType=BIGINT},</if>
            <if test="remark != null and remark != ''">REMARK = #{remark,jdbcType=VARCHAR},</if>
            <if test="modifyBy != null and modifyBy != ''">MODIFY_BY = #{modifyBy,jdbcType=VARCHAR},</if>
            MODIFY_TIME = NOW()
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
          AND CORP_CODE = #{corpCode,jdbcType=VARCHAR}
          AND deleted = 'F'
    </update>

    <!-- 软删除 -->
    <update id="softDeleteByArg" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.arg.MainbodyChanleDeleteArg">
        UPDATE <include refid="tableName"/>
        SET deleted = 'T',
            <choose>
                <when test="modifyBy != null and modifyBy != ''">MODIFY_BY = #{modifyBy,jdbcType=VARCHAR},</when>
                <otherwise>MODIFY_BY = 'SYSTEM_SDA',</otherwise>
            </choose>
            MODIFY_TIME = NOW()
        WHERE CORP_CODE = #{corpCode,jdbcType=VARCHAR}
          AND deleted = 'F'
          <if test="id != null">AND id = #{id,jdbcType=BIGINT}</if>
    </update>

    <!-- 根据ID删除 -->
    <update id="deleteById" parameterType="java.lang.Long">
        UPDATE <include refid="tableName"/>
        SET deleted = 'T',
            MODIFY_BY = 'SYSTEM_DBI',
            MODIFY_TIME = NOW()
        WHERE id = #{id,jdbcType=BIGINT}
          AND deleted = 'F'
    </update>

    <!-- 计数查询 -->
    <select id="countByArg" resultType="int" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.arg.MainbodyChanleQueryArg">
        SELECT COUNT(*)
        FROM <include refid="tableName"/>
        <where>
            CORP_CODE = #{corpCode,jdbcType=VARCHAR}
            AND deleted = 'F'
            <if test="payChanleId != null">AND PAY_CHANLE_ID = #{payChanleId,jdbcType=BIGINT}</if>
            <if test="mainbodyId != null">AND MAINBODY_ID = #{mainbodyId,jdbcType=BIGINT}</if>
            <if test="merchantId != null">AND MERCHANT_ID = #{merchantId,jdbcType=BIGINT}</if>
        </where>
    </select>

    <!-- 条件查询 -->
    <select id="queryByArg" resultMap="BaseResultMap" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.arg.MainbodyChanleQueryArg">
        SELECT <include refid="allColumns"/>
        FROM <include refid="tableName"/>
        <where>
            CORP_CODE = #{corpCode,jdbcType=VARCHAR}
            AND deleted = 'F'
            <if test="payChanleId != null">AND PAY_CHANLE_ID = #{payChanleId,jdbcType=BIGINT}</if>
            <if test="mainbodyId != null">AND MAINBODY_ID = #{mainbodyId,jdbcType=BIGINT}</if>
            <if test="merchantId != null">AND MERCHANT_ID = #{merchantId,jdbcType=BIGINT}</if>
        </where>
        ORDER BY id DESC
        <if test="limit != null and offset != null">
            LIMIT #{offset,jdbcType=INTEGER}, #{limit,jdbcType=INTEGER}
        </if>
    </select>

    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO <include refid="tableName"/> (<include refid="insertColumns"/>)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.corpCode,jdbcType=VARCHAR}, #{item.payChanleId,jdbcType=BIGINT}, #{item.mainbodyId,jdbcType=BIGINT}, #{item.merchantId,jdbcType=BIGINT}, #{item.remark,jdbcType=VARCHAR},
                #{item.createBy,jdbcType=VARCHAR}, #{item.modifyBy,jdbcType=VARCHAR}, #{item.deleted,jdbcType=VARCHAR},
                NOW(), NOW()
            )
        </foreach>
    </insert>

</mapper> 