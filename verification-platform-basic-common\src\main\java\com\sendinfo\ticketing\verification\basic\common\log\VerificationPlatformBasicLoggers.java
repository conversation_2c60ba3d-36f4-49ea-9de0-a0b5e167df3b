package com.sendinfo.ticketing.verification.basic.common.log;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class VerificationPlatformBasicLoggers {

    private VerificationPlatformBasicLoggers() {
    }

    public static final Logger MEDIA_CONVERTER_RULE_LOGGER = LoggerFactory.getLogger("MEDIA_CONVERTER_RULE_LOGGER");
    public static final Logger API_SERVICE_LOGGER = LoggerFactory.getLogger("API_SERVICE_LOGGER");
    public static final Logger API_SERVICE_HTTP_CALL_SUMMARY_LOGGER = LoggerFactory.getLogger("API_SERVICE_HTTP_CALL_SUMMARY_LOGGER");
    public static final Logger APPLICATION_LOGGER = LoggerFactory.getLogger("APPLICATION_LOGGER");
}
