package com.sendinfo.ticketing.verification.basic.component.infra.converter;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamNamespaceCreateRequest;
import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamNamespaceDeleteRequest;
import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamNamespaceQueryCondition;
import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamNamespaceUpdateRequest;
import com.sendinfo.ticketing.verification.basic.component.infra.mapper.SysParamNamespaceMapper;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamNamespaceCreateParam;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamNamespaceDeleteParam;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamNamespaceQueryParam;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamNamespaceUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.infra.SysParamNamespace;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamNamespaceDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamNamespaceQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamNamespaceUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.SysParamNamespaceDO;
import com.sendinfo.ticketing.verification.common.api.request.PageRequest;
import com.sendinfo.ticketing.verification.common.component.converter.CreateParam2DoConverter;
import com.sendinfo.ticketing.verification.common.component.converter.CreateReq2ParamConverter;
import com.sendinfo.ticketing.verification.common.component.converter.DeleteParam2ArgConverter;
import com.sendinfo.ticketing.verification.common.component.converter.DeleteReq2ParamConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadDo2ModelConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadPageReq2ParamConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadParam2ArgConverter;
import com.sendinfo.ticketing.verification.common.component.converter.UpdateParam2ArgConverter;
import com.sendinfo.ticketing.verification.common.component.converter.UpdateReq2ParamConverter;
import com.sendinfo.ticketing.verification.common.repository.arg.StatusUpdater;

/**
 * 参数命名空间转换器，负责不同层级对象间的转换，包括请求对象、参数对象、数据对象和模型对象
 *
 * <AUTHOR> 2025-07-21 15:30:00
 */
@Component("sysParamNamespaceConverter")
public class SysParamNamespaceConverter
        implements CreateParam2DoConverter<SysParamNamespaceCreateParam, SysParamNamespaceDO>,
        ReadParam2ArgConverter<SysParamNamespaceQueryParam, SysParamNamespaceQueryArg>,
        ReadDo2ModelConverter<SysParamNamespaceDO, SysParamNamespace>,
        UpdateParam2ArgConverter<SysParamNamespaceUpdateParam, SysParamNamespaceUpdateArg, SysParamNamespace>,
        DeleteParam2ArgConverter<SysParamNamespaceDeleteParam, SysParamNamespaceDeleteArg>,
        DeleteReq2ParamConverter<SysParamNamespaceDeleteRequest, SysParamNamespaceDeleteParam>,
        CreateReq2ParamConverter<SysParamNamespaceCreateRequest, SysParamNamespaceCreateParam>,
        UpdateReq2ParamConverter<SysParamNamespaceUpdateRequest, SysParamNamespaceUpdateParam>,
        ReadPageReq2ParamConverter<SysParamNamespaceQueryCondition, SysParamNamespaceQueryParam> {

    @Override
    public SysParamNamespaceDO c_p2d(SysParamNamespaceCreateParam createParam) {
        return SysParamNamespaceMapper.INSTANCE.convert(createParam);
    }

    @Override
    public List<SysParamNamespaceDO> c_ps2ds(List<SysParamNamespaceCreateParam> params) {
        return params.stream()
                .map(SysParamNamespaceMapper.INSTANCE::convert)
                .collect(Collectors.toList());
    }

    @Override
    public SysParamNamespaceUpdateArg u_p2a(SysParamNamespaceUpdateParam param, SysParamNamespace currentModel) {
        SysParamNamespaceUpdateArg updateArg = SysParamNamespaceMapper.INSTANCE.convert(param);
        Optional.ofNullable(param.getTargetStatus())
                .ifPresent(innerStatus -> updateArg.setStatusUpdater(StatusUpdater.transfer(currentModel.getStatus().getCode(),
                        innerStatus.getCode())));

        return updateArg;
    }

    @Override
    public SysParamNamespaceQueryArg r_p2a(SysParamNamespaceQueryParam param) {
        return SysParamNamespaceMapper.INSTANCE.convert(param);
    }

    @Override
    public SysParamNamespaceDeleteArg d_p2a(SysParamNamespaceDeleteParam param) {
        return SysParamNamespaceMapper.INSTANCE.convert(param);
    }

    @Override
    public SysParamNamespace r_d2m(SysParamNamespaceDO dataObject) {
        return SysParamNamespaceMapper.INSTANCE.convert(dataObject);
    }

    @Override
    public List<SysParamNamespace> r_ds2ms(List<SysParamNamespaceDO> dataObjects) {
        return dataObjects.stream()
                .map(this::r_d2m)
                .collect(Collectors.toList());
    }

    @Override
    public SysParamNamespaceCreateParam c_r2p(SysParamNamespaceCreateRequest req) {
        return SysParamNamespaceMapper.INSTANCE.convert(req);
    }

    @Override
    public SysParamNamespaceUpdateParam u_r2p(SysParamNamespaceUpdateRequest req) {
        return SysParamNamespaceMapper.INSTANCE.convert(req);
    }

    @Override
    public SysParamNamespaceQueryParam r_pr2p(PageRequest<SysParamNamespaceQueryCondition> pageReq) {
        SysParamNamespaceQueryParam queryParam = new SysParamNamespaceQueryParam();
        SysParamNamespaceQueryCondition condition = pageReq.getCondition();
        if (condition != null) {
            queryParam = SysParamNamespaceMapper.INSTANCE.convert(condition);
        }
        queryParam.setStartIndex(pageReq.getStartIndex());
        queryParam.setPageSize(pageReq.getPageSize());
        return queryParam;
    }

    @Override
    public SysParamNamespaceDeleteParam d_r2p(SysParamNamespaceDeleteRequest req) {
        return SysParamNamespaceMapper.INSTANCE.convert(req);
    }
}