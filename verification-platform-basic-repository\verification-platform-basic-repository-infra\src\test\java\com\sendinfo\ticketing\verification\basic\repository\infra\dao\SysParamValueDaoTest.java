package com.sendinfo.ticketing.verification.basic.repository.infra.dao;

import com.sendinfo.ticketing.verification.basic.repository.common.config.TicketInfraMybatisConfig;
import com.sendinfo.ticketing.verification.basic.repository.common.config.VerificationPlatformMybatisConfig;
import com.sendinfo.ticketing.verification.basic.repository.infra.TestMybatisConfig;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamValueDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamValueQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamValueUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.impl.SysParamValueDaoImpl;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.SysParamValueDO;
import com.sendinfo.ticketing.verification.common.repository.arg.StatusUpdater;
import org.apache.ibatis.session.SqlSessionFactory;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.AbstractTransactionalJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringRunner;

import javax.sql.DataSource;
import java.util.Arrays;
import java.util.List;

/**
 * 系统参数值DAO测试类
 *
 * <AUTHOR> 2025-05-20 15:40:00
 */
@SpringBootTest(classes = SysParamValueDaoTest.Config.class)
@EnableAutoConfiguration
@RunWith(SpringRunner.class)
@TestPropertySource(locations = "classpath:test-application.properties")
public class SysParamValueDaoTest extends AbstractTransactionalJUnit4SpringContextTests {

    @Autowired
    private SysParamValueDao sysParamValueDao;

    private static final String TEST_CORP_CODE = "TEST_CORP";
    private static final String TEST_PARAM_CODE = "TEST_PARAM_CODE";

    @Configuration
    @Import({ TicketInfraMybatisConfig.class })
    static class Config {

        @Bean
        SysParamValueDao sysParamValueDao(SqlSessionTemplate sqlSessionTemplate) {
            return new SysParamValueDaoImpl(sqlSessionTemplate);
        }
    }

    @Before
    public void setUp() {
        // 初始化测试数据
        for (int i = 1; i <= 10; i++) {
            SysParamValueDO param = createTestParam(i);
            sysParamValueDao.insert(param);
        }
    }

    @Test
    public void testInsert() {
        // Given
        SysParamValueDO param = createTestParam(100);

        // When
        sysParamValueDao.insert(param);

        // Then
        SysParamValueDO found = sysParamValueDao.queryById(param.getId());
        Assert.assertNotNull(found);
        Assert.assertEquals(param.getParamCode(), found.getParamCode());
        Assert.assertEquals(param.getParamValue(), found.getParamValue());
    }

    @Test
    public void testQueryById() {
        // When
        SysParamValueDO found = sysParamValueDao.queryById(1L);

        // Then
        Assert.assertNotNull(found);
        Assert.assertEquals(1L, found.getId().longValue());
    }

    @Test
    public void testQueryByParamCodeAndCorpCode() {
        // Given
        String paramCode = TEST_PARAM_CODE + "_1";
        String corpCode = TEST_CORP_CODE;

        // When
        SysParamValueDO found = sysParamValueDao.queryByParamCodeAndCorpCode(paramCode, corpCode);

        // Then
        Assert.assertNotNull(found);
        Assert.assertEquals(paramCode, found.getParamCode());
        Assert.assertEquals(corpCode, found.getCorpCode());
    }

    @Test
    public void testUpdateByArg() {
        // Given
        Long id = 1L;
        String newValue = "Updated Value";

        SysParamValueUpdateArg updateArg = new SysParamValueUpdateArg();
        updateArg.setId(id);
        updateArg.setParamValue(newValue);

        StatusUpdater<Integer> statusUpdater = new StatusUpdater<>(1, 0);
        updateArg.setStatusUpdater(statusUpdater);

        // When
        sysParamValueDao.updateByArg(updateArg);

        // Then
        SysParamValueDO found = sysParamValueDao.queryById(id);
        Assert.assertEquals(newValue, found.getParamValue());
        Assert.assertEquals(0, found.getStatus().intValue());
    }

    @Test
    public void testSoftDeleteByArg() {
        // Given
        Long id = 1L;
        SysParamValueDeleteArg deleteArg = new SysParamValueDeleteArg();
        deleteArg.setId(id);
        deleteArg.setModifyBy("test_user");

        // When
        sysParamValueDao.softDeleteByArg(deleteArg);

        // Then
        SysParamValueDO found = sysParamValueDao.queryById(id);
        Assert.assertNull(found);
    }

    @Test
    public void testCountByArg() {
        // Given
        SysParamValueQueryArg queryArg = new SysParamValueQueryArg();
        queryArg.setCorpCode(TEST_CORP_CODE);

        // When
        int count = sysParamValueDao.countByArg(queryArg);

        // Then
        Assert.assertEquals(10, count);
    }

    @Test
    public void testQueryByArg() {
        // Given
        SysParamValueQueryArg queryArg = new SysParamValueQueryArg();
        queryArg.setCorpCode(TEST_CORP_CODE);
        queryArg.setStatus(1);
        queryArg.setOffset(1);
        queryArg.setLimit(5);
        // When
        List<SysParamValueDO> results = sysParamValueDao.queryByArg(queryArg);

        // Then
        Assert.assertFalse(results.isEmpty());
        for (SysParamValueDO param : results) {
            Assert.assertEquals(1, param.getStatus().intValue());
        }
    }

    @Test
    public void testBatchInsert() {
        // Given
        List<SysParamValueDO> params = Arrays.asList(
                createTestParam(101),
                createTestParam(102));

        // When
        sysParamValueDao.batchInsert(params);

        // Then
        SysParamValueQueryArg queryArg = new SysParamValueQueryArg();
        queryArg.setCorpCode(TEST_CORP_CODE);
        int count = sysParamValueDao.countByArg(queryArg);
        Assert.assertEquals(12, count); // 10 from setUp + 2 new
    }

    @Test
    public void testQueryByTenantCorpCode() {
        // Given
        String tenantCorpCode = "0001";
        SysParamValueQueryArg queryArg = new SysParamValueQueryArg();
        queryArg.setTenantCorpCode(tenantCorpCode);
        queryArg.setOffset(0);
        queryArg.setLimit(10);
        // When
        List<SysParamValueDO> results = sysParamValueDao.queryByArg(queryArg);

        // Then
        Assert.assertFalse(results.isEmpty());
        for (SysParamValueDO param : results) {
            Assert.assertEquals(tenantCorpCode, param.getTenantCorpCode());
        }
    }

    @Test
    public void testQueryByParamKeyList() {
        // Given
        List<String> paramKeyList = Arrays.asList(
                "TEST_MODULE_1.TEST_COMPONENT_1." + TEST_PARAM_CODE + "_1",
                "TEST_MODULE_2.TEST_COMPONENT_2." + TEST_PARAM_CODE + "_2",
                "TEST_MODULE_3.TEST_COMPONENT_3." + TEST_PARAM_CODE + "_3");

        // When
        List<SysParamValueDO> results = sysParamValueDao.queryByParamKeyList(paramKeyList);

        // Then
        Assert.assertNotNull(results);
        Assert.assertEquals(3, results.size());

        // 验证返回的结果包含预期的paramKey
        for (SysParamValueDO result : results) {
            Assert.assertTrue(paramKeyList.contains(result.getParamKey()));
        }
    }

    @Test
    public void testQueryByParamKeyListWithCorpCode() {
        // Given
        String corpCode = TEST_CORP_CODE;
        List<String> paramKeyList = Arrays.asList(
                "TEST_MODULE_1.TEST_COMPONENT_1." + TEST_PARAM_CODE + "_1",
                "TEST_MODULE_2.TEST_COMPONENT_2." + TEST_PARAM_CODE + "_2",
                "TEST_MODULE_3.TEST_COMPONENT_3." + TEST_PARAM_CODE + "_3");

        // When
        List<SysParamValueDO> results = sysParamValueDao.queryByParamKeyList(corpCode, paramKeyList);

        // Then
        Assert.assertNotNull(results);
        Assert.assertEquals(3, results.size());

        // 验证返回的结果包含预期的paramKey和corpCode
        for (SysParamValueDO result : results) {
            Assert.assertTrue(paramKeyList.contains(result.getParamKey()));
            Assert.assertEquals(corpCode, result.getCorpCode());
        }
    }

    @Test
    public void testQueryByParamKeyListWithCorpCode_EmptyList() {
        // Given
        String corpCode = TEST_CORP_CODE;
        List<String> emptyList = Arrays.asList();

        // When
        List<SysParamValueDO> results = sysParamValueDao.queryByParamKeyList(corpCode, emptyList);

        // Then
        Assert.assertNotNull(results);
        Assert.assertTrue("Should return empty list for empty input", results.isEmpty());
    }

    @Test
    public void testQueryByParamKeyListWithCorpCode_NonExistentKeys() {
        // Given
        String corpCode = TEST_CORP_CODE;
        List<String> nonExistentList = Arrays.asList("NON_EXISTENT.KEY.1", "NON_EXISTENT.KEY.2");

        // When
        List<SysParamValueDO> results = sysParamValueDao.queryByParamKeyList(corpCode, nonExistentList);

        // Then
        Assert.assertNotNull(results);
        Assert.assertTrue("Should return empty list for non-existent keys", results.isEmpty());
    }

    @Test
    public void testQueryByParamKeyListWithCorpCode_WrongCorpCode() {
        // Given
        String wrongCorpCode = "WRONG_CORP_CODE";
        List<String> paramKeyList = Arrays.asList(
                "TEST_MODULE_1.TEST_COMPONENT_1." + TEST_PARAM_CODE + "_1",
                "TEST_MODULE_2.TEST_COMPONENT_2." + TEST_PARAM_CODE + "_2");

        // When
        List<SysParamValueDO> results = sysParamValueDao.queryByParamKeyList(wrongCorpCode, paramKeyList);

        // Then
        Assert.assertNotNull(results);
        Assert.assertTrue("Should return empty list for wrong corp code", results.isEmpty());
    }

    @Test
    public void testQueryByParamKeyListEmpty() {
        // Given
        List<String> emptyParamKeyList = Arrays.asList();

        // When
        List<SysParamValueDO> results = sysParamValueDao.queryByParamKeyList(emptyParamKeyList);

        // Then
        Assert.assertNotNull(results);
        Assert.assertTrue(results.isEmpty());
    }

    @Test
    public void testQueryByParamKeyListNotFound() {
        // Given
        List<String> nonExistentParamKeyList = Arrays.asList(
                "NON_EXISTENT.PARAM.KEY1",
                "NON_EXISTENT.PARAM.KEY2");

        // When
        List<SysParamValueDO> results = sysParamValueDao.queryByParamKeyList(nonExistentParamKeyList);

        // Then
        Assert.assertNotNull(results);
        Assert.assertTrue(results.isEmpty());
    }

    @Test
    public void testQueryByParamKeyAndTenantCorpCode() {
        // Given
        String paramKey = "TEST_MODULE_1.TEST_COMPONENT_1." + TEST_PARAM_CODE + "_1";
        String tenantCorpCode = "0001";

        // When
        SysParamValueDO result = sysParamValueDao.queryByParamKeyAndTenantCorpCode(paramKey, tenantCorpCode);

        // Then
        Assert.assertNotNull(result);
        Assert.assertEquals(paramKey, result.getParamKey());
        Assert.assertEquals(tenantCorpCode, result.getTenantCorpCode());
        Assert.assertEquals(TEST_PARAM_CODE + "_1", result.getParamCode());
    }

    @Test
    public void testQueryByParamKeyAndTenantCorpCodeNotFound() {
        // Given
        String nonExistentParamKey = "NON_EXISTENT.PARAM.KEY";
        String tenantCorpCode = "0001";

        // When
        SysParamValueDO result = sysParamValueDao.queryByParamKeyAndTenantCorpCode(nonExistentParamKey, tenantCorpCode);

        // Then
        Assert.assertNull(result);
    }

    @Test
    public void testQueryByParamKeyAndTenantCorpCodeWithWrongTenantCorpCode() {
        // Given
        String paramKey = "TEST_MODULE_1.TEST_COMPONENT_1." + TEST_PARAM_CODE + "_1";
        String wrongTenantCorpCode = "WRONG_TENANT_CORP_CODE";

        // When
        SysParamValueDO result = sysParamValueDao.queryByParamKeyAndTenantCorpCode(paramKey, wrongTenantCorpCode);

        // Then
        Assert.assertNull(result);
    }

    private SysParamValueDO createTestParam(long id) {
        SysParamValueDO param = new SysParamValueDO();
        param.setId(id);
        param.setCorpCode(TEST_CORP_CODE);
        param.setModuleCode("TEST_MODULE_" + id);
        param.setComponentCode("TEST_COMPONENT_" + id);
        param.setGroupCode("TEST_GROUP_" + id);
        param.setParamKey("TEST_MODULE_" + id + ".TEST_COMPONENT_" + id + "." + TEST_PARAM_CODE + "_" + id);
        param.setParamCode(TEST_PARAM_CODE + "_" + id);
        param.setParamValue("Test Value " + id);
        param.setTenantCorpCode("0001"); // 设置租户企业编码默认值
        param.setStatus(1);
        param.setCreateBy("test_user");
        param.setModifyBy("test_user");
        param.setDeleted("F");
        return param;
    }
}