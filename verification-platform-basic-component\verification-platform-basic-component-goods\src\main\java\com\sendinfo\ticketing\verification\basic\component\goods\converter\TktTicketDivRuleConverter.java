package com.sendinfo.ticketing.verification.basic.component.goods.converter;

import com.sendinfo.ticketing.verification.basic.component.goods.mapper.TktTicketDivRuleMapper;
import com.sendinfo.ticketing.verification.basic.component.goods.param.TktTicketDivRuleQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TktTicketDivRule;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TktTicketDivRuleQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TktTicketDivRuleDO;
import com.sendinfo.ticketing.verification.common.component.converter.ReadDo2ModelConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadParam2ArgConverter;
import org.springframework.stereotype.Component;

/**
 * @Auther: chengjie.li
 * @Date: 2025/6/12 11:56
 */
@Component("tktTicketDivRuleConverter")
public class TktTicketDivRuleConverter implements
        ReadParam2ArgConverter<TktTicketDivRuleQueryParam, TktTicketDivRuleQueryArg>,
        ReadDo2ModelConverter<TktTicketDivRuleDO, TktTicketDivRule> {
    @Override
    public TktTicketDivRule r_d2m(TktTicketDivRuleDO dataObject) {
        return TktTicketDivRuleMapper.INSTANCE.convert(dataObject);
    }

    @Override
    public TktTicketDivRuleQueryArg r_p2a(TktTicketDivRuleQueryParam param) {
        return TktTicketDivRuleMapper.INSTANCE.convert(param);
    }
}
