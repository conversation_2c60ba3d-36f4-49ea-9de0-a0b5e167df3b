<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sendinfo.ticketing.verification.basic.repository.infra.dao.SysParamValueDao">

    <!-- ResultMap -->
    <resultMap id="BaseResultMap" type="com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.SysParamValueDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="corp_code" property="corpCode" jdbcType="VARCHAR"/>
        <result column="module_code" property="moduleCode" jdbcType="VARCHAR"/>
        <result column="component_code" property="componentCode" jdbcType="VARCHAR"/>
        <result column="group_code" property="groupCode" jdbcType="VARCHAR"/>
        <result column="param_key" property="paramKey" jdbcType="VARCHAR"/>
        <result column="param_code" property="paramCode" jdbcType="VARCHAR"/>
        <result column="param_value" property="paramValue" jdbcType="VARCHAR"/>
        <result column="tenant_corp_code" property="tenantCorpCode" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="modify_by" property="modifyBy" jdbcType="VARCHAR"/>
        <result column="deleted" property="deleted" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- Reusable Column List -->
    <sql id="columns">
        `id`,
        `corp_code`,
        `module_code`,
        `component_code`,
        `group_code`,
        `param_key`,
        `param_code`,
        `param_value`,
        `tenant_corp_code`,
        `status`,
        `create_time`,
        `modify_time`,
        `create_by`,
        `modify_by`,
        `deleted`
    </sql>

    <!-- insert -->
    <insert id="insert" parameterType="com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.SysParamValueDO">
        insert into `infra_sys_param_value` (
            `id`,
            `corp_code`,
            `module_code`,
            `component_code`,
            `group_code`,
            `param_key`,
            `param_code`,
            `param_value`,
            `tenant_corp_code`,
            `status`,
            `create_by`,
            `modify_by`,
            `deleted`
        )
        values (
            #{id,jdbcType=BIGINT},
            #{corpCode,jdbcType=VARCHAR},
            #{moduleCode,jdbcType=VARCHAR},
            #{componentCode,jdbcType=VARCHAR},
            #{groupCode,jdbcType=VARCHAR},
            #{paramKey,jdbcType=VARCHAR},
            #{paramCode,jdbcType=VARCHAR},
            #{paramValue,jdbcType=VARCHAR},
            #{tenantCorpCode,jdbcType=VARCHAR},
            #{status,jdbcType=INTEGER},
            #{createBy,jdbcType=VARCHAR},
            #{modifyBy,jdbcType=VARCHAR},
            #{deleted,jdbcType=VARCHAR}
        )
    </insert>

    <!-- queryById -->
    <select id="queryById" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="columns"/>
        from `infra_sys_param_value`
        where `id` = #{id,jdbcType=BIGINT} and `deleted` = 'F'
    </select>

    <!-- queryByParamCodeAndCorpCode -->
    <select id="queryByParamCodeAndCorpCode" resultMap="BaseResultMap">
        select
        <include refid="columns"/>
        from `infra_sys_param_value`
        where `param_code` = #{paramCode,jdbcType=VARCHAR}
        and `corp_code` = #{corpCode,jdbcType=VARCHAR}
        and `deleted` = 'F'
    </select>

    <!-- queryByParamKeyList -->
    <select id="queryByParamKeyList" resultMap="BaseResultMap">
        select
        <include refid="columns"/>
        from `infra_sys_param_value`
        where `param_key` in
        <foreach collection="paramKeyList" item="paramKey" open="(" separator="," close=")">
            #{paramKey,jdbcType=VARCHAR}
        </foreach>
        and `deleted` = 'F'
    </select>

    <!-- queryByParamKeyListWithCorpCode -->
    <select id="queryByParamKeyListWithCorpCode" resultMap="BaseResultMap">
        select
        <include refid="columns"/>
        from `infra_sys_param_value`
        where `param_key` in
        <foreach collection="paramKeyList" item="paramKey" open="(" separator="," close=")">
            #{paramKey,jdbcType=VARCHAR}
        </foreach>
        and `corp_code` = #{corpCode,jdbcType=VARCHAR}
        and `deleted` = 'F'
    </select>

    <!-- queryByParamKeyAndTenantCorpCode -->
    <select id="queryByParamKeyAndTenantCorpCode" resultMap="BaseResultMap">
        select
        <include refid="columns"/>
        from `infra_sys_param_value`
        where `param_key` = #{paramKey,jdbcType=VARCHAR}
        and `tenant_corp_code` = #{tenantCorpCode,jdbcType=VARCHAR}
        and `deleted` = 'F'
    </select>

    <!-- updateByArg -->
    <update id="updateByArg" parameterType="com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamValueUpdateArg">
        update `infra_sys_param_value`
        <set>
            <if test="paramValue != null">
                `param_value` = #{paramValue,jdbcType=VARCHAR},
            </if>
            <if test="statusUpdater != null and statusUpdater.target != null">
                `status` = #{statusUpdater.target,jdbcType=INTEGER},
            </if>
            <if test="modifyBy != null">
                `modify_by` = #{modifyBy,jdbcType=VARCHAR},
            </if>
            `modify_time` = CURRENT_TIMESTAMP,
        </set>
        where `id` = #{id,jdbcType=BIGINT}
        <if test="statusUpdater != null and statusUpdater.current != null">
            and `status` = #{statusUpdater.current,jdbcType=INTEGER}
        </if>
        and `deleted` = 'F'
    </update>

    <!-- softDeleteByArg -->
    <update id="softDeleteByArg" parameterType="com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamValueDeleteArg">
        update `infra_sys_param_value`
        set `deleted` = 'T',
            `modify_by` = #{modifyBy,jdbcType=VARCHAR},
            `modify_time` = now()
        where `id` = #{id,jdbcType=BIGINT}
    </update>

    <!-- countByArg -->
    <select id="countByArg" resultType="java.lang.Integer" parameterType="com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamValueQueryArg">
        select COUNT(*)
        from `infra_sys_param_value`
        <where>
            <if test="corpCode != null and corpCode != ''">
                and `corp_code` = #{corpCode,jdbcType=VARCHAR}
            </if>
            <if test="paramCode != null and paramCode != ''">
                and `param_code` = #{paramCode,jdbcType=VARCHAR}
            </if>
            <if test="tenantCorpCode != null and tenantCorpCode != ''">
                and `tenant_corp_code` = #{tenantCorpCode,jdbcType=VARCHAR}
            </if>
            <if test="status != null">
                and `status` = #{status,jdbcType=INTEGER}
            </if>
            and `deleted` = 'F'
        </where>
    </select>

    <!-- queryByArg -->
    <select id="queryByArg" resultMap="BaseResultMap" parameterType="com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamValueQueryArg">
        select
        <include refid="columns"/>
        from `infra_sys_param_value`
        <where>
            <if test="corpCode != null and corpCode != ''">
                and `corp_code` = #{corpCode,jdbcType=VARCHAR}
            </if>
            <if test="moduleCode != null and moduleCode != ''">
                and `module_code` = #{moduleCode,jdbcType=VARCHAR}
            </if>
            <if test="componentCode != null and componentCode != ''">
                and `component_code` = #{componentCode,jdbcType=VARCHAR}
            </if>
            <if test="groupCode != null and groupCode != ''">
                and `group_code` = #{groupCode,jdbcType=VARCHAR}
            </if>
            <if test="paramKey != null and paramKey != ''">
                and `param_key` = #{paramKey,jdbcType=VARCHAR}
            </if>
            <if test="paramCode != null and paramCode != ''">
                and `param_code` = #{paramCode,jdbcType=VARCHAR}
            </if>
            <if test="tenantCorpCode != null and tenantCorpCode != ''">
                and `tenant_corp_code` = #{tenantCorpCode,jdbcType=VARCHAR}
            </if>
            <if test="status != null">
                and `status` = #{status,jdbcType=INTEGER}
            </if>
            and `deleted` = 'F'
        </where>
        <if test="sortItems != null and !sortItems.isEmpty()">
            order by
            <foreach collection="sortItems" separator="," item="sortItem">
                ${sortItem.column} ${sortItem.sortType}
            </foreach>
        </if>
        <if test="offset != null and limit != null">
            limit #{offset}, #{limit}
        </if>
    </select>

    <!-- batchInsert -->
    <insert id="batchInsert" parameterType="java.util.List">
        insert into `infra_sys_param_value` (
            `id`,
            `corp_code`,
            `module_code`,
            `component_code`,
            `param_code`,
            `param_key`,
            `param_value`,
            `tenant_corp_code`,
            `status`,
            `create_by`,
            `modify_by`,
            `deleted`
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id,jdbcType=BIGINT},
                #{item.corpCode,jdbcType=VARCHAR},
                #{item.moduleCode,jdbcType=VARCHAR},
                #{item.componentCode,jdbcType=VARCHAR},
                #{item.paramCode,jdbcType=VARCHAR},
                #{item.paramKey,jdbcType=VARCHAR},
                #{item.paramValue,jdbcType=VARCHAR},
                #{item.tenantCorpCode,jdbcType=VARCHAR},
                #{item.status,jdbcType=INTEGER},
                #{item.createBy,jdbcType=VARCHAR},
                #{item.modifyBy,jdbcType=VARCHAR},
                #{item.deleted,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
</mapper> 