package com.sendinfo.ticketing.verification.basic.component.infra.converter;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamGroupCreateRequest;
import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamGroupDeleteRequest;
import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamGroupQueryCondition;
import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamGroupUpdateRequest;
import com.sendinfo.ticketing.verification.basic.common.constant.CodePrefixConstants;
import com.sendinfo.ticketing.verification.basic.common.util.CodeGeneratorUtil;
import com.sendinfo.ticketing.verification.basic.component.infra.mapper.SysParamGroupMapper;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamGroupCreateParam;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamGroupDeleteParam;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamGroupQueryParam;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamGroupUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.infra.SysParamGroup;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamGroupDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamGroupQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamGroupUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.SysParamGroupDO;
import com.sendinfo.ticketing.verification.common.api.request.PageRequest;
import com.sendinfo.ticketing.verification.common.component.converter.CreateParam2DoConverter;
import com.sendinfo.ticketing.verification.common.component.converter.CreateReq2ParamConverter;
import com.sendinfo.ticketing.verification.common.component.converter.DeleteParam2ArgConverter;
import com.sendinfo.ticketing.verification.common.component.converter.DeleteReq2ParamConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadDo2ModelConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadPageReq2ParamConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadParam2ArgConverter;
import com.sendinfo.ticketing.verification.common.component.converter.UpdateParam2ArgConverter;
import com.sendinfo.ticketing.verification.common.component.converter.UpdateReq2ParamConverter;

/**
 * 系统参数分组转换器
 * 
 * <AUTHOR> 2025-07-22 11:45:32
 */
@Component("sysParamGroupConverter")
public class SysParamGroupConverter
        implements CreateParam2DoConverter<SysParamGroupCreateParam, SysParamGroupDO>,
        ReadParam2ArgConverter<SysParamGroupQueryParam, SysParamGroupQueryArg>,
        ReadDo2ModelConverter<SysParamGroupDO, SysParamGroup>,
        UpdateParam2ArgConverter<SysParamGroupUpdateParam, SysParamGroupUpdateArg, SysParamGroup>,
        DeleteParam2ArgConverter<SysParamGroupDeleteParam, SysParamGroupDeleteArg>,
        DeleteReq2ParamConverter<SysParamGroupDeleteRequest, SysParamGroupDeleteParam>,
        CreateReq2ParamConverter<SysParamGroupCreateRequest, SysParamGroupCreateParam>,
        UpdateReq2ParamConverter<SysParamGroupUpdateRequest, SysParamGroupUpdateParam>,
        ReadPageReq2ParamConverter<SysParamGroupQueryCondition, SysParamGroupQueryParam> {

    @Override
    public SysParamGroupDO c_p2d(SysParamGroupCreateParam createParam) {
        createParam.setGroupCode(CodeGeneratorUtil.generateCode(CodePrefixConstants.SYS_PARAM_GROUP_CODE_PREFIX));
        return SysParamGroupMapper.INSTANCE.convert(createParam);
    }

    @Override
    public List<SysParamGroupDO> c_ps2ds(List<SysParamGroupCreateParam> params) {
        return params.stream()
                .map(SysParamGroupMapper.INSTANCE::convert)
                .collect(Collectors.toList());
    }

    @Override
    public SysParamGroupUpdateArg u_p2a(SysParamGroupUpdateParam param, SysParamGroup currentModel) {
        SysParamGroupUpdateArg updateArg = SysParamGroupMapper.INSTANCE.convert(param);
        return updateArg;
    }

    @Override
    public SysParamGroupQueryArg r_p2a(SysParamGroupQueryParam param) {
        SysParamGroupQueryArg queryArg = SysParamGroupMapper.INSTANCE.convert(param);
        queryArg.setOffset(param.getStartIndex());
        queryArg.setLimit(param.getPageSize());
        return SysParamGroupMapper.INSTANCE.convert(param);
    }

    @Override
    public SysParamGroupDeleteArg d_p2a(SysParamGroupDeleteParam param) {
        return SysParamGroupMapper.INSTANCE.convert(param);
    }

    @Override
    public SysParamGroup r_d2m(SysParamGroupDO dataObject) {
        return SysParamGroupMapper.INSTANCE.convert(dataObject);
    }

    @Override
    public List<SysParamGroup> r_ds2ms(List<SysParamGroupDO> dataObjects) {
        return dataObjects.stream()
                .map(this::r_d2m)
                .collect(Collectors.toList());
    }

    @Override
    public SysParamGroupCreateParam c_r2p(SysParamGroupCreateRequest req) {
        return SysParamGroupMapper.INSTANCE.convert(req);
    }

    @Override
    public SysParamGroupUpdateParam u_r2p(SysParamGroupUpdateRequest req) {
        return SysParamGroupMapper.INSTANCE.convert(req);
    }

    @Override
    public SysParamGroupQueryParam r_pr2p(PageRequest<SysParamGroupQueryCondition> pageReq) {
        SysParamGroupQueryParam queryParam = new SysParamGroupQueryParam();
        SysParamGroupQueryCondition condition = pageReq.getCondition();
        if (condition != null) {
            queryParam = SysParamGroupMapper.INSTANCE.convert(condition);
        }
        queryParam.setStartIndex(pageReq.getStartIndex());
        queryParam.setPageSize(pageReq.getPageSize());
        return queryParam;
    }

    @Override
    public SysParamGroupDeleteParam d_r2p(SysParamGroupDeleteRequest req) {
        return SysParamGroupMapper.INSTANCE.convert(req);
    }
}
