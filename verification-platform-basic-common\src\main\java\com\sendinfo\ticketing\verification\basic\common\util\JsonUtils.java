package com.sendinfo.ticketing.verification.basic.common.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

import com.sendinfo.ticketing.verification.basic.common.log.VerificationPlatformBasicLoggers;

public class JsonUtils {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    static {
        OBJECT_MAPPER.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public static String toJsonString(Object object) {
        try {
            return OBJECT_MAPPER.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            VerificationPlatformBasicLoggers.APPLICATION_LOGGER.error("toJsonString got exception", e);
            throw new RuntimeException("toJsonString got exception", e);
        }
    }

    public static <T> T parseObject(String json, Class<T> clazz) {
        try {
            return OBJECT_MAPPER.readValue(json, clazz);
        } catch (JsonProcessingException e) {
            VerificationPlatformBasicLoggers.APPLICATION_LOGGER.error("parseObject got exception", e);
            throw new RuntimeException("parseObject got exception", e);
        }
    }

    public static <T> T parseObject(String json, TypeReference<T> typeReference) {
        try {
            return OBJECT_MAPPER.readValue(json, typeReference);
        } catch (JsonProcessingException e) {
            VerificationPlatformBasicLoggers.APPLICATION_LOGGER.error("parseObject got exception", e);
            throw new RuntimeException("parseObject got exception", e);
        }
    }
}
