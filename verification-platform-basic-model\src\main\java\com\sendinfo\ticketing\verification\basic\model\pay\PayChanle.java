package com.sendinfo.ticketing.verification.basic.model.pay;

import com.sendinfo.ticketing.verification.basic.enums.CommonUseFlagEnum;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 支付渠道模型
 * 对应数据库表 pay_chanle
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@ToString(callSuper = true)
public class PayChanle implements Serializable {


    private static final long serialVersionUID = 5661504341439803022L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 渠道名称：建行、农行
     */
    private String chanleName;

    /**
     * 渠道编码:对应支付中心的渠道产品编号
     */
    private String chanleCode;

    /**
     * 支付产品码：对应支付中心的支付产品码
     */
    private String payProductCode;

    /**
     * 备注
     */
    private String remark;

    /**
     * 启用标识
     */
    private CommonUseFlagEnum useFlag;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 修改人
     */
    private String modifyBy;

    /**
     * 修改时间
     */
    private String modifyTime;

    /**
     * 删除标志F未删除,T已删除
     */
    private String deleted;
} 