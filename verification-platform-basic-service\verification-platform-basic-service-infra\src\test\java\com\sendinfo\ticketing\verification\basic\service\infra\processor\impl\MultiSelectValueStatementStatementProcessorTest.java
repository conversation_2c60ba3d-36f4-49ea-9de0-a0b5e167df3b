/**
 * MULTI_SELECT类型参数值声明处理器测试
 *
 * <AUTHOR> 2025-08-04
 */
package com.sendinfo.ticketing.verification.basic.service.infra.processor.impl;

import com.sendinfo.ticketing.verification.basic.enums.SysParamDataType;
import com.sendinfo.ticketing.verification.basic.exception.VerificationBizRuntimeException;
import com.sendinfo.ticketing.verification.basic.model.infra.error.InfraErrorDef;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class MultiSelectValueStatementStatementProcessorTest {

    private MultiSelectValueStatementStatementProcessor processor;

    @Before
    public void setUp() {
        processor = new MultiSelectValueStatementStatementProcessor();
    }

    @Test
    public void testGetDataType() {
        assertEquals(SysParamDataType.MULTI_SELECT, processor.getDataType());
    }

    // ========== validateStatement 测试 ==========

    @Test
    public void testValidateStatement_成功校验_单个默认选项() {
        String validStatement = "{\"options\":[" +
                "{\"value\":\"java\",\"label\":\"Java\",\"isDefault\":true}," +
                "{\"value\":\"python\",\"label\":\"Python\",\"isDefault\":false}," +
                "{\"value\":\"javascript\",\"label\":\"JavaScript\",\"isDefault\":false}" +
                "]}";

        // 不应该抛出异常
        processor.validateStatement(validStatement);
    }

    @Test
    public void testValidateStatement_成功校验_多个默认选项() {
        String validStatement = "{\"options\":[" +
                "{\"value\":\"java\",\"label\":\"Java\",\"isDefault\":true}," +
                "{\"value\":\"python\",\"label\":\"Python\",\"isDefault\":true}," +
                "{\"value\":\"javascript\",\"label\":\"JavaScript\",\"isDefault\":false}" +
                "]}";

        // 不应该抛出异常
        processor.validateStatement(validStatement);
    }

    @Test
    public void testValidateStatement_成功校验_所有选项都是默认() {
        String validStatement = "{\"options\":[" +
                "{\"value\":\"option1\",\"label\":\"选项1\",\"isDefault\":true}," +
                "{\"value\":\"option2\",\"label\":\"选项2\",\"isDefault\":true}," +
                "{\"value\":\"option3\",\"label\":\"选项3\",\"isDefault\":true}" +
                "]}";

        // 不应该抛出异常
        processor.validateStatement(validStatement);
    }

    @Test(expected = VerificationBizRuntimeException.class)
    public void testValidateStatement_参数为null() {
        processor.validateStatement(null);
    }

    @Test(expected = VerificationBizRuntimeException.class)
    public void testValidateStatement_参数为空字符串() {
        processor.validateStatement("");
    }

    @Test(expected = VerificationBizRuntimeException.class)
    public void testValidateStatement_JSON格式错误() {
        String invalidJson = "{\"options\":[{\"value\":\"option1\"}";
        processor.validateStatement(invalidJson);
    }

    @Test(expected = VerificationBizRuntimeException.class)
    public void testValidateStatement_options为空数组() {
        String statement = "{\"options\":[]}";
        processor.validateStatement(statement);
    }

    @Test(expected = VerificationBizRuntimeException.class)
    public void testValidateStatement_没有默认选项() {
        String statement = "{\"options\":[" +
                "{\"value\":\"option1\",\"label\":\"选项1\",\"isDefault\":false}," +
                "{\"value\":\"option2\",\"label\":\"选项2\",\"isDefault\":false}" +
                "]}";
        processor.validateStatement(statement);
    }

    @Test(expected = VerificationBizRuntimeException.class)
    public void testValidateStatement_选项value为空() {
        String statement = "{\"options\":[" +
                "{\"value\":\"\",\"label\":\"选项1\",\"isDefault\":true}" +
                "]}";
        processor.validateStatement(statement);
    }

    @Test
    public void testValidateStatement_异常信息验证() {
        try {
            processor.validateStatement(null);
            fail("应该抛出异常");
        } catch (VerificationBizRuntimeException e) {
            assertEquals(InfraErrorDef.SYSPARAM_VALUE_STATEMENT_VALIDATION_ERROR.code(), e.getErrorCode());
            assertTrue(e.getMessage().contains("MULTI_SELECT类型参数值声明不能为空"));
        }
    }

    // ========== parseAndGetDefaultSysParamValue 测试 ==========

    @Test
    public void testParseAndGetDefaultSysParamValue_成功解析() {
        String statement = "{\"options\":[" +
                "{\"value\":\"java\",\"label\":\"Java\",\"isDefault\":true}," +
                "{\"value\":\"python\",\"label\":\"Python\",\"isDefault\":true}," +
                "{\"value\":\"javascript\",\"label\":\"JavaScript\",\"isDefault\":false}" +
                "]}";

        String result = processor.parseAndGetDefaultSysParamValue(statement);
        assertTrue(result.contains("java"));
        assertTrue(result.contains("python"));
        assertFalse(result.contains("javascript"));
        assertTrue(result.startsWith("["));
        assertTrue(result.endsWith("]"));
    }


    // ========== 综合测试 ==========

    @Test
    public void testValidateAndParse_完整流程() {
        String statement = "{\"options\":[" +
                "{\"value\":\"backend\",\"label\":\"后端开发\",\"isDefault\":true}," +
                "{\"value\":\"frontend\",\"label\":\"前端开发\",\"isDefault\":true}," +
                "{\"value\":\"mobile\",\"label\":\"移动开发\",\"isDefault\":false}" +
                "]}";

        // 先校验
        processor.validateStatement(statement);

        // 再解析
        String result = processor.parseAndGetDefaultSysParamValue(statement);
        assertTrue(result.contains("backend"));
        assertTrue(result.contains("frontend"));
        assertFalse(result.contains("mobile"));
    }

    // ========== 业务场景测试 ==========

    @Test
    public void testMultiSelect_编程语言选择() {
        String statement = "{\"options\":[" +
                "{\"value\":\"JAVA\",\"label\":\"Java\",\"isDefault\":true}," +
                "{\"value\":\"PYTHON\",\"label\":\"Python\",\"isDefault\":false}," +
                "{\"value\":\"JAVASCRIPT\",\"label\":\"JavaScript\",\"isDefault\":true}," +
                "{\"value\":\"CSHARP\",\"label\":\"C#\",\"isDefault\":false}," +
                "{\"value\":\"GO\",\"label\":\"Go\",\"isDefault\":false}" +
                "]}";

        processor.validateStatement(statement);
        String result = processor.parseAndGetDefaultSysParamValue(statement);
        assertTrue(result.contains("JAVA"));
        assertTrue(result.contains("JAVASCRIPT"));
        assertFalse(result.contains("PYTHON"));
        assertFalse(result.contains("CSHARP"));
        assertFalse(result.contains("GO"));
    }

    @Test
    public void testMultiSelect_技能标签选择() {
        String statement = "{\"options\":[" +
                "{\"value\":\"SPRING_BOOT\",\"label\":\"Spring Boot\",\"isDefault\":true}," +
                "{\"value\":\"MICROSERVICE\",\"label\":\"微服务\",\"isDefault\":true}," +
                "{\"value\":\"DOCKER\",\"label\":\"Docker\",\"isDefault\":false}," +
                "{\"value\":\"KUBERNETES\",\"label\":\"Kubernetes\",\"isDefault\":false}," +
                "{\"value\":\"REDIS\",\"label\":\"Redis\",\"isDefault\":true}" +
                "]}";

        processor.validateStatement(statement);
        String result = processor.parseAndGetDefaultSysParamValue(statement);
        assertTrue(result.contains("SPRING_BOOT"));
        assertTrue(result.contains("MICROSERVICE"));
        assertTrue(result.contains("REDIS"));
        assertFalse(result.contains("DOCKER"));
        assertFalse(result.contains("KUBERNETES"));
    }

    @Test
    public void testMultiSelect_数据库选择() {
        String statement = "{\"options\":[" +
                "{\"value\":\"MYSQL\",\"label\":\"MySQL\",\"isDefault\":true}," +
                "{\"value\":\"POSTGRESQL\",\"label\":\"PostgreSQL\",\"isDefault\":false}," +
                "{\"value\":\"ORACLE\",\"label\":\"Oracle\",\"isDefault\":false}," +
                "{\"value\":\"MONGODB\",\"label\":\"MongoDB\",\"isDefault\":true}," +
                "{\"value\":\"REDIS\",\"label\":\"Redis\",\"isDefault\":true}" +
                "]}";

        processor.validateStatement(statement);
        String result = processor.parseAndGetDefaultSysParamValue(statement);
        assertTrue(result.contains("MYSQL"));
        assertTrue(result.contains("MONGODB"));
        assertTrue(result.contains("REDIS"));
        assertFalse(result.contains("POSTGRESQL"));
        assertFalse(result.contains("ORACLE"));
    }

    @Test
    public void testMultiSelect_部门选择() {
        String statement = "{\"options\":[" +
                "{\"value\":\"IT\",\"label\":\"信息技术部\",\"isDefault\":true}," +
                "{\"value\":\"HR\",\"label\":\"人力资源部\",\"isDefault\":false}," +
                "{\"value\":\"FINANCE\",\"label\":\"财务部\",\"isDefault\":true}," +
                "{\"value\":\"SALES\",\"label\":\"销售部\",\"isDefault\":false}," +
                "{\"value\":\"MARKETING\",\"label\":\"市场部\",\"isDefault\":false}" +
                "]}";

        processor.validateStatement(statement);
        String result = processor.parseAndGetDefaultSysParamValue(statement);
        assertTrue(result.contains("IT"));
        assertTrue(result.contains("FINANCE"));
        assertFalse(result.contains("HR"));
        assertFalse(result.contains("SALES"));
        assertFalse(result.contains("MARKETING"));
    }

    @Test
    public void testMultiSelect_地区选择() {
        String statement = "{\"options\":[" +
                "{\"value\":\"BEIJING\",\"label\":\"北京\",\"isDefault\":true}," +
                "{\"value\":\"SHANGHAI\",\"label\":\"上海\",\"isDefault\":true}," +
                "{\"value\":\"GUANGZHOU\",\"label\":\"广州\",\"isDefault\":false}," +
                "{\"value\":\"SHENZHEN\",\"label\":\"深圳\",\"isDefault\":true}," +
                "{\"value\":\"HANGZHOU\",\"label\":\"杭州\",\"isDefault\":false}" +
                "]}";

        processor.validateStatement(statement);
        String result = processor.parseAndGetDefaultSysParamValue(statement);
        assertTrue(result.contains("BEIJING"));
        assertTrue(result.contains("SHANGHAI"));
        assertTrue(result.contains("SHENZHEN"));
        assertFalse(result.contains("GUANGZHOU"));
        assertFalse(result.contains("HANGZHOU"));
    }

    @Test
    public void testMultiSelect_产品类型选择() {
        String statement = "{\"options\":[" +
                "{\"value\":\"WEB_APP\",\"label\":\"Web应用\",\"isDefault\":true}," +
                "{\"value\":\"MOBILE_APP\",\"label\":\"移动应用\",\"isDefault\":true}," +
                "{\"value\":\"DESKTOP_APP\",\"label\":\"桌面应用\",\"isDefault\":false}," +
                "{\"value\":\"API_SERVICE\",\"label\":\"API服务\",\"isDefault\":true}," +
                "{\"value\":\"BATCH_JOB\",\"label\":\"批处理任务\",\"isDefault\":false}" +
                "]}";

        processor.validateStatement(statement);
        String result = processor.parseAndGetDefaultSysParamValue(statement);
        assertTrue(result.contains("WEB_APP"));
        assertTrue(result.contains("MOBILE_APP"));
        assertTrue(result.contains("API_SERVICE"));
        assertFalse(result.contains("DESKTOP_APP"));
        assertFalse(result.contains("BATCH_JOB"));
    }
}
