package com.sendinfo.ticketing.verification.basic.component.system.converter;

import com.sendinfo.ticketing.verification.basic.api.system.request.SysAuditRoleQueryRequest;
import com.sendinfo.ticketing.verification.basic.component.system.mapper.SysAuditRoleMapper;
import com.sendinfo.ticketing.verification.basic.component.system.param.SysAuditRoleQueryParam;
import com.sendinfo.ticketing.verification.basic.model.system.SysAuditRole;
import com.sendinfo.ticketing.verification.basic.repository.system.arg.SysAuditRoleQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.system.dataobject.SysAuditRoleDO;
import com.sendinfo.ticketing.verification.common.component.converter.*;
import org.springframework.stereotype.Component;

/**
 * 票型角色转换器
 * <AUTHOR>
 */
@Component("sysAuditRoleConverter")
public class SysAuditRoleConverter implements
        ReadParam2ArgConverter<SysAuditRoleQueryParam, SysAuditRoleQueryArg>,
        ReadDo2ModelConverter<SysAuditRoleDO, SysAuditRole>,
        ReadReq2ParamConverter<SysAuditRoleQueryRequest, SysAuditRoleQueryParam> {

    @Override
    public SysAuditRole r_d2m(SysAuditRoleDO dataObject) {
        return SysAuditRoleMapper.INSTANCE.convert(dataObject);
    }

    @Override
    public SysAuditRoleQueryArg r_p2a(SysAuditRoleQueryParam param) {
        return SysAuditRoleMapper.INSTANCE.convert(param);
    }

    @Override
    public SysAuditRoleQueryParam r_r2p(SysAuditRoleQueryRequest request) {
        return SysAuditRoleMapper.INSTANCE.convert(request);
    }
} 