package com.sendinfo.ticketing.verification.basic.component.goods.param;

import com.sendinfo.ticketing.verification.common.component.param.AbstractDeleteParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2025-07-24 10:41
 */
@Getter
@Setter
@ToString(callSuper = true)
public class UserTicketRelationDeleteParam extends AbstractDeleteParam {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 修改人
     */
    private String modifyBy;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 票型编码
     */
    private Long ticketId;
}
