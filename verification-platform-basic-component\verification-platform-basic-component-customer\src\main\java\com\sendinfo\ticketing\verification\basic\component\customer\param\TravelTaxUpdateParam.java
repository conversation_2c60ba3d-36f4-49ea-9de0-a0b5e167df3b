package com.sendinfo.ticketing.verification.basic.component.customer.param;

import com.sendinfo.ticketing.verification.common.component.param.AbstractUpdateParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * travel_tax信息更新参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class TravelTaxUpdateParam extends AbstractUpdateParam {
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 企业编码
     */
    private String corpCode;
    /**
     * 旅行社ID
     */
    private Long travelId;
    /**
     * 实际抬头
     */
    private String taxTitle;
    /**
     * 纳税人识别号
     */
    private String taxNo;
    /**
     * 开户银行
     */
    private String taxBank;
    /**
     * 银行账号
     */
    private String taxAccount;
    /**
     * 备注
     */
    private String remark;
    /**
     * 修改人
     */
    private String modifyBy;
} 