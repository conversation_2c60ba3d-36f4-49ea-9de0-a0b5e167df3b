package com.sendinfo.ticketing.verification.basic.repository.goods.dao;

import com.alibaba.nacos.api.exception.NacosException;
import com.sendinfo.ticketing.verification.basic.repository.common.config.SaasPwMybatisConfig;
import com.sendinfo.ticketing.verification.basic.repository.common.sharding.DynamicDataSourceMappingConfigPullStorage;
import com.sendinfo.ticketing.verification.basic.repository.common.sharding.ShardingIdGeneratorUtil;
import com.sendinfo.ticketing.verification.basic.repository.goods.dao.impl.TktModelParamDaoImpl;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TktModelParamDO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.AbstractTransactionalJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.List;

@SpringBootTest(classes = TktModelParamDaoTest.Config.class)
@EnableAutoConfiguration
@RunWith(SpringRunner.class)
@TestPropertySource(locations = "classpath:test-application.properties")
public class TktModelParamDaoTest extends AbstractTransactionalJUnit4SpringContextTests {

    @Autowired
    private TktModelParamDao tktModelParamDao;

    private static final String TEST_CORP_CODE = "TEST_CORP";
    private static final String TEST_USER = "test_user";

    @Configuration
    @Import(SaasPwMybatisConfig.class)
    static class Config {

        @Bean
        TktModelParamDao tktTktModelParamDao(@Qualifier(SaasPwMybatisConfig.BEAN_NAME_SQL_SESSION_TEMPLATE) SqlSessionTemplate sqlSessionTemplate) {
            return new TktModelParamDaoImpl(sqlSessionTemplate);
        }

        @Bean
        DynamicDataSourceMappingConfigPullStorage dynamicDataSourceMappingConfigPullStorage(@Value("${nacos.config.server-addr}") String serverAddr,
                                                                                            @Value("${nacos.config.namespace}") String namespace,
                                                                                            @Value("${verification-platform-basic.nacos.tenant-datasource-mapping.data-id:saas-pw-datasource-sharding-mapping.json}") String dataId,
                                                                                            @Value("${verification-platform-basic.nacos.tenant-datasource-mapping.group:DEFAULT_GROUP}") String group,
                                                                                            @Value("${verification-platform-basic.nacos.tenant-datasource-mapping.pulled.timeout:3000}") Integer timeout) throws NacosException {
            return new DynamicDataSourceMappingConfigPullStorage(serverAddr, namespace, dataId, group, timeout);
        }
    }

    @Before
    public void setUp() {
        // Initialize test data
        for (int i = 1; i <= 5; i++) {
            TktModelParamDO paramDO = createTestModelParamDO(i, "MODEL_" + i);
            tktModelParamDao.insert(paramDO);
        }
    }

    @Test
    public void testInsert() {
        // Given
        TktModelParamDO paramDO = createTestModelParamDO(6L, "MODEL_6");

        // When
        tktModelParamDao.insert(paramDO);

        // Then
        TktModelParamDO found = tktModelParamDao.queryById(paramDO.getId());
        Assert.assertNotNull(found);
        Assert.assertEquals(paramDO.getModelCode(), found.getModelCode());
        Assert.assertEquals(paramDO.getCorpCode(), found.getCorpCode());
    }

    @Test
    public void testBatchQueryTicketModelParamByIds() {
        // When
        List<TktModelParamDO> params = tktModelParamDao.batchQueryTicketModelParamByIds(
                Arrays.asList(1L, 2L, 3L), TEST_CORP_CODE);
        if(params.size() > 0) {
            // Then
            Assert.assertEquals(3, params.size());
            params.forEach(param -> {
                Assert.assertTrue(param.getId() >= 1L && param.getId() <= 3L);
                Assert.assertEquals(TEST_CORP_CODE, param.getCorpCode());
            });
        }
    }

    private TktModelParamDO createTestModelParamDO(long id, String modelCode) {
        TktModelParamDO paramDO = new TktModelParamDO();
        id = ShardingIdGeneratorUtil.generateIdByCorpCode(TEST_CORP_CODE);
        paramDO.setId(id);
        paramDO.setCorpCode(TEST_CORP_CODE);
        paramDO.setTitle("Test Model " + id);
        paramDO.setModelType("0");
        paramDO.setModelKind(0);
        paramDO.setBackImg("/images/back_" + id + ".png");
        paramDO.setPageContent("{\"content\":\"test\"}");
        paramDO.setHnPrintContent("HN Print Content");
        paramDO.setPrintContent("Print Content");
        paramDO.setWidth(100);
        paramDO.setHeight(200);
        paramDO.setPrintDirection(0);
        paramDO.setPageInfo("Page Info");
        paramDO.setUseFlag("1");
        paramDO.setCreateBy(TEST_USER);
        paramDO.setModifyBy(TEST_USER);
        paramDO.setDeleted("F");
        paramDO.setRoleApply(0);
        paramDO.setModelCode(modelCode);
        paramDO.setContinuousPrint("F");
        paramDO.setTransferParam("Transfer Param");
        return paramDO;
    }
}
