package com.sendinfo.ticketing.verification.basic.repository.pay.dao;

import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysMainbodyDO;
import com.sendinfo.ticketing.verification.common.repository.BatchInsertDAO;
import com.sendinfo.ticketing.verification.common.repository.CountableDAO;
import com.sendinfo.ticketing.verification.common.repository.GenericDAO;
import com.sendinfo.ticketing.verification.common.repository.QueryableDAO;

/**
 * 经营主体数据访问接口
 * 用于定义sys_mainbody表的数据访问接口
 * 继承通用DAO接口组合，提供完整的CRUD操作
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface SysMainbodyDao extends GenericDAO<Long, SysMainbodyDO, SysMainbodyUpdateArg, SysMainbodyDeleteArg>,
        CountableDAO<SysMainbodyQueryArg>,
        QueryableDAO<SysMainbodyQueryArg, SysMainbodyDO>,
        BatchInsertDAO<Long, SysMainbodyDO> {

} 