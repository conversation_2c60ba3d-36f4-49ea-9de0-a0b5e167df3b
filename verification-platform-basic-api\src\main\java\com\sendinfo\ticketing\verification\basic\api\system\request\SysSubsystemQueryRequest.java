package com.sendinfo.ticketing.verification.basic.api.system.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * 子系统管理查询请求参数
 * <AUTHOR>
 */
@Getter
@Setter
@ToString(callSuper = true)
public class SysSubsystemQueryRequest implements Serializable {

    private static final long serialVersionUID = -4927866227374587856L;

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户账号类型
     */
    private String userAccType;

    /**
     * 子系统名称
     */
    private String subsystemName;

    /**
     * 子系统编码
     */
    private String subsystemCode;
} 