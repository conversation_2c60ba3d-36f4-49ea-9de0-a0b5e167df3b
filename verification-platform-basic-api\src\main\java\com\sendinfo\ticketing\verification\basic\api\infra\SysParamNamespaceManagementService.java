package com.sendinfo.ticketing.verification.basic.api.infra;

import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamNamespaceUpdateRequest;
import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamNamespaceStatusSwitchRequest;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

/**
 * 参数命名空间管理服务
 * <AUTHOR> 2025-07-21
 */
public interface SysParamNamespaceManagementService {
    /**
     * 更新参数命名空间
     *
     * @param request 更新请求
     * @return 更新结果
     */
    ResultModel<Boolean> updateSysParamNamespace(SysParamNamespaceUpdateRequest request);

    /**
     * 启用参数命名空间
     *
     * @param request 状态切换请求
     * @return 操作结果
     */
    ResultModel<Boolean> enableSysParamNamespace(SysParamNamespaceStatusSwitchRequest request);
    
    /**
     * 禁用参数命名空间
     *
     * @param request 状态切换请求
     * @return 操作结果
     */
    ResultModel<Boolean> disableSysParamNamespace(SysParamNamespaceStatusSwitchRequest request);
}