package com.sendinfo.ticketing.verification.basic.component.customer.impl;

import com.sendinfo.ticketing.verification.basic.component.customer.DirectorInfoReadComponent;
import com.sendinfo.ticketing.verification.basic.component.customer.converter.DirectorInfoConverter;
import com.sendinfo.ticketing.verification.basic.component.customer.param.DirectorInfoQueryParam;
import com.sendinfo.ticketing.verification.basic.model.customer.DirectorInfo;
import com.sendinfo.ticketing.verification.basic.repository.customer.arg.DirectorInfoQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.customer.dao.DirectorInfoDao;
import com.sendinfo.ticketing.verification.basic.repository.customer.dataobject.DirectorInfoDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedCountRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedListRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleRead;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 主管信息读取组件实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("directorInfoReadComponent")
@Getter
public class DirectorInfoReadComponentImpl implements
        DirectorInfoReadComponent,
        DaoBasedSingleRead<Long, DirectorInfoQueryParam, DirectorInfo, DirectorInfoDO>,
        DaoBasedListRead<Long, DirectorInfoQueryParam, DirectorInfo, DirectorInfoDO, DirectorInfoQueryArg>,
        DaoBasedCountRead<Long, DirectorInfoQueryParam, DirectorInfo, DirectorInfoQueryArg> {

    private final DirectorInfoDao dao;
    private final DirectorInfoConverter converter;

    public DirectorInfoReadComponentImpl(DirectorInfoDao dao, DirectorInfoConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }

    @Override
    public List<DirectorInfo> queryByTravelId(Long travelId, String corpCode) {
        return converter.r_ds2ms(dao.queryByTravelId(travelId, corpCode));
    }
} 