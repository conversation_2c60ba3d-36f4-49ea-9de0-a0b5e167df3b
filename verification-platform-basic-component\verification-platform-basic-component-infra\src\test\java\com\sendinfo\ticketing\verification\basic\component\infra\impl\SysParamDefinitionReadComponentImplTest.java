package com.sendinfo.ticketing.verification.basic.component.infra.impl;

import com.sendinfo.ticketing.verification.basic.component.infra.converter.SysParamDefinitionConverter;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamDefinitionQueryParam;
import com.sendinfo.ticketing.verification.basic.enums.SysParamDataType;
import com.sendinfo.ticketing.verification.basic.model.infra.SysParamDefinition;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.SysParamDefinitionDao;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.SysParamDefinitionDO;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamDefinitionQueryArg;
import com.sendinfo.ticketing.verification.basic.enums.CommonStatusEnum;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.never;

/**
 * 系统参数定义查询组件实现测试
 *
 * <AUTHOR> 2025-07-28 15:30:00
 */
@RunWith(MockitoJUnitRunner.class)
public class SysParamDefinitionReadComponentImplTest {

    @Mock
    private SysParamDefinitionDao dao;

    private SysParamDefinitionConverter converter;
    private SysParamDefinitionReadComponentImpl component;

    // 测试常量
    private static final String TEST_CORP_CODE = "TEST_CORP";
    private static final String TEST_COMPONENT_CODE = "TEST_COMPONENT";
    private static final String TEST_PARAM_CODE = "TEST_PARAM";
    private static final String TEST_PARAM_NAME = "Test Parameter";
    private static final String TEST_DESCRIPTION = "Test Description";
    private static final String TEST_DATA_TYPE = "STRING";
    private static final String TEST_VALIDATION_RULES = "{\"maxLength\": 100}";
    private static final String TEST_PARAM_VALUE_STATEMENT = "Test param value statement";

    private static final Long TEST_ID = 1L;

    @Before
    public void setUp() {
        converter = new SysParamDefinitionConverter();
        component = new SysParamDefinitionReadComponentImpl(dao, converter);
    }

    @Test
    public void testGet() {
        // Given
        SysParamDefinitionDO dataObject = createTestDO();
        SysParamDefinition expectedModel = createTestModel();

        Mockito.when(dao.queryById(TEST_ID)).thenReturn(dataObject);

        // When
        SysParamDefinition result = component.get(TEST_ID);

        // Then
        Assert.assertNotNull(result);
        Assert.assertEquals(expectedModel.getId(), result.getId());
        Assert.assertEquals(expectedModel.getParamCode(), result.getParamCode());
        Assert.assertEquals(expectedModel.getParamName(), result.getParamName());
        Mockito.verify(dao).queryById(TEST_ID);
    }

    @Test
    public void testGetWithNullId() {
        // When
        SysParamDefinition result = component.get(null);

        // Then
        Assert.assertNull(result);
        Mockito.verify(dao, never()).queryById(null);
    }

    @Test
    public void testList() {
        // Given
        SysParamDefinitionQueryParam queryParam = createTestQueryParam();
        List<SysParamDefinitionDO> dataObjects = Arrays.asList(createTestDO());
        ArgumentCaptor<SysParamDefinitionQueryArg> argCaptor = ArgumentCaptor
                .forClass(SysParamDefinitionQueryArg.class);

        Mockito.when(dao.queryByArg(Mockito.any(SysParamDefinitionQueryArg.class))).thenReturn(dataObjects);

        // When
        List<SysParamDefinition> result = component.list(queryParam);

        // Then
        Assert.assertNotNull(result);
        Assert.assertEquals(1, result.size());

        // 验证DAO方法调用并捕获参数
        Mockito.verify(dao).queryByArg(argCaptor.capture());

        // 验证查询参数映射
        SysParamDefinitionQueryArg capturedArg = argCaptor.getValue();
        Assert.assertEquals(queryParam.getComponentCode(), capturedArg.getComponentCode());
        Assert.assertEquals(queryParam.getParamCode(), capturedArg.getParamCode());
        Assert.assertEquals((Integer) queryParam.getStartIndex(), capturedArg.getOffset());
        Assert.assertEquals((Integer) queryParam.getPageSize(), capturedArg.getLimit());
    }

    @Test
    public void testCount() {
        // Given
        SysParamDefinitionQueryParam queryParam = createTestQueryParam();
        ArgumentCaptor<SysParamDefinitionQueryArg> argCaptor = ArgumentCaptor
                .forClass(SysParamDefinitionQueryArg.class);

        Mockito.when(dao.countByArg(Mockito.any(SysParamDefinitionQueryArg.class))).thenReturn(5);

        // When
        int result = component.count(queryParam);

        // Then
        Assert.assertEquals(5, result);

        // 验证DAO方法调用并捕获参数
        Mockito.verify(dao).countByArg(argCaptor.capture());

        // 验证查询参数映射
        SysParamDefinitionQueryArg capturedArg = argCaptor.getValue();
        Assert.assertEquals(queryParam.getComponentCode(), capturedArg.getComponentCode());
        Assert.assertEquals(queryParam.getParamCode(), capturedArg.getParamCode());
    }

    @Test
    public void testQueryByParamCode() {
        // Given
        SysParamDefinitionDO dataObject = createTestDO();

        Mockito.when(dao.queryByParamCode(TEST_PARAM_CODE, TEST_CORP_CODE)).thenReturn(dataObject);

        // When
        SysParamDefinition result = component.queryByParamCode(TEST_PARAM_CODE, TEST_CORP_CODE);

        // Then
        Assert.assertNotNull(result);
        Assert.assertEquals(TEST_PARAM_CODE, result.getParamCode());
        Assert.assertEquals(TEST_CORP_CODE, result.getCorpCode());
        Mockito.verify(dao).queryByParamCode(TEST_PARAM_CODE, TEST_CORP_CODE);
    }

    @Test
    public void testQueryByParamCodeNotFound() {
        // Given
        Mockito.when(dao.queryByParamCode(TEST_PARAM_CODE, TEST_CORP_CODE)).thenReturn(null);

        // When
        SysParamDefinition result = component.queryByParamCode(TEST_PARAM_CODE, TEST_CORP_CODE);

        // Then
        Assert.assertNull(result);
        Mockito.verify(dao).queryByParamCode(TEST_PARAM_CODE, TEST_CORP_CODE);
    }

    @Test
    public void testQueryByComponentCode() {
        // Given
        List<SysParamDefinitionDO> dataObjects = Arrays.asList(createTestDO());

        Mockito.when(dao.queryByComponentCode(TEST_COMPONENT_CODE)).thenReturn(dataObjects);

        // When
        List<SysParamDefinition> result = component.queryByComponentCode(TEST_COMPONENT_CODE);

        // Then
        Assert.assertNotNull(result);
        Assert.assertEquals(1, result.size());
        Assert.assertEquals(TEST_COMPONENT_CODE, result.get(0).getComponentCode());
        Assert.assertEquals(TEST_CORP_CODE, result.get(0).getCorpCode());
        Mockito.verify(dao).queryByComponentCode(TEST_COMPONENT_CODE);
    }

    @Test
    public void testQueryByComponentCodeEmptyResult() {
        // Given
        Mockito.when(dao.queryByComponentCode(TEST_COMPONENT_CODE)).thenReturn(Arrays.asList());

        // When
        List<SysParamDefinition> result = component.queryByComponentCode(TEST_COMPONENT_CODE);

        // Then
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isEmpty());
        Mockito.verify(dao).queryByComponentCode(TEST_COMPONENT_CODE);
    }

    @Test
    public void testListWithEmptyResult() {
        // Given
        SysParamDefinitionQueryParam queryParam = createTestQueryParam();

        Mockito.when(dao.queryByArg(Mockito.any(SysParamDefinitionQueryArg.class))).thenReturn(Arrays.asList());

        // When
        List<SysParamDefinition> result = component.list(queryParam);

        // Then
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isEmpty());
        Mockito.verify(dao).queryByArg(Mockito.any(SysParamDefinitionQueryArg.class));
    }

    @Test
    public void testQueryByComponentCodeList() {
        // Given
        List<String> componentCodeList = Arrays.asList(TEST_COMPONENT_CODE, "TEST_COMPONENT_2");
        List<SysParamDefinitionDO> dataObjects = Arrays.asList(createTestDO(), createTestDO2());

        Mockito.when(dao.queryByComponentCodeList(componentCodeList)).thenReturn(dataObjects);

        // When
        List<SysParamDefinition> result = component.queryByComponentCodeList(componentCodeList);

        // Then
        Assert.assertNotNull(result);
        Assert.assertEquals(2, result.size());
        Mockito.verify(dao).queryByComponentCodeList(componentCodeList);
    }

    @Test
    public void testQueryByComponentCodeList_EmptyList() {
        // Given
        List<String> emptyList = Arrays.asList();

        Mockito.when(dao.queryByComponentCodeList(emptyList)).thenReturn(Arrays.asList());

        // When
        List<SysParamDefinition> result = component.queryByComponentCodeList(emptyList);

        // Then
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isEmpty());
        Mockito.verify(dao).queryByComponentCodeList(emptyList);
    }

    @Test
    public void testQueryByComponentCodeList_NullList() {
        // Given
        List<String> nullList = null;

        Mockito.when(dao.queryByComponentCodeList(nullList)).thenReturn(Arrays.asList());

        // When
        List<SysParamDefinition> result = component.queryByComponentCodeList(nullList);

        // Then
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isEmpty());
        Mockito.verify(dao).queryByComponentCodeList(nullList);
    }

    private SysParamDefinitionQueryParam createTestQueryParam() {
        SysParamDefinitionQueryParam param = new SysParamDefinitionQueryParam();
        param.setCorpCode(TEST_CORP_CODE);
        param.setComponentCode(TEST_COMPONENT_CODE);
        param.setParamCode(TEST_PARAM_CODE);
        param.setStartIndex(0);
        param.setPageSize(10);
        return param;
    }

    private SysParamDefinitionDO createTestDO() {
        SysParamDefinitionDO dataObject = new SysParamDefinitionDO();
        dataObject.setId(TEST_ID);
        dataObject.setCorpCode(TEST_CORP_CODE);
        dataObject.setComponentCode(TEST_COMPONENT_CODE);
        dataObject.setParamCode(TEST_PARAM_CODE);
        dataObject.setParamName(TEST_PARAM_NAME);
        dataObject.setDescription(TEST_DESCRIPTION);
        dataObject.setDataType(TEST_DATA_TYPE);
        dataObject.setValidationRules(TEST_VALIDATION_RULES);
        dataObject.setParamValueStatement(TEST_PARAM_VALUE_STATEMENT);
        dataObject.setIsSensitive(0);
        dataObject.setStatus(1);
        dataObject.setDeleted("F");
        return dataObject;
    }

    private SysParamDefinitionDO createTestDO2() {
        SysParamDefinitionDO dataObject = new SysParamDefinitionDO();
        dataObject.setId(2L);
        dataObject.setCorpCode(TEST_CORP_CODE);
        dataObject.setComponentCode("TEST_COMPONENT_2");
        dataObject.setParamCode("TEST_PARAM_2");
        dataObject.setParamName("Test Parameter 2");
        dataObject.setDescription("Test Description 2");
        dataObject.setDataType(TEST_DATA_TYPE);
        dataObject.setValidationRules(TEST_VALIDATION_RULES);
        dataObject.setParamValueStatement(TEST_PARAM_VALUE_STATEMENT);
        dataObject.setIsSensitive(0);
        dataObject.setStatus(1);
        dataObject.setDeleted("F");
        return dataObject;
    }

    private SysParamDefinition createTestModel() {
        SysParamDefinition model = new SysParamDefinition();
        model.setId(TEST_ID);
        model.setCorpCode(TEST_CORP_CODE);
        model.setComponentCode(TEST_COMPONENT_CODE);
        model.setParamCode(TEST_PARAM_CODE);
        model.setParamName(TEST_PARAM_NAME);
        model.setDescription(TEST_DESCRIPTION);
        model.setDataType(SysParamDataType.INPUT);
        model.setValidationRules(TEST_VALIDATION_RULES);
        model.setParamValueStatement(TEST_PARAM_VALUE_STATEMENT);
        model.setIsSensitive(1);
        model.setStatus(CommonStatusEnum.ENABLE);
        return model;
    }
}
