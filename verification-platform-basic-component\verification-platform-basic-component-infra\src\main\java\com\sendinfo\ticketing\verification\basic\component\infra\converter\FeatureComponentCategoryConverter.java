package com.sendinfo.ticketing.verification.basic.component.infra.converter;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

import com.sendinfo.ticketing.verification.basic.api.infra.request.FeatureComponentCategoryCreateRequest;
import com.sendinfo.ticketing.verification.basic.api.infra.request.FeatureComponentCategoryDeleteRequest;
import com.sendinfo.ticketing.verification.basic.api.infra.request.FeatureComponentCategoryQueryCondition;
import com.sendinfo.ticketing.verification.basic.api.infra.request.FeatureComponentCategoryUpdateRequest;
import com.sendinfo.ticketing.verification.basic.component.infra.mapper.FeatureComponentCategoryMapper;
import com.sendinfo.ticketing.verification.basic.component.infra.param.FeatureComponentCategoryCreateParam;
import com.sendinfo.ticketing.verification.basic.component.infra.param.FeatureComponentCategoryDeleteParam;
import com.sendinfo.ticketing.verification.basic.component.infra.param.FeatureComponentCategoryQueryParam;
import com.sendinfo.ticketing.verification.basic.component.infra.param.FeatureComponentCategoryUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.infra.FeatureComponentCategory;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.FeatureComponentCategoryDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.FeatureComponentCategoryQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.FeatureComponentCategoryUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.FeatureComponentCategoryDO;
import com.sendinfo.ticketing.verification.common.api.request.PageRequest;
import com.sendinfo.ticketing.verification.common.component.converter.CreateParam2DoConverter;
import com.sendinfo.ticketing.verification.common.component.converter.CreateReq2ParamConverter;
import com.sendinfo.ticketing.verification.common.component.converter.DeleteParam2ArgConverter;
import com.sendinfo.ticketing.verification.common.component.converter.DeleteReq2ParamConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadDo2ModelConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadPageReq2ParamConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadParam2ArgConverter;
import com.sendinfo.ticketing.verification.common.component.converter.UpdateParam2ArgConverter;
import com.sendinfo.ticketing.verification.common.component.converter.UpdateReq2ParamConverter;

/**
 * 组件分类转换器
 * 
 * <AUTHOR> 2025-07-22 11:45:32
 */
@Component("featureComponentCategoryConverter")
public class FeatureComponentCategoryConverter
        implements CreateParam2DoConverter<FeatureComponentCategoryCreateParam, FeatureComponentCategoryDO>,
        ReadParam2ArgConverter<FeatureComponentCategoryQueryParam, FeatureComponentCategoryQueryArg>,
        ReadDo2ModelConverter<FeatureComponentCategoryDO, FeatureComponentCategory>,
        UpdateParam2ArgConverter<FeatureComponentCategoryUpdateParam, FeatureComponentCategoryUpdateArg, FeatureComponentCategory>,
        DeleteParam2ArgConverter<FeatureComponentCategoryDeleteParam, FeatureComponentCategoryDeleteArg>,
        DeleteReq2ParamConverter<FeatureComponentCategoryDeleteRequest, FeatureComponentCategoryDeleteParam>,
        CreateReq2ParamConverter<FeatureComponentCategoryCreateRequest, FeatureComponentCategoryCreateParam>,
        UpdateReq2ParamConverter<FeatureComponentCategoryUpdateRequest, FeatureComponentCategoryUpdateParam>,
        ReadPageReq2ParamConverter<FeatureComponentCategoryQueryCondition, FeatureComponentCategoryQueryParam> {

    @Override
    public FeatureComponentCategoryDO c_p2d(FeatureComponentCategoryCreateParam createParam) {
        return FeatureComponentCategoryMapper.INSTANCE.convert(createParam);
    }

    @Override
    public List<FeatureComponentCategoryDO> c_ps2ds(List<FeatureComponentCategoryCreateParam> params) {
        return params.stream()
                .map(FeatureComponentCategoryMapper.INSTANCE::convert)
                .collect(Collectors.toList());
    }

    @Override
    public FeatureComponentCategoryUpdateArg u_p2a(FeatureComponentCategoryUpdateParam param, FeatureComponentCategory currentModel) {
        return FeatureComponentCategoryMapper.INSTANCE.convert(param);
    }

    @Override
    public FeatureComponentCategoryQueryArg r_p2a(FeatureComponentCategoryQueryParam param) {
        return FeatureComponentCategoryMapper.INSTANCE.convert(param);
    }

    @Override
    public FeatureComponentCategoryDeleteArg d_p2a(FeatureComponentCategoryDeleteParam param) {
        return FeatureComponentCategoryMapper.INSTANCE.convert(param);
    }

    @Override
    public FeatureComponentCategory r_d2m(FeatureComponentCategoryDO dataObject) {
        return FeatureComponentCategoryMapper.INSTANCE.convert(dataObject);
    }

    @Override
    public List<FeatureComponentCategory> r_ds2ms(List<FeatureComponentCategoryDO> dataObjects) {
        return dataObjects.stream()
                .map(this::r_d2m)
                .collect(Collectors.toList());
    }

    @Override
    public FeatureComponentCategoryCreateParam c_r2p(FeatureComponentCategoryCreateRequest req) {
        return FeatureComponentCategoryMapper.INSTANCE.convert(req);
    }

    @Override
    public FeatureComponentCategoryUpdateParam u_r2p(FeatureComponentCategoryUpdateRequest req) {
        return FeatureComponentCategoryMapper.INSTANCE.convert(req);
    }

    @Override
    public FeatureComponentCategoryQueryParam r_pr2p(PageRequest<FeatureComponentCategoryQueryCondition> pageReq) {
        FeatureComponentCategoryQueryParam queryParam = new FeatureComponentCategoryQueryParam();
        FeatureComponentCategoryQueryCondition condition = pageReq.getCondition();
        if (condition != null) {
            queryParam.setCorpCode(condition.getCorpCode());
            queryParam.setCategoryName(condition.getCategoryName());
        }
        queryParam.setStartIndex(pageReq.getStartIndex());
        queryParam.setPageSize(pageReq.getPageSize());
        return queryParam;
    }

    @Override
    public FeatureComponentCategoryDeleteParam d_r2p(FeatureComponentCategoryDeleteRequest req) {
        return FeatureComponentCategoryMapper.INSTANCE.convert(req);
    }
}
