package com.sendinfo.ticketing.verification.basic.api.goods;

import com.sendinfo.ticketing.verification.basic.api.goods.request.TktGroupInfoQueryRequest;
import com.sendinfo.ticketing.verification.basic.model.goods.TktGroupInfo;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import java.util.List;

/**
 * @Auther: chengjie.li
 * @Date: 2025/6/13 10:22
 */
public interface TktGroupInfoReadService {

    /**
     * 根据条件查询分组列表
     *
     * @param request 条件
     * @return 分组列表
     */
    ResultModel<List<TktGroupInfo>> queryTktGroupInfoList(TktGroupInfoQueryRequest request);
}
