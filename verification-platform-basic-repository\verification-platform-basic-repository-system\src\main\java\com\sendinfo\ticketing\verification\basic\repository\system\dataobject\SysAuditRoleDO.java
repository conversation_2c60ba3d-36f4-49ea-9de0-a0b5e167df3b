package com.sendinfo.ticketing.verification.basic.repository.system.dataobject;

import com.sendinfo.ticketing.verification.common.repository.dataobject.AbstractTenantBaseDO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 票型角色数据对象
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class SysAuditRoleDO extends AbstractTenantBaseDO<Integer> {

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 权限类型
     */
    private Integer roleType;

    /**
     * 子系统ID
     */
    private Integer subsystemId;

    /**
     * 启用状态 F:停用 T:启动
     */
    private String useFlag;

    /**
     * 备注
     */
    private String remark;
} 