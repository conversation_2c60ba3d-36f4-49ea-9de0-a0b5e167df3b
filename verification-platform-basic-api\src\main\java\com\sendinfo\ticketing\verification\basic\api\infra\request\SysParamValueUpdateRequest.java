package com.sendinfo.ticketing.verification.basic.api.infra.request;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 系统参数值更新请求
 *
 * <AUTHOR> 2025-05-20 15:40:00
 */
@Getter
@Setter
@ToString
public class SysParamValueUpdateRequest implements Serializable {
    private static final long serialVersionUID = -7654321098765432103L;

    /**
     * 主键ID
     */
    @NotNull(message = "ID不能为空")
    private Long id;

    /**
     * 参数值
     */
    @NotBlank(message = "参数值不能为空")
    @Size(max = 2000, message = "参数值长度不能超过2000")
    private String paramValue;

    /**
     * 修改人
     */
    @NotBlank(message = "修改人不能为空")
    private String modifyBy;

    /**
     * 状态,0-禁用,1-启用
     */
    private int status;
}