package com.sendinfo.ticketing.verification.basic.repository.pay.arg;

import com.sendinfo.ticketing.verification.common.repository.arg.AbstractDeleteArg;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 经营主体支付配置关联子商户表删除参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class SysMainbodyManageMmSubMerchantsDeleteArg extends AbstractDeleteArg {

    /**
     * 企业编码
     * 用于租户隔离，必填字段
     */
    private String corpCode;

    /**
     * 主键ID
     * 要删除的记录主键
     */
    private Long id;

    /**
     * 修改人
     * 记录删除操作的用户
     */
    private String modifyBy;
} 