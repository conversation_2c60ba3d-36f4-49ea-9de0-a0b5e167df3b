package com.sendinfo.ticketing.verification.basic.repository.pay.arg;

import com.sendinfo.ticketing.verification.common.repository.arg.AbstractUpdateArg;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 商户渠道配置更新参数
 * 用于封装mainbody_chanle表的更新条件和目标字段，支持租户隔离
 * 包含所有可更新字段和状态安全更新器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class MainbodyChanleUpdateArg extends AbstractUpdateArg<Long> {
    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 支付渠道ID
     */
    private Long payChanleId;

    /**
     * 主体ID
     */
    private Long mainbodyId;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 修改人
     */
    private String modifyBy;
} 