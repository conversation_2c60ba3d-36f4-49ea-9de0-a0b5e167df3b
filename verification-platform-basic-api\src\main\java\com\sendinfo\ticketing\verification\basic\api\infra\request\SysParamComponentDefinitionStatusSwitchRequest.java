package com.sendinfo.ticketing.verification.basic.api.infra.request;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 系统参数组件定义状态切换请求
 */
@Getter
@Setter
@ToString
public class SysParamComponentDefinitionStatusSwitchRequest implements Serializable {
    private static final long serialVersionUID = -7654321098765432105L;

    /**
     * 企业编码
     */
    @NotBlank(message = "企业编码不能为空")
    private String corpCode;
    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空")
    private Long id;

    /**
     * 目标状态：1-启用，0-禁用
     */
    @NotNull(message = "目标状态不能为空")
    private Integer targetStatus;

    /**
     * 修改人
     */
    @NotNull(message = "修改人不能为空")
    private String modifyBy;
}
