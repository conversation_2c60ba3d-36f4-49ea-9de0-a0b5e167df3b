package com.sendinfo.ticketing.verification.basic.component.goods.param;

import com.sendinfo.ticketing.verification.common.component.param.AbstractPageQueryParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 打印设置查询参数
 * <AUTHOR>
 */
@Getter
@Setter
@ToString(callSuper = true)
public class TktPrintQueryParam extends AbstractPageQueryParam {
    /**
     * 票种ID
     */
    private Long ticketId;

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 打印模板名称模糊匹配
     */
    private String blurryPrintName;

    /**
     * 散客打印模板名称
     */
    private String tourPrintName;

    /**
     * 团队打印模板名称
     */
    private String teamPrintName;

    /**
     * 网络取票模板名称
     */
    private String takePrintName;

    /**
     * 打印机类型
     */
    private String printerType;
} 