package com.sendinfo.ticketing.verification.basic.component.goods.mapper;

import com.sendinfo.ticketing.verification.basic.component.goods.param.TktSaleRuleQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TktSaleRule;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TktSaleRuleQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TktSaleRuleDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


@Mapper
public interface TktSaleRuleMapper {
    TktSaleRuleMapper INSTANCE = Mappers.getMapper(TktSaleRuleMapper.class);

    // DO -> Model
    TktSaleRule convert(TktSaleRuleDO dataObject);

    // QueryParam -> QueryArg
    TktSaleRuleQueryArg convert(TktSaleRuleQueryParam param);

} 