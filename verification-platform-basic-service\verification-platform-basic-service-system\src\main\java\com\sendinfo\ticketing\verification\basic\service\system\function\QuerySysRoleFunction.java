package com.sendinfo.ticketing.verification.basic.service.system.function;

import com.sendinfo.ticketing.verification.basic.api.system.request.SysRoleQueryRequest;
import com.sendinfo.ticketing.verification.basic.component.system.SysRoleReadComponent;
import com.sendinfo.ticketing.verification.basic.component.system.converter.SysRoleConverter;
import com.sendinfo.ticketing.verification.basic.model.system.SysRole;
import com.sendinfo.ticketing.verification.basic.model.system.error.SystemErrorDef;
import com.sendinfo.ticketing.verification.basic.service.system.enums.SystemAttachmentKey;
import com.sendinfo.ticketing.verification.common.service.function.QuerySingleByIdFunction;
import com.sendinfo.ticketing.verification.common.service.stereotype.Function;
import com.sendinfo.ticketing.verification.common.service.support.QuerySingleByIdLogicAction;
import com.sendinfo.ticketing.verification.flow.Hint;
import com.sendinfo.ticketing.verification.flow.Question;
import com.sendinfo.ticketing.verification.flow.function.LogicAction;
import lombok.Getter;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 系统角色查询功能
 * <AUTHOR>
 */
@Getter
@Function("querySysRoleFunction")
public class QuerySysRoleFunction implements QuerySingleByIdFunction<Long> {

    private final SysRoleReadComponent sysRoleReadComponent;
    private final SysRoleConverter converter;

    private final LogicAction<Long> querySingleById;

    public QuerySysRoleFunction(SysRoleReadComponent sysRoleReadComponent, SysRoleConverter converter) {
        this.sysRoleReadComponent = sysRoleReadComponent;
        this.converter = converter;
        querySingleById = new QuerySingleByIdLogicAction<>(this.sysRoleReadComponent,
                SystemAttachmentKey.SYS_ROLE_ATTACHMENT_KEY, SystemErrorDef.QUERY_SYS_ROLE_ERROR);
    }

    /**
     * 查询系统角色列表
     *
     * @param question 查询参数
     * @return Hint
     */
    public Hint querySysRoles(Question<SysRoleQueryRequest> question) {
        SysRoleQueryRequest req = question.getBody();
        List<SysRole> sysRoleList = sysRoleReadComponent.querySysRoles(converter.r_r2p(req));
        question.setAttachment(SystemAttachmentKey.SYS_ROLE_DATA_LIST_ATTACHMENT_KEY, sysRoleList);

        return Hint.gotoNext();
    }
} 