package com.sendinfo.ticketing.verification.basic.component.goods;

import com.sendinfo.ticketing.verification.basic.component.goods.param.GoodsPropertyQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.GoodsProperty;
import com.sendinfo.ticketing.verification.common.component.ReadComponent;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-07-18 16:13
 */
public interface GoodsPropertyReadComponent extends ReadComponent<Long, GoodsPropertyQueryParam, GoodsProperty> {

    /**
     * 批量查询票型ID的票型扩展信息
     * @param ticketIds
     * @param corpCode
     * @return
     */
    List<GoodsProperty> queryByTicketIds(List<Long> ticketIds, String corpCode);
}
