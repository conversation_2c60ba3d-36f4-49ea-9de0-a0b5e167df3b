package com.sendinfo.ticketing.verification.basic.repository.goods.dataobject;

import com.sendinfo.ticketing.verification.common.repository.dataobject.AbstractTenantBaseDO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@ToString(callSuper = true)
public class TicketCalendarPriceDO extends AbstractTenantBaseDO<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 票型ID
     */
    private Long ticketId;

    /**
     * 销售价格
     */
    private BigDecimal price;

    /**
     * 成本价
     */
    private BigDecimal costPrice;

    /**
     * 票型税额 =销售价/（1+税率）*税率
     */
    private BigDecimal ticketTax;

    /**
     * 票面打印价
     */
    private BigDecimal printPrice;

    /**
     * 开始时间
     */
    private Date beginTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 国际星期格式：1，2，3，4，5，6，7
     */
    private String weeDay;

    /**
     * 备注
     */
    private String remark;
}
