package com.sendinfo.ticketing.verification.basic.api.infra.request;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 系统参数值创建请求
 *
 * <AUTHOR> 2025-05-20 15:40:00
 */
@Getter
@Setter
@ToString
public class SysParamValueCreateRequest implements Serializable {
    private static final long serialVersionUID = -7654321098765432102L;

    /**
     * 功能模块编码(菜单编码)
     */
    @NotBlank(message = "功能模块编码不能为空")
    @Size(max = 128, message = "功能模块编码长度不能超过128")
    private String moduleCode;

    /**
     * 组件编码
     */
    @NotBlank(message = "组件编码不能为空")
    @Size(max = 64, message = "组件编码长度不能超过64")
    private String componentCode;

    /**
     * 分组编码
     */
    @Size(max = 64, message = "分组编码长度不能超过64")
    private String groupCode;

    /**
     * 参数全路径标识: 菜单modelCode.组件componentCode.参数编码paramCode
     */
    @NotBlank(message = "参数全路径标识不能为空")
    @Size(max = 256, message = "参数全路径标识长度不能超过256")
    private String paramKey;

    /**
     * 参数编码
     */
    @NotBlank(message = "参数编码不能为空")
    @Size(max = 100, message = "参数编码长度不能超过100")
    private String paramCode;

    /**
     * 企业编码
     */
    @NotBlank(message = "企业编码不能为空")
    @Size(max = 50, message = "企业编码长度不能超过50")
    private String corpCode;

    /**
     * 参数值
     */
    @NotBlank(message = "参数值不能为空")
    @Size(max = 2000, message = "参数值长度不能超过2000")
    private String paramValue;

    /**
     * 租户企业编码 (0001代表全局默认值)
     */
    @NotBlank(message = "租户企业编码不能为空")
    @Size(max = 64, message = "租户企业编码长度不能超过64")
    private String tenantCorpCode;

    /**
     * 创建人
     */
    @NotBlank(message = "创建人不能为空")
    private String createBy;

    private int status;
}