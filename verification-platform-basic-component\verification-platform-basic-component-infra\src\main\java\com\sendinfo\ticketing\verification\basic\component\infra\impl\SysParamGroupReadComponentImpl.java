package com.sendinfo.ticketing.verification.basic.component.infra.impl;

import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.List;

import com.sendinfo.ticketing.verification.basic.component.infra.SysParamGroupReadComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.converter.SysParamGroupConverter;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamGroupQueryParam;
import com.sendinfo.ticketing.verification.basic.model.infra.SysParamGroup;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamGroupQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.SysParamGroupDao;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.SysParamGroupDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedCountRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedPageRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleRead;

/**
 * 系统参数分组查询组件实现
 * 
 * <AUTHOR> 2025-07-22 11:45:32
 */
@Component("sysParamGroupReadComponent")
@Getter
public class SysParamGroupReadComponentImpl
        implements SysParamGroupReadComponent,
        DaoBasedPageRead<Long, SysParamGroupQueryParam, SysParamGroup, SysParamGroupDO, SysParamGroupQueryArg>,
        DaoBasedSingleRead<Long, SysParamGroupQueryParam, SysParamGroup, SysParamGroupDO>,
        DaoBasedCountRead<Long, SysParamGroupQueryParam, SysParamGroup, SysParamGroupQueryArg> {

    private final SysParamGroupDao dao;
    private final SysParamGroupConverter converter;

    public SysParamGroupReadComponentImpl(SysParamGroupDao dao, SysParamGroupConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }

    @Override
    public SysParamGroup queryByGroupCode(String groupCode, String corpCode) {
        SysParamGroupDO dataObject = dao.queryByGroupCode(groupCode, corpCode);
        return dataObject != null ? converter.r_d2m(dataObject) : null;
    }

    @Override
    public List<SysParamGroup> queryListByModuleCode(String moduleCode, String corpCode) {
        List<SysParamGroupDO> dataObjects = dao.queryListByModuleCode(moduleCode, corpCode);
        return converter.r_ds2ms(dataObjects);
    }
}
