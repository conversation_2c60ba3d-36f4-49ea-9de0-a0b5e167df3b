package com.sendinfo.ticketing.verification.basic.repository.pay.arg;

import com.sendinfo.ticketing.verification.common.repository.arg.AbstractQueryArg;
import com.sendinfo.ticketing.verification.common.repository.arg.Pageable;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 支付标签查询参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class SysPaySetQueryArg extends AbstractQueryArg implements Pageable {
    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 支付平台编码
     */
    private String payTypeCode;

    /**
     * 启用状态
     */
    private String useFlag;

    /**
     * 主体ID
     */
    private Long mainbodyId;

    /**
     * 支付类型（主扫，被扫，现金）
     */
    private String payType;

    /**
     * 自定义支付名称
     */
    private String payName;

    /**
     * 支付标签名称
     */
    private String paylableName;

    /**
     * 售票员默认收款方式
     */
    private String payDefault;

    /**
     * 分页偏移量
     */
    private Integer offset;

    /**
     * 分页大小
     */
    private Integer limit;
} 