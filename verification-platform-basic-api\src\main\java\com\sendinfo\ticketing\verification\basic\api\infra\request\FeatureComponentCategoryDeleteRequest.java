package com.sendinfo.ticketing.verification.basic.api.infra.request;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 组件分类删除请求
 * 
 * <AUTHOR> 2025-07-22 11:45:32
 */
@Getter
@Setter
@ToString
public class FeatureComponentCategoryDeleteRequest implements Serializable {
    private static final long serialVersionUID = -7654321098765432104L;

    /**
     * 企业编码
     */
    @NotBlank(message = "企业编码不能为空")
    private String corpCode;

    /**
     * 主键ID
     */
    @NotNull(message = "ID不能为空")
    private Long id;

    /**
     * 修改人
     */
    @NotNull(message = "修改人不能为空")
    private String modifyBy;
}
