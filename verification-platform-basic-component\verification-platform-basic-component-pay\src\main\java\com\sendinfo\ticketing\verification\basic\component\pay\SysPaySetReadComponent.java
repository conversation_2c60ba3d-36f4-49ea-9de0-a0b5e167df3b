package com.sendinfo.ticketing.verification.basic.component.pay;

import com.sendinfo.ticketing.verification.basic.component.pay.param.SysPaySetQueryParam;
import com.sendinfo.ticketing.verification.basic.model.pay.SysPaySet;
import com.sendinfo.ticketing.verification.common.component.ReadComponent;

import java.util.List;
import java.util.Set;

/**
 * 支付标签读取组件接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface SysPaySetReadComponent extends ReadComponent<Long, SysPaySetQueryParam, SysPaySet> {

    /**
     * 查询启用的支付标签
     *
     * @param payType  支付类型
     * @param idSet    主键集合
     * @param corpCode 企业编码
     * @return 启用的支付标签
     */
    List<SysPaySet> queryEnableListByPayTypeAndId(String payType, Set<Long> idSet, String corpCode);
}