package com.sendinfo.ticketing.verification.basic.service.system;

import com.alibaba.fastjson.JSON;
import com.sendinfo.ticketing.verification.basic.api.system.SysRoleReadService;
import com.sendinfo.ticketing.verification.basic.api.system.request.SysRoleQueryRequest;
import com.sendinfo.ticketing.verification.basic.model.system.SysRole;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.apache.dubbo.config.bootstrap.DubboBootstrap;

import java.util.List;

/**
 * 系统角色读取服务客户端测试
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/4 16:20
 */
public class SysRoleReadClientTest {

	public static void main(String[] args) {
		// 配置注册中心
		RegistryConfig registryConfig = new RegistryConfig();
		registryConfig.setAddress("nacos://testnacos.sendinfocs.com:8848?namespace=bb13d33c-bda0-4552-91c3-173151d43186");

		// 配置DubboBootstrap
		DubboBootstrap bootstrap = DubboBootstrap.getInstance()
				.application("sys-role-read-client")
				.registry(registryConfig);

		// 创建服务引用
		ReferenceConfig<SysRoleReadService> referenceConfig = new ReferenceConfig<>();
		referenceConfig.setInterface(SysRoleReadService.class);
		referenceConfig.setVersion("1.0.0");
		referenceConfig.setTimeout(10000);
		referenceConfig.setCheck(false);

		bootstrap.reference(referenceConfig);
		bootstrap.start();

		// 获取服务代理
		SysRoleReadService sysRoleReadService = referenceConfig.get();

		// 构造请求参数
		SysRoleQueryRequest queryRequest = new SysRoleQueryRequest();
		queryRequest.setCorpCode("TEST_CORP");
		queryRequest.setRoleName("测试角色");
		queryRequest.setAccType("1");
		queryRequest.setUseFlag("T");
		queryRequest.setDeptParentId(1);
		queryRequest.setDeptLevel("1");

		// 调用远程服务
		try {
			ResultModel<List<SysRole>> resultModel = sysRoleReadService.querySysRoles(queryRequest);
			if (resultModel == null) {
				System.out.println("调用失败：返回结果为null");
				return;
			}
			if (resultModel.isSuccess()) {
				System.out.println("调用成功：");
				System.out.println("返回数据：" + JSON.toJSONString(resultModel.getModel()));
				System.out.println("数据条数：" + (resultModel.getModel() != null ? resultModel.getModel().size() : 0));
			} else {
				System.out.println("调用失败：" + resultModel.getErrorMessage());
				System.out.println("错误代码：" + resultModel.getErrorCode());
			}
		} catch (Exception e) {
			System.err.println("调用异常：" + e.getMessage());
			e.printStackTrace();
		} finally {
			// 清理资源
			bootstrap.stop();
		}
	}
} 