<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sendinfo.ticketing.verification.basic.repository.pay.dao.PayChanleDao">

    <resultMap id="BaseResultMap" type="com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.PayChanleDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="CORP_CODE" property="corpCode" jdbcType="VARCHAR"/>
        <result column="PRODUCT_ID" property="productId" jdbcType="BIGINT"/>
        <result column="CHANLE_NAME" property="chanleName" jdbcType="VARCHAR"/>
        <result column="CHANLE_CODE" property="chanleCode" jdbcType="VARCHAR"/>
        <result column="PAY_PRODUCT_CODE" property="payProductCode" jdbcType="VARCHAR"/>
        <result column="REMARK" property="remark" jdbcType="VARCHAR"/>
        <result column="CREATE_BY" property="createBy" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="MODIFY_BY" property="modifyBy" jdbcType="VARCHAR"/>
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="DELETED" property="deleted" jdbcType="VARCHAR"/>
        <result column="USE_FLAG" property="useFlag" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="tableName">pay_chanle</sql>

    <sql id="allColumns">
        id, CORP_CODE, PRODUCT_ID, CHANLE_NAME, CHANLE_CODE, PAY_PRODUCT_CODE, REMARK,
        CREATE_BY, CREATE_TIME, MODIFY_BY, MODIFY_TIME, DELETED, USE_FLAG
    </sql>

    <sql id="insertColumns">
        CORP_CODE, PRODUCT_ID, CHANLE_NAME, CHANLE_CODE, PAY_PRODUCT_CODE, REMARK,
        CREATE_BY, MODIFY_BY, DELETED, USE_FLAG,
        CREATE_TIME, MODIFY_TIME
    </sql>

    <insert id="insert" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.PayChanleDO" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO <include refid="tableName"/> (<include refid="insertColumns"/>)
        VALUES (
            #{corpCode,jdbcType=VARCHAR}, #{productId,jdbcType=BIGINT}, #{chanleName,jdbcType=VARCHAR},
            #{chanleCode,jdbcType=VARCHAR}, #{payProductCode,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
            #{createBy,jdbcType=VARCHAR}, #{modifyBy,jdbcType=VARCHAR}, #{deleted,jdbcType=VARCHAR}, #{useFlag,jdbcType=VARCHAR},
            NOW(), NOW()
        )
    </insert>

    <select id="queryById" resultMap="BaseResultMap" parameterType="map">
        SELECT <include refid="allColumns"/>
        FROM <include refid="tableName"/>
        WHERE id = #{id,jdbcType=BIGINT}
          AND deleted = 'F'
    </select>

    <update id="updateByArg" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.arg.PayChanleUpdateArg">
        UPDATE <include refid="tableName"/>
        <set>
            <if test="productId != null">PRODUCT_ID = #{productId,jdbcType=BIGINT},</if>
            <if test="chanleName != null and chanleName != ''">CHANLE_NAME = #{chanleName,jdbcType=VARCHAR},</if>
            <if test="chanleCode != null and chanleCode != ''">CHANLE_CODE = #{chanleCode,jdbcType=VARCHAR},</if>
            <if test="payProductCode != null and payProductCode != ''">PAY_PRODUCT_CODE = #{payProductCode,jdbcType=VARCHAR},</if>
            <if test="remark != null">REMARK = #{remark,jdbcType=VARCHAR},</if>
            <if test="useFlag != null">USE_FLAG = #{useFlag,jdbcType=VARCHAR},</if>
            <if test="useFlagUpdater != null and useFlagUpdater.target != null">USE_FLAG = #{useFlagUpdater.target,jdbcType=VARCHAR},</if>
            <if test="modifyBy != null and modifyBy != ''">MODIFY_BY = #{modifyBy,jdbcType=VARCHAR},</if>
            MODIFY_TIME = NOW()
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
          AND CORP_CODE = #{corpCode,jdbcType=VARCHAR}
          AND deleted = 'F'
            <if test="useFlagUpdater != null and useFlagUpdater.current != null">
                AND USE_FLAG = #{useFlagUpdater.current,jdbcType=VARCHAR}
            </if>
    </update>

    <update id="softDeleteByArg" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.arg.PayChanleDeleteArg">
        UPDATE <include refid="tableName"/>
        SET deleted = 'T',
            <choose>
                <when test="modifyBy != null and modifyBy != ''">MODIFY_BY = #{modifyBy,jdbcType=VARCHAR},</when>
                <otherwise>MODIFY_BY = 'SYSTEM_SDA',</otherwise>
            </choose>
            MODIFY_TIME = NOW()
        WHERE CORP_CODE = #{corpCode,jdbcType=VARCHAR}
          AND deleted = 'F'
          <if test="id != null">AND id = #{id,jdbcType=BIGINT}</if>
    </update>

    <update id="deleteById" parameterType="java.lang.Long">
        UPDATE <include refid="tableName"/>
        SET deleted = 'T',
            MODIFY_BY = 'SYSTEM_DBI',
            MODIFY_TIME = NOW()
        WHERE id = #{id,jdbcType=BIGINT}
          AND deleted = 'F'
    </update>

    <select id="countByArg" resultType="int" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.arg.PayChanleQueryArg">
        SELECT COUNT(*)
        FROM <include refid="tableName"/>
        <where>
            CORP_CODE = #{corpCode,jdbcType=VARCHAR}
            AND deleted = 'F'
            <if test="productId != null">AND PRODUCT_ID = #{productId,jdbcType=BIGINT}</if>
            <if test="chanleCode != null and chanleCode != ''">AND CHANLE_CODE = #{chanleCode,jdbcType=VARCHAR}</if>
            <if test="chanleName != null and chanleName != ''">AND CHANLE_NAME LIKE CONCAT('%', #{chanleName,jdbcType=VARCHAR}, '%')</if>
            <if test="payProductCode != null and payProductCode != ''">AND PAY_PRODUCT_CODE = #{payProductCode,jdbcType=VARCHAR}</if>
            <if test="useFlag != null">AND USE_FLAG = #{useFlag,jdbcType=VARCHAR}</if>
        </where>
    </select>

    <select id="queryByArg" resultMap="BaseResultMap" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.arg.PayChanleQueryArg">
        SELECT <include refid="allColumns"/>
        FROM <include refid="tableName"/>
        <where>
            CORP_CODE = #{corpCode,jdbcType=VARCHAR}
            AND deleted = 'F'
            <if test="productId != null">AND PRODUCT_ID = #{productId,jdbcType=BIGINT}</if>
            <if test="chanleCode != null and chanleCode != ''">AND CHANLE_CODE = #{chanleCode,jdbcType=VARCHAR}</if>
            <if test="chanleName != null and chanleName != ''">AND CHANLE_NAME LIKE CONCAT('%', #{chanleName,jdbcType=VARCHAR}, '%')</if>
            <if test="payProductCode != null and payProductCode != ''">AND PAY_PRODUCT_CODE = #{payProductCode,jdbcType=VARCHAR}</if>
            <if test="useFlag != null">AND USE_FLAG = #{useFlag,jdbcType=VARCHAR}</if>
        </where>
        ORDER BY id DESC
        <if test="limit != null and offset != null">
            LIMIT #{offset,jdbcType=INTEGER}, #{limit,jdbcType=INTEGER}
        </if>
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO <include refid="tableName"/> (<include refid="insertColumns"/>)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.corpCode,jdbcType=VARCHAR}, #{item.productId,jdbcType=BIGINT}, #{item.chanleName,jdbcType=VARCHAR},
                #{item.chanleCode,jdbcType=VARCHAR}, #{item.payProductCode,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR},
                #{item.createBy,jdbcType=VARCHAR}, #{item.modifyBy,jdbcType=VARCHAR}, #{item.deleted,jdbcType=VARCHAR}, #{item.useFlag,jdbcType=VARCHAR},
                NOW(), NOW()
            )
        </foreach>
    </insert>

</mapper> 