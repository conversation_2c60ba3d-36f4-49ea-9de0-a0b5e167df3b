package com.sendinfo.ticketing.verification.basic.api.system.request;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Auther: chengjie.li
 * @Date: 2025/6/23 16:55
 */
@Getter
@Setter
public class DictQueryByCodeRequest implements Serializable {
    private static final long serialVersionUID = -7723557999986994694L;

    /**
     * 字典编码
     */
    @NotNull
    private String dictCode;


    /**
     * 企业编码
     */
    @NotNull
    private String corpCode;


    /**
     * 字典类型
     */
    @NotNull
    private Integer dictType;
}
