package com.sendinfo.ticketing.verification.basic.service.auth.cache.impl;

import com.alibaba.fastjson2.JSON;
import com.sendinfo.paas.cache.client.api.Cache;
import com.sendinfo.paas.cache.client.api.CacheResult;
import com.sendinfo.ticketing.verification.basic.exception.VerificationBizRuntimeException;
import com.sendinfo.ticketing.verification.basic.model.auth.UserSessionData;
import com.sendinfo.ticketing.verification.basic.model.auth.enums.JwtTokenSource;
import com.sendinfo.ticketing.verification.basic.model.auth.error.AuthErrorDef;
import com.sendinfo.ticketing.verification.basic.service.auth.cache.UserSessionDataCacheService;
import com.sendinfo.ticketing.verification.basic.service.auth.config.OldPwJwtConfigurationProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025-07-23 15:08:22
 */
@Service("userSessionDataCacheService")
@Slf4j
public class UserSessionDataCacheServiceImpl implements UserSessionDataCacheService {

    private final Cache cache;

//    static final DefaultIdStrategy STRATEGY = new DefaultIdStrategy(IdStrategy.DEFAULT_FLAGS
//            | IdStrategy.PRESERVE_NULL_ELEMENTS
//            | IdStrategy.MORPH_COLLECTION_INTERFACES
//            | IdStrategy.MORPH_MAP_INTERFACES
//            | IdStrategy.MORPH_NON_FINAL_POJOS);

    private final OldPwJwtConfigurationProperties oldPwJwtConfigurationProperties;

    private final static String OLD_USER_SESSION_DATA_PREFIX = "migration.onlineuser";
    private final static String MANAGE_TOKEN_JWT_TOKEN_PREFIX = "migration.manageToken";
    private final static String CLIENT_TOKEN_JWT_TOKEN_PREFIX = "migration.clientToken";

    public UserSessionDataCacheServiceImpl(@Qualifier("oldPwCache") Cache cache,
                                           OldPwJwtConfigurationProperties oldPwJwtConfigurationProperties) {
        this.cache = cache;
        this.oldPwJwtConfigurationProperties = oldPwJwtConfigurationProperties;
    }

    @Override
    public UserSessionData getUserSessionByUserIdFromOldPw(String expectedToken, String userId, JwtTokenSource source) {
        String actualToken = getCacheJWTToken(userId, source);
        if (!StringUtils.equalsIgnoreCase(actualToken, expectedToken)) {
            log.error("token does not match actual token. actual token: {}, expected token: {}", actualToken, expectedToken);
            throw new VerificationBizRuntimeException(AuthErrorDef.CACHE_JWT_TOKEN_NOT_EXIST);
        }

        String onlineUserValue = String.format("%s.%s",
                OLD_USER_SESSION_DATA_PREFIX, userId);
        CacheResult<byte[]> cacheResult = cache.sync().get(onlineUserValue);
        if (cacheResult.isSuccess() && cacheResult.getData() != null) {
            return JSON.parseObject(cacheResult.getData(), UserSessionData.class);
        } else {
            log.error("get user session data is empty. {}", actualToken);
            throw new VerificationBizRuntimeException(AuthErrorDef.CACHE_JWT_TOKEN_NOT_EXIST);
        }
    }


    public String getCacheJWTToken(String userId, JwtTokenSource source) {
        String key = null;
        switch (source) {
            case OLD_PW_CLIENT:
                key = CLIENT_TOKEN_JWT_TOKEN_PREFIX + "." + userId;
                break;
            case OLD_PW_MANAGEMENT:
                key = MANAGE_TOKEN_JWT_TOKEN_PREFIX + "." + userId;
                break;
            default:
                throw new IllegalArgumentException("Unsupported JwtTokenSource: " + source);
        }

        CacheResult<byte[]> cacheResult = cache.sync().get(key);
        if (cacheResult.isSuccess() && cacheResult.getData() != null) {
            return JSON.parseObject(cacheResult.getData(), String.class);
        } else {
            String errorMessage = cacheResult.getError() == null ? "Unknown" : cacheResult.getError().toString();
            log.error("getCacheJWTToken failed. key = {}, errorMessage = {}", key, errorMessage);
            throw new VerificationBizRuntimeException(AuthErrorDef.CACHE_JWT_TOKEN_NOT_EXIST);
        }
    }

}
