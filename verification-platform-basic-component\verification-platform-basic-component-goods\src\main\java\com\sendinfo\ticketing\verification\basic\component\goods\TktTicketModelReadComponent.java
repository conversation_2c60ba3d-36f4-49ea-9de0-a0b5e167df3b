package com.sendinfo.ticketing.verification.basic.component.goods;

import com.sendinfo.ticketing.verification.basic.component.goods.param.TktTicketModelQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TktTicketModel;
import com.sendinfo.ticketing.verification.common.component.ReadComponent;

import java.util.List;
import java.util.Set;

/**
 * @Auther: chengjie.li
 * @Date: 2025/6/10 20:58
 */
public interface TktTicketModelReadComponent extends ReadComponent<Long, TktTicketModelQueryParam, TktTicketModel> {

    /**
     * 根据企业编码和票型编码查询票型
     *
     * @param ticketCode 票型编码
     * @param corpCode   企业编码
     * @return 匹配的票型信息
     */
    TktTicketModel queryTicketModelByCode(String ticketCode, String corpCode);

    /**
     * 根据票型编码查询票型
     *
     * @param ticketCodes 票型编码集合
     * @param corpCode    企业编码
     * @return 票型集合
     */
    List<TktTicketModel> batchQueryTicketModelByCodes(Set<String> ticketCodes, String corpCode);
}
