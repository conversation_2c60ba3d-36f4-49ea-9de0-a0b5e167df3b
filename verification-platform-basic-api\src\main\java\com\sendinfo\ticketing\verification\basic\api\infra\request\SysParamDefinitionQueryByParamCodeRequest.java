/**
 * 根据参数编码查询系统参数定义请求
 *
 * <AUTHOR> 2025-08-01
 */
package com.sendinfo.ticketing.verification.basic.api.infra.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

@Getter
@Setter
@ToString
public class SysParamDefinitionQueryByParamCodeRequest implements Serializable {
    private static final long serialVersionUID = -7654321098765432111L;

    /**
     * 参数编码
     */
    @NotBlank(message = "参数编码不能为空")
    @Size(max = 100, message = "参数编码长度不能超过100")
    private String paramCode;

    /**
     * 企业编码
     */
    @NotBlank(message = "企业编码不能为空")
    @Size(max = 64, message = "企业编码长度不能超过64")
    private String corpCode;
}
