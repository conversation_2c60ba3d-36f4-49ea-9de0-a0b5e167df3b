package com.sendinfo.ticketing.verification.basic.service.goods.impl;

import com.sendinfo.ticketing.verification.basic.api.goods.TktModelParamReadService;
import com.sendinfo.ticketing.verification.basic.api.goods.request.TktGroupInfoQueryRequest;
import com.sendinfo.ticketing.verification.basic.exception.TicketPlatformBizRuntimeException;
import com.sendinfo.ticketing.verification.basic.model.goods.TktGroupInfo;
import com.sendinfo.ticketing.verification.basic.model.goods.TktModelParam;
import com.sendinfo.ticketing.verification.basic.model.goods.error.GoodsErrorDef;
import com.sendinfo.ticketing.verification.basic.service.goods.enums.GoodsAttachmentKey;
import com.sendinfo.ticketing.verification.basic.service.goods.function.QueryTktModelParamFunction;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;
import com.sendinfo.ticketing.verification.common.api.result.Results;
import com.sendinfo.ticketing.verification.common.service.support.ApiCommonFailedBiActions;
import com.sendinfo.ticketing.verification.flow.FlowAgentBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/7/28 18:28
 **/
@Slf4j
@Service("tktModelParamReadService")
public class TktModelParamReadServiceImpl implements TktModelParamReadService {

    private final FlowAgentBuilder flowAgentBuilder;
    private final QueryTktModelParamFunction queryTktModelParamFunction;

    public TktModelParamReadServiceImpl(FlowAgentBuilder flowAgentBuilder, QueryTktModelParamFunction queryTktModelParamFunction) {
        this.flowAgentBuilder = flowAgentBuilder;
        this.queryTktModelParamFunction = queryTktModelParamFunction;
    }

    @Override
    public ResultModel<List<TktModelParam>> batchQueryTicketModelParamByIds(List<Long> ids, String corpCode) {
        return flowAgentBuilder.<Pair<List<Long>,String>, ResultModel<List<TktModelParam>>>validateThenChooseBuilder()
                .appendLogicAction(queryTktModelParamFunction::batchQueryTktModelParam)
                .withSuccessfulAction(q -> {
                    List<TktModelParam> tktModelParams = q.getAttachment(GoodsAttachmentKey.TKT_MODEL_PARAM_DATA_LIST_ATTACHMENT_KEY);
                    return Results.success(tktModelParams);
                })
                .withFailedBiAction(ApiCommonFailedBiActions::reasonMappingToResult)
                .withExceptionBiAction((q, th) -> {
                    log.error("[TktModelParamReadServiceImpl] batchQueryTicketModelParamByIds error.question is :{}", q, th);
                    return Results.fail(GoodsErrorDef.QUERY_TKT_MODEL_PARAM_ERROR);
                })
                .rethrowException(TicketPlatformBizRuntimeException.class::isInstance)
                .build()
                .prompt(Pair.of(ids, corpCode))
                .getResult();
    }
}
