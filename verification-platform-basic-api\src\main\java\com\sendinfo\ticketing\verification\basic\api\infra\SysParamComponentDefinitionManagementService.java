package com.sendinfo.ticketing.verification.basic.api.infra;

import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamComponentDefinitionUpdateRequest;
import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamComponentDefinitionStatusSwitchRequest;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

/**
 * 系统参数组件定义管理服务
 */
public interface SysParamComponentDefinitionManagementService {

    /**
     * 更新系统参数组件定义
     *
     * @param request 更新请求
     * @return 操作结果
     */
    ResultModel<Boolean> updateSysParamComponentDefinition(SysParamComponentDefinitionUpdateRequest request);

    /**
     * 启用系统参数组件定义
     *
     * @param request 状态切换请求
     * @return 操作结果
     */
    ResultModel<Boolean> enableSysParamComponentDefinition(SysParamComponentDefinitionStatusSwitchRequest request);

    /**
     * 禁用系统参数组件定义
     *
     * @param request 状态切换请求
     * @return 操作结果
     */
    ResultModel<Boolean> disableSysParamComponentDefinition(SysParamComponentDefinitionStatusSwitchRequest request);
}
