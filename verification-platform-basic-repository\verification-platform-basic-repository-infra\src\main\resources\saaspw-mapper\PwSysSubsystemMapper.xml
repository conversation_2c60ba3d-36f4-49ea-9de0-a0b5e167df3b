<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sendinfo.ticketing.verification.basic.repository.infra.dao.PwSysSubsystemDao">

    <!-- ResultMap -->
    <resultMap id="BaseResultMap" type="com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.PwSysSubsystemDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="corp_code" property="corpCode" jdbcType="VARCHAR"/>
        <result column="apply_model" property="applyModel" jdbcType="VARCHAR"/>
        <result column="apply_name" property="applyName" jdbcType="VARCHAR"/>
        <result column="apply_desc" property="applyDesc" jdbcType="VARCHAR"/>
        <result column="use_flag" property="useFlag" jdbcType="VARCHAR"/>
        <result column="sort_no" property="sortNo" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="modify_by" property="modifyBy" jdbcType="VARCHAR"/>
        <result column="deleted" property="deleted" jdbcType="VARCHAR"/>
        <result column="apply_code" property="applyCode" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 表名 -->
    <sql id="tableName">sys_subsystem</sql>

    <!-- Reusable Column List -->
    <sql id="columns">
        `id`,
        `corp_code`,
        `apply_model`,
        `apply_name`,
        `apply_desc`,
        `use_flag`,
        `sort_no`,
        `create_time`,
        `modify_time`,
        `create_by`,
        `modify_by`,
        `deleted`,
        `apply_code`
    </sql>

    <!-- queryAllInUseList -->
    <select id="queryAllInUseList" resultMap="BaseResultMap">
        select
        <include refid="columns"/>
        from <include refid="tableName"/>
        where `use_flag` = 'T'
        and `deleted` = 'F'
        and corp_code = #{corpCode,jdbcType=VARCHAR}
        order by `sort_no` asc, `id` asc
    </select>

    <!-- Custom queryById with corpCode -->
    <select id="queryById" resultMap="BaseResultMap">
        select
        <include refid="columns"/>
        from <include refid="tableName"/>
        where `id` = #{id,jdbcType=BIGINT}
        and `deleted` = 'F'
    </select>

    <!-- insert -->
    <insert id="insert" parameterType="com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.PwSysSubsystemDO"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into <include refid="tableName"/> (
            `corp_code`,`apply_model`,`apply_name`,`apply_desc`,
            `use_flag`,`sort_no`,`create_time`,`create_by`,
            `modify_by`,`deleted`,`apply_code`
        )
        values (
            #{corpCode,jdbcType=VARCHAR},
            #{applyModel,jdbcType=VARCHAR},
            #{applyName,jdbcType=VARCHAR},
            #{applyDesc,jdbcType=VARCHAR},
            #{useFlag,jdbcType=VARCHAR},
            #{sortNo,jdbcType=INTEGER},
            now(),
            #{createBy,jdbcType=VARCHAR},
            #{modifyBy,jdbcType=VARCHAR},
            #{deleted,jdbcType=VARCHAR},
            #{applyCode,jdbcType=VARCHAR}
        )
    </insert>



    <!-- updateByArg -->
    <update id="updateByArg" parameterType="com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwSysSubsystemUpdateArg">
        update <include refid="tableName"/>
        <set>
            <if test="subsystemName != null">
                `apply_name` = #{subsystemName,jdbcType=VARCHAR},
            </if>
            <if test="subsystemCode != null">
                `apply_code` = #{subsystemCode,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                `use_flag` = #{status,jdbcType=VARCHAR},
            </if>
            <if test="sortOrder != null">
                `sort_no` = #{sortOrder,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                `apply_desc` = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="modifyBy != null">
                `modify_by` = #{modifyBy,jdbcType=VARCHAR},
            </if>
            `modify_time` = CURRENT_TIMESTAMP
        </set>
        where `id` = #{id,jdbcType=BIGINT}
        and `deleted` = 'F'
    </update>

    <!-- softDeleteByArg -->
    <update id="softDeleteByArg" parameterType="com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwSysSubsystemDeleteArg">
        update <include refid="tableName"/>
        set `deleted` = 'T',
            `modify_by` = #{modifyBy,jdbcType=VARCHAR},
            `modify_time` = now()
        where `id` = #{id,jdbcType=BIGINT}
    </update>

    <!-- deleteByArg (物理删除) -->
    <delete id="deleteByArg" parameterType="com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwSysSubsystemDeleteArg">
        DELETE FROM <include refid="tableName"/>
        where `id` = #{id,jdbcType=BIGINT}
    </delete>

    <!-- countByArg -->
    <select id="countByArg" resultType="java.lang.Integer" parameterType="com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwSysSubsystemQueryArg">
        select count(1)
        from <include refid="tableName"/>
        <where>
            and `deleted` = 'F'
            <if test="corpCode != null">
                and `corp_code` = #{corpCode,jdbcType=VARCHAR}
            </if>
            <if test="applyModel != null">
                and `apply_model` = #{applyModel,jdbcType=VARCHAR}
            </if>
            <if test="applyName != null">
                and `apply_name` like concat('%', #{applyName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="useFlag != null">
                and `use_flag` = #{useFlag,jdbcType=VARCHAR}
            </if>
            <if test="applyCode != null">
                and `apply_code` = #{applyCode,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <!-- queryByArg -->
    <select id="queryByArg" resultMap="BaseResultMap" parameterType="com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwSysSubsystemQueryArg">
        select
        <include refid="columns"/>
        from <include refid="tableName"/>
        <where>
            and `deleted` = 'F'
            <if test="corpCode != null">
                and `corp_code` = #{corpCode,jdbcType=VARCHAR}
            </if>
            <if test="applyModel != null">
                and `apply_model` = #{applyModel,jdbcType=VARCHAR}
            </if>
            <if test="applyName != null">
                and `apply_name` like concat('%', #{applyName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="useFlag != null">
                and `use_flag` = #{useFlag,jdbcType=VARCHAR}
            </if>
            <if test="applyCode != null">
                and `apply_code` = #{applyCode,jdbcType=VARCHAR}
            </if>
        </where>
        <if test="sortItems != null and !sortItems.isEmpty()">
            order by
            <foreach collection="sortItems" separator="," item="sortItem">
                ${sortItem.column} ${sortItem.sortType}
            </foreach>
        </if>
        <if test="sortItems == null or sortItems.isEmpty()">
            order by `sort_no` asc, `id` asc
        </if>
        <if test="offset != null and limit != null">
            limit #{offset}, #{limit}
        </if>
    </select>

</mapper>