package com.sendinfo.ticketing.verification.basic.component.goods.converter;

import com.sendinfo.ticketing.verification.basic.api.goods.request.ExplainerServiceCategoryQueryRequest;
import com.sendinfo.ticketing.verification.basic.component.goods.mapper.ExplainerServiceCategoryMapper;
import com.sendinfo.ticketing.verification.basic.component.goods.param.ExplainerServiceCategoryCreateParam;
import com.sendinfo.ticketing.verification.basic.component.goods.param.ExplainerServiceCategoryQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.ExplainerServiceCategory;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.ExplainerServiceCategoryQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.ExplainerServiceCategoryDO;
import com.sendinfo.ticketing.verification.common.component.converter.ReadDo2ModelConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadParam2ArgConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadReq2ParamConverter;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 服务类目转换器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("explainerServiceCategoryConverter")
public class ExplainerServiceCategoryConverter implements
        ReadDo2ModelConverter<ExplainerServiceCategoryDO, ExplainerServiceCategory>,
        ReadParam2ArgConverter<ExplainerServiceCategoryQueryParam, ExplainerServiceCategoryQueryArg>,
        ReadReq2ParamConverter<ExplainerServiceCategoryQueryRequest, ExplainerServiceCategoryQueryParam> {

    /**
     * 数据对象转模型对象
     *
     * @param dataObject 数据对象
     * @return 模型对象
     */
    @Override
    public ExplainerServiceCategory r_d2m(ExplainerServiceCategoryDO dataObject) {
        return ExplainerServiceCategoryMapper.INSTANCE.convert(dataObject);
    }

    @Override
    public List<ExplainerServiceCategory> r_ds2ms(List<ExplainerServiceCategoryDO> dataObjects) {
        return dataObjects.stream()
                .map(ExplainerServiceCategoryMapper.INSTANCE::convert)
                .collect(Collectors.toList());
    }

    /**
     * 查询参数转查询Arg
     *
     * @param param 查询参数
     * @return 查询Arg
     */
    @Override
    public ExplainerServiceCategoryQueryArg r_p2a(ExplainerServiceCategoryQueryParam param) {
        return ExplainerServiceCategoryMapper.INSTANCE.convert(param);
    }

    /**
     * 创建参数转数据对象
     *
     * @param createParam 创建参数
     * @return 数据对象
     */
    public ExplainerServiceCategoryDO c_p2d(ExplainerServiceCategoryCreateParam createParam) {
        return ExplainerServiceCategoryMapper.INSTANCE.convert(createParam);
    }

    @Override
    public ExplainerServiceCategoryQueryParam r_r2p(ExplainerServiceCategoryQueryRequest queryRequest) {
        return ExplainerServiceCategoryMapper.INSTANCE.convert(queryRequest);
    }
}