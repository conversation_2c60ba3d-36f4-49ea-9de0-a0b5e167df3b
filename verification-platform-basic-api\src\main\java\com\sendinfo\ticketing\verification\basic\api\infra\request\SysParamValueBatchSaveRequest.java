package com.sendinfo.ticketing.verification.basic.api.infra.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> 2025-07-29 02:24:54
 */
@Getter
@Setter
@ToString
public class SysParamValueBatchSaveRequest implements Serializable {


    private static final long serialVersionUID = -7862511705830994610L;
    /**
     * 企业编码
     */
    @NotBlank(message = "企业编码不能为空")
    private String corpCode;


    /**
     * 系统参数值保存请求列表
     */
    @NotEmpty(message = "系统参数值保存请求列表不能为空")
    private List<SysParamValueSaveRequest> sysParamValueSaveRequests;

    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空")
    private String operator;
}
