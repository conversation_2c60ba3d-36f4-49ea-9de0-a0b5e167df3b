package com.sendinfo.ticketing.verification.basic.component.customer.impl;

import com.sendinfo.ticketing.verification.basic.component.customer.SalesmanRelationTravelReadComponent;
import com.sendinfo.ticketing.verification.basic.component.customer.converter.SalesmanRelationTravelConverter;
import com.sendinfo.ticketing.verification.basic.component.customer.param.SalesmanRelationTravelQueryParam;
import com.sendinfo.ticketing.verification.basic.model.customer.SalesmanRelationTravel;
import com.sendinfo.ticketing.verification.basic.repository.customer.arg.SalesmanRelationTravelQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.customer.dao.SalesmanRelationTravelDao;
import com.sendinfo.ticketing.verification.basic.repository.customer.dataobject.SalesmanRelationTravelDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedCountRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedListRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleRead;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 业务员关联客户读取组件实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("salesmanRelationTravelReadComponent")
@Getter
public class SalesmanRelationTravelReadComponentImpl implements
        SalesmanRelationTravelReadComponent,
        DaoBasedSingleRead<Long, SalesmanRelationTravelQueryParam, SalesmanRelationTravel, SalesmanRelationTravelDO>,
        DaoBasedListRead<Long, SalesmanRelationTravelQueryParam, SalesmanRelationTravel, SalesmanRelationTravelDO, SalesmanRelationTravelQueryArg>,
        DaoBasedCountRead<Long, SalesmanRelationTravelQueryParam, SalesmanRelationTravel, SalesmanRelationTravelQueryArg> {

    private final SalesmanRelationTravelDao dao;
    private final SalesmanRelationTravelConverter converter;

    public SalesmanRelationTravelReadComponentImpl(SalesmanRelationTravelDao dao, SalesmanRelationTravelConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }

    @Override
    public List<SalesmanRelationTravel> queryByTravelId(Long travelId, String corpCode) {
        List<SalesmanRelationTravelDO> dataObjects = dao.queryByTravelId(travelId, corpCode);
        return converter.r_ds2ms(dataObjects);
    }
} 