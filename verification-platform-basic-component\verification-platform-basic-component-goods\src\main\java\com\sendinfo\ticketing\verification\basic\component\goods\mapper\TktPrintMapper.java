package com.sendinfo.ticketing.verification.basic.component.goods.mapper;

import com.sendinfo.ticketing.verification.basic.api.goods.request.TktPrintQueryRequest;
import com.sendinfo.ticketing.verification.basic.component.goods.param.TktPrintQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TktPrint;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TktPrintQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TktPrintDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 打印设置对象映射器
 * <AUTHOR>
 */
@Mapper
public interface TktPrintMapper {

    TktPrintMapper INSTANCE = Mappers.getMapper(TktPrintMapper.class);

    /**
     * convert TktPrintDO to TktPrint
     * @param dataObject
     * @return TktPrint
     */
    TktPrint convert(TktPrintDO dataObject);

    /**
     * convert TktPrintQueryParam to TktPrintQueryArg
     *
     * @param param
     * @return  TktPrintQueryArg
     */
    TktPrintQueryArg convert(TktPrintQueryParam param);

    /**
     * convert TktPrintQueryRequest to TktPrintQueryParam
     *
     * @param condition
     * @return  TktPrintQueryParam
     */
    TktPrintQueryParam convert(TktPrintQueryRequest condition);
} 