package com.sendinfo.ticketing.verification.basic.api.infra.request;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 系统参数分组更新请求
 * 
 * <AUTHOR> 2025-07-22 11:45:32
 */
@Getter
@Setter
@ToString
public class SysParamGroupUpdateRequest implements Serializable {
    private static final long serialVersionUID = -7654321098765432103L;

    /**
     * 参数分组ID
     */
    @NotNull(message = "参数分组ID不能为空")
    private Long id;

    /**
     * 企业编码
     */
    @NotBlank(message = "企业编码不能为空")
    private String corpCode;

    /**
     * 分组名称
     */
    @Size(max = 128, message = "分组名称长度不能超过128")
    private String groupName;

    /**
     * 分组描述
     */
    @Size(max = 256, message = "分组描述长度不能超过256")
    private String description;

    /**
     * 排序
     */
    private Integer sortNo;

    /**
     * 修改人
     */
    private String modifyBy;
}
