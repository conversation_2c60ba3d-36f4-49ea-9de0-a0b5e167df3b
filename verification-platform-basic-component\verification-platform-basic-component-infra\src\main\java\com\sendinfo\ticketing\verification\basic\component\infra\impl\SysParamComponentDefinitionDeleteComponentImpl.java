package com.sendinfo.ticketing.verification.basic.component.infra.impl;

import lombok.Getter;
import org.springframework.stereotype.Component;

import com.sendinfo.ticketing.verification.basic.component.infra.SysParamComponentDefinitionDeleteComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.converter.SysParamComponentDefinitionConverter;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamComponentDefinitionDeleteParam;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamComponentDefinitionDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.SysParamComponentDefinitionDao;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleDelete;

/**
 * 系统参数组件定义删除组件实现
 */
@Component("sysParamComponentDefinitionDeleteComponent")
@Getter
public class SysParamComponentDefinitionDeleteComponentImpl
        implements SysParamComponentDefinitionDeleteComponent,
        DaoBasedSingleDelete<Long, SysParamComponentDefinitionDeleteParam, SysParamComponentDefinitionDeleteArg> {

    private final SysParamComponentDefinitionDao dao;
    private final SysParamComponentDefinitionConverter converter;

    public SysParamComponentDefinitionDeleteComponentImpl(SysParamComponentDefinitionDao dao, SysParamComponentDefinitionConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }
}
