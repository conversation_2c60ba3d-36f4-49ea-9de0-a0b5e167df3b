package com.sendinfo.ticketing.verification.basic.api.goods.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Set;

/**
 * @Auther: chengjie.li
 * @Date: 2025/6/11 18:06
 */
@Getter
@Setter
@ToString(callSuper = true)
public class TktSaleRuleBatchQueryByTicketIdsRequest implements Serializable {
    private static final long serialVersionUID = 4061943398867945106L;

    /**
     * 票型ID
     */
    @NotEmpty
    private Set<Long> ticketIds;

    /**
     * 企业编码
     */
    @NotNull
    private String corpCode;
}
