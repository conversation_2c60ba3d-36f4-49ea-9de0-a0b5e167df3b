package com.sendinfo.ticketing.verification.basic.component.customer.param;

import com.sendinfo.ticketing.verification.common.component.param.AbstractDeleteParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 导游删除参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class GuideDeleteParam extends AbstractDeleteParam {

    /**
     * 主键ID
     */
    private Long id;
} 