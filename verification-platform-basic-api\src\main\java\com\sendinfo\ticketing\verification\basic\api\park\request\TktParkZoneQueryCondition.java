package com.sendinfo.ticketing.verification.basic.api.park.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2025/6/8 16:12
 **/
@Getter
@Setter
@ToString(callSuper = true)
public class TktParkZoneQueryCondition implements Serializable {
    private static final long serialVersionUID = 2165024584789457864L;
    @NotNull(message = "corpCode不能为空")
    /** 租户标识，必填，确保租户数据隔离 */
    private String corpCode;
    /** 父景区ID */
    private Long parkParentId;
    /** 景区编号 */
    private String parkCode;
    /** 景区名称模糊查询 */
    private String parkNameLike;
    /** 景区名称 */
    private String parkName;
    /** 景区类型（数据字典：第一级景点、售票点、检票点、售检票点） */
    private String parkType;
    /** 是否分时景点 T是 F否 */
    private String tsParkzone;
    /** 景区简称 */
    private String abbreviation;
    /** 负责人 */
    private String principal;
    /** 部门电话 */
    private String departmentTel;
    /** 传真 */
    private String fax;
    /** 启用标志:0不启用（默认），1:启用 */
    private Integer useFlag;
    /** 排序 */
    private Integer sortNum;
    /** 备注 */
    private String remark;
    /** 点位标价 */
    private BigDecimal parkPrice;
    /** 是否不检票直接入园T/F */
    private String checkIn;
    /** 不检票检票类型1人证比对2单身份证 */
    private String checkType;
    /** 检查健康码 */
    private String checkHealth;
    /** 分成点经营类型 */
    private String divBusinessType;
    /** 点位标签 逗号分隔 */
    private String pointsLabel;
    /** 检票时间间隔 分钟为单位 */
    private Integer checkTimeInterval;
    /** 放行次数 */
    private Integer letPassCount;
    /** 联动查询可检 */
    private String reactCheck;
    /** 联动查询可检类型 */
    private Integer reactCheckType;
    /** 联动查询可检景点 */
    private Long reactCheckPark;
    /** 检票点类型 0入口 1出口 2游玩景点 */
    private String parkCheckType;
    /** 是否子母票检票景点 T是 F否 */
    private String childMotherTicketPark;
    /** 日夜场开关 T：开启 F:关闭 */
    private String dayNigthFlag;
    /** 日场开始时间 */
    private String dayStartTime;
    /** 日场结束时间 */
    private String dayEndTime;
    /** 夜场开始时间 */
    private String nigthStartTime;
    /** 夜场结束时间 */
    private String nigthEndTime;
    /** 端类型 1：pc端 2：移动端 */
    private Integer terminalType;
    /** 部门ID */
    private Long deptId;
    /** 外部编码 */
    private String outParkCode;
    /** 迁移数据id与type */
    private String transferParam;
    /** 检票时间间隔 */
    private Integer checkTimeOrderInterval;
    /** 关联卡号与景区 */
    private String cardCodeWithPark;
    /** 检票时间间隔内是否允许检票默认F */
    private String allowCheckFlag;
    /** 是否关联车船号：0：不关联 1：关联 */
    private String carShipNoFlag;
    /** 车船号编码 */
    private String carShipNoCode;
    /** 0开园1闭园 */
    private Integer printShow;
    /** 语音提示 0：默认语音 1:按票型授权设置 */
    private Integer playSoundWay;
    /** 是否需要授权码授权 0:不授权 1:按票型授权设置 */
    private Integer userAuthWay;
}
