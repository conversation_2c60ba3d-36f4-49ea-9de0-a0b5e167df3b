package com.sendinfo.ticketing.verification.basic.component.customer;

import com.sendinfo.ticketing.verification.basic.component.customer.param.DirectorInfoQueryParam;
import com.sendinfo.ticketing.verification.basic.model.customer.DirectorInfo;
import com.sendinfo.ticketing.verification.common.component.ReadComponent;

import java.util.List;

/**
 * 主管信息读取组件接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface DirectorInfoReadComponent extends ReadComponent<Long, DirectorInfoQueryParam, DirectorInfo> {

    /**
     * 根据旅行社ID查询启用的主管信息
     *
     * @param travelId 旅行社ID
     * @param corpCode 企业编码
     * @return 主管信息列表
     */
    List<DirectorInfo> queryByTravelId(Long travelId, String corpCode);
} 