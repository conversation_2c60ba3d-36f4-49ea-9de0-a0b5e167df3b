package com.sendinfo.ticketing.verification.basic.api.infra.request;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.PositiveOrZero;
import javax.validation.constraints.Size;
import java.io.Serializable;

import com.sendinfo.ticketing.verification.basic.enums.CommonStatusEnum;

/**
 * 系统参数定义更新请求
 */
@Getter
@Setter
@ToString
public class SysParamDefinitionUpdateRequest implements Serializable {
    private static final long serialVersionUID = -6543210987654321098L;
    /**
     * 企业编码
     */
    @NotBlank(message = "企业编码不能为空")
    private String corpCode;
    /**
     * 主键ID
     */
    @NotNull(message = "ID不能为空")
    private Long id;

    /**
     * 参数名称
     */
    @Size(max = 64, message = "参数名称长度不能超过64")
    private String paramName;

    /**
     * 参数描述
     */
    @Size(max = 512, message = "参数描述长度不能超过512")
    private String description;

    /**
     * 数据类型 {@link com.sendinfo.ticketing.verification.basic.enums.SysParamDataType}
     */
    @Size(max = 16, message = "数据类型长度不能超过16")
    private String dataType;

    /**
     * 校验规则
     */
    @Size(max = 1024, message = "校验规则长度不能超过1024")
    private String validationRules;

    /**
     * 参数值声明，定义了参数值范围、默认值 (根据定义表的data_type存储)
     */
    @Size(max = 1024, message = "参数值声明长度不能超过1024")
    private String paramValueStatement;

    /**
     * 状态：1-启用，0-禁用
     */
    private Integer status;

    /**
     * 修改人
     */
    @NotNull(message = "修改人不能为空")
    private String modifyBy;
}