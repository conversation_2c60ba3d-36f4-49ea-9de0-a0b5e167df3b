package com.sendinfo.ticketing.verification.basic.repository.pay.arg;

import com.sendinfo.ticketing.verification.common.repository.arg.AbstractDeleteArg;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 商户渠道配置删除参数
 * 用于封装mainbody_chanle表的删除条件，支持租户隔离和软删除
 * 仅支持软删除操作，不提供物理删除功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class MainbodyChanleDeleteArg extends AbstractDeleteArg {
    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 修改人
     */
    private String modifyBy;
} 