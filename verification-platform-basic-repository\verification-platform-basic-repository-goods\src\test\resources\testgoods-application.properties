## H2 DataSource
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.url=jdbc:h2:mem:testdb;MODE=MySQL;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;DATABASE_TO_LOWER=TRUE;CASE_INSENSITIVE_IDENTIFIERS=TRUE
spring.datasource.username=sa
spring.datasource.password=
## SQL initialization
spring.sql.init.schema-locations=classpath:/create-tables.sql
spring.sql.init.mode=always
## H2 Console
spring.h2.console.enabled=true
spring.h2.console.mediumConvertPath=/h2-console
sendinfo-paas-rds.client.enable=false