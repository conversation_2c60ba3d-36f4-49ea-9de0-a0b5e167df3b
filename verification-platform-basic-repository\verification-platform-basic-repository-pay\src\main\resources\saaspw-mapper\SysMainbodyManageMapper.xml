<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sendinfo.ticketing.verification.basic.repository.pay.dao.SysMainbodyManageDao">

    <resultMap id="BaseResultMap" type="com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysMainbodyManageDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="corp_code" property="corpCode" jdbcType="VARCHAR"/>
        <result column="MAINBODY_NAME" property="mainbodyName" jdbcType="VARCHAR"/>
        <result column="MAINBODY_SHORT_NAME" property="mainbodyShortName" jdbcType="VARCHAR"/>
        <result column="MERCHANT_NO" property="merchantNo" jdbcType="VARCHAR"/>
        <result column="MERCHANT_SOURCE_NO" property="merchantSourceNo" jdbcType="VARCHAR"/>
        <result column="PAY_CHANNEL_NAME" property="payChannelName" jdbcType="VARCHAR"/>
        <result column="PAY_CHANNEL_CODE" property="payChannelCode" jdbcType="VARCHAR"/>
        <result column="PAYCENTER_PUBLIC_KEY" property="paycenterPublicKey" jdbcType="VARCHAR"/>
        <result column="MCH_PRIVATE_KEY" property="mchPrivateKey" jdbcType="VARCHAR"/>
        <result column="USE_FLAG" property="useFlag" jdbcType="VARCHAR"/>
        <result column="LINK_MAN" property="linkMan" jdbcType="VARCHAR"/>
        <result column="link_tel" property="linkTel" jdbcType="VARCHAR"/>
        <result column="LOGO" property="logo" jdbcType="VARCHAR"/>
        <result column="REMARK" property="remark" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="CREATE_BY" property="createBy" jdbcType="VARCHAR"/>
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="MODIFY_BY" property="modifyBy" jdbcType="VARCHAR"/>
        <result column="DELETED" property="deleted" jdbcType="VARCHAR"/>
        <result column="MAINBODY_ID" property="mainbodyId" jdbcType="BIGINT"/>
        <result column="MAINBODY_NUMBER" property="mainbodyNumber" jdbcType="VARCHAR"/>
        <result column="SEETLEMENT_MERCHANT_NO" property="seettlementMerchantNo" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="tableName">sys_mainbody_manage</sql>

    <sql id="allColumns">
        id, corp_code, MAINBODY_NAME, MAINBODY_SHORT_NAME, MERCHANT_NO, MERCHANT_SOURCE_NO,
        PAY_CHANNEL_NAME, PAY_CHANNEL_CODE, PAYCENTER_PUBLIC_KEY, MCH_PRIVATE_KEY, USE_FLAG,
        LINK_MAN, link_tel, LOGO, REMARK, CREATE_TIME, CREATE_BY, MODIFY_TIME, MODIFY_BY,
        DELETED, MAINBODY_ID, MAINBODY_NUMBER, SEETLEMENT_MERCHANT_NO
    </sql>

    <sql id="insertColumns">
        corp_code, MAINBODY_NAME, MAINBODY_SHORT_NAME, MERCHANT_NO, MERCHANT_SOURCE_NO,
        PAY_CHANNEL_NAME, PAY_CHANNEL_CODE, PAYCENTER_PUBLIC_KEY, MCH_PRIVATE_KEY, USE_FLAG,
        LINK_MAN, link_tel, LOGO, REMARK, CREATE_BY, MODIFY_BY, DELETED, MAINBODY_ID,
        MAINBODY_NUMBER, SEETLEMENT_MERCHANT_NO, CREATE_TIME, MODIFY_TIME
    </sql>

    <insert id="insert" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysMainbodyManageDO" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO <include refid="tableName"/> (<include refid="insertColumns"/>)
        VALUES (
            #{corpCode,jdbcType=VARCHAR}, #{mainbodyName,jdbcType=VARCHAR}, #{mainbodyShortName,jdbcType=VARCHAR},
            #{merchantNo,jdbcType=VARCHAR}, #{merchantSourceNo,jdbcType=VARCHAR}, #{payChannelName,jdbcType=VARCHAR},
            #{payChannelCode,jdbcType=VARCHAR}, #{paycenterPublicKey,jdbcType=VARCHAR}, #{mchPrivateKey,jdbcType=VARCHAR},
            #{useFlag,jdbcType=VARCHAR}, #{linkMan,jdbcType=VARCHAR}, #{linkTel,jdbcType=VARCHAR},
            #{logo,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR},
            #{modifyBy,jdbcType=VARCHAR}, #{deleted,jdbcType=VARCHAR}, #{mainbodyId,jdbcType=BIGINT},
            #{mainbodyNumber,jdbcType=VARCHAR}, #{seettlementMerchantNo,jdbcType=VARCHAR}, NOW(), NOW()
        )
    </insert>

    <select id="queryById" resultMap="BaseResultMap" parameterType="map">
        SELECT <include refid="allColumns"/>
        FROM <include refid="tableName"/>
        WHERE id = #{id,jdbcType=BIGINT}
          AND deleted = 'F'
    </select>

    <update id="updateByArg" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageUpdateArg">
        UPDATE <include refid="tableName"/>
        <set>
            <if test="merchantNo != null and merchantNo != ''">MERCHANT_NO = #{merchantNo,jdbcType=VARCHAR},</if>
            <if test="merchantSourceNo != null">MERCHANT_SOURCE_NO = #{merchantSourceNo,jdbcType=VARCHAR},</if>
            <if test="payChannelName != null and payChannelName != ''">PAY_CHANNEL_NAME = #{payChannelName,jdbcType=VARCHAR},</if>
            <if test="payChannelCode != null and payChannelCode != ''">PAY_CHANNEL_CODE = #{payChannelCode,jdbcType=VARCHAR},</if>
            <if test="paycenterPublicKey != null">PAYCENTER_PUBLIC_KEY = #{paycenterPublicKey,jdbcType=VARCHAR},</if>
            <if test="mchPrivateKey != null">MCH_PRIVATE_KEY = #{mchPrivateKey,jdbcType=VARCHAR},</if>
            <if test="useFlag != null">USE_FLAG = #{useFlag,jdbcType=VARCHAR},</if>
            <if test="useFlagUpdater != null and useFlagUpdater.target != null">USE_FLAG = #{useFlagUpdater.target,jdbcType=VARCHAR},</if>
            <if test="logo != null">LOGO = #{logo,jdbcType=VARCHAR},</if>
            <if test="remark != null">REMARK = #{remark,jdbcType=VARCHAR},</if>
            <if test="seettlementMerchantNo != null">SEETLEMENT_MERCHANT_NO = #{seettlementMerchantNo,jdbcType=VARCHAR},</if>
            <if test="modifyBy != null and modifyBy != ''">MODIFY_BY = #{modifyBy,jdbcType=VARCHAR},</if>
            MODIFY_TIME = NOW()
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
          AND CORP_CODE = #{corpCode,jdbcType=VARCHAR}
          AND deleted = 'F'
            <if test="useFlagUpdater != null and useFlagUpdater.current != null">
                AND USE_FLAG = #{useFlagUpdater.current,jdbcType=VARCHAR}
            </if>
    </update>

    <update id="softDeleteByArg" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageDeleteArg">
        UPDATE <include refid="tableName"/>
        SET deleted = 'T',
            <choose>
                <when test="modifyBy != null and modifyBy != ''">MODIFY_BY = #{modifyBy,jdbcType=VARCHAR},</when>
                <otherwise>MODIFY_BY = 'SYSTEM_SDA',</otherwise>
            </choose>
            MODIFY_TIME = NOW()
        WHERE CORP_CODE = #{corpCode,jdbcType=VARCHAR}
          AND deleted = 'F'
          <if test="id != null">AND id = #{id,jdbcType=BIGINT}</if>
    </update>

    <update id="deleteById" parameterType="java.lang.Long">
        UPDATE <include refid="tableName"/>
        SET deleted = 'T',
            MODIFY_BY = 'SYSTEM_DBI',
            MODIFY_TIME = NOW()
        WHERE id = #{id,jdbcType=BIGINT}
          AND deleted = 'F'
    </update>

    <select id="countByArg" resultType="int" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageQueryArg">
        SELECT COUNT(*)
        FROM <include refid="tableName"/>
        <where>
            CORP_CODE = #{corpCode,jdbcType=VARCHAR}
            AND deleted = 'F'
            <if test="mainbodyId != null">AND MAINBODY_ID = #{mainbodyId,jdbcType=BIGINT}</if>
            <if test="merchantNo != null and merchantNo != ''">AND MERCHANT_NO = #{merchantNo,jdbcType=VARCHAR}</if>
            <if test="mainbodyName != null and mainbodyName != ''">AND MAINBODY_NAME LIKE CONCAT('%', #{mainbodyName,jdbcType=VARCHAR}, '%')</if>
            <if test="mainbodyShortName != null and mainbodyShortName != ''">AND MAINBODY_SHORT_NAME LIKE CONCAT('%', #{mainbodyShortName,jdbcType=VARCHAR}, '%')</if>
            <if test="payChannelName != null and payChannelName != ''">AND PAY_CHANNEL_NAME LIKE CONCAT('%', #{payChannelName,jdbcType=VARCHAR}, '%')</if>
            <if test="payChannelCode != null and payChannelCode != ''">AND PAY_CHANNEL_CODE = #{payChannelCode,jdbcType=VARCHAR}</if>
            <if test="useFlag != null">AND USE_FLAG = #{useFlag,jdbcType=VARCHAR}</if>
            <if test="linkMan != null and linkMan != ''">AND LINK_MAN LIKE CONCAT('%', #{linkMan,jdbcType=VARCHAR}, '%')</if>
            <if test="linkTel != null and linkTel != ''">AND link_tel = #{linkTel,jdbcType=VARCHAR}</if>
            <if test="mainbodyNumber != null and mainbodyNumber != ''">AND MAINBODY_NUMBER = #{mainbodyNumber,jdbcType=VARCHAR}</if>
        </where>
    </select>

    <select id="queryByArg" resultMap="BaseResultMap" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageQueryArg">
        SELECT <include refid="allColumns"/>
        FROM <include refid="tableName"/>
        <where>
            CORP_CODE = #{corpCode,jdbcType=VARCHAR}
            AND deleted = 'F'
            <if test="mainbodyId != null">AND MAINBODY_ID = #{mainbodyId,jdbcType=BIGINT}</if>
            <if test="merchantNo != null and merchantNo != ''">AND MERCHANT_NO = #{merchantNo,jdbcType=VARCHAR}</if>
            <if test="mainbodyName != null and mainbodyName != ''">AND MAINBODY_NAME LIKE CONCAT('%', #{mainbodyName,jdbcType=VARCHAR}, '%')</if>
            <if test="mainbodyShortName != null and mainbodyShortName != ''">AND MAINBODY_SHORT_NAME LIKE CONCAT('%', #{mainbodyShortName,jdbcType=VARCHAR}, '%')</if>
            <if test="payChannelName != null and payChannelName != ''">AND PAY_CHANNEL_NAME LIKE CONCAT('%', #{payChannelName,jdbcType=VARCHAR}, '%')</if>
            <if test="payChannelCode != null and payChannelCode != ''">AND PAY_CHANNEL_CODE = #{payChannelCode,jdbcType=VARCHAR}</if>
            <if test="useFlag != null">AND USE_FLAG = #{useFlag,jdbcType=VARCHAR}</if>
            <if test="linkMan != null and linkMan != ''">AND LINK_MAN LIKE CONCAT('%', #{linkMan,jdbcType=VARCHAR}, '%')</if>
            <if test="linkTel != null and linkTel != ''">AND link_tel = #{linkTel,jdbcType=VARCHAR}</if>
            <if test="mainbodyNumber != null and mainbodyNumber != ''">AND MAINBODY_NUMBER = #{mainbodyNumber,jdbcType=VARCHAR}</if>
        </where>
        <if test="sortItems != null and !sortItems.isEmpty()">
            ORDER BY
            <foreach collection="sortItems" item="sortItem" separator=",">
                `${sortItem.column}` ${sortItem.sortType}
            </foreach>
        </if>
        <if test="limit != null and offset != null">
            LIMIT #{offset,jdbcType=INTEGER}, #{limit,jdbcType=INTEGER}
        </if>
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO <include refid="tableName"/> (<include refid="insertColumns"/>)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.corpCode,jdbcType=VARCHAR}, #{item.mainbodyName,jdbcType=VARCHAR}, #{item.mainbodyShortName,jdbcType=VARCHAR},
                #{item.merchantNo,jdbcType=VARCHAR}, #{item.merchantSourceNo,jdbcType=VARCHAR}, #{item.payChannelName,jdbcType=VARCHAR},
                #{item.payChannelCode,jdbcType=VARCHAR}, #{item.paycenterPublicKey,jdbcType=VARCHAR}, #{item.mchPrivateKey,jdbcType=VARCHAR},
                #{item.useFlag,jdbcType=VARCHAR}, #{item.linkMan,jdbcType=VARCHAR}, #{item.linkTel,jdbcType=VARCHAR},
                #{item.logo,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, #{item.createBy,jdbcType=VARCHAR},
                #{item.modifyBy,jdbcType=VARCHAR}, #{item.deleted,jdbcType=VARCHAR}, #{item.mainbodyId,jdbcType=BIGINT},
                #{item.mainbodyNumber,jdbcType=VARCHAR}, #{item.seettlementMerchantNo,jdbcType=VARCHAR}, NOW(), NOW()
            )
        </foreach>
    </insert>

    <select id="queryByMainbodyId" resultMap="BaseResultMap">
        SELECT <include refid="allColumns"/>
        FROM <include refid="tableName"/>
        WHERE deleted = 'F'
          AND corp_code = #{corpCode,jdbcType=VARCHAR}
          AND MAINBODY_ID = #{mainbodyId,jdbcType=BIGINT}
    </select>

</mapper> 