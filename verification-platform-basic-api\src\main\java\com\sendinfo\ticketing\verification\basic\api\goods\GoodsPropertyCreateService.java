package com.sendinfo.ticketing.verification.basic.api.goods;

import com.sendinfo.ticketing.verification.basic.api.goods.request.GoodsPropertyCreateRequest;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-07-18 17:38
 */
public interface GoodsPropertyCreateService {

    /**
     * 单个保存
     *
     * @param request 新增参数请求对象
     * @return 保存对象的ID
     */
    ResultModel<Long> singleSave(GoodsPropertyCreateRequest request);

    /**
     * 批量保存
     *
     * @param requestList 新增参数请求对象列表
     * @return 批量保存成功数量
     */
    ResultModel<Long> batchSave(List<GoodsPropertyCreateRequest> requestList);
}
