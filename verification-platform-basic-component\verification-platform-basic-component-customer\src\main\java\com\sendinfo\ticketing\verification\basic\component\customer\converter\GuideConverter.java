package com.sendinfo.ticketing.verification.basic.component.customer.converter;

import com.sendinfo.ticketing.verification.basic.component.customer.mapper.GuideMapper;
import com.sendinfo.ticketing.verification.basic.component.customer.param.GuideQueryParam;
import com.sendinfo.ticketing.verification.basic.model.customer.Guide;
import com.sendinfo.ticketing.verification.basic.repository.customer.arg.GuideQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.customer.dataobject.GuideDO;
import com.sendinfo.ticketing.verification.common.component.converter.ReadDo2ModelConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadParam2ArgConverter;
import org.springframework.stereotype.Component;

/**
 * 导游转换器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("guideConverter")
public class GuideConverter implements ReadDo2ModelConverter<GuideDO, Guide>,
        ReadParam2ArgConverter<GuideQueryParam, GuideQueryArg> {
    @Override
    public Guide r_d2m(GuideDO dataObject) {
        return GuideMapper.INSTANCE.convert(dataObject);
    }

    @Override
    public GuideQueryArg r_p2a(GuideQueryParam param) {
        return GuideMapper.INSTANCE.convert(param);
    }
}