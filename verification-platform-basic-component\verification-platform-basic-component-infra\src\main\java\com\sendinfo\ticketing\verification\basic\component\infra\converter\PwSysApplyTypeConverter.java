package com.sendinfo.ticketing.verification.basic.component.infra.converter;

import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.List;

import com.sendinfo.ticketing.verification.basic.component.infra.mapper.PwSysApplyTypeMapper;
import com.sendinfo.ticketing.verification.basic.model.infra.PwSysApplyType;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.PwSysApplyTypeDO;
import com.sendinfo.ticketing.verification.common.component.converter.ReadDo2ModelConverter;

/**
 * 系统应用类型转换器
 * 
 * <AUTHOR> 2025-07-24 15:40:00
 */
@Component
@Getter
public class PwSysApplyTypeConverter implements 
        ReadDo2ModelConverter<PwSysApplyTypeDO, PwSysApplyType> {


    @Override
    public PwSysApplyType r_d2m(PwSysApplyTypeDO dataObject) {
        return PwSysApplyTypeMapper.INSTANCE.convert(dataObject);
    }

    @Override
    public List<PwSysApplyType> r_ds2ms(List<PwSysApplyTypeDO> dataObjects) {
        return ReadDo2ModelConverter.super.r_ds2ms(dataObjects);
    }

}