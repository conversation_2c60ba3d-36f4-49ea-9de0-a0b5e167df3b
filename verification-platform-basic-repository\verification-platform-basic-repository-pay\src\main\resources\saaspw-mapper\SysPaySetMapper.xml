<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sendinfo.ticketing.verification.basic.repository.pay.dao.SysPaySetDao">

    <resultMap id="BaseResultMap" type="com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysPaySetDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="corp_code" property="corpCode" jdbcType="VARCHAR"/>
        <result column="PAYLABLE_NAME" property="paylableName" jdbcType="VARCHAR"/>
        <result column="PAY_TYPE_CODE" property="payTypeCode" jdbcType="VARCHAR"/>
        <result column="PAYLABLE_ICON" property="paylableIcon" jdbcType="VARCHAR"/>
        <result column="USE_FLAG" property="useFlag" jdbcType="VARCHAR"/>
        <result column="REMARK" property="remark" jdbcType="VARCHAR"/>
        <result column="CREATE_BY" property="createBy" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="MODIFY_BY" property="modifyBy" jdbcType="VARCHAR"/>
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="DELETED" property="deleted" jdbcType="VARCHAR"/>
        <result column="PAY_DEFAULT" property="payDefault" jdbcType="VARCHAR"/>
        <result column="MAINBODY_ID" property="mainbodyId" jdbcType="BIGINT"/>
        <result column="PAY_TYPE" property="payType" jdbcType="VARCHAR"/>
        <result column="PAY_NAME" property="payName" jdbcType="VARCHAR"/>
        <result column="transfer_param" property="transferParam" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="tableName">sys_pay_set</sql>

    <sql id="allColumns">
        id, corp_code, PAYLABLE_NAME, PAY_TYPE_CODE, PAYLABLE_ICON, USE_FLAG, REMARK,
        CREATE_BY, CREATE_TIME, MODIFY_BY, MODIFY_TIME, DELETED, PAY_DEFAULT, MAINBODY_ID, PAY_TYPE, PAY_NAME, transfer_param
    </sql>

    <sql id="insertColumns">
        corp_code, PAYLABLE_NAME, PAY_TYPE_CODE, PAYLABLE_ICON, USE_FLAG, REMARK,
        CREATE_BY, MODIFY_BY, DELETED, PAY_DEFAULT, MAINBODY_ID, PAY_TYPE, PAY_NAME, transfer_param,
        CREATE_TIME, MODIFY_TIME
    </sql>

    <insert id="insert" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysPaySetDO" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO <include refid="tableName"/> (<include refid="insertColumns"/>)
        VALUES (
            #{corpCode,jdbcType=VARCHAR}, #{paylableName,jdbcType=VARCHAR}, #{payTypeCode,jdbcType=VARCHAR},
            #{paylableIcon,jdbcType=VARCHAR}, #{useFlag,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
            #{createBy,jdbcType=VARCHAR}, #{modifyBy,jdbcType=VARCHAR}, #{deleted,jdbcType=VARCHAR}, #{payDefault,jdbcType=VARCHAR},
            #{mainbodyId,jdbcType=BIGINT}, #{payType,jdbcType=VARCHAR}, #{payName,jdbcType=VARCHAR}, #{transferParam,jdbcType=VARCHAR},
            NOW(), NOW()
        )
    </insert>

    <select id="queryById" resultMap="BaseResultMap" parameterType="map">
        SELECT <include refid="allColumns"/>
        FROM <include refid="tableName"/>
        WHERE id = #{id,jdbcType=BIGINT}
          AND deleted = 'F'
    </select>

    <update id="updateByArg" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysPaySetUpdateArg">
        UPDATE <include refid="tableName"/>
        <set>
            <if test="paylableIcon != null and paylableIcon != ''">PAYLABLE_ICON = #{paylableIcon,jdbcType=VARCHAR},</if>
            <if test="useFlag != null">USE_FLAG = #{useFlag,jdbcType=VARCHAR},</if>
            <if test="useFlagUpdater != null and useFlagUpdater.target != null">USE_FLAG = #{useFlagUpdater.target,jdbcType=VARCHAR},</if>
            <if test="remark != null">REMARK = #{remark,jdbcType=VARCHAR},</if>
            <if test="payDefault != null">PAY_DEFAULT = #{payDefault,jdbcType=VARCHAR},</if>
            <if test="payDefaultUpdater != null and payDefaultUpdater.target != null">PAY_DEFAULT = #{payDefaultUpdater.target,jdbcType=VARCHAR},</if>
            <if test="mainbodyId != null">MAINBODY_ID = #{mainbodyId,jdbcType=BIGINT},</if>
            <if test="payType != null and payType != ''">PAY_TYPE = #{payType,jdbcType=VARCHAR},</if>
            <if test="payName != null and payName != ''">PAY_NAME = #{payName,jdbcType=VARCHAR},</if>
            <if test="transferParam != null">transfer_param = #{transferParam,jdbcType=VARCHAR},</if>
            <if test="modifyBy != null and modifyBy != ''">MODIFY_BY = #{modifyBy,jdbcType=VARCHAR},</if>
            MODIFY_TIME = NOW()
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
          AND corp_code = #{corpCode,jdbcType=VARCHAR}
          AND deleted = 'F'
            <if test="useFlagUpdater != null and useFlagUpdater.current != null">
                AND USE_FLAG = #{useFlagUpdater.current,jdbcType=VARCHAR}
            </if>
            <if test="payDefaultUpdater != null and payDefaultUpdater.current != null">
                AND PAY_DEFAULT = #{payDefaultUpdater.current,jdbcType=VARCHAR}
            </if>
    </update>

    <update id="softDeleteByArg" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysPaySetDeleteArg">
        UPDATE <include refid="tableName"/>
        SET deleted = 'T',
            <choose>
                <when test="modifyBy != null and modifyBy != ''">MODIFY_BY = #{modifyBy,jdbcType=VARCHAR},</when>
                <otherwise>MODIFY_BY = 'SYSTEM_SDA',</otherwise>
            </choose>
            MODIFY_TIME = NOW()
        WHERE corp_code = #{corpCode,jdbcType=VARCHAR}
          AND deleted = 'F'
          <if test="id != null">AND id = #{id,jdbcType=BIGINT}</if>
    </update>

    <update id="deleteById" parameterType="java.lang.Long">
        UPDATE <include refid="tableName"/>
        SET deleted = 'T',
            MODIFY_BY = 'SYSTEM_DBI',
            MODIFY_TIME = NOW()
        WHERE id = #{id,jdbcType=BIGINT}
          AND deleted = 'F'
    </update>

    <select id="countByArg" resultType="int" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysPaySetQueryArg">
        SELECT COUNT(*)
        FROM <include refid="tableName"/>
        <where>
            corp_code = #{corpCode,jdbcType=VARCHAR}
            AND deleted = 'F'
            <if test="payTypeCode != null and payTypeCode != ''">AND PAY_TYPE_CODE = #{payTypeCode,jdbcType=VARCHAR}</if>
            <if test="useFlag != null">AND USE_FLAG = #{useFlag,jdbcType=VARCHAR}</if>
            <if test="mainbodyId != null">AND MAINBODY_ID = #{mainbodyId,jdbcType=BIGINT}</if>
            <if test="payType != null and payType != ''">AND PAY_TYPE = #{payType,jdbcType=VARCHAR}</if>
            <if test="payName != null and payName != ''">AND PAY_NAME LIKE CONCAT('%', #{payName,jdbcType=VARCHAR}, '%')</if>
            <if test="paylableName != null and paylableName != ''">AND PAYLABLE_NAME LIKE CONCAT('%', #{paylableName,jdbcType=VARCHAR}, '%')</if>
            <if test="payDefault != null">AND PAY_DEFAULT = #{payDefault,jdbcType=VARCHAR}</if>
        </where>
    </select>

    <select id="queryByArg" resultMap="BaseResultMap" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysPaySetQueryArg">
        SELECT <include refid="allColumns"/>
        FROM <include refid="tableName"/>
        <where>
            corp_code = #{corpCode,jdbcType=VARCHAR}
            AND deleted = 'F'
            <if test="payTypeCode != null and payTypeCode != ''">AND PAY_TYPE_CODE = #{payTypeCode,jdbcType=VARCHAR}</if>
            <if test="useFlag != null">AND USE_FLAG = #{useFlag,jdbcType=VARCHAR}</if>
            <if test="mainbodyId != null">AND MAINBODY_ID = #{mainbodyId,jdbcType=BIGINT}</if>
            <if test="payType != null and payType != ''">AND PAY_TYPE = #{payType,jdbcType=VARCHAR}</if>
            <if test="payName != null and payName != ''">AND PAY_NAME LIKE CONCAT('%', #{payName,jdbcType=VARCHAR}, '%')</if>
            <if test="paylableName != null and paylableName != ''">AND PAYLABLE_NAME LIKE CONCAT('%', #{paylableName,jdbcType=VARCHAR}, '%')</if>
            <if test="payDefault != null">AND PAY_DEFAULT = #{payDefault,jdbcType=VARCHAR}</if>
        </where>
        ORDER BY id DESC
        <if test="limit != null and offset != null">
            LIMIT #{offset,jdbcType=INTEGER}, #{limit,jdbcType=INTEGER}
        </if>
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO <include refid="tableName"/> (<include refid="insertColumns"/>)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.corpCode,jdbcType=VARCHAR}, #{item.paylableName,jdbcType=VARCHAR}, #{item.payTypeCode,jdbcType=VARCHAR},
                #{item.paylableIcon,jdbcType=VARCHAR}, #{item.useFlag,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR},
                #{item.createBy,jdbcType=VARCHAR}, #{item.modifyBy,jdbcType=VARCHAR}, #{item.deleted,jdbcType=VARCHAR}, #{item.payDefault,jdbcType=VARCHAR},
                #{item.mainbodyId,jdbcType=BIGINT}, #{item.payType,jdbcType=VARCHAR}, #{item.payName,jdbcType=VARCHAR}, #{item.transferParam,jdbcType=VARCHAR},
                NOW(), NOW()
            )
        </foreach>
    </insert>

    <select id="queryEnableListByPayTypeAndId"
            resultType="com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysPaySetDO">
        SELECT <include refid="allColumns"/>
            FROM <include refid="tableName"/>
                WHERE corp_code = #{corpCode,jdbcType=VARCHAR}
                  AND deleted = 'F'
                  AND USE_FLAG = 'T'
                    <if test="payType != null and payType != ''">
                        and PAY_TYPE = #{payType}
                    </if>
                    <if test="idSet!=null and idSet.size()>0">
                        and id in
                        <foreach item="item" index="index" collection="idSet" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </if>
    </select>

</mapper> 