/**
 * 系统参数值声明处理器接口
 *
 * <AUTHOR> 2025-08-04
 */
package com.sendinfo.ticketing.verification.basic.service.infra.processor;

import com.sendinfo.ticketing.verification.basic.enums.SysParamDataType;
import com.sendinfo.ticketing.verification.basic.exception.VerificationBizRuntimeException;

/**
 * 系统参数值声明处理器接口
 * 负责校验和解析不同数据类型的参数值声明
 */
public interface SysParamValueStatementProcessor {

    /**
     * 获取处理器对应的数据类型
     *
     * @return 数据类型
     */
    SysParamDataType getDataType();

    /**
     * 校验参数值声明格式是否正确
     *
     * @param paramValueStatement 参数值声明JSON字符串
     *
     */
    void validateStatement(String paramValueStatement);

    /**
     * 解析参数值声明并获取默认值
     *
     * @param paramValueStatement 参数值声明JSON字符串
     * @return 解析出的默认值
     * @throws VerificationBizRuntimeException 解析失败时抛出运行时异常
     */
    String parseAndGetDefaultSysParamValue(String paramValueStatement);
}
