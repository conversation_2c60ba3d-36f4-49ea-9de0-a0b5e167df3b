package com.sendinfo.ticketing.verification.basic.component.infra.converter;

import groovyjarjarantlr4.runtime.tree.CommonErrorNode;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamDefinitionCreateRequest;
import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamDefinitionDeleteRequest;
import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamDefinitionQueryCondition;
import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamDefinitionUpdateRequest;
import com.sendinfo.ticketing.verification.basic.component.infra.mapper.SysParamDefinitionMapper;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamDefinitionCreateParam;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamDefinitionDeleteParam;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamDefinitionQueryParam;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamDefinitionUpdateParam;
import com.sendinfo.ticketing.verification.basic.exception.VerificationBizRuntimeException;
import com.sendinfo.ticketing.verification.basic.model.infra.SysParamDefinition;
import com.sendinfo.ticketing.verification.basic.model.infra.SysParamDefinitionCountByComponent;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamDefinitionDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamDefinitionQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamDefinitionUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.SysParamDefinitionCountByComponentDO;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.SysParamDefinitionDO;
import com.sendinfo.ticketing.verification.common.api.request.PageRequest;
import com.sendinfo.ticketing.verification.common.component.converter.CreateParam2DoConverter;
import com.sendinfo.ticketing.verification.common.component.converter.CreateReq2ParamConverter;
import com.sendinfo.ticketing.verification.common.component.converter.DeleteParam2ArgConverter;
import com.sendinfo.ticketing.verification.common.component.converter.DeleteReq2ParamConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadDo2ModelConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadPageReq2ParamConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadParam2ArgConverter;
import com.sendinfo.ticketing.verification.common.component.converter.UpdateParam2ArgConverter;
import com.sendinfo.ticketing.verification.common.component.converter.UpdateReq2ParamConverter;
import com.sendinfo.ticketing.verification.common.repository.arg.StatusUpdater;

@Component("sysParamDefinitionConverter")
public class SysParamDefinitionConverter
        implements CreateParam2DoConverter<SysParamDefinitionCreateParam, SysParamDefinitionDO>,
        ReadParam2ArgConverter<SysParamDefinitionQueryParam, SysParamDefinitionQueryArg>,
        ReadDo2ModelConverter<SysParamDefinitionDO, SysParamDefinition>,
        UpdateParam2ArgConverter<SysParamDefinitionUpdateParam, SysParamDefinitionUpdateArg, SysParamDefinition>,
        DeleteParam2ArgConverter<SysParamDefinitionDeleteParam, SysParamDefinitionDeleteArg>,
        DeleteReq2ParamConverter<SysParamDefinitionDeleteRequest, SysParamDefinitionDeleteParam>,
        CreateReq2ParamConverter<SysParamDefinitionCreateRequest, SysParamDefinitionCreateParam>,
        UpdateReq2ParamConverter<SysParamDefinitionUpdateRequest, SysParamDefinitionUpdateParam>,
        ReadPageReq2ParamConverter<SysParamDefinitionQueryCondition, SysParamDefinitionQueryParam> {

    @Override
    public SysParamDefinitionDO c_p2d(SysParamDefinitionCreateParam createParam) {
        return SysParamDefinitionMapper.INSTANCE.convert(createParam);
    }

    @Override
    public List<SysParamDefinitionDO> c_ps2ds(List<SysParamDefinitionCreateParam> params) {
        return params.stream()
                .map(SysParamDefinitionMapper.INSTANCE::convert)
                .collect(Collectors.toList());
    }

    @Override
    public SysParamDefinitionUpdateArg u_p2a(SysParamDefinitionUpdateParam param, SysParamDefinition currentModel) {
        SysParamDefinitionUpdateArg updateArg = SysParamDefinitionMapper.INSTANCE.convert(param);
        Optional.ofNullable(param.getTargetStatus())
                .ifPresent(innerStatus -> updateArg.setStatusUpdater(StatusUpdater.transfer(currentModel.getStatus().getCode(), innerStatus.getCode())));

        return updateArg;
    }

    @Override
    public SysParamDefinitionQueryArg r_p2a(SysParamDefinitionQueryParam param) {
        return SysParamDefinitionMapper.INSTANCE.convert(param);
    }

    @Override
    public SysParamDefinitionDeleteArg d_p2a(SysParamDefinitionDeleteParam param) {
        return SysParamDefinitionMapper.INSTANCE.convert(param);
    }

    @Override
    public SysParamDefinition r_d2m(SysParamDefinitionDO dataObject) {
        return SysParamDefinitionMapper.INSTANCE.convert(dataObject);
    }

    @Override
    public List<SysParamDefinition> r_ds2ms(List<SysParamDefinitionDO> dataObjects) {
        return dataObjects.stream()
                .map(this::r_d2m)
                .collect(Collectors.toList());
    }

    @Override
    public SysParamDefinitionCreateParam c_r2p(SysParamDefinitionCreateRequest req) {
        SysParamDefinitionCreateParam createParam = SysParamDefinitionMapper.INSTANCE.convert(req);
        if (createParam.getDataType() == null) {
            throw new IllegalArgumentException("数据类型无效：" + req.getDataType());
        }
        return createParam;
    }

    @Override
    public SysParamDefinitionUpdateParam u_r2p(SysParamDefinitionUpdateRequest req) {
        return SysParamDefinitionMapper.INSTANCE.convert(req);
    }

    @Override
    public SysParamDefinitionQueryParam r_pr2p(PageRequest<SysParamDefinitionQueryCondition> pageReq) {
        SysParamDefinitionQueryParam queryParam = new SysParamDefinitionQueryParam();
        SysParamDefinitionQueryCondition condition = pageReq.getCondition();
        if (condition != null) {
            queryParam = SysParamDefinitionMapper.INSTANCE.convert(condition);
        }
        queryParam.setStartIndex(pageReq.getStartIndex());
        queryParam.setPageSize(pageReq.getPageSize());
        return queryParam;
    }

    @Override
    public SysParamDefinitionDeleteParam d_r2p(SysParamDefinitionDeleteRequest req) {
        return SysParamDefinitionMapper.INSTANCE.convert(req);
    }

    public List<SysParamDefinitionCountByComponent> convertCountByComponentList(List<SysParamDefinitionCountByComponentDO> dataObjects) {
        return dataObjects.stream()
                .map(SysParamDefinitionMapper.INSTANCE::convert)
                .collect(Collectors.toList());
    }
} 