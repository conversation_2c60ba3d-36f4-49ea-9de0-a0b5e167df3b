package com.sendinfo.ticketing.verification.basic.component.infra.converter;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

import com.sendinfo.ticketing.verification.basic.api.infra.request.PwClientMenuQueryCondition;
import com.sendinfo.ticketing.verification.basic.component.infra.mapper.PwClientMenuMapper;
import com.sendinfo.ticketing.verification.basic.component.infra.param.PwClientMenuQueryParam;
import com.sendinfo.ticketing.verification.basic.component.infra.param.PwClientMenuUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.infra.PwClientMenu;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwClientMenuQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwClientMenuUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.PwClientMenuDO;
import com.sendinfo.ticketing.verification.common.api.request.PageRequest;
import com.sendinfo.ticketing.verification.common.component.converter.ReadDo2ModelConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadPageReq2ParamConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadParam2ArgConverter;
import com.sendinfo.ticketing.verification.common.component.converter.UpdateParam2ArgConverter;

/**
 * 客户端菜单转换器
 *
 * <AUTHOR> Generated 2025-07-25
 */
@Component("pwClientMenuConverter")
public class PwClientMenuConverter implements
        ReadParam2ArgConverter<PwClientMenuQueryParam, PwClientMenuQueryArg>,
        ReadPageReq2ParamConverter<PwClientMenuQueryCondition, PwClientMenuQueryParam>,
        ReadDo2ModelConverter<PwClientMenuDO, PwClientMenu>,
        UpdateParam2ArgConverter<PwClientMenuUpdateParam, PwClientMenuUpdateArg, PwClientMenu> {

    @Override
    public PwClientMenu r_d2m(PwClientMenuDO dataObject) {
        return PwClientMenuMapper.INSTANCE.convert(dataObject);
    }

    @Override
    public PwClientMenuQueryArg r_p2a(PwClientMenuQueryParam param) {
        return PwClientMenuMapper.INSTANCE.convert(param);
    }

    @Override
    public PwClientMenuUpdateArg u_p2a(PwClientMenuUpdateParam param, PwClientMenu currentModel) {
        return PwClientMenuMapper.INSTANCE.convert(param);
    }

    /**
     * DO列表转Model列表
     */
    public List<PwClientMenu> r_ds2ms(List<PwClientMenuDO> dataObjects) {
        if (dataObjects == null) {
            return null;
        }
        return dataObjects.stream().map(this::r_d2m).collect(Collectors.toList());
    }

    @Override
    public PwClientMenuQueryParam r_pr2p(PageRequest<PwClientMenuQueryCondition> pageReq) {
        PwClientMenuQueryParam queryParam = new PwClientMenuQueryParam();
        PwClientMenuQueryCondition condition = pageReq.getCondition();
        if (condition != null) {
            queryParam = PwClientMenuMapper.INSTANCE.convert(condition);
        }
        queryParam.setStartIndex(pageReq.getStartIndex());
        queryParam.setPageSize(pageReq.getPageSize());
        return queryParam;
    }
}
