package com.sendinfo.ticketing.verification.basic.repository.pay.dataobject;

import com.sendinfo.ticketing.verification.common.repository.dataobject.AbstractTenantBaseDO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 经营主体支付配置子商户关联表数据对象
 * 对应数据库表 sys_mainbody_manage_mm_sub_merchants
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class SysMainbodyManageMmSubMerchantsDO extends AbstractTenantBaseDO<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 经营主体管理ID
     * 关联经营主体管理表的主键
     */
    private Long mainbodyManageId;

    /**
     * 子商户ID
     * 关联子商户表的主键
     */
    private Long subMerchantsId;

    /**
     * 子商户名称
     * 子商户的显示名称
     */
    private String subMerchantsName;

    /**
     * 子商户号
     * 子商户的唯一标识号
     */
    private String merchantNo;
} 