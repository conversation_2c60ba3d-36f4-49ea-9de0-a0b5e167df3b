package com.sendinfo.ticketing.verification.basic.api.goods.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * 打印设置查询条件
 * <AUTHOR>
 */
@Data
public class TktPrintQueryRequest implements Serializable {

    private static final long serialVersionUID = -4876543210123456789L;

    /**
     * 票型编码
     */
    @NotEmpty
    private String ticketCode;

    /**
     * 企业编码
     */
    @NotEmpty
    private String corpCode;

}