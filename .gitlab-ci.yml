variables:
  MAVEN_CLI_OPTS: "--batch-mode"
  MAVEN_OPTS: "-Dmaven.repo.local=.m2/repository"

cache:
  paths:
    - .m2/repository/
    - target/

build:
  stage: build
  image: harbor.sendinfo.com/sendinfo-paas/sendinfo-maven:3.8.8-jdk11
  script:
    - mvn $MAVEN_CLI_OPTS compile -U

test:
  stage: test
  image: harbor.sendinfo.com/sendinfo-paas/sendinfo-maven:3.8.8-jdk11
  script:
    - mvn $MAVEN_CLI_OPTS test -U

deploy:
  stage: deploy
  image: harbor.sendinfo.com/sendinfo-paas/sendinfo-maven:3.8.8-jdk11
  script:
    - mvn $MAVEN_CLI_OPTS -am -pl verification-platform-basic-api deploy
  only:
    refs:
      - master
      - /^.*release.*$/
      - /^.*snapshot.*$/
  when: on_success

notifyFailWelink:
  stage: notify
  #  image: harbor.sendinfo.com/sendinfo-paas/sendinfo-maven:3.8.6-jdk-8-welink
  script:
    - echo ${CI_COMMIT_TITLE}
    - java -jar /usr/local/bin/welink_notify.jar -g 837129742296576768 -l $CI_PIPELINE_URL -t CI运行失败通知  项目=$CI_PROJECT_NAME 分支=$CI_COMMIT_REF_NAME  消息="${CI_COMMIT_TITLE}"   操作人=$GITLAB_USER_NAME SHA=$CI_COMMIT_SHA
  when: on_failure

notifySuccessWelink:
  stage: notify
  #  image: harbor.sendinfo.com/sendinfo-paas/sendinfo-maven:3.8.6-jdk-8-welink
  script:
    - echo ${CI_COMMIT_TITLE}
    - java -jar /usr/local/bin/welink_notify.jar -g 837129742296576768 -l $CI_PIPELINE_URL -t CI运行成功通知  项目=$CI_PROJECT_NAME 分支=$CI_COMMIT_REF_NAME  消息="${CI_COMMIT_TITLE}"  操作人=$GITLAB_USER_NAME SHA=$CI_COMMIT_SHA
  when: on_success

stages:
  - build
  - test
  - deploy
  - notify
