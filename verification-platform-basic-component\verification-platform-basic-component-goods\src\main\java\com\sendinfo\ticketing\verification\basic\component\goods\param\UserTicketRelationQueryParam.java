package com.sendinfo.ticketing.verification.basic.component.goods.param;

import com.sendinfo.ticketing.verification.common.component.param.AbstractQueryParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2025-07-24 10:41
 */
@Getter
@Setter
@ToString(callSuper = true)
public class UserTicketRelationQueryParam extends AbstractQueryParam {

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 票型编码
     */
    private Long ticketId;
}
