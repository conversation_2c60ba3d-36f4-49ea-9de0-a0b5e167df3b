package com.sendinfo.ticketing.verification.basic.repository.pay.dao;

import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageMmSubMerchantsDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageMmSubMerchantsQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageMmSubMerchantsUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysMainbodyManageMmSubMerchantsDO;
import com.sendinfo.ticketing.verification.common.repository.BatchInsertDAO;
import com.sendinfo.ticketing.verification.common.repository.CountableDAO;
import com.sendinfo.ticketing.verification.common.repository.GenericDAO;
import com.sendinfo.ticketing.verification.common.repository.QueryableDAO;

import java.util.List;
import java.util.Set;

/**
 * 经营主体支付配置子商户关联表数据访问接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface SysMainbodyManageMmSubMerchantsDao extends
        GenericDAO<Long, SysMainbodyManageMmSubMerchantsDO, SysMainbodyManageMmSubMerchantsUpdateArg, SysMainbodyManageMmSubMerchantsDeleteArg>,
        CountableDAO<SysMainbodyManageMmSubMerchantsQueryArg>,
        QueryableDAO<SysMainbodyManageMmSubMerchantsQueryArg, SysMainbodyManageMmSubMerchantsDO>,
        BatchInsertDAO<Long, SysMainbodyManageMmSubMerchantsDO> {

    /**
     * 根据经营主体支付配置ID查询经营主体支付配置子商户关联信息列表
     *
     * @param mainbodyManageId 经营主体支付配置ID
     * @param corpCode         企业编码
     * @return 经营主体支付配置子商户关联信息列表
     */
    List<SysMainbodyManageMmSubMerchantsDO> queryByMainbodyManageId(Long mainbodyManageId, String corpCode);

    /**
     * 根据子商户ID查询主体支付配置子商户关联信息列表
     *
     * @param subMerchantsId 子商户ID
     * @param corpCode       企业编码
     * @return 主体支付配置子商户关联信息列表
     */
    List<SysMainbodyManageMmSubMerchantsDO> queryBySubMerchantsId(Long subMerchantsId, String corpCode);

    /**
     * 根据子商户ID集合查询主体支付配置子商户关联信息列表
     *
     * @param subMerchantsIdSet 子商户ID集合
     * @param corpCode          企业编码
     * @return 主体支付配置子商户关联信息列表
     */
    List<SysMainbodyManageMmSubMerchantsDO> queryBySubMerchantsIdSet(Set<Long> subMerchantsIdSet, String corpCode);

} 