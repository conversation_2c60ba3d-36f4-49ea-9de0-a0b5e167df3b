package com.sendinfo.ticketing.verification.basic.component.goods.converter;

import com.sendinfo.ticketing.verification.basic.api.goods.request.TktPrintQueryCondition;
import com.sendinfo.ticketing.verification.basic.component.goods.mapper.TktPrintMapper;
import com.sendinfo.ticketing.verification.basic.component.goods.param.TktPrintQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TktPrint;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TktPrintQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TktPrintDO;
import com.sendinfo.ticketing.verification.common.api.request.PageRequest;
import com.sendinfo.ticketing.verification.common.component.converter.ReadDo2ModelConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadPageReq2ParamConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadParam2ArgConverter;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 打印设置转换器
 * <AUTHOR>
 */
@Component("tktPrintConverter")
public class TktPrintConverter implements
        ReadParam2ArgConverter<TktPrintQueryParam, TktPrintQueryArg>,
        ReadDo2ModelConverter<TktPrintDO, TktPrint>,
        ReadPageReq2ParamConverter<TktPrintQueryCondition, TktPrintQueryParam>{

    @Override
    public TktPrintQueryArg r_p2a(TktPrintQueryParam param) {
        return TktPrintMapper.INSTANCE.convert(param);
    }

    @Override
    public TktPrint r_d2m(TktPrintDO dataObject) {
        return TktPrintMapper.INSTANCE.convert(dataObject);
    }

    @Override
    public List<TktPrint> r_ds2ms(List<TktPrintDO> dataObjects) {
        return dataObjects.stream()
                .map(this::r_d2m)
                .collect(Collectors.toList());
    }

    @Override
    public TktPrintQueryParam r_pr2p(PageRequest<TktPrintQueryCondition> pageCondition) {
        TktPrintQueryParam queryParam = new TktPrintQueryParam();
        TktPrintQueryCondition condition = pageCondition.getCondition();
        if (condition != null) {
        }
        queryParam.setStartIndex(pageCondition.getStartIndex());
        queryParam.setPageSize(pageCondition.getPageSize());
        return queryParam;
    }
}