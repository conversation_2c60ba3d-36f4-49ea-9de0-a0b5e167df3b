package com.sendinfo.ticketing.verification.basic.api.park;

import com.sendinfo.ticketing.verification.basic.api.park.request.SysTenementInfoCondition;
import com.sendinfo.ticketing.verification.basic.model.park.SysTenementInfo;
import com.sendinfo.ticketing.verification.common.api.request.PageRequest;
import com.sendinfo.ticketing.verification.common.api.result.PageResultModel;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import javax.validation.constraints.NotNull;
import java.util.List;

public interface SysTenementInfoReadService {
    /**
     * 分页查询景区租户配置信息
     * @param pageRequest
     * @return
     */
    PageResultModel<SysTenementInfo> searchSysTenementInfo(PageRequest<SysTenementInfoCondition> pageRequest);
    /**
     * 根据corpCode查询景区租户配置信息
     * @param corpCode
     * @return
     */
    ResultModel<List<SysTenementInfo>> querySysTenementInfoByCorpCode(@NotNull String corpCode);
}
