package com.sendinfo.ticketing.verification.basic.repository.system.arg;

import com.sendinfo.ticketing.verification.common.repository.arg.AbstractDeleteArg;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 票型角色删除参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class SysAuditRoleDeleteArg extends AbstractDeleteArg {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 企业码
     */
    private String corpCode;

    /**
     * 修改人
     */
    private String modifyBy;
} 