package com.sendinfo.ticketing.verification.basic.service.system.impl;

import com.sendinfo.ticketing.verification.basic.api.system.UserInfoReadService;
import com.sendinfo.ticketing.verification.basic.api.system.request.SellerUserInfoQueryRequest;
import com.sendinfo.ticketing.verification.basic.model.system.UserInfo;
import com.sendinfo.ticketing.verification.basic.model.system.error.SystemErrorDef;
import com.sendinfo.ticketing.verification.basic.service.system.enums.SystemAttachmentKey;
import com.sendinfo.ticketing.verification.basic.service.system.function.QueryUserInfoFunction;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;
import com.sendinfo.ticketing.verification.common.api.result.Results;
import com.sendinfo.ticketing.verification.common.service.support.ApiCommonFailedBiActions;
import com.sendinfo.ticketing.verification.flow.FlowAgentBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Auther: chengjie.li
 * @Date: 2025/7/16 16:13
 */
@Service("userInfoReadService")
@Slf4j
public class UserInfoReadServiceImpl implements UserInfoReadService {

    private final FlowAgentBuilder flowAgentBuilder;

    private final QueryUserInfoFunction queryUserInfoFunction;

    public UserInfoReadServiceImpl(FlowAgentBuilder flowAgentBuilder, QueryUserInfoFunction queryUserInfoFunction) {
        this.flowAgentBuilder = flowAgentBuilder;
        this.queryUserInfoFunction = queryUserInfoFunction;
    }

    @Override
    public ResultModel<UserInfo> queryById(Long id, String corpCode) {
        return flowAgentBuilder.<Long, ResultModel<UserInfo>>validateThenChooseBuilder()
                .appendLogicAction(queryUserInfoFunction::querySingleById)
                .withSuccessfulAction(q -> {
                    UserInfo userInfo = q.getAttachment(SystemAttachmentKey.USER_INFO_ATTACHMENT_KEY);
                    return Results.success(userInfo);
                })
                .withFailedBiAction(ApiCommonFailedBiActions::reasonMappingToResult)
                .withExceptionBiAction((q, th) -> {
                    log.error("[UserInfoReadServiceImpl] queryById error, question is {}", q, th);
                    return Results.fail(SystemErrorDef.QUERY_USER_INFO_ERROR);
                })
                .build()
                .prompt(id)
                .getResult();
    }

    @Override
    public ResultModel<UserInfo> queryByAccName(String accName, String corpCode) {
        return flowAgentBuilder.<Pair<String, String>, ResultModel<UserInfo>>validateThenChooseBuilder()
                .appendLogicAction(queryUserInfoFunction::queryByAccName)
                .withSuccessfulAction(q -> {
                    UserInfo userInfo = q.getAttachment(SystemAttachmentKey.USER_INFO_ATTACHMENT_KEY);
                    return Results.success(userInfo);
                })
                .withFailedBiAction(ApiCommonFailedBiActions::reasonMappingToResult)
                .withExceptionBiAction((q, th) -> {
                    log.error("[UserInfoReadServiceImpl] queryByAccName error, question is {}", q, th);
                    return Results.fail(SystemErrorDef.QUERY_USER_INFO_ERROR);
                })
                .build()
                .prompt(Pair.of(accName, corpCode))
                .getResult();
    }

    @Override
    public ResultModel<List<UserInfo>> queryListBySellerUserInfo(SellerUserInfoQueryRequest request) {
        return flowAgentBuilder.<SellerUserInfoQueryRequest, ResultModel<List<UserInfo>>>validateThenChooseBuilder()
                .appendLogicAction(queryUserInfoFunction::queryListBySellerUserInfo)
                .withSuccessfulAction(q -> {
                    List<UserInfo> userInfoList = q.getAttachment(SystemAttachmentKey.USER_INFO_DATA_LIST_ATTACHMENT_KEY);
                    return Results.success(userInfoList);
                })
                .withFailedBiAction(ApiCommonFailedBiActions::reasonMappingToResult)
                .withExceptionBiAction((q, th) -> {
                    log.error("[UserInfoReadServiceImpl] queryListBySellerUserInfo error, question is {}", q, th);
                    return Results.fail(SystemErrorDef.QUERY_USER_INFO_ERROR);
                })
                .build()
                .prompt(request)
                .getResult();
    }
}
