package com.sendinfo.ticketing.verification.basic.repository.pay.dao;

import com.alibaba.nacos.api.exception.NacosException;
import com.sendinfo.ticketing.verification.basic.repository.common.config.SaasPwMybatisConfig;
import com.sendinfo.ticketing.verification.basic.repository.common.sharding.DynamicDataSourceMappingConfigPullStorage;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dao.impl.SysMainbodyManageDaoImpl;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysMainbodyManageDO;
import com.sendinfo.ticketing.verification.common.repository.arg.StatusUpdater;
import org.junit.Test;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.AbstractTransactionalJUnit4SpringContextTests;

import java.util.List;
import java.util.Random;

import static org.junit.Assert.*;

/**
 * 经营主体支付配置数据访问层单元测试
 * <p>
 * 本测试类覆盖了SysMainbodyManageDao的所有方法，包括：
 * 1. 插入操作测试
 * 2. 批量插入操作测试
 * 3. 根据ID查询测试
 * 4. 条件查询测试
 * 5. 计数查询测试
 * 6. 更新操作测试
 * 7. 软删除操作测试
 * 8. 自定义查询方法测试
 * <p>
 * 测试目的：验证SysMainbodyManageDao的所有方法都能正常工作，确保数据访问层的正确性
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootTest(classes = SysMainbodyManageDaoTest.Config.class)
@EnableAutoConfiguration
@TestPropertySource(locations = "classpath:test-application.properties")
public class SysMainbodyManageDaoTest extends AbstractTransactionalJUnit4SpringContextTests {

    @Autowired
    private SysMainbodyManageDao sysMainbodyManageDao;

    /**
     * 测试用的企业编码
     */
    private static final String TEST_CORP_CODE = "TEST_CORP_001";

    /**
     * 测试用户
     */
    private static final String TEST_USER = "test_user";

    /**
     * 测试配置类
     * 配置MyBatis和DAO Bean
     */
    @Configuration
    @Import(SaasPwMybatisConfig.class)
    static class Config {

        @Bean
        public SysMainbodyManageDao sysMainbodyManageDao(@Qualifier(SaasPwMybatisConfig.BEAN_NAME_SQL_SESSION_TEMPLATE) SqlSessionTemplate sqlSessionTemplate) {
            return new SysMainbodyManageDaoImpl(sqlSessionTemplate);
        }

        @Bean
        DynamicDataSourceMappingConfigPullStorage dynamicDataSourceMappingConfigPullStorage(@Value("${nacos.config.server-addr}") String serverAddr,
                                                                                            @Value("${nacos.config.namespace}") String namespace,
                                                                                            @Value("${verification-platform-basic.nacos.tenant-datasource-mapping.data-id:saas-pw-datasource-sharding-mapping.json}") String dataId,
                                                                                            @Value("${verification-platform-basic.nacos.tenant-datasource-mapping.group:DEFAULT_GROUP}") String group,
                                                                                            @Value("${verification-platform-basic.nacos.tenant-datasource-mapping.pulled.timeout:3000}") Integer timeout) throws NacosException {
            return new DynamicDataSourceMappingConfigPullStorage(serverAddr, namespace, dataId, group, timeout);
        }
    }

    /**
     * 测试插入操作
     * <p>
     * 测试步骤：
     * 1. 创建测试经营主体支付配置
     * 2. 执行插入操作
     * 3. 验证主键ID被自动生成
     * 4. 通过ID查询验证记录确实被插入
     * 5. 验证插入的数据字段值正确
     */
    @Test
    public void testInsert() {
        // 1. 创建测试经营主体数据
        SysMainbodyManageDO sysMainbodyManageDO = createTestDO();

        // 2. 执行插入操作
        sysMainbodyManageDao.insert(sysMainbodyManageDO);

        // 3. 验证主键ID被自动生成
        assertNotNull("插入后主键ID应该被自动生成", sysMainbodyManageDO.getId());

        // 4. 通过ID查询验证记录确实被插入
        SysMainbodyManageDO insertedMainbody = sysMainbodyManageDao.queryById(sysMainbodyManageDO.getId());
        assertNotNull("插入后应能通过ID查询到记录", insertedMainbody);
        assertEquals("经营主体名称应该一致", sysMainbodyManageDO.getMainbodyName(), insertedMainbody.getMainbodyName());
        assertEquals("企业编码应该一致", TEST_CORP_CODE, insertedMainbody.getCorpCode());
    }

    /**
     * 测试批量插入操作
     * <p>
     * 测试步骤：
     * 1. 创建多个测试经营主体支付配置
     * 2. 执行批量插入操作
     * 3. 验证所有记录的主键ID都被自动生成
     * 4. 通过ID查询验证所有记录都被正确插入
     */
    @Test
    public void testBatchInsert() {
        // 1. 创建多个测试经营主体支付配置
        List<SysMainbodyManageDO> mainbodyList = new java.util.ArrayList<>();
        for (int i = 0; i < 3; i++) {
            SysMainbodyManageDO mainbodyDO = createTestDO();
            mainbodyList.add(mainbodyDO);
        }

        // 2. 执行批量插入操作
        sysMainbodyManageDao.batchInsert(mainbodyList);

        // 3. 查询插入数据
        SysMainbodyManageQueryArg queryArg = new SysMainbodyManageQueryArg();
        queryArg.setCorpCode(TEST_CORP_CODE);
        List<SysMainbodyManageDO> mainbodyDOList = sysMainbodyManageDao.queryByArg(queryArg);

        // 4. 通过ID查询验证所有记录都被正确插入
        for (SysMainbodyManageDO mainbodyDO : mainbodyDOList) {
            assertNotNull("每个记录都应该生成ID", mainbodyDO.getId());
        }
    }

    /**
     * 测试根据ID查询操作
     * <p>
     * 测试步骤：
     * 1. 插入测试记录
     * 2. 使用插入记录的ID进行查询
     * 3. 验证查询结果正确
     */
    @Test
    public void testQueryById() {
        // 1. 插入测试记录
        SysMainbodyManageDO mainbodyDO = createTestDO();
        sysMainbodyManageDao.insert(mainbodyDO);

        // 2. 使用插入记录的ID进行查询
        SysMainbodyManageDO queriedMainbody = sysMainbodyManageDao.queryById(mainbodyDO.getId());

        // 3. 验证查询结果正确
        assertNotNull("应该能查询到记录", queriedMainbody);
        assertEquals("ID应该一致", mainbodyDO.getId(), queriedMainbody.getId());
        assertEquals("经营主体名称应该一致", mainbodyDO.getMainbodyName(), queriedMainbody.getMainbodyName());
        assertEquals("企业编码应该一致", TEST_CORP_CODE, queriedMainbody.getCorpCode());
    }

    /**
     * 测试基本的更新功能
     * <p>
     * 目的：验证 updateByArg 方法的基本更新功能
     * 步骤：
     * 1. 插入一条测试记录
     * 2. 创建更新参数，修改部分字段
     * 3. 执行更新操作
     * 4. 查询更新后的记录，验证字段值已正确更新
     */
    @Test
    public void testUpdateByArg() {
        // 1. 插入测试记录
        SysMainbodyManageDO mainbodyDO = createTestDO();
        sysMainbodyManageDao.insert(mainbodyDO);

        // 2. 创建更新参数
        SysMainbodyManageUpdateArg updateArg = new SysMainbodyManageUpdateArg();
        updateArg.setId(mainbodyDO.getId());
        updateArg.setCorpCode(TEST_CORP_CODE);
        updateArg.setMerchantNo("UPDATED_MERCHANT_001");
        updateArg.setPayChannelName("更新后的支付渠道");
        updateArg.setUseFlag("T");
        updateArg.setRemark("更新后的备注");
        updateArg.setModifyBy("testUpdater");

        // 3. 执行更新操作
        int updateCount = sysMainbodyManageDao.updateByArg(updateArg);
        assertEquals("应该更新1条记录", 1, updateCount);

        // 4. 验证更新结果
        SysMainbodyManageDO updatedMainbody = sysMainbodyManageDao.queryById(mainbodyDO.getId());
        assertEquals("商户号应该被更新", "UPDATED_MERCHANT_001", updatedMainbody.getMerchantNo());
        assertEquals("支付渠道名称应该被更新", "更新后的支付渠道", updatedMainbody.getPayChannelName());
        assertEquals("启用标志应该被更新", "T", updatedMainbody.getUseFlag());
        assertEquals("备注应该被更新", "更新后的备注", updatedMainbody.getRemark());
    }

    /**
     * 测试条件状态更新功能
     * <p>
     * 目的：验证使用StatusUpdater进行条件状态更新的功能
     * 步骤：
     * 1. 插入一条测试记录，设置初始状态
     * 2. 创建更新参数，使用StatusUpdater
     * 3. 执行更新操作
     * 4. 验证状态已正确更新
     */
    @Test
    public void testUpdateWithStatusUpdater() {
        // 1. 插入测试记录
        SysMainbodyManageDO mainbodyDO = createTestDO();
        mainbodyDO.setUseFlag("F");
        sysMainbodyManageDao.insert(mainbodyDO);

        // 2. 创建更新参数，使用StatusUpdater
        SysMainbodyManageUpdateArg updateArg = new SysMainbodyManageUpdateArg();
        updateArg.setId(mainbodyDO.getId());
        updateArg.setCorpCode(TEST_CORP_CODE);
        updateArg.setUseFlagUpdater(new StatusUpdater<>("F", "T"));
        updateArg.setModifyBy("testUpdater");

        // 3. 执行更新操作
        int updateCount = sysMainbodyManageDao.updateByArg(updateArg);
        assertEquals("应该更新1条记录", 1, updateCount);

        // 4. 验证更新结果
        SysMainbodyManageDO updatedMainbody = sysMainbodyManageDao.queryById(mainbodyDO.getId());
        assertEquals("启用标志应该被更新为T", "T", updatedMainbody.getUseFlag());
    }

    /**
     * 测试软删除功能
     * <p>
     * 目的：验证软删除功能，记录不会被物理删除，而是标记为已删除
     * 步骤：
     * 1. 插入测试记录
     * 2. 执行软删除操作
     * 3. 验证记录被标记为已删除
     * 4. 验证通过正常查询无法查到该记录
     */
    @Test
    public void testSoftDeleteByArg() {
        // 1. 插入测试记录
        SysMainbodyManageDO mainbodyDO = createTestDO();
        sysMainbodyManageDao.insert(mainbodyDO);

        // 2. 执行软删除操作
        SysMainbodyManageDeleteArg deleteArg = new SysMainbodyManageDeleteArg();
        deleteArg.setId(mainbodyDO.getId());
        deleteArg.setCorpCode(TEST_CORP_CODE);
        deleteArg.setModifyBy("testDeleter");

        int deleteCount = sysMainbodyManageDao.softDeleteByArg(deleteArg);
        assertEquals("应该删除1条记录", 1, deleteCount);

        // 3. 验证记录被标记为已删除
        SysMainbodyManageDO deletedMainbody = sysMainbodyManageDao.queryById(mainbodyDO.getId());
        assertNull("软删除后通过ID查询应该返回null", deletedMainbody);
    }

    /**
     * 测试条件查询功能
     * <p>
     * 目的：验证根据条件查询经营主体支付配置的功能
     * 步骤：
     * 1. 插入多条测试记录
     * 2. 创建查询参数，设置查询条件
     * 3. 执行查询操作
     * 4. 验证查询结果符合条件
     */
    @Test
    public void testQueryByArg() {
        // 1. 插入多条测试记录
        SysMainbodyManageDO mainbody1 = createTestDO();
        mainbody1.setMainbodyName("测试主体1");
        mainbody1.setUseFlag("T");
        sysMainbodyManageDao.insert(mainbody1);

        SysMainbodyManageDO mainbody2 = createTestDO();
        mainbody2.setMainbodyName("测试主体2");
        mainbody2.setUseFlag("F");
        sysMainbodyManageDao.insert(mainbody2);

        // 2. 创建查询参数
        SysMainbodyManageQueryArg queryArg = new SysMainbodyManageQueryArg();
        queryArg.setCorpCode(TEST_CORP_CODE);
        queryArg.setUseFlag("T");

        // 3. 执行查询操作
        List<SysMainbodyManageDO> resultList = sysMainbodyManageDao.queryByArg(queryArg);

        // 4. 验证查询结果
        assertNotNull("查询结果不应为null", resultList);
        assertTrue("应该查询到启用的经营主体支付配置", resultList.size() > 0);
        for (SysMainbodyManageDO mainbody : resultList) {
            assertEquals("所有查询结果都应该是启用的", "T", mainbody.getUseFlag());
        }
    }

    /**
     * 测试计数查询功能
     * <p>
     * 目的：验证根据条件统计记录数量的功能
     * 步骤：
     * 1. 插入多条测试记录
     * 2. 创建查询参数，设置查询条件
     * 3. 执行计数查询
     * 4. 验证计数结果正确
     */
    @Test
    public void testCountByArg() {
        // 1. 插入多条测试记录
        SysMainbodyManageDO mainbody1 = createTestDO();
        mainbody1.setUseFlag("T");
        sysMainbodyManageDao.insert(mainbody1);

        SysMainbodyManageDO mainbody2 = createTestDO();
        mainbody2.setUseFlag("T");
        sysMainbodyManageDao.insert(mainbody2);

        SysMainbodyManageDO mainbody3 = createTestDO();
        mainbody3.setUseFlag("F");
        sysMainbodyManageDao.insert(mainbody3);

        // 2. 创建查询参数
        SysMainbodyManageQueryArg queryArg = new SysMainbodyManageQueryArg();
        queryArg.setCorpCode(TEST_CORP_CODE);
        queryArg.setUseFlag("T");

        // 3. 执行计数查询
        int count = sysMainbodyManageDao.countByArg(queryArg);

        // 4. 验证计数结果
        assertEquals("应该统计到2条启用的记录", 2, count);
    }

    /**
     * 测试根据ID删除功能
     * <p>
     * 目的：验证根据ID直接删除记录的功能
     * 步骤：
     * 1. 插入测试记录
     * 2. 执行根据ID删除操作
     * 3. 验证记录被软删除
     */
    @Test
    public void testDeleteById() {
        // 1. 插入测试记录
        SysMainbodyManageDO mainbodyDO = createTestDO();
        sysMainbodyManageDao.insert(mainbodyDO);

        // 2. 执行根据ID删除操作
        int deleteCount = sysMainbodyManageDao.deleteById(mainbodyDO.getId());
        assertEquals("应该删除1条记录", 1, deleteCount);

        // 3. 验证记录被软删除
        SysMainbodyManageDO deletedMainbody = sysMainbodyManageDao.queryById(mainbodyDO.getId());
        assertNull("删除后通过ID查询应该返回null", deletedMainbody);
    }

    /**
     * 测试根据经营主体ID查询功能
     * <p>
     * 目的：验证根据经营主体ID查询经营主体支付配置的功能
     * 步骤：
     * 1. 插入测试记录
     * 2. 使用经营主体ID进行查询
     * 3. 验证查询结果正确
     */
    @Test
    public void testQueryByMainbodyId() {
        // 1. 插入测试记录
        SysMainbodyManageDO mainbodyDO = createTestDO();
        Long mainbodyId = 12345L;
        mainbodyDO.setMainbodyId(mainbodyId);
        sysMainbodyManageDao.insert(mainbodyDO);

        // 2. 使用经营主体ID进行查询
        SysMainbodyManageDO queriedMainbody = sysMainbodyManageDao.queryByMainbodyId(mainbodyId, TEST_CORP_CODE);

        // 3. 验证查询结果正确
        assertNotNull("应该能查询到记录", queriedMainbody);
        assertEquals("经营主体ID应该一致", mainbodyId, queriedMainbody.getMainbodyId());
        assertEquals("企业编码应该一致", TEST_CORP_CODE, queriedMainbody.getCorpCode());
    }

    /**
     * 创建测试用的经营主体支付配置数据对象
     *
     * @return 测试用的经营主体支付配置数据对象
     */
    private SysMainbodyManageDO createTestDO() {
        SysMainbodyManageDO mainbodyDO = new SysMainbodyManageDO();
        mainbodyDO.setCorpCode(TEST_CORP_CODE);
        mainbodyDO.setMainbodyName("测试经营主体" + new Random().nextInt(1000));
        mainbodyDO.setMainbodyShortName("测试简称");
        mainbodyDO.setMerchantNo("TEST_MERCHANT_" + new Random().nextInt(1000));
        mainbodyDO.setMerchantSourceNo("TEST_SOURCE_" + new Random().nextInt(1000));
        mainbodyDO.setPayChannelName("测试支付渠道");
        mainbodyDO.setPayChannelCode("TEST_CHANNEL");
        mainbodyDO.setPaycenterPublicKey("测试公钥");
        mainbodyDO.setMchPrivateKey("测试私钥");
        mainbodyDO.setUseFlag("T");
        mainbodyDO.setLinkMan("测试联系人");
        mainbodyDO.setLinkTel("13800138000");
        mainbodyDO.setLogo("测试logo");
        mainbodyDO.setRemark("测试备注");
        mainbodyDO.setMainbodyId(99L);
        mainbodyDO.setMainbodyNumber("TEST_NUMBER_" + new Random().nextInt(1000));
        mainbodyDO.setSeettlementMerchantNo("TEST_SETTLEMENT_" + new Random().nextInt(1000));
        mainbodyDO.setCreateBy(TEST_USER);
        mainbodyDO.setModifyBy(TEST_USER);
        mainbodyDO.setDeleted("F");
        return mainbodyDO;
    }
} 