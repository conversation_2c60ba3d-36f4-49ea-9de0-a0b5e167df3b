package com.sendinfo.ticketing.verification.basic.component.pay.component;

import com.sendinfo.ticketing.verification.basic.component.pay.converter.TktSellerPayawayConverter;
import com.sendinfo.ticketing.verification.basic.component.pay.impl.TktSellerPayawayReadComponentImpl;
import com.sendinfo.ticketing.verification.basic.enums.CommonUseFlagEnum;
import com.sendinfo.ticketing.verification.basic.model.pay.TktSellerPayaway;
import com.sendinfo.ticketing.verification.basic.model.pay.enums.ClientTypeEnum;
import com.sendinfo.ticketing.verification.basic.model.pay.enums.SaleModelEnum;
import com.sendinfo.ticketing.verification.basic.repository.pay.dao.TktSellerPayawayDao;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.TktSellerPayawayDO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static junit.framework.TestCase.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;

/**
 * TktSellerPayawayReadComponent单元测试
 * 测试售票员收款方式读取组件的各种查询功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RunWith(MockitoJUnitRunner.class)
public class TktSellerPayawayReadComponentTest {

    @Mock
    private TktSellerPayawayDao tktSellerPayawayDao;

    @Mock
    private TktSellerPayawayConverter tktSellerPayawayConverter;

    @InjectMocks
    private TktSellerPayawayReadComponentImpl tktSellerPayawayReadComponent;

    // 测试常量
    private static final Long TEST_ACC_ID = 1L;
    private static final Integer TEST_CLIENT_TYPE = 1;
    private static final Integer TEST_SALE_MODEL = 1;
    private static final String TEST_CORP_CODE = "TEST_CORP_001";
    private static final Long TEST_ID_1 = 1L;
    private static final Long TEST_ID_2 = 2L;
    private static final String TEST_SELLER = "test_seller";
    private static final String TEST_PAY_AWAY = "现金";
    private static final Integer TEST_SORT = 1;
    private static final Long TEST_PAY_ID = 1L;
    private static final String TEST_CREATE_BY = "testUser";
    private static final String TEST_MODIFY_BY = "testUser";

    /**
     * 测试目的：验证queryEnableSellerPayAwayList方法的正常查询功能
     * 测试步骤：
     * 1. 创建测试输入数据
     * 2. Mock依赖组件返回预期结果
     * 3. 调用queryEnableSellerPayAwayList方法
     * 4. 验证返回结果的正确性
     */
    @Test
    public void testQueryEnableSellerPayAwayList_ShouldReturnEnabledPayaways() {
        // 1. 创建测试输入数据
        List<TktSellerPayawayDO> mockDoList = Arrays.asList(
                createTktSellerPayawayDO(TEST_ID_1),
                createTktSellerPayawayDO(TEST_ID_2)
        );
        List<TktSellerPayaway> expectedResult = Arrays.asList(
                createTktSellerPayaway(TEST_ID_1),
                createTktSellerPayaway(TEST_ID_2)
        );

        // 2. Mock依赖组件返回预期结果
        when(tktSellerPayawayDao.queryEnableSellerPayAwayList(TEST_ACC_ID, TEST_CLIENT_TYPE, TEST_SALE_MODEL, TEST_CORP_CODE))
                .thenReturn(mockDoList);
        when(tktSellerPayawayConverter.r_ds2ms(mockDoList))
                .thenReturn(expectedResult);

        // 3. 调用queryEnableSellerPayAwayList方法
        List<TktSellerPayaway> result = tktSellerPayawayReadComponent.queryEnableSellerPayAwayList(TEST_ACC_ID, TEST_CLIENT_TYPE, TEST_SALE_MODEL, TEST_CORP_CODE);

        // 4. 验证返回结果的正确性
        assertNotNull("查询结果不应为null", result);
        assertEquals("应该返回2条记录", 2, result.size());
        assertEquals("第一条记录的ID应该一致", TEST_ID_1, result.get(0).getId());
        assertEquals("第二条记录的ID应该一致", TEST_ID_2, result.get(1).getId());

        // 验证方法调用
        verify(tktSellerPayawayDao, times(1)).queryEnableSellerPayAwayList(TEST_ACC_ID, TEST_CLIENT_TYPE, TEST_SALE_MODEL, TEST_CORP_CODE);
        verify(tktSellerPayawayConverter, times(1)).r_ds2ms(mockDoList);
    }

    /**
     * 测试目的：验证queryEnableSellerPayAwayList方法在null账户ID时的处理
     */
    @Test
    public void testQueryEnableSellerPayAwayList_WithNullAccId() {
        // 1. 创建测试数据
        List<TktSellerPayawayDO> mockDoList = Arrays.asList();
        List<TktSellerPayaway> expectedResult = Arrays.asList();

        // 2. Mock依赖组件返回空结果
        when(tktSellerPayawayDao.queryEnableSellerPayAwayList(null, TEST_CLIENT_TYPE, TEST_SALE_MODEL, TEST_CORP_CODE))
                .thenReturn(mockDoList);
        when(tktSellerPayawayConverter.r_ds2ms(mockDoList))
                .thenReturn(expectedResult);

        // 3. 调用方法
        List<TktSellerPayaway> result = tktSellerPayawayReadComponent.queryEnableSellerPayAwayList(null, TEST_CLIENT_TYPE, TEST_SALE_MODEL, TEST_CORP_CODE);

        // 4. 验证结果
        assertNotNull("查询结果不应为null", result);
        assertTrue("null账户ID应该返回空结果", result.isEmpty());

        // 验证方法调用
        verify(tktSellerPayawayDao, times(1)).queryEnableSellerPayAwayList(null, TEST_CLIENT_TYPE, TEST_SALE_MODEL, TEST_CORP_CODE);
        verify(tktSellerPayawayConverter, times(1)).r_ds2ms(mockDoList);
    }

    /**
     * 测试目的：验证queryEnableSellerPayAwayList方法在null客户类型时的处理
     */
    @Test
    public void testQueryEnableSellerPayAwayList_WithNullClientType() {
        // 1. 创建测试数据
        List<TktSellerPayawayDO> mockDoList = Arrays.asList();
        List<TktSellerPayaway> expectedResult = Arrays.asList();

        // 2. Mock依赖组件返回空结果
        when(tktSellerPayawayDao.queryEnableSellerPayAwayList(TEST_ACC_ID, null, TEST_SALE_MODEL, TEST_CORP_CODE))
                .thenReturn(mockDoList);
        when(tktSellerPayawayConverter.r_ds2ms(mockDoList))
                .thenReturn(expectedResult);

        // 3. 调用方法
        List<TktSellerPayaway> result = tktSellerPayawayReadComponent.queryEnableSellerPayAwayList(TEST_ACC_ID, null, TEST_SALE_MODEL, TEST_CORP_CODE);

        // 4. 验证结果
        assertNotNull("查询结果不应为null", result);
        assertTrue("null客户类型应该返回空结果", result.isEmpty());

        // 验证方法调用
        verify(tktSellerPayawayDao, times(1)).queryEnableSellerPayAwayList(TEST_ACC_ID, null, TEST_SALE_MODEL, TEST_CORP_CODE);
        verify(tktSellerPayawayConverter, times(1)).r_ds2ms(mockDoList);
    }

    /**
     * 测试目的：验证queryEnableSellerPayAwayList方法在null售票模式时的处理
     */
    @Test
    public void testQueryEnableSellerPayAwayList_WithNullSaleModel() {
        // 1. 创建测试数据
        List<TktSellerPayawayDO> mockDoList = Arrays.asList();
        List<TktSellerPayaway> expectedResult = Arrays.asList();

        // 2. Mock依赖组件返回空结果
        when(tktSellerPayawayDao.queryEnableSellerPayAwayList(TEST_ACC_ID, TEST_CLIENT_TYPE, null, TEST_CORP_CODE))
                .thenReturn(mockDoList);
        when(tktSellerPayawayConverter.r_ds2ms(mockDoList))
                .thenReturn(expectedResult);

        // 3. 调用方法
        List<TktSellerPayaway> result = tktSellerPayawayReadComponent.queryEnableSellerPayAwayList(TEST_ACC_ID, TEST_CLIENT_TYPE, null, TEST_CORP_CODE);

        // 4. 验证结果
        assertNotNull("查询结果不应为null", result);
        assertTrue("null售票模式应该返回空结果", result.isEmpty());

        // 验证方法调用
        verify(tktSellerPayawayDao, times(1)).queryEnableSellerPayAwayList(TEST_ACC_ID, TEST_CLIENT_TYPE, null, TEST_CORP_CODE);
        verify(tktSellerPayawayConverter, times(1)).r_ds2ms(mockDoList);
    }

    /**
     * 测试目的：验证queryEnableSellerPayAwayList方法在null企业编码时的处理
     */
    @Test
    public void testQueryEnableSellerPayAwayList_WithNullCorpCode() {
        // 1. 创建测试数据
        List<TktSellerPayawayDO> mockDoList = Arrays.asList();
        List<TktSellerPayaway> expectedResult = Arrays.asList();

        // 2. Mock依赖组件返回空结果
        when(tktSellerPayawayDao.queryEnableSellerPayAwayList(TEST_ACC_ID, TEST_CLIENT_TYPE, TEST_SALE_MODEL, null))
                .thenReturn(mockDoList);
        when(tktSellerPayawayConverter.r_ds2ms(mockDoList))
                .thenReturn(expectedResult);

        // 3. 调用方法
        List<TktSellerPayaway> result = tktSellerPayawayReadComponent.queryEnableSellerPayAwayList(TEST_ACC_ID, TEST_CLIENT_TYPE, TEST_SALE_MODEL, null);

        // 4. 验证结果
        assertNotNull("查询结果不应为null", result);
        assertTrue("null企业编码应该返回空结果", result.isEmpty());

        // 验证方法调用
        verify(tktSellerPayawayDao, times(1)).queryEnableSellerPayAwayList(TEST_ACC_ID, TEST_CLIENT_TYPE, TEST_SALE_MODEL, null);
        verify(tktSellerPayawayConverter, times(1)).r_ds2ms(mockDoList);
    }

    /**
     * 测试目的：验证queryEnableSellerPayAwayList方法在DAO返回空结果时的处理
     */
    @Test
    public void testQueryEnableSellerPayAwayList_WithEmptyResult() {
        // 1. 创建测试数据
        List<TktSellerPayawayDO> mockDoList = Arrays.asList();
        List<TktSellerPayaway> expectedResult = Arrays.asList();

        // 2. Mock依赖组件返回空结果
        when(tktSellerPayawayDao.queryEnableSellerPayAwayList(TEST_ACC_ID, TEST_CLIENT_TYPE, TEST_SALE_MODEL, TEST_CORP_CODE))
                .thenReturn(mockDoList);
        when(tktSellerPayawayConverter.r_ds2ms(mockDoList))
                .thenReturn(expectedResult);

        // 3. 调用方法
        List<TktSellerPayaway> result = tktSellerPayawayReadComponent.queryEnableSellerPayAwayList(TEST_ACC_ID, TEST_CLIENT_TYPE, TEST_SALE_MODEL, TEST_CORP_CODE);

        // 4. 验证结果
        assertNotNull("查询结果不应为null", result);
        assertTrue("无匹配数据时应该返回空结果", result.isEmpty());

        // 验证方法调用
        verify(tktSellerPayawayDao, times(1)).queryEnableSellerPayAwayList(TEST_ACC_ID, TEST_CLIENT_TYPE, TEST_SALE_MODEL, TEST_CORP_CODE);
        verify(tktSellerPayawayConverter, times(1)).r_ds2ms(mockDoList);
    }

    /**
     * 测试目的：验证queryEnableSellerPayAwayList方法在DAO抛出异常时的处理
     */
    @Test
    public void testQueryEnableSellerPayAwayList_WithDaoException() {
        // 1. 创建测试数据

        // 2. Mock依赖组件抛出异常
        when(tktSellerPayawayDao.queryEnableSellerPayAwayList(TEST_ACC_ID, TEST_CLIENT_TYPE, TEST_SALE_MODEL, TEST_CORP_CODE))
                .thenThrow(new RuntimeException("数据库连接异常"));

        // 3. 调用方法并验证异常
        try {
            tktSellerPayawayReadComponent.queryEnableSellerPayAwayList(TEST_ACC_ID, TEST_CLIENT_TYPE, TEST_SALE_MODEL, TEST_CORP_CODE);
            assertTrue("应该抛出RuntimeException", false);
        } catch (RuntimeException e) {
            assertEquals("数据库连接异常", e.getMessage());
        }

        // 验证方法调用
        verify(tktSellerPayawayDao, times(1)).queryEnableSellerPayAwayList(TEST_ACC_ID, TEST_CLIENT_TYPE, TEST_SALE_MODEL, TEST_CORP_CODE);
        verify(tktSellerPayawayConverter, never()).r_ds2ms(any());
    }

    /**
     * 测试目的：验证queryEnableSellerPayAwayList方法在Converter抛出异常时的处理
     */
    @Test
    public void testQueryEnableSellerPayAwayList_WithConverterException() {
        // 1. 创建测试数据
        List<TktSellerPayawayDO> mockDoList = Arrays.asList(createTktSellerPayawayDO(TEST_ID_1));

        // 2. Mock依赖组件抛出异常
        when(tktSellerPayawayDao.queryEnableSellerPayAwayList(TEST_ACC_ID, TEST_CLIENT_TYPE, TEST_SALE_MODEL, TEST_CORP_CODE))
                .thenReturn(mockDoList);
        when(tktSellerPayawayConverter.r_ds2ms(mockDoList))
                .thenThrow(new RuntimeException("转换异常"));

        // 3. 调用方法并验证异常
        try {
            tktSellerPayawayReadComponent.queryEnableSellerPayAwayList(TEST_ACC_ID, TEST_CLIENT_TYPE, TEST_SALE_MODEL, TEST_CORP_CODE);
            assertTrue("应该抛出RuntimeException", false);
        } catch (RuntimeException e) {
            assertEquals("转换异常", e.getMessage());
        }

        // 验证方法调用
        verify(tktSellerPayawayDao, times(1)).queryEnableSellerPayAwayList(TEST_ACC_ID, TEST_CLIENT_TYPE, TEST_SALE_MODEL, TEST_CORP_CODE);
        verify(tktSellerPayawayConverter, times(1)).r_ds2ms(mockDoList);
    }

    /**
     * 测试目的：验证queryEnableSellerPayAwayList方法处理不同参数组合时的逻辑
     */
    @Test
    public void testQueryEnableSellerPayAwayList_WithDifferentParameters() {
        // 1. 创建不同参数组合的测试数据
        List<TktSellerPayawayDO> mockDoList1 = Arrays.asList(createTktSellerPayawayDO(TEST_ID_1));
        List<TktSellerPayaway> expectedResult1 = Arrays.asList(createTktSellerPayaway(TEST_ID_1));

        List<TktSellerPayawayDO> mockDoList2 = Arrays.asList(createTktSellerPayawayDO(TEST_ID_2));
        List<TktSellerPayaway> expectedResult2 = Arrays.asList(createTktSellerPayaway(TEST_ID_2));

        // 2. Mock依赖组件返回不同结果
        when(tktSellerPayawayDao.queryEnableSellerPayAwayList(TEST_ACC_ID, 1, 1, TEST_CORP_CODE))
                .thenReturn(mockDoList1);
        when(tktSellerPayawayConverter.r_ds2ms(mockDoList1))
                .thenReturn(expectedResult1);

        when(tktSellerPayawayDao.queryEnableSellerPayAwayList(TEST_ACC_ID, 2, 2, TEST_CORP_CODE))
                .thenReturn(mockDoList2);
        when(tktSellerPayawayConverter.r_ds2ms(mockDoList2))
                .thenReturn(expectedResult2);

        // 3. 调用方法并验证结果
        List<TktSellerPayaway> result1 = tktSellerPayawayReadComponent.queryEnableSellerPayAwayList(TEST_ACC_ID, 1, 1, TEST_CORP_CODE);
        List<TktSellerPayaway> result2 = tktSellerPayawayReadComponent.queryEnableSellerPayAwayList(TEST_ACC_ID, 2, 2, TEST_CORP_CODE);

        // 4. 验证结果
        assertNotNull("查询结果1不应为null", result1);
        assertEquals("查询结果1应该返回1条记录", 1, result1.size());
        assertEquals("查询结果1的ID应该一致", TEST_ID_1, result1.get(0).getId());

        assertNotNull("查询结果2不应为null", result2);
        assertEquals("查询结果2应该返回1条记录", 1, result2.size());
        assertEquals("查询结果2的ID应该一致", TEST_ID_2, result2.get(0).getId());

        // 验证方法调用
        verify(tktSellerPayawayDao, times(1)).queryEnableSellerPayAwayList(TEST_ACC_ID, 1, 1, TEST_CORP_CODE);
        verify(tktSellerPayawayDao, times(1)).queryEnableSellerPayAwayList(TEST_ACC_ID, 2, 2, TEST_CORP_CODE);
        verify(tktSellerPayawayConverter, times(1)).r_ds2ms(mockDoList1);
        verify(tktSellerPayawayConverter, times(1)).r_ds2ms(mockDoList2);
    }

    /**
     * 测试目的：验证queryEnableSellerPayAwayList方法处理大量结果时的性能
     */
    @Test
    public void testQueryEnableSellerPayAwayList_WithLargeResult() {
        // 1. 创建大量结果数据
        List<TktSellerPayawayDO> mockDoList = Arrays.asList(
                createTktSellerPayawayDO(1L),
                createTktSellerPayawayDO(2L),
                createTktSellerPayawayDO(3L),
                createTktSellerPayawayDO(4L),
                createTktSellerPayawayDO(5L)
        );
        List<TktSellerPayaway> expectedResult = Arrays.asList(
                createTktSellerPayaway(1L),
                createTktSellerPayaway(2L),
                createTktSellerPayaway(3L),
                createTktSellerPayaway(4L),
                createTktSellerPayaway(5L)
        );

        // 2. Mock依赖组件返回预期结果
        when(tktSellerPayawayDao.queryEnableSellerPayAwayList(TEST_ACC_ID, TEST_CLIENT_TYPE, TEST_SALE_MODEL, TEST_CORP_CODE))
                .thenReturn(mockDoList);
        when(tktSellerPayawayConverter.r_ds2ms(mockDoList))
                .thenReturn(expectedResult);

        // 3. 调用方法
        List<TktSellerPayaway> result = tktSellerPayawayReadComponent.queryEnableSellerPayAwayList(TEST_ACC_ID, TEST_CLIENT_TYPE, TEST_SALE_MODEL, TEST_CORP_CODE);

        // 4. 验证结果
        assertNotNull("查询结果不应为null", result);
        assertEquals("应该返回5条记录", 5, result.size());

        // 验证方法调用
        verify(tktSellerPayawayDao, times(1)).queryEnableSellerPayAwayList(TEST_ACC_ID, TEST_CLIENT_TYPE, TEST_SALE_MODEL, TEST_CORP_CODE);
        verify(tktSellerPayawayConverter, times(1)).r_ds2ms(mockDoList);
    }

    // ==================== 测试数据构建方法 ====================

    /**
     * 创建TktSellerPayawayDO测试数据
     */
    private TktSellerPayawayDO createTktSellerPayawayDO(Long id) {
        TktSellerPayawayDO tktSellerPayawayDO = new TktSellerPayawayDO();
        tktSellerPayawayDO.setId(id);
        tktSellerPayawayDO.setCorpCode(TEST_CORP_CODE);
        tktSellerPayawayDO.setSeller(TEST_SELLER);
        tktSellerPayawayDO.setAccId(TEST_ACC_ID);
        tktSellerPayawayDO.setSaleModel(TEST_SALE_MODEL);
        tktSellerPayawayDO.setClientType(TEST_CLIENT_TYPE);
        tktSellerPayawayDO.setPayAway(TEST_PAY_AWAY);
        tktSellerPayawayDO.setSort(TEST_SORT);
        tktSellerPayawayDO.setUseFlag("T");
        tktSellerPayawayDO.setPayId(TEST_PAY_ID);
        tktSellerPayawayDO.setCreateBy(TEST_CREATE_BY);
        tktSellerPayawayDO.setModifyBy(TEST_MODIFY_BY);
        tktSellerPayawayDO.setDeleted("F");
        return tktSellerPayawayDO;
    }

    /**
     * 创建TktSellerPayaway测试数据
     */
    private TktSellerPayaway createTktSellerPayaway(Long id) {
        TktSellerPayaway tktSellerPayaway = new TktSellerPayaway();
        tktSellerPayaway.setId(id);
        tktSellerPayaway.setCorpCode(TEST_CORP_CODE);
        tktSellerPayaway.setSeller(TEST_SELLER);
        tktSellerPayaway.setAccId(TEST_ACC_ID);
        tktSellerPayaway.setSaleModel(SaleModelEnum.NORMAL_SALE);
        tktSellerPayaway.setClientType(ClientTypeEnum.INDIVIDUAL);
        tktSellerPayaway.setPayAway(TEST_PAY_AWAY);
        tktSellerPayaway.setSort(TEST_SORT);
        tktSellerPayaway.setUseFlag(CommonUseFlagEnum.ENABLED);
        tktSellerPayaway.setPayId(TEST_PAY_ID);
        tktSellerPayaway.setCreateBy(TEST_CREATE_BY);
        tktSellerPayaway.setModifyBy(TEST_MODIFY_BY);
        return tktSellerPayaway;
    }
} 