package com.sendinfo.ticketing.verification.basic.component.goods.converter;

import com.sendinfo.ticketing.verification.basic.component.goods.param.TktSaleRuleQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TktSaleRule;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TktSaleRuleQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TktSaleRuleDO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

/**
 * @Auther: chengjie.li
 * @Date: 2025/6/18 12:15
 */
@RunWith(MockitoJUnitRunner.class)
public class TktSaleRuleConverterTest {

    @InjectMocks
    private TktSaleRuleConverter tktSaleRuleConverter;

    private static final String TEST_CORP_CODE = "TEST_CORP_001";
    private static final Long TEST_TICKET_ID = 1L;
    private static final String TEST_SALE_MODEL = "0";
    private static final String TEST_CONSUMER_FLAG = "T";
    private static final String TEST_TRAVEL_FLAG = "T";
    private static final String TEST_SEASON_TYPE_OFF = "T";
    private static final String TEST_SEASON_TYPE_BUSY = "T";
    private static final String TEST_SALE_CHANNEL = "0";
    private static final String TEST_SALE_LIMIT = "T";
    private static final Integer TEST_MIN_NUM = 1;
    private static final Integer TEST_MAX_NUM = 10;
    private static final String TEST_SALE_REG_IDCARD = "T";
    private static final String TEST_BUY_LIMIT_FLAG = "T";
    private static final Integer TEST_TIME_LIMIT_TYPE = 1;
    private static final Integer TEST_TIME_LIMIT_VAL = 24;
    private static final Integer TEST_LIMIT_AMOUNT = 5;
    private static final String TEST_AGE_LIMIT_FLAG = "T";
    private static final String TEST_CREATE_BY = "testUser";

    /**
     * 测试目的：验证TktSaleRuleDO转换为TktSaleRule的功能
     * 测试步骤：
     * 1. 创建完整的TktSaleRuleDO对象
     * 2. 调用r_d2m方法进行转换
     * 3. 验证转换结果的正确性
     */
    @Test
    public void testReadDo2Model_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        TktSaleRuleDO tktSaleRuleDO = createTktSaleRuleDO();

        // 2. 执行转换
        TktSaleRule result = tktSaleRuleConverter.r_d2m(tktSaleRuleDO);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", result);
        assertEquals("企业编码应该一致", TEST_CORP_CODE, result.getCorpCode());
        assertEquals("票型ID应该一致", TEST_TICKET_ID, result.getTicketId());
        assertEquals("销售模式应该一致", TEST_SALE_MODEL, result.getSaleModel());
        assertEquals("散客可售标志应该一致", TEST_CONSUMER_FLAG, result.getConsumerFlag());
        assertEquals("旅行社可售标志应该一致", TEST_TRAVEL_FLAG, result.getTravelFlag());
        assertEquals("淡季可售标志应该一致", TEST_SEASON_TYPE_OFF, result.getSeasonTypeOff());
        assertEquals("旺季可售标志应该一致", TEST_SEASON_TYPE_BUSY, result.getSeasonTypeBusy());
        assertEquals("销售渠道应该一致", TEST_SALE_CHANNEL, result.getSaleChannel());
        assertEquals("是否限制购买数量应该一致", TEST_SALE_LIMIT, result.getSaleLimit());
        assertEquals("最少购票数量应该一致", TEST_MIN_NUM, result.getMinNum());
        assertEquals("最多购票数量应该一致", TEST_MAX_NUM, result.getMaxNum());
        assertEquals("购票登记证件应该一致", TEST_SALE_REG_IDCARD, result.getSaleRegIdcard());
        assertEquals("游客购票限制应该一致", TEST_BUY_LIMIT_FLAG, result.getBuyLimitFlag());
        assertEquals("限购时间类型应该一致", TEST_TIME_LIMIT_TYPE, result.getTimeLimitType());
        assertEquals("时间限制值应该一致", TEST_TIME_LIMIT_VAL, result.getTimeLimitVal());
        assertEquals("限购张数应该一致", TEST_LIMIT_AMOUNT, result.getLimitAmount());
        assertEquals("游客年龄限制应该一致", TEST_AGE_LIMIT_FLAG, result.getAgeLimitFlag());
        assertEquals("创建人应该一致", TEST_CREATE_BY, result.getCreateBy());
        assertEquals("修改人应该一致", TEST_CREATE_BY, result.getModifyBy());
    }

    /**
     * 测试目的：验证TktSaleRuleQueryParam转换为TktSaleRuleQueryArg的功能
     * 测试步骤：
     * 1. 创建完整的TktSaleRuleQueryParam对象
     * 2. 调用r_p2a方法进行转换
     * 3. 验证转换结果的正确性
     */
    @Test
    public void testReadQueryParam2QueryArg_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        TktSaleRuleQueryParam queryParam = createTktSaleRuleQueryParam();

        // 2. 执行转换
        TktSaleRuleQueryArg queryArg = tktSaleRuleConverter.r_p2a(queryParam);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", queryArg);
        assertEquals("企业编码应该一致", TEST_CORP_CODE, queryArg.getCorpCode());
        assertEquals("票型ID应该一致", TEST_TICKET_ID, queryArg.getTicketId());
        assertEquals("销售模式应该一致", TEST_SALE_MODEL, queryArg.getSaleModel());
        assertEquals("销售渠道应该一致", TEST_SALE_CHANNEL, queryArg.getSaleChannel());
        assertEquals("是否限制购买数量应该一致", TEST_SALE_LIMIT, queryArg.getSaleLimit());
        assertEquals("游客购票限制应该一致", TEST_BUY_LIMIT_FLAG, queryArg.getBuyLimitFlag());
        assertEquals("游客年龄限制应该一致", TEST_AGE_LIMIT_FLAG, queryArg.getAgeLimitFlag());
    }

    /**
     * 测试目的：验证List<TktSaleRuleDO>转换为List<TktSaleRule>的功能
     * 测试步骤：
     * 1. 创建完整的TktSaleRuleDO列表
     * 2. 调用r_ds2ms方法进行转换
     * 3. 验证转换结果的正确性
     */
    @Test
    public void testReadDos2Models_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        List<TktSaleRuleDO> tktSaleRuleDOList = createTktSaleRuleDOList();

        // 2. 执行转换
        List<TktSaleRule> result = tktSaleRuleConverter.r_ds2ms(tktSaleRuleDOList);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", result);
        assertEquals("列表大小应该一致", tktSaleRuleDOList.size(), result.size());

        // 验证第一个元素
        TktSaleRule firstResult = result.get(0);
        assertEquals("第一个元素的票型ID应该一致", TEST_TICKET_ID, firstResult.getTicketId());
        assertEquals("第一个元素的销售模式应该一致", TEST_SALE_MODEL, firstResult.getSaleModel());
        assertEquals("第一个元素的散客可售标志应该一致", TEST_CONSUMER_FLAG, firstResult.getConsumerFlag());

        // 验证第二个元素
        TktSaleRule secondResult = result.get(1);
        assertEquals("第二个元素的票型ID应该一致", Optional.of(TEST_TICKET_ID + 1).get(), secondResult.getTicketId());
        assertEquals("第二个元素的销售模式应该一致", TEST_SALE_MODEL, secondResult.getSaleModel());
        assertEquals("第二个元素的散客可售标志应该一致", TEST_CONSUMER_FLAG, secondResult.getConsumerFlag());
    }

    private TktSaleRuleQueryParam createTktSaleRuleQueryParam() {
        TktSaleRuleQueryParam queryParam = new TktSaleRuleQueryParam();
        queryParam.setCorpCode(TEST_CORP_CODE);
        queryParam.setTicketId(TEST_TICKET_ID);
        queryParam.setSaleModel(TEST_SALE_MODEL);
        queryParam.setSaleChannel(TEST_SALE_CHANNEL);
        queryParam.setSaleLimit(TEST_SALE_LIMIT);
        queryParam.setBuyLimitFlag(TEST_BUY_LIMIT_FLAG);
        queryParam.setAgeLimitFlag(TEST_AGE_LIMIT_FLAG);
        return queryParam;
    }

    private TktSaleRuleDO createTktSaleRuleDO() {
        TktSaleRuleDO saleRuleDO = new TktSaleRuleDO();
        saleRuleDO.setCorpCode(TEST_CORP_CODE);
        saleRuleDO.setTicketId(TEST_TICKET_ID);
        saleRuleDO.setSaleModel(TEST_SALE_MODEL);
        saleRuleDO.setConsumerFlag(TEST_CONSUMER_FLAG);
        saleRuleDO.setTravelFlag(TEST_TRAVEL_FLAG);
        saleRuleDO.setSeasonTypeOff(TEST_SEASON_TYPE_OFF);
        saleRuleDO.setSeasonTypeBusy(TEST_SEASON_TYPE_BUSY);
        saleRuleDO.setSaleChannel(TEST_SALE_CHANNEL);
        saleRuleDO.setSaleLimit(TEST_SALE_LIMIT);
        saleRuleDO.setMinNum(TEST_MIN_NUM);
        saleRuleDO.setMaxNum(TEST_MAX_NUM);
        saleRuleDO.setSaleRegIdcard(TEST_SALE_REG_IDCARD);
        saleRuleDO.setBuyLimitFlag(TEST_BUY_LIMIT_FLAG);
        saleRuleDO.setTimeLimitType(TEST_TIME_LIMIT_TYPE);
        saleRuleDO.setTimeLimitVal(TEST_TIME_LIMIT_VAL);
        saleRuleDO.setLimitAmount(TEST_LIMIT_AMOUNT);
        saleRuleDO.setAgeLimitFlag(TEST_AGE_LIMIT_FLAG);
        saleRuleDO.setCreateBy(TEST_CREATE_BY);
        saleRuleDO.setModifyBy(TEST_CREATE_BY);
        return saleRuleDO;
    }

    private List<TktSaleRuleDO> createTktSaleRuleDOList() {
        List<TktSaleRuleDO> list = new ArrayList<>();

        TktSaleRuleDO saleRuleDO1 = new TktSaleRuleDO();
        saleRuleDO1.setCorpCode(TEST_CORP_CODE);
        saleRuleDO1.setTicketId(TEST_TICKET_ID);
        saleRuleDO1.setSaleModel(TEST_SALE_MODEL);
        saleRuleDO1.setConsumerFlag(TEST_CONSUMER_FLAG);
        saleRuleDO1.setTravelFlag(TEST_TRAVEL_FLAG);
        saleRuleDO1.setSeasonTypeOff(TEST_SEASON_TYPE_OFF);
        saleRuleDO1.setSeasonTypeBusy(TEST_SEASON_TYPE_BUSY);
        saleRuleDO1.setSaleChannel(TEST_SALE_CHANNEL);
        saleRuleDO1.setSaleLimit(TEST_SALE_LIMIT);
        saleRuleDO1.setMinNum(TEST_MIN_NUM);
        saleRuleDO1.setMaxNum(TEST_MAX_NUM);
        saleRuleDO1.setSaleRegIdcard(TEST_SALE_REG_IDCARD);
        saleRuleDO1.setBuyLimitFlag(TEST_BUY_LIMIT_FLAG);
        saleRuleDO1.setTimeLimitType(TEST_TIME_LIMIT_TYPE);
        saleRuleDO1.setTimeLimitVal(TEST_TIME_LIMIT_VAL);
        saleRuleDO1.setLimitAmount(TEST_LIMIT_AMOUNT);
        saleRuleDO1.setAgeLimitFlag(TEST_AGE_LIMIT_FLAG);
        saleRuleDO1.setCreateBy(TEST_CREATE_BY);
        saleRuleDO1.setModifyBy(TEST_CREATE_BY);
        list.add(saleRuleDO1);

        TktSaleRuleDO saleRuleDO2 = new TktSaleRuleDO();
        saleRuleDO2.setCorpCode(TEST_CORP_CODE);
        saleRuleDO2.setTicketId(TEST_TICKET_ID + 1);
        saleRuleDO2.setSaleModel(TEST_SALE_MODEL);
        saleRuleDO2.setConsumerFlag(TEST_CONSUMER_FLAG);
        saleRuleDO2.setTravelFlag(TEST_TRAVEL_FLAG);
        saleRuleDO2.setSeasonTypeOff(TEST_SEASON_TYPE_OFF);
        saleRuleDO2.setSeasonTypeBusy(TEST_SEASON_TYPE_BUSY);
        saleRuleDO2.setSaleChannel(TEST_SALE_CHANNEL);
        saleRuleDO2.setSaleLimit(TEST_SALE_LIMIT);
        saleRuleDO2.setMinNum(TEST_MIN_NUM + 1);
        saleRuleDO2.setMaxNum(TEST_MAX_NUM + 1);
        saleRuleDO2.setSaleRegIdcard(TEST_SALE_REG_IDCARD);
        saleRuleDO2.setBuyLimitFlag(TEST_BUY_LIMIT_FLAG);
        saleRuleDO2.setTimeLimitType(TEST_TIME_LIMIT_TYPE);
        saleRuleDO2.setTimeLimitVal(TEST_TIME_LIMIT_VAL);
        saleRuleDO2.setLimitAmount(TEST_LIMIT_AMOUNT + 1);
        saleRuleDO2.setAgeLimitFlag(TEST_AGE_LIMIT_FLAG);
        saleRuleDO2.setCreateBy(TEST_CREATE_BY);
        saleRuleDO2.setModifyBy(TEST_CREATE_BY);
        list.add(saleRuleDO2);

        return list;
    }
}