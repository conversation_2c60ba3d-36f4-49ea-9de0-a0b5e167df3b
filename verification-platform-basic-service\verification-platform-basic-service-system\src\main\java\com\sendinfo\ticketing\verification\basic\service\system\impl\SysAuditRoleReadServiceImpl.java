package com.sendinfo.ticketing.verification.basic.service.system.impl;

import com.sendinfo.ticketing.verification.basic.api.system.SysAuditRoleReadService;
import com.sendinfo.ticketing.verification.basic.api.system.request.SysAuditRoleQueryRequest;
import com.sendinfo.ticketing.verification.basic.model.system.SysAuditRole;
import com.sendinfo.ticketing.verification.basic.model.system.error.SystemErrorDef;
import com.sendinfo.ticketing.verification.basic.service.system.enums.SystemAttachmentKey;
import com.sendinfo.ticketing.verification.basic.service.system.function.QuerySysAuditRoleFunction;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;
import com.sendinfo.ticketing.verification.common.api.result.Results;
import com.sendinfo.ticketing.verification.common.service.support.ApiCommonFailedBiActions;
import com.sendinfo.ticketing.verification.flow.FlowAgentBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * 票型角色读取服务实现类
 * <AUTHOR>
 */
@Slf4j
@Service("sysAuditRoleReadService")
public class SysAuditRoleReadServiceImpl implements SysAuditRoleReadService {

    private final FlowAgentBuilder flowAgentBuilder;
    private final QuerySysAuditRoleFunction querySysAuditRoleFunction;

    public SysAuditRoleReadServiceImpl(FlowAgentBuilder flowAgentBuilder, QuerySysAuditRoleFunction querySysAuditRoleFunction) {
        this.flowAgentBuilder = flowAgentBuilder;
        this.querySysAuditRoleFunction = querySysAuditRoleFunction;
    }

    @Override
    public ResultModel<List<SysAuditRole>> querySysAuditRoles(SysAuditRoleQueryRequest request) {
        return flowAgentBuilder.<SysAuditRoleQueryRequest, ResultModel<List<SysAuditRole>>>validateThenChooseBuilder()
                .appendLogicAction(querySysAuditRoleFunction::querySysAuditRoles)
                .withSuccessfulAction(q -> Results.success(q.getAttachment(SystemAttachmentKey.SYS_AUDIT_ROLE_DATA_LIST_ATTACHMENT_KEY)))
                .withFailedBiAction(ApiCommonFailedBiActions::reasonMappingToResult)
                .withExceptionBiAction((q, th) -> {
                    log.error("SysAuditRoleReadServiceImpl querySysAuditRoles error. question is {}, error is {}", q, th);
                    return Results.fail(SystemErrorDef.QUERY_SYS_AUDIT_ROLE_ERROR);
                })
                .build()
                .prompt(request)
                .getResult();
    }

    @Override
    public ResultModel<List<SysAuditRole>> querySysAuditRoleByIds(Set<Integer> ids, String corpCode) {
        return flowAgentBuilder.<Pair<Set<Integer>, String>, ResultModel<List<SysAuditRole>>>validateThenChooseBuilder()
                .appendLogicAction(querySysAuditRoleFunction::querySysAuditRoleByIds)
                .withSuccessfulAction(q -> Results.success(q.getAttachment(SystemAttachmentKey.SYS_AUDIT_ROLE_DATA_LIST_ATTACHMENT_KEY)))
                .withFailedBiAction(ApiCommonFailedBiActions::reasonMappingToResult)
                .withExceptionBiAction((q, th) -> {
                    log.error("SysAuditRoleReadServiceImpl querySysAuditRoleByIds error. question is {}, error is {}", q, th);
                    return Results.fail(SystemErrorDef.QUERY_SYS_AUDIT_ROLE_ERROR);
                })
                .build()
                .prompt(Pair.of(ids, corpCode))
                .getResult();
    }
} 