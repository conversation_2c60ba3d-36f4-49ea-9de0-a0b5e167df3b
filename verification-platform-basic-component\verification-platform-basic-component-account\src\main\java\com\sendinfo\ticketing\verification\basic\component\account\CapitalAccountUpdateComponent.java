package com.sendinfo.ticketing.verification.basic.component.account;

import com.sendinfo.ticketing.verification.basic.component.account.param.CapitalAccountUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.account.CapitalAccount;
import com.sendinfo.ticketing.verification.common.component.UpdateComponent;

/**
 * 资金账户更新组件接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface CapitalAccountUpdateComponent extends UpdateComponent<CapitalAccountUpdateParam, CapitalAccount> {
} 