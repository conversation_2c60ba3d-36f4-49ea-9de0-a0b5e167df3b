package com.sendinfo.ticketing.verification.basic.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 启用状态枚举
 * 定义启用、禁用状态
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
public enum CommonUseFlagEnum {
    ENABLED("T", "启用"),
    DISABLED("F", "禁用");

    @JsonValue
    private final String code;
    private final String description;

    CommonUseFlagEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    // --- 高效反向查找机制 ---
    private static final Map<String, CommonUseFlagEnum> CODE_MAP =
        Arrays.stream(values()).collect(Collectors.toMap(CommonUseFlagEnum::getCode, Function.identity()));

    /**
     * 根据 code 获取枚举实例
     *
     * @param code 启用状态编码
     * @return 对应的枚举实例，如果找不到则返回 null
     */
    @JsonCreator
    public static CommonUseFlagEnum ofCode(String code) {
        if (code == null) {
            return null;
        }
        return CODE_MAP.get(code);
    }
} 