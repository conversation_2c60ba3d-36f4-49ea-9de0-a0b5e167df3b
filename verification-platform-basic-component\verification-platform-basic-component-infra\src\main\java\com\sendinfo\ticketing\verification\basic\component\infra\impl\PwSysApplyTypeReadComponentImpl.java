package com.sendinfo.ticketing.verification.basic.component.infra.impl;

import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import com.sendinfo.ticketing.verification.basic.component.infra.PwSysApplyTypeReadComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.converter.PwSysApplyTypeConverter;
import com.sendinfo.ticketing.verification.basic.model.infra.PwSysApplyType;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.PwSysApplyTypeDao;

/**
 * 系统应用类型读取组件实现
 * 
 * <AUTHOR> 2025-07-24 15:40:00
 */
@Component("pwSysApplyTypeReadComponent")
@Getter
public class PwSysApplyTypeReadComponentImpl implements PwSysApplyTypeReadComponent {

    private final PwSysApplyTypeDao dao;
    private final PwSysApplyTypeConverter converter;

    public PwSysApplyTypeReadComponentImpl(PwSysApplyTypeDao dao, PwSysApplyTypeConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }

    /**
     * 根据子系统ID查询应用类型列表
     * 只返回启用且未删除的记录
     */
    public List<PwSysApplyType> queryBySubSystemId(Integer subsystemId) {
        return Optional.ofNullable(subsystemId)
                .map(dao::queryBySubSystemId)
                .map(converter::r_ds2ms)
                .orElse(Collections.emptyList());
    }


}