package com.sendinfo.ticketing.verification.basic.api.infra;

import java.util.List;

import com.sendinfo.ticketing.verification.basic.api.infra.request.PwSysFunctionsMarkAsRequisitionRequest;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

/**
 * 客户端菜单管理服务
 * 
 * <AUTHOR> 2025-07-24
 */
public interface PwSysFunctionManageService {

    /**
     * 标记系统功能菜单为已征用
     *
     * @param request  功能菜单ID列表
     *
     * @return
     */
    ResultModel<Boolean> markAsRequisition(PwSysFunctionsMarkAsRequisitionRequest request);
}
