package com.sendinfo.ticketing.verification.basic.component.pay.component;

import com.sendinfo.ticketing.verification.basic.component.pay.converter.SysPaySetConverter;
import com.sendinfo.ticketing.verification.basic.component.pay.impl.SysPaySetReadComponentImpl;
import com.sendinfo.ticketing.verification.basic.enums.CommonUseFlagEnum;
import com.sendinfo.ticketing.verification.basic.model.pay.SysPaySet;
import com.sendinfo.ticketing.verification.basic.model.pay.enums.PayDefaultEnum;
import com.sendinfo.ticketing.verification.basic.model.pay.enums.PayTypeEnum;
import com.sendinfo.ticketing.verification.basic.repository.pay.dao.SysPaySetDao;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysPaySetDO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

import static junit.framework.TestCase.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;

/**
 * SysPaySetReadComponent单元测试
 * 测试支付标签读取组件的各种查询功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RunWith(MockitoJUnitRunner.class)
public class SysPaySetReadComponentTest {

    @Mock
    private SysPaySetDao sysPaySetDao;

    @Mock
    private SysPaySetConverter sysPaySetConverter;

    @InjectMocks
    private SysPaySetReadComponentImpl sysPaySetReadComponent;

    // 测试常量
    private static final String TEST_PAY_TYPE = "主扫";
    private static final String TEST_CORP_CODE = "TEST_CORP_001";
    private static final Long TEST_ID_1 = 1L;
    private static final Long TEST_ID_2 = 2L;
    private static final Long TEST_ID_3 = 3L;
    private static final String TEST_PAY_LABEL = "测试支付标签";
    private static final String TEST_PAY_TYPE_CODE = "PAY_TYPE_001";
    private static final String TEST_PAY_LABEL_ICON = "icon_pay.png";
    private static final String TEST_REMARK = "这是一个测试支付标签";
    private static final Long TEST_MAINBODY_ID = 100L;
    private static final String TEST_PAY_NAME = "自定义支付";
    private static final String TEST_TRANSFER_PARAM = "transfer_data";
    private static final String TEST_CREATE_BY = "testUser";
    private static final String TEST_MODIFY_BY = "testUser";

    /**
     * 测试目的：验证queryEnableListByPayTypeAndId方法的正常查询功能
     * 测试步骤：
     * 1. 创建测试输入数据
     * 2. Mock依赖组件返回预期结果
     * 3. 调用queryEnableListByPayTypeAndId方法
     * 4. 验证返回结果的正确性
     */
    @Test
    public void testQueryEnableListByPayTypeAndId_ShouldReturnEnabledPaySets() {
        // 1. 创建测试输入数据
        Set<Long> idSet = Set.of(TEST_ID_1, TEST_ID_2, TEST_ID_3);
        List<SysPaySetDO> mockDoList = Arrays.asList(
                createSysPaySetDO(TEST_ID_1),
                createSysPaySetDO(TEST_ID_2)
        );
        List<SysPaySet> expectedResult = Arrays.asList(
                createSysPaySet(TEST_ID_1),
                createSysPaySet(TEST_ID_2)
        );

        // 2. Mock依赖组件返回预期结果
        when(sysPaySetDao.queryEnableListByPayTypeAndId(TEST_PAY_TYPE, idSet, TEST_CORP_CODE))
                .thenReturn(mockDoList);
        when(sysPaySetConverter.r_ds2ms(mockDoList))
                .thenReturn(expectedResult);

        // 3. 调用queryEnableListByPayTypeAndId方法
        List<SysPaySet> result = sysPaySetReadComponent.queryEnableListByPayTypeAndId(TEST_PAY_TYPE, idSet, TEST_CORP_CODE);

        // 4. 验证返回结果的正确性
        assertNotNull("查询结果不应为null", result);
        assertEquals("应该返回2条记录", 2, result.size());
        assertEquals("第一条记录的ID应该一致", TEST_ID_1, result.get(0).getId());
        assertEquals("第二条记录的ID应该一致", TEST_ID_2, result.get(1).getId());

        // 验证方法调用
        verify(sysPaySetDao, times(1)).queryEnableListByPayTypeAndId(TEST_PAY_TYPE, idSet, TEST_CORP_CODE);
        verify(sysPaySetConverter, times(1)).r_ds2ms(mockDoList);
    }

    /**
     * 测试目的：验证queryEnableListByPayTypeAndId方法在空ID集合时的处理
     */
    @Test
    public void testQueryEnableListByPayTypeAndId_WithEmptyIdSet() {
        // 1. 创建空ID集合
        Set<Long> emptyIdSet = Set.of();
        List<SysPaySetDO> mockDoList = Arrays.asList();
        List<SysPaySet> expectedResult = Arrays.asList();

        // 2. Mock依赖组件返回空结果
        when(sysPaySetDao.queryEnableListByPayTypeAndId(TEST_PAY_TYPE, emptyIdSet, TEST_CORP_CODE))
                .thenReturn(mockDoList);
        when(sysPaySetConverter.r_ds2ms(mockDoList))
                .thenReturn(expectedResult);

        // 3. 调用方法
        List<SysPaySet> result = sysPaySetReadComponent.queryEnableListByPayTypeAndId(TEST_PAY_TYPE, emptyIdSet, TEST_CORP_CODE);

        // 4. 验证结果
        assertNotNull("查询结果不应为null", result);
        assertTrue("空ID集合应该返回空结果", result.isEmpty());

        // 验证方法调用
        verify(sysPaySetDao, times(1)).queryEnableListByPayTypeAndId(TEST_PAY_TYPE, emptyIdSet, TEST_CORP_CODE);
        verify(sysPaySetConverter, times(1)).r_ds2ms(mockDoList);
    }

    /**
     * 测试目的：验证queryEnableListByPayTypeAndId方法在null支付类型时的处理
     */
    @Test
    public void testQueryEnableListByPayTypeAndId_WithNullPayType() {
        // 1. 创建测试数据
        Set<Long> idSet = Set.of(TEST_ID_1);
        List<SysPaySetDO> mockDoList = Arrays.asList();
        List<SysPaySet> expectedResult = Arrays.asList();

        // 2. Mock依赖组件返回空结果
        when(sysPaySetDao.queryEnableListByPayTypeAndId(null, idSet, TEST_CORP_CODE))
                .thenReturn(mockDoList);
        when(sysPaySetConverter.r_ds2ms(mockDoList))
                .thenReturn(expectedResult);

        // 3. 调用方法
        List<SysPaySet> result = sysPaySetReadComponent.queryEnableListByPayTypeAndId(null, idSet, TEST_CORP_CODE);

        // 4. 验证结果
        assertNotNull("查询结果不应为null", result);
        assertTrue("null支付类型应该返回空结果", result.isEmpty());

        // 验证方法调用
        verify(sysPaySetDao, times(1)).queryEnableListByPayTypeAndId(null, idSet, TEST_CORP_CODE);
        verify(sysPaySetConverter, times(1)).r_ds2ms(mockDoList);
    }

    /**
     * 测试目的：验证queryEnableListByPayTypeAndId方法在null企业编码时的处理
     */
    @Test
    public void testQueryEnableListByPayTypeAndId_WithNullCorpCode() {
        // 1. 创建测试数据
        Set<Long> idSet = Set.of(TEST_ID_1);
        List<SysPaySetDO> mockDoList = Arrays.asList();
        List<SysPaySet> expectedResult = Arrays.asList();

        // 2. Mock依赖组件返回空结果
        when(sysPaySetDao.queryEnableListByPayTypeAndId(TEST_PAY_TYPE, idSet, null))
                .thenReturn(mockDoList);
        when(sysPaySetConverter.r_ds2ms(mockDoList))
                .thenReturn(expectedResult);

        // 3. 调用方法
        List<SysPaySet> result = sysPaySetReadComponent.queryEnableListByPayTypeAndId(TEST_PAY_TYPE, idSet, null);

        // 4. 验证结果
        assertNotNull("查询结果不应为null", result);
        assertTrue("null企业编码应该返回空结果", result.isEmpty());

        // 验证方法调用
        verify(sysPaySetDao, times(1)).queryEnableListByPayTypeAndId(TEST_PAY_TYPE, idSet, null);
        verify(sysPaySetConverter, times(1)).r_ds2ms(mockDoList);
    }

    /**
     * 测试目的：验证queryEnableListByPayTypeAndId方法在DAO返回空结果时的处理
     */
    @Test
    public void testQueryEnableListByPayTypeAndId_WithEmptyResult() {
        // 1. 创建测试数据
        Set<Long> idSet = Set.of(TEST_ID_1, TEST_ID_2);
        List<SysPaySetDO> mockDoList = Arrays.asList();
        List<SysPaySet> expectedResult = Arrays.asList();

        // 2. Mock依赖组件返回空结果
        when(sysPaySetDao.queryEnableListByPayTypeAndId(TEST_PAY_TYPE, idSet, TEST_CORP_CODE))
                .thenReturn(mockDoList);
        when(sysPaySetConverter.r_ds2ms(mockDoList))
                .thenReturn(expectedResult);

        // 3. 调用方法
        List<SysPaySet> result = sysPaySetReadComponent.queryEnableListByPayTypeAndId(TEST_PAY_TYPE, idSet, TEST_CORP_CODE);

        // 4. 验证结果
        assertNotNull("查询结果不应为null", result);
        assertTrue("无匹配数据时应该返回空结果", result.isEmpty());

        // 验证方法调用
        verify(sysPaySetDao, times(1)).queryEnableListByPayTypeAndId(TEST_PAY_TYPE, idSet, TEST_CORP_CODE);
        verify(sysPaySetConverter, times(1)).r_ds2ms(mockDoList);
    }

    /**
     * 测试目的：验证queryEnableListByPayTypeAndId方法处理大量ID时的性能
     */
    @Test
    public void testQueryEnableListByPayTypeAndId_WithLargeIdSet() {
        // 1. 创建大量ID集合
        Set<Long> largeIdSet = Set.of(1L, 2L, 3L, 4L, 5L, 6L, 7L, 8L, 9L, 10L);
        List<SysPaySetDO> mockDoList = Arrays.asList(
                createSysPaySetDO(1L),
                createSysPaySetDO(2L),
                createSysPaySetDO(3L)
        );
        List<SysPaySet> expectedResult = Arrays.asList(
                createSysPaySet(1L),
                createSysPaySet(2L),
                createSysPaySet(3L)
        );

        // 2. Mock依赖组件返回预期结果
        when(sysPaySetDao.queryEnableListByPayTypeAndId(TEST_PAY_TYPE, largeIdSet, TEST_CORP_CODE))
                .thenReturn(mockDoList);
        when(sysPaySetConverter.r_ds2ms(mockDoList))
                .thenReturn(expectedResult);

        // 3. 调用方法
        List<SysPaySet> result = sysPaySetReadComponent.queryEnableListByPayTypeAndId(TEST_PAY_TYPE, largeIdSet, TEST_CORP_CODE);

        // 4. 验证结果
        assertNotNull("查询结果不应为null", result);
        assertEquals("应该返回3条记录", 3, result.size());

        // 验证方法调用
        verify(sysPaySetDao, times(1)).queryEnableListByPayTypeAndId(TEST_PAY_TYPE, largeIdSet, TEST_CORP_CODE);
        verify(sysPaySetConverter, times(1)).r_ds2ms(mockDoList);
    }

    // ==================== 测试数据构建方法 ====================

    /**
     * 创建SysPaySetDO测试数据
     */
    private SysPaySetDO createSysPaySetDO(Long id) {
        SysPaySetDO sysPaySetDO = new SysPaySetDO();
        sysPaySetDO.setId(id);
        sysPaySetDO.setPaylableName(TEST_PAY_LABEL);
        sysPaySetDO.setPayTypeCode(TEST_PAY_TYPE_CODE);
        sysPaySetDO.setPaylableIcon(TEST_PAY_LABEL_ICON);
        sysPaySetDO.setUseFlag(CommonUseFlagEnum.ENABLED.getCode());
        sysPaySetDO.setRemark(TEST_REMARK);
        sysPaySetDO.setPayDefault(PayDefaultEnum.YES.getCode());
        sysPaySetDO.setMainbodyId(TEST_MAINBODY_ID);
        sysPaySetDO.setPayType(PayTypeEnum.CASH_PAYMENT.getCode());
        sysPaySetDO.setPayName(TEST_PAY_NAME);
        sysPaySetDO.setTransferParam(TEST_TRANSFER_PARAM);
        sysPaySetDO.setCreateBy(TEST_CREATE_BY);
        sysPaySetDO.setModifyBy(TEST_MODIFY_BY);
        return sysPaySetDO;
    }

    /**
     * 创建SysPaySet测试数据
     */
    private SysPaySet createSysPaySet(Long id) {
        SysPaySet sysPaySet = new SysPaySet();
        sysPaySet.setId(id);
        sysPaySet.setPaylableName(TEST_PAY_LABEL);
        sysPaySet.setPayTypeCode(TEST_PAY_TYPE_CODE);
        sysPaySet.setPaylableIcon(TEST_PAY_LABEL_ICON);
        sysPaySet.setUseFlag(CommonUseFlagEnum.ENABLED);
        sysPaySet.setRemark(TEST_REMARK);
        sysPaySet.setPayDefault(PayDefaultEnum.YES);
        sysPaySet.setMainbodyId(TEST_MAINBODY_ID);
        sysPaySet.setPayType(PayTypeEnum.CASH_PAYMENT);
        sysPaySet.setPayName(TEST_PAY_NAME);
        sysPaySet.setTransferParam(TEST_TRANSFER_PARAM);
        sysPaySet.setCreateBy(TEST_CREATE_BY);
        sysPaySet.setModifyBy(TEST_MODIFY_BY);
        return sysPaySet;
    }
} 