package com.sendinfo.ticketing.verification.basic.api.goods.request;

import com.sendinfo.ticketing.verification.basic.enums.ExplainerUserFlagEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 服务类目查询请求对象
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString
public class ExplainerServiceCategoryQueryRequest implements Serializable {

    /**
     * 企业编码
     */
    @NotNull(message = "企业编码不能为空")
    private String corpCode;

    /**
     * 业态(系统字典)
     */
    private String businessType;

    /**
     * 服务编码
     */
    private String categoryNo;

    /**
     * 服务类型名称
     */
    private String categoryName;

    /**
     * 状态F禁用 T启用
     */
    private String useFlag;
} 