package com.sendinfo.ticketing.verification.basic.api.customer.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * 旅行社信息查询请求对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/17 19:33
 */
@Getter
@Setter
@ToString
public class TravelInfoQueryRequest implements Serializable {

	/**
	 * 企业编码
	 */
	private String corpCode;
	/**
	 * 旅行社名称
	 */
	private String travelName;
	/**
	 * 旅行社编码
	 */
	private String travelCode;
	/**
	 * 旅行社类型ID
	 */
	private String travelTypeId;
	/**
	 * 旅行社分组ID
	 */
	private Long travelGroupId;
	/**
	 * 地区编号
	 */
	private String areaCode;
	/**
	 * 地区名称
	 */
	private String areaName;
	/**
	 * 是否地接社
	 */
	private String travelIsLocal;
	/**
	 * 是否组团社
	 */
	private String travelIsClub;

	/**
	 * 是否返回禁用客户信息
	 */
	private String isReturnDisableTravel;

}
