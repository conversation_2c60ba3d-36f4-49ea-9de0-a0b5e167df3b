package com.sendinfo.ticketing.verification.basic.api.config.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2025/6/2 16:02
 **/
@Getter
@Setter
@ToString
public class TenantTesbConfigCreateRequest implements Serializable {
    private static final long serialVersionUID = -7030708126600197810L;
    /**
     * 配置编码
     */
    private String configCode;

    /**
     * 配置值
     */
    private String configValue;

    /**
     * 配置名称
     */
    private String configName;

    /**
     * 配置父ID
     */
    private Long configParentId;

    /**
     * 状态：1-启用，0-禁用
     */
    private Integer status;

    /**
     * 排序
     */
    private Integer sortNum;

    /**
     * 描述
     */
    private String description;

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 创建人
     */
    private String createBy;

}
