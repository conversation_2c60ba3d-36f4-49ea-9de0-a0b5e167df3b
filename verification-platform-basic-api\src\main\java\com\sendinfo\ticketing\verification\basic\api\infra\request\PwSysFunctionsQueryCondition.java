package com.sendinfo.ticketing.verification.basic.api.infra.request;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * 系统功能模块查询条件
 * 
 * <AUTHOR> 2025-07-24
 */
@Getter
@Setter
@ToString
public class PwSysFunctionsQueryCondition implements Serializable {
    private static final long serialVersionUID = -3456789012345678901L;

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 名称
     */
    private String funName;

    /**
     * 唯一编码
     */
    private String funCode;

    /**
     * 菜单编码
     */
    private String menuCode;

    /**
     * 功能类别 menu（菜单）、column（栏目）、button（按钮）
     */
    private String functionType;

    /**
     * 子系统ID
     */
    private Integer subsystemId;

}
