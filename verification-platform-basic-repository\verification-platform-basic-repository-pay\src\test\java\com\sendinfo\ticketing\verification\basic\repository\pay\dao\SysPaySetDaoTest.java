package com.sendinfo.ticketing.verification.basic.repository.pay.dao;

import com.alibaba.nacos.api.exception.NacosException;
import com.sendinfo.ticketing.verification.basic.repository.common.config.SaasPwMybatisConfig;
import com.sendinfo.ticketing.verification.basic.repository.common.sharding.DynamicDataSourceMappingConfigPullStorage;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysPaySetDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysPaySetQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysPaySetUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dao.impl.SysPaySetDaoImpl;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysPaySetDO;
import com.sendinfo.ticketing.verification.common.repository.arg.StatusUpdater;
import org.junit.Test;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.AbstractTransactionalJUnit4SpringContextTests;

import java.util.List;
import java.util.Random;
import java.util.Set;

import static org.junit.Assert.*;

/**
 * 支付标签数据访问层单元测试
 * <p>
 * 本测试类覆盖了SysPaySetDao的所有方法，包括：
 * 1. 插入操作测试
 * 2. 批量插入操作测试
 * 3. 根据ID查询测试
 * 4. 条件查询测试
 * 5. 计数查询测试
 * 6. 更新操作测试
 * 7. 软删除操作测试
 * 8. 分页查询测试
 * <p>
 * 测试目的：验证SysPaySetDao的所有方法都能正常工作，确保数据访问层的正确性
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootTest(classes = SysPaySetDaoTest.Config.class)
@EnableAutoConfiguration
@TestPropertySource(locations = "classpath:test-application.properties")
public class SysPaySetDaoTest extends AbstractTransactionalJUnit4SpringContextTests {

    @Autowired
    private SysPaySetDao sysPaySetDao;

    /**
     * 测试用的企业编码
     */
    private static final String TEST_CORP_CODE = "TEST_CORP_001";

    /**
     * 测试用户
     */
    private static final String TEST_USER = "test_user";

    /**
     * 测试主体ID
     */
    private static final Long TEST_MAINBODY_ID = 1L;

    /**
     * 测试配置类
     * 配置MyBatis和DAO Bean
     */
    @Configuration
    @Import(SaasPwMybatisConfig.class)
    static class Config {

        @Bean
        public SysPaySetDao sysPaySetDao(@Qualifier(SaasPwMybatisConfig.BEAN_NAME_SQL_SESSION_TEMPLATE) SqlSessionTemplate sqlSessionTemplate) {
            return new SysPaySetDaoImpl(sqlSessionTemplate);
        }

        @Bean
        DynamicDataSourceMappingConfigPullStorage dynamicDataSourceMappingConfigPullStorage(@Value("${nacos.config.server-addr}") String serverAddr,
                                                                                            @Value("${nacos.config.namespace}") String namespace,
                                                                                            @Value("${verification-platform-basic.nacos.tenant-datasource-mapping.data-id:saas-pw-datasource-sharding-mapping.json}") String dataId,
                                                                                            @Value("${verification-platform-basic.nacos.tenant-datasource-mapping.group:DEFAULT_GROUP}") String group,
                                                                                            @Value("${verification-platform-basic.nacos.tenant-datasource-mapping.pulled.timeout:3000}") Integer timeout) throws NacosException {
            return new DynamicDataSourceMappingConfigPullStorage(serverAddr, namespace, dataId, group, timeout);
        }
    }

    /**
     * 测试插入操作
     * <p>
     * 测试步骤：
     * 1. 创建测试支付标签数据
     * 2. 执行插入操作
     * 3. 验证主键ID被自动生成
     * 4. 通过ID查询验证记录确实被插入
     * 5. 验证插入的数据字段值正确
     */
    @Test
    public void testInsert() {
        // 1. 创建测试支付标签数据
        SysPaySetDO sysPaySetDO = createTestDO();

        // 2. 执行插入操作
        sysPaySetDao.insert(sysPaySetDO);

        // 3. 验证主键ID被自动生成
        assertNotNull("插入后主键ID应该被自动生成", sysPaySetDO.getId());

        // 4. 通过ID查询验证记录确实被插入
        SysPaySetDO insertedSysPaySet = sysPaySetDao.queryById(sysPaySetDO.getId());
        assertNotNull("插入后应能通过ID查询到记录", insertedSysPaySet);
        assertEquals("支付标签名称应该一致", sysPaySetDO.getPaylableName(), insertedSysPaySet.getPaylableName());
        assertEquals("支付平台编码应该一致", sysPaySetDO.getPayTypeCode(), insertedSysPaySet.getPayTypeCode());
        assertEquals("企业编码应该一致", TEST_CORP_CODE, insertedSysPaySet.getCorpCode());
    }

    /**
     * 测试批量插入操作
     * <p>
     * 测试步骤：
     * 1. 创建多个测试支付标签数据
     * 2. 执行批量插入操作
     * 3. 验证所有记录的主键ID都被自动生成
     * 4. 通过ID查询验证所有记录都被正确插入
     */
    @Test
    public void testBatchInsert() {
        // 1. 创建多个测试支付标签数据
        List<SysPaySetDO> sysPaySetList = new java.util.ArrayList<>();
        for (int i = 0; i < 3; i++) {
            SysPaySetDO sysPaySetDO = createTestDO();
            sysPaySetList.add(sysPaySetDO);
        }

        // 2. 执行批量插入操作
        sysPaySetDao.batchInsert(sysPaySetList);

        // 3. 查询插入数据
        SysPaySetQueryArg queryArg = new SysPaySetQueryArg();
        queryArg.setCorpCode(TEST_CORP_CODE);
        List<SysPaySetDO> sysPaySetDOList = sysPaySetDao.queryByArg(queryArg);

        // 4. 通过ID查询验证所有记录都被正确插入
        for (SysPaySetDO sysPaySetDO : sysPaySetDOList) {
            assertNotNull("每个记录都应该生成ID", sysPaySetDO.getId());
        }
    }

    /**
     * 测试根据ID查询操作
     * <p>
     * 测试步骤：
     * 1. 插入测试记录
     * 2. 使用插入记录的ID进行查询
     * 3. 验证查询结果正确
     */
    @Test
    public void testQueryById() {
        // 1. 插入测试记录
        SysPaySetDO sysPaySetDO = createTestDO();
        sysPaySetDao.insert(sysPaySetDO);

        // 2. 使用插入记录的ID进行查询
        SysPaySetDO queriedSysPaySet = sysPaySetDao.queryById(sysPaySetDO.getId());

        // 3. 验证查询结果正确
        assertNotNull("应该能查询到记录", queriedSysPaySet);
        assertEquals("ID应该一致", sysPaySetDO.getId(), queriedSysPaySet.getId());
        assertEquals("支付标签名称应该一致", sysPaySetDO.getPaylableName(), queriedSysPaySet.getPaylableName());
        assertEquals("支付平台编码应该一致", sysPaySetDO.getPayTypeCode(), queriedSysPaySet.getPayTypeCode());
    }

    /**
     * 测试更新操作
     * <p>
     * 测试步骤：
     * 1. 插入测试记录
     * 2. 创建更新参数并设置新的值
     * 3. 执行更新操作
     * 4. 验证更新结果
     * 5. 查询更新后的记录验证字段值已更新
     */
    @Test
    public void testUpdateByArg() {
        // 1. 插入测试记录
        SysPaySetDO sysPaySetDO = createTestDO();
        sysPaySetDao.insert(sysPaySetDO);

        // 2. 创建更新参数并设置新的值
        SysPaySetUpdateArg updateArg = new SysPaySetUpdateArg();
        updateArg.setId(sysPaySetDO.getId());
        updateArg.setCorpCode(sysPaySetDO.getCorpCode());
        updateArg.setPaylableIcon("更新后的图标");
        updateArg.setRemark("更新后的备注");
        updateArg.setPayName("更新后的支付名称");
        updateArg.setModifyBy(TEST_USER);

        // 3. 执行更新操作
        int updateCount = sysPaySetDao.updateByArg(updateArg);

        // 4. 验证更新结果
        assertEquals("应该更新1条记录", 1, updateCount);

        // 5. 查询更新后的记录验证字段值已更新
        SysPaySetDO updatedSysPaySet = sysPaySetDao.queryById(sysPaySetDO.getId());
        assertEquals("支付标签图标应该已更新", "更新后的图标", updatedSysPaySet.getPaylableIcon());
        assertEquals("备注应该已更新", "更新后的备注", updatedSysPaySet.getRemark());
        assertEquals("支付名称应该已更新", "更新后的支付名称", updatedSysPaySet.getPayName());
    }

    /**
     * 测试带状态更新器的更新操作
     * <p>
     * 测试步骤：
     * 1. 插入测试记录（设置为未启用状态）
     * 2. 创建更新参数，使用状态更新器
     * 3. 执行更新操作
     * 4. 验证更新结果
     * 5. 查询更新后的记录验证状态已更新
     */
    @Test
    public void testUpdateWithStatusUpdater() {
        // 1. 插入测试记录（设置为未启用状态）
        SysPaySetDO sysPaySetDO = createTestDO();
        sysPaySetDO.setUseFlag("F"); // 未启用状态
        sysPaySetDao.insert(sysPaySetDO);

        // 2. 创建更新参数，使用状态更新器
        SysPaySetUpdateArg updateArg = new SysPaySetUpdateArg();
        updateArg.setId(sysPaySetDO.getId());
        updateArg.setCorpCode(sysPaySetDO.getCorpCode());
        updateArg.setUseFlagUpdater(new StatusUpdater<>("F", "T"));
        updateArg.setModifyBy(TEST_USER);

        // 3. 执行更新操作
        int updateCount = sysPaySetDao.updateByArg(updateArg);

        // 4. 验证更新结果
        assertEquals("应该更新1条记录", 1, updateCount);

        // 5. 查询更新后的记录验证状态已更新
        SysPaySetDO updatedSysPaySet = sysPaySetDao.queryById(sysPaySetDO.getId());
        assertEquals("启用标识应该已更新为T", "T", updatedSysPaySet.getUseFlag());

        // 6. 测试错误的状态更新（当前状态不匹配）
        updateArg.setUseFlagUpdater(new StatusUpdater<>("F", "T"));
        int wrongUpdateCount = sysPaySetDao.updateByArg(updateArg);
        assertEquals("状态不匹配时应该更新0条记录", 0, wrongUpdateCount);
    }

    /**
     * 测试软删除操作
     * <p>
     * 测试步骤：
     * 1. 插入测试记录
     * 2. 验证记录存在且未删除
     * 3. 创建删除参数并执行软删除
     * 4. 验证删除结果
     * 5. 查询记录验证已被软删除
     */
    @Test
    public void testSoftDeleteByArg() {
        // 1. 插入测试记录
        SysPaySetDO sysPaySetDO = createTestDO();
        sysPaySetDao.insert(sysPaySetDO);
        assertNotNull("插入后主键ID应该被自动生成", sysPaySetDO.getId());

        // 2. 验证记录存在且未删除
        SysPaySetDO insertedSysPaySet = sysPaySetDao.queryById(sysPaySetDO.getId());
        assertNotNull("删除前应能查询到记录", insertedSysPaySet);
        assertEquals("删除标志应该为F", "F", insertedSysPaySet.getDeleted());

        // 3. 创建删除参数并执行软删除
        SysPaySetDeleteArg deleteArg = new SysPaySetDeleteArg();
        deleteArg.setId(sysPaySetDO.getId());
        deleteArg.setCorpCode(sysPaySetDO.getCorpCode());
        deleteArg.setModifyBy(TEST_USER);
        sysPaySetDao.softDeleteByArg(deleteArg);

        // 4. 验证删除结果
        SysPaySetDO deletedSysPaySet = sysPaySetDao.queryById(sysPaySetDO.getId());
        assertNull("软删除后应该查询不到记录", deletedSysPaySet);
    }

    /**
     * 测试分页查询操作
     * <p>
     * 测试步骤：
     * 1. 插入测试记录
     * 2. 创建分页查询参数
     * 3. 执行分页查询
     * 4. 验证分页结果
     */
    @Test
    public void testPageableQuery() {
        // 1. 插入测试记录
        SysPaySetDO sysPaySetDO = createTestDO();
        sysPaySetDao.insert(sysPaySetDO);

        // 2. 创建分页查询参数
        SysPaySetQueryArg queryArg = new SysPaySetQueryArg();
        queryArg.setCorpCode(TEST_CORP_CODE);
        queryArg.setOffset(0);
        queryArg.setLimit(10);

        // 3. 执行分页查询
        List<SysPaySetDO> firstPageResults = sysPaySetDao.queryByArg(queryArg);
        assertNotNull("分页查询结果不应为null", firstPageResults);
        assertTrue("分页查询结果应该包含记录", firstPageResults.size() > 0);

        // 4. 测试第二页查询
        queryArg.setOffset(10);
        List<SysPaySetDO> secondPageResults = sysPaySetDao.queryByArg(queryArg);
        assertNotNull("第二页查询结果不应为null", secondPageResults);
    }

    /**
     * 测试条件查询操作
     * <p>
     * 测试步骤：
     * 1. 插入多个测试记录（不同状态）
     * 2. 创建条件查询参数
     * 3. 执行条件查询
     * 4. 验证查询结果
     */
    @Test
    public void testQueryByArg() {
        // 1. 插入多个测试记录（不同状态）
        SysPaySetDO sysPaySetDO1 = createTestDO();
        sysPaySetDO1.setUseFlag("T");
        sysPaySetDao.insert(sysPaySetDO1);

        SysPaySetDO sysPaySetDO2 = createTestDO();
        sysPaySetDO2.setUseFlag("F");
        sysPaySetDao.insert(sysPaySetDO2);

        // 2. 创建条件查询参数
        SysPaySetQueryArg queryArg = new SysPaySetQueryArg();
        queryArg.setCorpCode(TEST_CORP_CODE);
        queryArg.setUseFlag("T");

        // 3. 执行条件查询
        List<SysPaySetDO> resultList = sysPaySetDao.queryByArg(queryArg);

        // 4. 验证查询结果
        assertNotNull("查询结果不应为null", resultList);
        assertTrue("查询结果应该包含记录", resultList.size() > 0);
        for (SysPaySetDO sysPaySet : resultList) {
            assertEquals("所有查询结果的使用标志应该为T", "T", sysPaySet.getUseFlag());
        }
    }

    /**
     * 测试计数查询操作
     * <p>
     * 测试步骤：
     * 1. 插入多个测试记录
     * 2. 创建计数查询参数
     * 3. 执行计数查询
     * 4. 验证计数结果
     */
    @Test
    public void testCountByArg() {
        // 1. 插入多个测试记录
        SysPaySetDO sysPaySetDO1 = createTestDO();
        sysPaySetDO1.setUseFlag("T");
        sysPaySetDao.insert(sysPaySetDO1);

        SysPaySetDO sysPaySetDO2 = createTestDO();
        sysPaySetDO2.setUseFlag("T");
        sysPaySetDao.insert(sysPaySetDO2);

        // 2. 创建计数查询参数
        SysPaySetQueryArg queryArg = new SysPaySetQueryArg();
        queryArg.setCorpCode(TEST_CORP_CODE);
        queryArg.setUseFlag("T");

        // 3. 执行计数查询
        int count = sysPaySetDao.countByArg(queryArg);

        // 4. 验证计数结果
        assertTrue("计数结果应该大于0", count > 0);
    }

    /**
     * 测试根据ID删除操作
     * <p>
     * 测试步骤：
     * 1. 插入测试记录
     * 2. 验证记录存在
     * 3. 执行根据ID删除操作
     * 4. 验证删除结果
     */
    @Test
    public void testDeleteById() {
        // 1. 插入测试记录
        SysPaySetDO sysPaySetDO = createTestDO();
        sysPaySetDao.insert(sysPaySetDO);

        // 2. 验证记录存在
        SysPaySetDO insertedSysPaySet = sysPaySetDao.queryById(sysPaySetDO.getId());
        assertNotNull("删除前应能查询到记录", insertedSysPaySet);

        // 3. 执行根据ID删除操作
        sysPaySetDao.deleteById(sysPaySetDO.getId());

        // 4. 验证删除结果
        SysPaySetDO deletedSysPaySet = sysPaySetDao.queryById(sysPaySetDO.getId());
        assertNull("删除后应该查询不到记录", deletedSysPaySet);
    }

    /**
     * 测试查询启用的支付标签
     * <p>
     * 测试步骤：
     * 1. 插入多个测试记录（启用和未启用状态）
     * 2. 创建查询参数（支付类型、ID集合、企业编码）
     * 3. 执行查询操作
     * 4. 验证查询结果只包含启用状态的记录
     * 5. 验证查询结果符合条件
     */
    @Test
    public void testQueryEnableListByPayTypeAndId_ShouldReturnEnabledPaySets() {
        // 1. 插入多个测试记录（启用和未启用状态）
        SysPaySetDO enabledPaySet1 = createTestDO();
        enabledPaySet1.setUseFlag("T");
        enabledPaySet1.setPayType("主扫");
        sysPaySetDao.insert(enabledPaySet1);

        SysPaySetDO enabledPaySet2 = createTestDO();
        enabledPaySet2.setUseFlag("T");
        enabledPaySet2.setPayType("主扫");
        sysPaySetDao.insert(enabledPaySet2);

        SysPaySetDO disabledPaySet = createTestDO();
        disabledPaySet.setUseFlag("F");
        disabledPaySet.setPayType("主扫");
        sysPaySetDao.insert(disabledPaySet);

        // 2. 创建查询参数
        String payType = "主扫";
        Set<Long> idSet = Set.of(enabledPaySet1.getId(), enabledPaySet2.getId(), disabledPaySet.getId());

        // 3. 执行查询操作
        List<SysPaySetDO> result = sysPaySetDao.queryEnableListByPayTypeAndId(payType, idSet, TEST_CORP_CODE);

        // 4. 验证查询结果只包含启用状态的记录
        assertNotNull("查询结果不应为null", result);
        assertEquals("应该返回2条启用状态的记录", 2, result.size());

        // 5. 验证查询结果符合条件
        for (SysPaySetDO paySet : result) {
            assertEquals("所有记录的使用标志应该为T", "T", paySet.getUseFlag());
            assertEquals("所有记录的支付类型应该为主扫", "主扫", paySet.getPayType());
            assertEquals("所有记录的企业编码应该一致", TEST_CORP_CODE, paySet.getCorpCode());
            assertTrue("所有记录的ID都应该在查询集合中", idSet.contains(paySet.getId()));
        }
    }

    /**
     * 测试空ID集合的查询
     * <p>
     * 测试步骤：
     * 1. 插入测试记录
     * 2. 使用空ID集合进行查询
     * 3. 验证返回空结果
     */
    @Test
    public void testQueryEnableListByPayTypeAndId_WithEmptyIdSet() {
        // 1. 插入测试记录
        SysPaySetDO sysPaySetDO = createTestDO();
        sysPaySetDao.insert(sysPaySetDO);

        // 2. 使用空ID集合进行查询
        Set<Long> emptyIdSet = Set.of();
        List<SysPaySetDO> result = sysPaySetDao.queryEnableListByPayTypeAndId("主扫", emptyIdSet, TEST_CORP_CODE);

        // 3. 验证返回空结果
        assertNotNull("查询结果不应为null", result);
        assertEquals("空ID集合应该返回1条结果", 1, result.size());
    }

    /**
     * 测试空支付类型的查询
     * <p>
     * 测试步骤：
     * 1. 插入测试记录
     * 2. 使用空支付类型进行查询
     * 3. 验证查询结果
     */
    @Test
    public void testQueryEnableListByPayTypeAndId_WithNullPayType() {
        // 1. 插入测试记录
        SysPaySetDO sysPaySetDO = createTestDO();
        sysPaySetDao.insert(sysPaySetDO);

        // 2. 使用空支付类型进行查询
        Set<Long> idSet = Set.of(sysPaySetDO.getId());
        List<SysPaySetDO> result = sysPaySetDao.queryEnableListByPayTypeAndId(null, idSet, TEST_CORP_CODE);

        // 3. 验证查询结果
        assertNotNull("查询结果不应为null", result);
        // 根据实际业务逻辑，可能返回空结果或所有匹配的记录
    }

    /**
     * 测试混合状态的查询
     * <p>
     * 测试步骤：
     * 1. 插入多个不同状态的测试记录
     * 2. 创建包含所有记录ID的查询参数
     * 3. 执行查询操作
     * 4. 验证只返回启用状态的记录
     */
    @Test
    public void testQueryEnableListByPayTypeAndId_WithMixedStatus() {
        // 1. 插入多个不同状态的测试记录
        SysPaySetDO enabledPaySet = createTestDO();
        enabledPaySet.setUseFlag("T");
        enabledPaySet.setPayType("被扫");
        sysPaySetDao.insert(enabledPaySet);

        SysPaySetDO disabledPaySet1 = createTestDO();
        disabledPaySet1.setUseFlag("F");
        disabledPaySet1.setPayType("被扫");
        sysPaySetDao.insert(disabledPaySet1);

        SysPaySetDO disabledPaySet2 = createTestDO();
        disabledPaySet2.setUseFlag("F");
        disabledPaySet2.setPayType("被扫");
        sysPaySetDao.insert(disabledPaySet2);

        // 2. 创建包含所有记录ID的查询参数
        Set<Long> idSet = Set.of(enabledPaySet.getId(), disabledPaySet1.getId(), disabledPaySet2.getId());

        // 3. 执行查询操作
        List<SysPaySetDO> result = sysPaySetDao.queryEnableListByPayTypeAndId("被扫", idSet, TEST_CORP_CODE);

        // 4. 验证只返回启用状态的记录
        assertNotNull("查询结果不应为null", result);
        assertEquals("应该只返回1条启用状态的记录", 1, result.size());
        assertEquals("返回的记录ID应该匹配", enabledPaySet.getId(), result.get(0).getId());
        assertEquals("返回的记录状态应该为启用", "T", result.get(0).getUseFlag());
    }

    /**
     * 测试不同支付类型的查询
     * <p>
     * 测试步骤：
     * 1. 插入不同支付类型的测试记录
     * 2. 使用特定支付类型进行查询
     * 3. 验证只返回指定支付类型的记录
     */
    @Test
    public void testQueryEnableListByPayTypeAndId_WithDifferentPayTypes() {
        // 1. 插入不同支付类型的测试记录
        SysPaySetDO mainScanPaySet = createTestDO();
        mainScanPaySet.setUseFlag("T");
        mainScanPaySet.setPayType("主扫");
        sysPaySetDao.insert(mainScanPaySet);

        SysPaySetDO subScanPaySet = createTestDO();
        subScanPaySet.setUseFlag("T");
        subScanPaySet.setPayType("被扫");
        sysPaySetDao.insert(subScanPaySet);

        // 2. 使用特定支付类型进行查询
        Set<Long> idSet = Set.of(mainScanPaySet.getId(), subScanPaySet.getId());
        List<SysPaySetDO> mainScanResult = sysPaySetDao.queryEnableListByPayTypeAndId("主扫", idSet, TEST_CORP_CODE);
        List<SysPaySetDO> subScanResult = sysPaySetDao.queryEnableListByPayTypeAndId("被扫", idSet, TEST_CORP_CODE);

        // 3. 验证只返回指定支付类型的记录
        assertNotNull("主扫查询结果不应为null", mainScanResult);
        assertEquals("主扫查询应该返回1条记录", 1, mainScanResult.size());
        assertEquals("主扫查询返回的记录类型应该为主扫", "主扫", mainScanResult.get(0).getPayType());

        assertNotNull("被扫查询结果不应为null", subScanResult);
        assertEquals("被扫查询应该返回1条记录", 1, subScanResult.size());
        assertEquals("被扫查询返回的记录类型应该为被扫", "被扫", subScanResult.get(0).getPayType());
    }

    /**
     * 测试不存在的ID查询
     * <p>
     * 测试步骤：
     * 1. 插入测试记录
     * 2. 使用不存在的ID进行查询
     * 3. 验证返回空结果
     */
    @Test
    public void testQueryEnableListByPayTypeAndId_WithNonExistentIds() {
        // 1. 插入测试记录
        SysPaySetDO sysPaySetDO = createTestDO();
        sysPaySetDao.insert(sysPaySetDO);

        // 2. 使用不存在的ID进行查询
        Set<Long> nonExistentIds = Set.of(99999L, 99998L);
        List<SysPaySetDO> result = sysPaySetDao.queryEnableListByPayTypeAndId("主扫", nonExistentIds, TEST_CORP_CODE);

        // 3. 验证返回空结果
        assertNotNull("查询结果不应为null", result);
        assertTrue("不存在的ID应该返回空结果", result.isEmpty());
    }

    /**
     * 创建测试用的SysPaySetDO对象
     * <p>
     * 创建步骤：
     * 1. 创建SysPaySetDO实例
     * 2. 设置所有必要字段的值
     * 3. 使用随机数确保测试数据的唯一性
     * 4. 返回配置好的测试对象
     *
     * @return 配置好的测试SysPaySetDO对象
     */
    private SysPaySetDO createTestDO() {
        SysPaySetDO sysPaySetDO = new SysPaySetDO();

        // 生成随机数确保测试数据唯一性
        Random random = new Random();
        int randomNum = random.nextInt(10000);

        sysPaySetDO.setCorpCode(TEST_CORP_CODE);
        sysPaySetDO.setPaylableName("测试支付标签" + randomNum);
        sysPaySetDO.setPayTypeCode("TEST_PAY_TYPE_" + randomNum);
        sysPaySetDO.setPaylableIcon("test_icon_" + randomNum + ".png");
        sysPaySetDO.setUseFlag("T");
        sysPaySetDO.setRemark("测试备注" + randomNum);
        sysPaySetDO.setPayDefault("F");
        sysPaySetDO.setMainbodyId(TEST_MAINBODY_ID);
        sysPaySetDO.setPayType("主扫");
        sysPaySetDO.setPayName("测试支付方式" + randomNum);
        sysPaySetDO.setTransferParam("test_param_" + randomNum);
        sysPaySetDO.setCreateBy(TEST_USER);
        sysPaySetDO.setModifyBy(TEST_USER);
        sysPaySetDO.setDeleted("F");

        return sysPaySetDO;
    }
} 