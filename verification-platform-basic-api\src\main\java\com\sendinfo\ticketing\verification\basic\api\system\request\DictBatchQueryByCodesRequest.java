package com.sendinfo.ticketing.verification.basic.api.system.request;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Set;

/**
 * @Auther: chengjie.li
 * @Date: 2025/6/23 16:55
 */
@Getter
@Setter
public class DictBatchQueryByCodesRequest implements Serializable {

    private static final long serialVersionUID = -5210586370888791964L;
    /**
     * 字典编码
     */
    @NotEmpty
    private Set<String> dictCodes;


    /**
     * 企业编码
     */
    @NotNull
    private String corpCode;


    /**
     * 字典类型
     */
    @NotNull
    private Integer dictType;
}
