<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sendinfo.ticketing.verification.basic.repository.infra.dao.PwSysFunctionsDao">

    <!-- ResultMap -->
    <resultMap id="BaseResultMap" type="com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.PwSysFunctionsDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="corp_code" property="corpCode" jdbcType="VARCHAR"/>
        <result column="fun_name" property="funName" jdbcType="VARCHAR"/>
        <result column="fun_code" property="funCode" jdbcType="VARCHAR"/>
        <result column="fun_ico" property="funIco" jdbcType="VARCHAR"/>
        <result column="fun_url" property="funUrl" jdbcType="VARCHAR"/>
        <result column="fun_type" property="funType" jdbcType="VARCHAR"/>
        <result column="opt_type" property="optType" jdbcType="VARCHAR"/>
        <result column="parent_id" property="parentId" jdbcType="INTEGER"/>
        <result column="parent_ids" property="parentIds" jdbcType="VARCHAR"/>
        <result column="sort_no" property="sortNo" jdbcType="INTEGER"/>
        <result column="hierarchy" property="hierarchy" jdbcType="SMALLINT"/>
        <result column="open_type" property="openType" jdbcType="VARCHAR"/>
        <result column="function_type" property="functionType" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="modify_by" property="modifyBy" jdbcType="VARCHAR"/>
        <result column="deleted" property="deleted" jdbcType="VARCHAR"/>
        <result column="outer_chain" property="outerChain" jdbcType="VARCHAR"/>
        <result column="subsystem_id" property="subsystemId" jdbcType="INTEGER"/>
        <result column="cloud_fun_url" property="cloudFunUrl" jdbcType="VARCHAR"/>
        <result column="use_flag" property="useFlag" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="LONGVARCHAR"/>
        <result column="is_group_show" property="isGroupShow" jdbcType="TINYINT"/>
        <result column="requisition_flag" property="requisitionFlag" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 表名 -->
    <sql id="tableName">sys_functions</sql>

    <!-- Reusable Column List -->
    <sql id="columns">
        `id`,
        `corp_code`,
        `fun_name`,
        `fun_code`,
        `fun_ico`,
        `fun_url`,
        `fun_type`,
        `opt_type`,
        `parent_id`,
        `parent_ids`,
        `sort_no`,
        `hierarchy`,
        `open_type`,
        `function_type`,
        `create_time`,
        `create_by`,
        `modify_time`,
        `modify_by`,
        `deleted`,
        `outer_chain`,
        `subsystem_id`,
        `cloud_fun_url`,
        `use_flag`,
        `description`,
        `is_group_show`,
        `requisition_flag`
    </sql>

    <!-- queryById -->
    <select id="queryById" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="columns"/>
        from <include refid="tableName"/>
        where `id` = #{id,jdbcType=INTEGER} and `deleted` = 'F'
    </select>

    <!-- countByArg -->
    <select id="countByArg" resultType="java.lang.Integer" parameterType="com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwSysFunctionsQueryArg">
        select count(1)
        from <include refid="tableName"/>
        <where>
            and `deleted` = 'F'
            and (`PARENT_ID` is null or `PARENT_ID` = 0)
            and (`REQUISITION_FLAG`=0 or `REQUISITION_FLAG` is null)
            <if test="corpCode != null">
                and `corp_code` = #{corpCode,jdbcType=VARCHAR}
            </if>
            <if test="funName != null">
                and `fun_name` like concat('%', #{funName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="funCode != null">
                and `fun_code` = #{funCode,jdbcType=VARCHAR}
            </if>
            <if test="functionType != null">
                and `function_type` = #{functionType,jdbcType=VARCHAR}
            </if>
            <if test="subsystemId != null">
                and `subsystem_id` = #{subsystemId,jdbcType=INTEGER}
            </if>
        </where>
    </select>

    <!-- queryByArg -->
    <select id="queryByArg" resultMap="BaseResultMap" parameterType="com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwSysFunctionsQueryArg">
        select
        <include refid="columns"/>
        from <include refid="tableName"/>
        <where>
            and `deleted` = 'F'
            and (`PARENT_ID` is null or `PARENT_ID` = 0)
            and (`REQUISITION_FLAG`=0 or `REQUISITION_FLAG` is null)
            <if test="corpCode != null">
                and `corp_code` = #{corpCode,jdbcType=VARCHAR}
            </if>
            <if test="funName != null">
                and `fun_name` like concat('%', #{funName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="funCode != null">
                and `fun_code` = #{funCode,jdbcType=VARCHAR}
            </if>
            <if test="functionType != null">
                and `function_type` = #{functionType,jdbcType=VARCHAR}
            </if>
            <if test="subsystemId != null">
                and `subsystem_id` = #{subsystemId,jdbcType=INTEGER}
            </if>
        </where>
        <if test="sortItems != null and !sortItems.isEmpty()">
            order by
            <foreach collection="sortItems" separator="," item="sortItem">
                ${sortItem.column} ${sortItem.sortType}
            </foreach>
        </if>
        <if test="offset != null and limit != null">
            limit #{offset}, #{limit}
        </if>
    </select>

    <!-- queryBySubSystemId -->
    <select id="queryBySubSystemId" resultMap="BaseResultMap">
        select
        <include refid="columns"/>
        from <include refid="tableName"/>
        where `subsystem_id` = #{subsystemId,jdbcType=INTEGER}
        and `deleted` = 'F'
        order by `sort_no` asc, `id` asc
    </select>

    <!-- queryByParentIds -->
    <select id="queryByParentIds" resultMap="BaseResultMap">
        select
        <include refid="columns"/>
        from <include refid="tableName"/>
        where `parent_id` in
        <foreach collection="parentIds" item="parentId" open="(" separator="," close=")">
            #{parentId,jdbcType=BIGINT}
        </foreach>
        and `deleted` = 'F'
        order by `sort_no` asc, `id` asc
    </select>

    <!-- queryHasRequisitionList -->
    <select id="queryHasRequisitionList" resultMap="BaseResultMap">
        select
        <include refid="columns"/>
        from <include refid="tableName"/>
        where `requisition_flag` = 1
        and `deleted` = 'F'
        and `corp_code` = #{corpCode,jdbcType=VARCHAR}
        and `subsystem_id` = #{subsystemId,jdbcType=INTEGER}
        order by `sort_no` asc, `id` asc
    </select>

    <!-- insert -->
    <insert id="insert" parameterType="com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.PwSysFunctionsDO" useGeneratedKeys="true" keyProperty="id">
        insert into  sys_functions(
         `corp_code`,
        `fun_name`,
        `fun_code`,
        `fun_ico`,
        `fun_url`,
        `fun_type`,
        `opt_type`,
        `parent_id`,
        `parent_ids`,
        `sort_no`,
        `hierarchy`,
        `open_type`,
        `function_type`,
        `create_time`,
        `create_by`,
        `modify_time`,
        `modify_by`,
        `deleted`,
        `outer_chain`,
        `subsystem_id`,
        `cloud_fun_url`,
        `use_flag`,
        `description`,
        `is_group_show`,
        `requisition_flag`
        )
        values (
        #{corpCode,jdbcType=VARCHAR},
        #{funName,jdbcType=VARCHAR},
        #{funCode,jdbcType=VARCHAR},
        #{funIco,jdbcType=VARCHAR},
        #{funUrl,jdbcType=VARCHAR},
        #{funType,jdbcType=VARCHAR},
        #{optType,jdbcType=VARCHAR},
        #{parentId,jdbcType=INTEGER},
        #{parentIds,jdbcType=VARCHAR},
        #{sortNo,jdbcType=INTEGER},
        #{hierarchy,jdbcType=SMALLINT},
        #{openType,jdbcType=VARCHAR},
        #{functionType,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{createBy,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{modifyBy,jdbcType=VARCHAR},
        #{deleted,jdbcType=CHAR},
        #{outerChain,jdbcType=VARCHAR},
        #{subsystemId,jdbcType=INTEGER},
        #{cloudFunUrl,jdbcType=VARCHAR},
        #{useFlag,jdbcType=CHAR},
        #{description,jdbcType=LONGVARCHAR},
        #{isGroupShow,jdbcType=TINYINT},
        #{requisitionFlag,jdbcType=INTEGER}
        )
<!--        insert into <include refid="tableName"/>-->
--         (
--             `corp_code`,
--             `fun_name`,
--             `fun_code`,
--             `fun_ico`,
--             `fun_url`,
--             `fun_type`,
--             `opt_type`,
--             `parent_id`,
--             `parent_ids`,
--             `sort_no`,
--             `hierarchy`,
--             `open_type`,
--             `function_type`,
--             `create_time`,
--             `create_by`,
--             `modify_time`,
--             `modify_by`,
--             `deleted`,
--             `outer_chain`,
--             `subsystem_id`,
--             `cloud_fun_url`,
--             `use_flag`,
--             `description`,
--             `is_group_show`,
--             `requisition_flag`
--         )
--         values
--         (
--             #{corpCode,jdbcType=VARCHAR},
--             #{funName,jdbcType=VARCHAR},
--             #{funCode,jdbcType=VARCHAR},
--             #{funIco,jdbcType=VARCHAR},
--             #{funUrl,jdbcType=VARCHAR},
--             #{funType,jdbcType=VARCHAR},
--             #{optType,jdbcType=VARCHAR},
--             #{parentId,jdbcType=INTEGER},
--             #{parentIds,jdbcType=VARCHAR},
--             #{sortNo,jdbcType=INTEGER},
--             #{hierarchy,jdbcType=SMALLINT},
--             #{openType,jdbcType=VARCHAR},
--             #{functionType,jdbcType=VARCHAR},
--             #{createTime,jdbcType=TIMESTAMP},
--             #{createBy,jdbcType=VARCHAR},
--             #{modifyTime,jdbcType=TIMESTAMP},
--             #{modifyBy,jdbcType=VARCHAR},
--             #{deleted,jdbcType=CHAR},
--             #{outerChain,jdbcType=VARCHAR},
--             #{subsystemId,jdbcType=INTEGER},
--             #{cloudFunUrl,jdbcType=VARCHAR},
--             #{useFlag,jdbcType=CHAR},
--             #{description,jdbcType=LONGVARCHAR},
--             #{isGroupShow,jdbcType=TINYINT},
--             #{requisitionFlag,jdbcType=INTEGER}
<!--        )-->
    </insert>

    <!-- updateByArg -->
    <update id="updateByArg" parameterType="com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwSysFunctionsUpdateArg">
        update <include refid="tableName"/>
        <set>
            <if test="corpCode != null">
                `corp_code` = #{corpCode,jdbcType=VARCHAR},
            </if>
            <if test="requisitionFlag != null">
                `requisition_flag` = #{requisitionFlag,jdbcType=INTEGER},
            </if>
            <if test="modifyBy != null and modifyBy != ''">
                `modify_by` = #{modifyBy,jdbcType=VARCHAR},
            </if>
            `modify_time` = NOW()
        </set>
        where `id` = #{id,jdbcType=INTEGER}
        and `deleted` = 'F'
    </update>

    <!-- deleteByArg -->
    <update id="deleteByArg" parameterType="com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwSysFunctionsDeleteArg">
        update <include refid="tableName"/>
        set `deleted` = 'T',
            `modify_time` = #{modifyTime,jdbcType=TIMESTAMP},
            `modify_by` = #{modifyBy,jdbcType=VARCHAR}
        where `id` = #{id,jdbcType=INTEGER}
        and `deleted` = 'F'
    </update>

    <!-- batchUpdateAsRequisition -->
    <update id="batchUpdateAsRequisition" parameterType="java.util.Map">
        update <include refid="tableName"/>
        set `requisition_flag` = 1,
            `modify_time` = now(),
            `modify_by` = #{modifyBy,jdbcType=VARCHAR}
        where `id` in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id,jdbcType=BIGINT}
        </foreach>
        <if test="corpCode != null">
            and `corp_code` = #{corpCode,jdbcType=VARCHAR}
        </if>
        and `deleted` = 'F'
    </update>

</mapper>
