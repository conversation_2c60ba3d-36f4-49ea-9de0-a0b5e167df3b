package com.sendinfo.ticketing.verification.basic.repository.goods.dao.impl;

import com.sendinfo.ticketing.verification.repository.jdbc.Statement;
import com.sendinfo.ticketing.verification.repository.jdbc.mybatis.*;
import com.sendinfo.ticketing.verification.basic.repository.goods.dao.TktModelParamDao;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TktModelParamDO;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TktModelParamUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TktModelParamDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TktModelParamQueryArg;

import lombok.Getter;
import org.apache.ibatis.session.SqlSession;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
@Repository("tktModelParamDao")
public class TktModelParamDaoImpl implements TktModelParamDao,
        SqlSessionGenericDAO<Long, TktModelParamDO, TktModelParamUpdateArg, TktModelParamDeleteArg>,
        SqlSessionCountableDAO<TktModelParamQueryArg>,
        SqlSessionQueryableDAO<TktModelParamQueryArg,TktModelParamDO>{

    private final SqlSession sqlSession;
    private final Statement statement;

    public TktModelParamDaoImpl(SqlSessionTemplate sqlSessionTemplate) {
        this.sqlSession = sqlSessionTemplate;
        this.statement = Statement.of(TktModelParamDao.class);
    }

    @Override
    public List<TktModelParamDO> batchQueryTicketModelParamByIds(List<Long> ids, String corpCode) {
        Map<String, Object> params = new HashMap<>();
        params.put("ids", ids);
        params.put("corpCode", corpCode);
        return sqlSession.selectList(statement.get("batchQueryTicketModelParamByIds"), params);
    }

    @Override
    public void sort(TktModelParamQueryArg queryArg) {
        if (queryArg != null && queryArg.getSortItems().isEmpty()) {
            queryArg.desc("id");
        }
    }
}
