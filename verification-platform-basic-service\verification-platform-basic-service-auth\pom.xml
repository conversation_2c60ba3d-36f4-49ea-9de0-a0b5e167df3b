<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.sendinfo.ticketing</groupId>
        <artifactId>verification-platform-basic-service</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>verification-platform-basic-service-auth</artifactId>
    <name>verification-platform-basic-service-auth</name>
    <description>基础服务: 权限验证模块</description>
    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.sendinfo.ticketing</groupId>
            <artifactId>verification-platform-basic-service-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sendinfo.paas</groupId>
            <artifactId>sendinfo-paas-cache-client-api</artifactId>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-api</artifactId>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-impl</artifactId>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-jackson</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
    </dependencies>
</project>
