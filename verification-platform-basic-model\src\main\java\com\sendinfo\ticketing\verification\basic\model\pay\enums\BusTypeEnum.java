package com.sendinfo.ticketing.verification.basic.model.pay.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 业务类型枚举
 * 定义业务类型：1-分成点 2-窗口 3-门票
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
public enum BusTypeEnum {
    SHARE_POINT(1, "分成点"),
    WINDOW(2, "窗口"),
    TICKET(3, "门票");

    @JsonValue
    private final Integer code;
    private final String description;

    BusTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    // --- 高效反向查找机制 ---
    private static final Map<Integer, BusTypeEnum> CODE_MAP = 
        Arrays.stream(values()).collect(Collectors.toMap(BusTypeEnum::getCode, Function.identity()));

    /**
     * 根据 code 获取枚举实例
     *
     * @param code 业务类型编码
     * @return 对应的枚举实例，如果找不到则返回 null
     */
    @JsonCreator
    public static BusTypeEnum ofCode(Integer code) {
        if (code == null) {
            return null;
        }
        return CODE_MAP.get(code);
    }
} 