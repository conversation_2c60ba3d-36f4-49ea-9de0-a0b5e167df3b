package com.sendinfo.ticketing.verification.basic.service.infra.function;

import lombok.Getter;

import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamGroupQueryCondition;
import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamGroupQueryByModuleCodeRequest;
import com.sendinfo.ticketing.verification.basic.component.infra.SysParamGroupReadComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.SysParamGroupComponentRelationReadComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.SysParamComponentDefinitionReadComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.SysParamDefinitionReadComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.SysParamValueReadComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.converter.SysParamGroupConverter;
import com.sendinfo.ticketing.verification.basic.model.infra.error.InfraErrorDef;
import com.sendinfo.ticketing.verification.basic.model.infra.SysParamGroup;
import com.sendinfo.ticketing.verification.basic.model.infra.SysParamGroupComponentRelation;
import com.sendinfo.ticketing.verification.basic.model.infra.SysParamComponentDefinition;
import com.sendinfo.ticketing.verification.basic.model.infra.SysParamDefinition;
import com.sendinfo.ticketing.verification.basic.model.infra.SysParamValue;
import com.sendinfo.ticketing.verification.basic.service.common.enums.CommonAttachmentKey;
import com.sendinfo.ticketing.verification.basic.service.infra.enums.SysParamGroupAttachmentKey;
import com.sendinfo.ticketing.verification.common.api.request.PageRequest;
import com.sendinfo.ticketing.verification.common.service.function.QueryPageByConditionFunction;
import com.sendinfo.ticketing.verification.common.service.function.QuerySingleByIdFunction;
import com.sendinfo.ticketing.verification.common.service.stereotype.Function;
import com.sendinfo.ticketing.verification.common.service.support.QueryPageByConditionLogicAction;
import com.sendinfo.ticketing.verification.common.service.support.QuerySingleByIdLogicAction;
import com.sendinfo.ticketing.verification.flow.function.LogicAction;
import com.sendinfo.ticketing.verification.flow.Hint;
import com.sendinfo.ticketing.verification.flow.Question;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 查询系统参数分组功能类
 * 
 * <AUTHOR> 2025-07-22 11:45:32
 */
@Getter
@Function("querySysParamGroupFunction")
public class QuerySysParamGroupFunction implements
        QuerySingleByIdFunction<Long>,
        QueryPageByConditionFunction<SysParamGroupQueryCondition> {

    private final SysParamGroupReadComponent readComponent;
    private final SysParamGroupComponentRelationReadComponent groupComponentRelationReadComponent;
    private final SysParamComponentDefinitionReadComponent componentDefinitionReadComponent;
    private final SysParamDefinitionReadComponent definitionReadComponent;
    private final SysParamValueReadComponent valueReadComponent;
    private final LogicAction<Long> querySingleById;
    private final LogicAction<PageRequest<SysParamGroupQueryCondition>> queryPageByCondition;

    public QuerySysParamGroupFunction(SysParamGroupReadComponent sysParamGroupReadComponent,
            SysParamGroupComponentRelationReadComponent groupComponentRelationReadComponent,
            SysParamComponentDefinitionReadComponent componentDefinitionReadComponent,
            SysParamDefinitionReadComponent definitionReadComponent,
            SysParamValueReadComponent valueReadComponent,
            SysParamGroupConverter converter) {
        this.readComponent = sysParamGroupReadComponent;
        this.groupComponentRelationReadComponent = groupComponentRelationReadComponent;
        this.componentDefinitionReadComponent = componentDefinitionReadComponent;
        this.definitionReadComponent = definitionReadComponent;
        this.valueReadComponent = valueReadComponent;

        // 单个查询初始化
        querySingleById = new QuerySingleByIdLogicAction<>(
                sysParamGroupReadComponent,
                SysParamGroupAttachmentKey.SYS_PARAM_GROUP_ATTACHMENT_KEY,
                InfraErrorDef.SYSPARAM_GROUP_NOT_FOUND_ERROR);

        // 分页查询初始化
        queryPageByCondition = new QueryPageByConditionLogicAction<>(
                sysParamGroupReadComponent,
                converter::r_pr2p,
                SysParamGroupAttachmentKey.SYS_PARAM_GROUP_DATA_LIST_ATTACHMENT_KEY,
                SysParamGroupAttachmentKey.SYS_PARAM_GROUP_DATA_COUNT_ATTACHMENT_KEY);
    }

    /**
     * 根据分组编码查询
     */
    public Hint queryByGroupCode(Question<String> question) {
        String groupCode = question.getBody();
        String corpCode = question.getAttachment(CommonAttachmentKey.CORP_CODE);

        SysParamGroup group = readComponent.queryByGroupCode(groupCode, corpCode);
        if (group != null) {
            // 填充完整数据
            fillGroupWithCompleteData(group, corpCode);
            question.setAttachment(SysParamGroupAttachmentKey.SYS_PARAM_GROUP_ATTACHMENT_KEY, group);
            return Hint.gotoSuccess();
        } else {
            return Hint.gotoFailed(InfraErrorDef.SYSPARAM_GROUP_NOT_FOUND_ERROR);
        }
    }

    /**
     * 根据模块编码查询分组列表
     */
    public Hint queryListByModuleCode(Question<SysParamGroupQueryByModuleCodeRequest> question) {
        SysParamGroupQueryByModuleCodeRequest request = question.getBody();
        String moduleCode = request.getModuleCode();
        String corpCode = request.getCorpCode();

        List<SysParamGroup> groups = readComponent.queryListByModuleCode(moduleCode, corpCode);
        // 填充完整数据
        for (SysParamGroup group : groups) {
            fillGroupWithCompleteData(group, corpCode);
        }
        question.setAttachment(SysParamGroupAttachmentKey.SYS_PARAM_GROUP_DATA_LIST_ATTACHMENT_KEY, groups);
        return Hint.gotoSuccess();
    }

    /**
     * 填充分组的完整数据
     */
    private void fillGroupWithCompleteData(SysParamGroup group, String corpCode) {
        String groupCode = group.getGroupCode();
        String moduleCode = group.getModuleCode();

        // 1. 获取组件关联列表
        List<SysParamGroupComponentRelation> relations = groupComponentRelationReadComponent
                .queryListByGroupCode(groupCode, corpCode);

        // 2. 收集组件编码列表
        List<String> componentCodeList = relations.stream()
                .map(SysParamGroupComponentRelation::getComponentCode)
                .collect(Collectors.toList());

        if (componentCodeList.isEmpty()) {
            return;
        }

        // 3. 获取所有组件定义
        List<SysParamComponentDefinition> componentDefinitions = componentDefinitionReadComponent
                .queryByComponentCodeList(componentCodeList, corpCode);

        // 4. 获取所有参数定义
        List<SysParamDefinition> paramDefinitions = definitionReadComponent.queryByComponentCodeList(componentCodeList);

        // 5. 构建参数Key列表
        List<String> paramKeyList = paramDefinitions.stream()
                .map(def -> moduleCode + "." + def.getComponentCode() + "." + def.getParamCode())
                .collect(Collectors.toList());

        // 6. 获取参数值
        List<SysParamValue> paramValues = valueReadComponent.queryByParamKeyList(corpCode, paramKeyList);
        Map<String, SysParamValue> valueMap = paramValues.stream()
                .collect(Collectors.toMap(SysParamValue::getParamKey, v -> v));

        // 7. 数据组装
        Map<String, List<SysParamDefinition>> definitionsByComponent = paramDefinitions.stream()
                .collect(Collectors.groupingBy(SysParamDefinition::getComponentCode));

        List<SysParamComponentDefinition> resultComponents = componentDefinitions.stream()
                .map(component -> {
                    List<SysParamDefinition> definitions = definitionsByComponent.get(component.getComponentCode());
                    if (definitions != null) {
                        // 为每个参数定义设置参数值
                        definitions.forEach(def -> {
                            String paramKey = moduleCode + "." + def.getComponentCode() + "." + def.getParamCode();
                            SysParamValue value = valueMap.get(paramKey);
                            def.setSysParamValue(value);
                        });
                        component.setSysParamDefinitions(definitions);
                    }
                    return component;
                })
                .collect(Collectors.toList());

        group.setSysParamComponentDefinitions(resultComponents);
    }
}
