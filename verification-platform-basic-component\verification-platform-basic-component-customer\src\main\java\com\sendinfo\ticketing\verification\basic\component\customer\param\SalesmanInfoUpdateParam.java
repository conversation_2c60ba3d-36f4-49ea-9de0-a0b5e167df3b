package com.sendinfo.ticketing.verification.basic.component.customer.param;

import com.sendinfo.ticketing.verification.common.component.param.AbstractUpdateParam;
import lombok.Getter;
import lombok.Setter;

/**
 * 业务员信息更新参数
 * 用于封装更新业务员信息所需的参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
public class SalesmanInfoUpdateParam extends AbstractUpdateParam<Long> {

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 业务员名称
     */
    private String salesmanName;

    /**
     * 业务员简称
     */
    private String salesmanAbbreviation;

    /**
     * 禁启状态:T启用F禁用
     */
    private String useFlag;

    /**
     * 0是业务员1是推广员
     */
    private Integer salesmanType;

    /**
     * 修改人
     */
    private String modifyBy;
} 