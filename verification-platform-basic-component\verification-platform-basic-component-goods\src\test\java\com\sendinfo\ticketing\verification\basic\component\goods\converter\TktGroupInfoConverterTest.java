package com.sendinfo.ticketing.verification.basic.component.goods.converter;

import com.sendinfo.ticketing.verification.basic.api.goods.request.TktGroupInfoQueryRequest;
import com.sendinfo.ticketing.verification.basic.component.goods.param.TktGroupInfoQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TktGroupInfo;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TktGroupInfoQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TktGroupInfoDO;
import org.checkerframework.checker.units.qual.A;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

/**
 * @Auther: chengjie.li
 * @Date: 2025/6/18 12:00
 */
@RunWith(MockitoJUnitRunner.class)
public class TktGroupInfoConverterTest {

    @InjectMocks
    private TktGroupInfoConverter tktGroupInfoConverter;

    private static final String TEST_CORP_CODE = "TEST_CORP_001";
    private static final String TEST_GROUP_NAME = "TEST_GROUP_NAME";
    private static final Integer TEST_SORT_NUM = 1;
    private static final String TEST_REMARK = "TEST_REMARK";
    private static final String TEST_USE_FLAG = "T";
    private static final String TEST_TRANSFER_PARAM = "TEST_TRANSFER_PARAM";
    private static final String TEST_DEPT_ID = "TEST_DEPT_ID";
    private static final String TEST_GROUP_CODE = "TEST_GROUP_CODE";
    private static final String TEST_GRADE = "1";
    private static final String TEST_PARENT_ID = "TEST_PARENT_ID";
    private static final String TEST_CREATE_BY = "testUser";

    /**
     * 测试目的：验证TktGroupInfoDO转换为TktGroupInfo的功能
     * 测试步骤：
     * 1. 创建完整的TktGroupInfoDO对象
     * 2. 调用r_d2m方法进行转换
     * 3. 验证转换结果的正确性
     */
    @Test
    public void testReadDo2Model_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        TktGroupInfoDO tktGroupInfoDO = createTktGroupInfoDO();

        // 2. 执行转换
        TktGroupInfo result = tktGroupInfoConverter.r_d2m(tktGroupInfoDO);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", result);
        assertEquals("企业编码应该一致", TEST_CORP_CODE, result.getCorpCode());
        assertEquals("分组名称应该一致", TEST_GROUP_NAME, result.getGroupName());
        assertEquals("排序应该一致", TEST_SORT_NUM, result.getSortNum());
        assertEquals("备注应该一致", TEST_REMARK, result.getRemark());
        assertEquals("启用标志应该一致", TEST_USE_FLAG, result.getUseFlag());
        assertEquals("迁移数据应该一致", TEST_TRANSFER_PARAM, result.getTransferParam());
        assertEquals("部门ID应该一致", TEST_DEPT_ID, result.getDeptId());
        assertEquals("分组编码应该一致", TEST_GROUP_CODE, result.getGroupCode());
        assertEquals("分级应该一致", TEST_GRADE, result.getGrade());
        assertEquals("父ID应该一致", TEST_PARENT_ID, result.getParentId());
        assertEquals("创建人应该一致", TEST_CREATE_BY, result.getCreateBy());
        assertEquals("修改人应该一致", TEST_CREATE_BY, result.getModifyBy());
    }

    /**
     * 测试目的：验证TktGroupInfoQueryParam转换为TktGroupInfoQueryArg的功能
     * 测试步骤：
     * 1. 创建完整的TktGroupInfoQueryParam对象
     * 2. 调用r_p2a方法进行转换
     * 3. 验证转换结果的正确性
     */
    @Test
    public void testReadQueryParam2QueryArg_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        TktGroupInfoQueryParam queryParam = createTktGroupInfoQueryParam();

        // 2. 执行转换
        TktGroupInfoQueryArg queryArg = tktGroupInfoConverter.r_p2a(queryParam);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", queryArg);
        assertEquals("分组名称应该一致", TEST_GROUP_NAME, queryArg.getGroupName());
        assertEquals("排序应该一致", TEST_SORT_NUM, queryArg.getSortNum());
        assertEquals("备注应该一致", TEST_REMARK, queryArg.getRemark());
        assertEquals("启用标志应该一致", TEST_USE_FLAG, queryArg.getUseFlag());
        assertEquals("迁移数据应该一致", TEST_TRANSFER_PARAM, queryArg.getTransferParam());
        assertEquals("部门ID应该一致", TEST_DEPT_ID, queryArg.getDeptId());
        assertEquals("分组编码应该一致", TEST_GROUP_CODE, queryArg.getGroupCode());
        assertEquals("分级应该一致", TEST_GRADE, queryArg.getGrade());
        assertEquals("父ID应该一致", TEST_PARENT_ID, queryArg.getParentId());
    }

    /**
     * 测试目的：验证TktGroupInfoQueryRequest转换为TktGroupInfoQueryParam的功能
     * 测试步骤：
     * 1. 创建完整的TktGroupInfoQueryRequest对象
     * 2. 调用r_r2p方法进行转换
     * 3. 验证转换结果的正确性
     */
    @Test
    public void testReadQueryRequest2QueryParam_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        TktGroupInfoQueryRequest queryRequest = createTktGroupInfoQueryRequest();

        // 2. 执行转换
        TktGroupInfoQueryParam queryParam = tktGroupInfoConverter.r_r2p(queryRequest);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", queryParam);
        assertEquals("企业编码应该一致", TEST_CORP_CODE, queryParam.getCorpCode());
    }

    private TktGroupInfoQueryRequest createTktGroupInfoQueryRequest() {
        TktGroupInfoQueryRequest queryRequest = new TktGroupInfoQueryRequest();
        queryRequest.setCorpCode(TEST_CORP_CODE);
        return queryRequest;
    }

    private TktGroupInfoQueryParam createTktGroupInfoQueryParam() {
        TktGroupInfoQueryParam queryParam = new TktGroupInfoQueryParam();
        queryParam.setCorpCode(TEST_CORP_CODE);
        queryParam.setGroupName(TEST_GROUP_NAME);
        queryParam.setSortNum(TEST_SORT_NUM);
        queryParam.setRemark(TEST_REMARK);
        queryParam.setUseFlag(TEST_USE_FLAG);
        queryParam.setTransferParam(TEST_TRANSFER_PARAM);
        queryParam.setDeptId(TEST_DEPT_ID);
        queryParam.setGroupCode(TEST_GROUP_CODE);
        queryParam.setGrade(Integer.valueOf(TEST_GRADE));
        queryParam.setParentId(TEST_PARENT_ID);
        return queryParam;
    }

    private TktGroupInfoDO createTktGroupInfoDO() {
        TktGroupInfoDO groupInfoDO = new TktGroupInfoDO();
        groupInfoDO.setCorpCode(TEST_CORP_CODE);
        groupInfoDO.setGroupName(TEST_GROUP_NAME);
        groupInfoDO.setSortNum(TEST_SORT_NUM);
        groupInfoDO.setRemark(TEST_REMARK);
        groupInfoDO.setUseFlag(TEST_USE_FLAG);
        groupInfoDO.setTransferParam(TEST_TRANSFER_PARAM);
        groupInfoDO.setDeptId(TEST_DEPT_ID);
        groupInfoDO.setGroupCode(TEST_GROUP_CODE);
        groupInfoDO.setGrade(TEST_GRADE);
        groupInfoDO.setParentId(TEST_PARENT_ID);
        groupInfoDO.setCreateBy(TEST_CREATE_BY);
        groupInfoDO.setModifyBy(TEST_CREATE_BY);
        return groupInfoDO;
    }
} 