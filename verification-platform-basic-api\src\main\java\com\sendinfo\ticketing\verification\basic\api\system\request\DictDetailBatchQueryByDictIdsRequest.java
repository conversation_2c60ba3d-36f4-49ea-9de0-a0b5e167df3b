package com.sendinfo.ticketing.verification.basic.api.system.request;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Set;

/**
 * @Auther: chengjie.li
 * @Date: 2025/6/23 16:52
 */
@Getter
@Setter
public class DictDetailBatchQueryByDictIdsRequest implements Serializable {

    private static final long serialVersionUID = 5628347053777621422L;
    /**
     * 字典ID
     */
    @NotEmpty
    private Set<Long> dictIds;

    /**
     * 企业编码
     */
    @NotNull
    private String corpCode;
}
