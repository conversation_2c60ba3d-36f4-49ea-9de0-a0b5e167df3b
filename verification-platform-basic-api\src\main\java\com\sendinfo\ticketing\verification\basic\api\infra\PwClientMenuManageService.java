package com.sendinfo.ticketing.verification.basic.api.infra;

import java.util.List;

import com.sendinfo.ticketing.verification.basic.api.infra.request.PwClientMenuMarkAsRequisitionRequest;
import com.sendinfo.ticketing.verification.basic.api.infra.request.PwClientMenuQueryCondition;
import com.sendinfo.ticketing.verification.basic.model.infra.PwClientMenu;
import com.sendinfo.ticketing.verification.common.api.request.PageRequest;
import com.sendinfo.ticketing.verification.common.api.result.PageResultModel;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

/**
 * 客户端菜单管理服务
 * 
 * <AUTHOR> 2025-07-24
 */
public interface PwClientMenuManageService {

    /**
     * 标记客户端菜单为已征用
     *
     * @param request  客户端菜单ID列表
     *
     * @return
     */
    ResultModel<Boolean> markAsRequisition(PwClientMenuMarkAsRequisitionRequest request);
}
