package com.sendinfo.ticketing.verification.basic.api.infra;

import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamDefinitionDeleteRequest;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

/**
 * 系统参数定义删除服务
 * <AUTHOR> 2025-05-19 15:30:00
 */
public interface SysParamDefinitionDeleteService {
    /**
     * 删除系统参数定义
     *
     * @param request 删除请求
     * @return
     */
    ResultModel<String> deleteSysParamDefinition(SysParamDefinitionDeleteRequest request);
} 