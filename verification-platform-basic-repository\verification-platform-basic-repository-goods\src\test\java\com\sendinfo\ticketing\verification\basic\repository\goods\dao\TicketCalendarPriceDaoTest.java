package com.sendinfo.ticketing.verification.basic.repository.goods.dao;

import com.alibaba.nacos.api.exception.NacosException;
import com.sendinfo.ticketing.verification.basic.repository.common.config.SaasPwMybatisConfig;
import com.sendinfo.ticketing.verification.basic.repository.common.sharding.DynamicDataSourceMappingConfigPullStorage;
import com.sendinfo.ticketing.verification.basic.repository.goods.dao.impl.TicketCalendarPriceDaoImpl;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TicketCalendarPriceDO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.AbstractTransactionalJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@SpringBootTest(classes = TicketCalendarPriceDaoTest.Config.class)
@EnableAutoConfiguration
@RunWith(SpringRunner.class)
@TestPropertySource(locations = "classpath:test-application.properties")
public class TicketCalendarPriceDaoTest extends AbstractTransactionalJUnit4SpringContextTests {

    @Autowired
    private TicketCalendarPriceDao ticketCalendarPriceDao;

    private static final String TEST_CORP_CODE = "TEST_CORP";
    private static final String TEST_USER = "test_user";

    @Configuration
    @Import(SaasPwMybatisConfig.class)
    static class Config {

        @Bean
        TicketCalendarPriceDao tktTicketCalendarPriceDao(@Qualifier(SaasPwMybatisConfig.BEAN_NAME_SQL_SESSION_TEMPLATE) SqlSessionTemplate sqlSessionTemplate) {
            return new TicketCalendarPriceDaoImpl(sqlSessionTemplate);
        }

        @Bean
        DynamicDataSourceMappingConfigPullStorage dynamicDataSourceMappingConfigPullStorage(@Value("${nacos.config.server-addr}") String serverAddr,
                                                                                            @Value("${nacos.config.namespace}") String namespace,
                                                                                            @Value("${verification-platform-basic.nacos.tenant-datasource-mapping.data-id:saas-pw-datasource-sharding-mapping.json}") String dataId,
                                                                                            @Value("${verification-platform-basic.nacos.tenant-datasource-mapping.group:DEFAULT_GROUP}") String group,
                                                                                            @Value("${verification-platform-basic.nacos.tenant-datasource-mapping.pulled.timeout:3000}") Integer timeout) throws NacosException {
            return new DynamicDataSourceMappingConfigPullStorage(serverAddr, namespace, dataId, group, timeout);
        }
    }

    @Before
    public void setUp() {
        // 初始化测试数据
        for (int i = 1; i <= 5; i++) {
            TicketCalendarPriceDO priceDO = createTestPriceDO(i, 1000L + i);
            ticketCalendarPriceDao.insert(priceDO);
        }
    }

    @Test
    public void testInsert() {
        // Given
        TicketCalendarPriceDO priceDO = createTestPriceDO(6L, 1006L);

        // When
        ticketCalendarPriceDao.insert(priceDO);

        // Then
        TicketCalendarPriceDO found = ticketCalendarPriceDao.queryById(priceDO.getId());
        Assert.assertNotNull(found);
        Assert.assertEquals(priceDO.getTicketId(), found.getTicketId());
        Assert.assertEquals(priceDO.getCorpCode(), found.getCorpCode());
    }

    @Test
    public void testQueryById() {
        // When
        TicketCalendarPriceDO found = ticketCalendarPriceDao.queryById(1L);

        // Then
        Assert.assertNotNull(found);
        Assert.assertEquals(1L, found.getId().longValue());
    }

    @Test
    public void testQueryByTicketId() {
        // When
        TicketCalendarPriceDO found = ticketCalendarPriceDao.queryByTicketId(1001L, TEST_CORP_CODE);

        // Then
        Assert.assertNotNull(found);
        Assert.assertEquals(1001L, found.getTicketId().longValue());
        Assert.assertEquals(TEST_CORP_CODE, found.getCorpCode());
    }

    @Test
    public void testBatchQueryByTicketIds() {
        // When
        List<TicketCalendarPriceDO> prices = ticketCalendarPriceDao.batchQueryByTicketIds(
                Arrays.asList(1001L, 1002L, 1003L), TEST_CORP_CODE);

        // Then
        Assert.assertNotNull(prices);
    }

    private TicketCalendarPriceDO createTestPriceDO(long id, long ticketId) {
        TicketCalendarPriceDO priceDO = new TicketCalendarPriceDO();
        priceDO.setId(id);
        priceDO.setTicketId(ticketId);
        priceDO.setPrice(new BigDecimal("100.00"));
        priceDO.setCostPrice(new BigDecimal("80.00"));
        priceDO.setTicketTax(new BigDecimal("10.00"));
        priceDO.setPrintPrice(new BigDecimal("100.00"));
        priceDO.setBeginTime(new Date());
        priceDO.setEndTime(new Date(System.currentTimeMillis() + 86400000));
        priceDO.setWeeDay("1,2,3,4,5");
        priceDO.setCorpCode(TEST_CORP_CODE);
        priceDO.setCreateBy(TEST_USER);
        priceDO.setModifyBy(TEST_USER);
        priceDO.setDeleted("F");
        return priceDO;
    }
}
