package com.sendinfo.ticketing.verification.basic.api.infra;

import com.sendinfo.ticketing.verification.basic.api.infra.request.FeatureComponentCategoryDeleteRequest;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

/**
 * 组件分类删除服务
 * 
 * <AUTHOR> 2025-07-22 11:45:32
 */
public interface FeatureComponentCategoryDeleteService {
    
    /**
     * 删除组件分类
     *
     * @param request 删除请求
     * @return 删除结果
     */
    ResultModel<Boolean> deleteFeatureComponentCategory(FeatureComponentCategoryDeleteRequest request);
}
