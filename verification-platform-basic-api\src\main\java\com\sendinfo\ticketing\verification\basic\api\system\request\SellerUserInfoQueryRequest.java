package com.sendinfo.ticketing.verification.basic.api.system.request;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Set;

/**
 * @Auther: chengjie.li
 * @Date: 2025/7/16 17:06
 * 用户：售票员信息查询参数
 */
@Getter
@Setter
public class SellerUserInfoQueryRequest implements Serializable {

    private static final long serialVersionUID = -4711651758144006325L;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 部门id集合
     */
    private Set<Long> deptIdSet;

    /**
     * 企业编码
     */
    @NotNull
    private String corpCode;
}
