package com.sendinfo.ticketing.verification.basic.repository.goods.dao;

import com.sendinfo.ticketing.verification.common.repository.*;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TktModelParamDO;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TktModelParamUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TktModelParamDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TktModelParamQueryArg;

import java.util.List;

public interface TktModelParamDao extends
        GenericDAO<Long, TktModelParamDO, TktModelParamUpdateArg, TktModelParamDeleteArg>,
        CountableDAO<TktModelParamQueryArg>,
        QueryableDAO<TktModelParamQueryArg, TktModelParamDO>{
    /**
     * 批量查询
     * @param ids
     * @param corpCode
     * @return
     */
    List<TktModelParamDO> batchQueryTicketModelParamByIds(List<Long> ids,String corpCode);
}
