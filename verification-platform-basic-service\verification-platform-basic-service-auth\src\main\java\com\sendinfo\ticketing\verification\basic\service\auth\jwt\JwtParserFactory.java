package com.sendinfo.ticketing.verification.basic.service.auth.jwt;

import io.jsonwebtoken.JwtParser;

/**
 * <AUTHOR>
 * @since 2025-07-23 13:22:09
 */
public interface JwtParserFactory {

    /**
     * 获取jwt
     *
     * @param jwtIdentity id
     * @return parser
     */
    JwtParser getParser(JwtIdentity jwtIdentity);



    interface JwtParserBuilder {
        /**
         * 支持 id
         *
         * @return id
         */
        JwtIdentity supportedIdentity();

        /**
         * 构建
         *
         * @return parse
         */
        JwtParser build();
    }
}
