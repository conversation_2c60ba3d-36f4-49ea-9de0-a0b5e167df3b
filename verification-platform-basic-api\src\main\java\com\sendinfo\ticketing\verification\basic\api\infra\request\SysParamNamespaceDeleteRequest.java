package com.sendinfo.ticketing.verification.basic.api.infra.request;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 参数命名空间删除请求
 */
@Getter
@Setter
@ToString
public class SysParamNamespaceDeleteRequest implements Serializable {
    private static final long serialVersionUID = -8765432109876543211L;

    /**
     * 主键ID
     */
    @NotNull(message = "ID不能为空")
    private Long id;

    /**
     * 企业编码
     */
    @NotBlank(message = "企业编码不能为空")
    private String corpCode;

    /**
     * 操作人
     */
    @NotBlank(message = "修改人不能为空")
    private String modifyBy;
}