package com.sendinfo.ticketing.verification.basic.model.pay.properties;

import com.sendinfo.ticketing.verification.common.model.properties.PropertyKeyDefinition;

/**
 * 经营主体子商户属性键定义
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum SysMainbodyManageSubMerchantsDefinitionPropertyKey implements PropertyKeyDefinition {
    TAGS("tags");

    private final String key;

    SysMainbodyManageSubMerchantsDefinitionPropertyKey(String key) {
        this.key = key;
    }

    @Override
    public String key() {
        return key;
    }
} 