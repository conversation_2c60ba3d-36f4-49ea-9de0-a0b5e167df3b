package com.sendinfo.ticketing.verification.basic.component.infra.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamValueCreateRequest;
import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamValueDeleteRequest;
import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamValueUpdateRequest;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamValueCreateParam;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamValueDeleteParam;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamValueQueryParam;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamValueUpdateParam;
import com.sendinfo.ticketing.verification.basic.enums.CommonStatusEnum;
import com.sendinfo.ticketing.verification.basic.model.infra.SysParamValue;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamValueDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamValueQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamValueUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.SysParamValueDO;

/**
 *  系统参数值对象映射器
 *
 *  <AUTHOR> 2025-05-20 15:40:00
 */
@Mapper(imports = {
        CommonStatusEnum.class
})
public interface SysParamValueMapper {

    SysParamValueMapper INSTANCE = Mappers.getMapper(SysParamValueMapper.class);

    /**
     * Convert SysParamValueCreateParam to SysParamValueDO
     */
    @Mapping(target = "status", expression = "java(createParam.getStatus().getCode())")
    SysParamValueDO convert(SysParamValueCreateParam createParam);

    /**
     * Convert SysParamValueUpdateParam to SysParamValueUpdateArg
     */
    @Mapping(target = "statusUpdater", ignore = true)
    SysParamValueUpdateArg convert(SysParamValueUpdateParam param);

    /**
     * Convert SysParamValueQueryParam to SysParamValueQueryArg
     */
    @Mapping(target = "status", expression = "java(param.getStatus() != null ? param.getStatus().getCode() : null)")
    SysParamValueQueryArg convert(SysParamValueQueryParam param);

    /**
     * Convert SysParamValueDO to SysParamValue
     */
    @Mapping(target = "status", expression = "java(CommonStatusEnum.of(dataObject.getStatus()))")
    SysParamValue convert(SysParamValueDO dataObject);

    /**
     * Convert SysParamValueCreateRequest to SysParamValueCreateParam
     */
    @Mapping(target = "status", expression = "java(CommonStatusEnum.of(req.getStatus()))")
    SysParamValueCreateParam convert(SysParamValueCreateRequest req);

    /**
     * Convert SysParamValueUpdateRequest to SysParamValueUpdateParam
     */
    @Mapping(target = "targetStatus", expression = "java(CommonStatusEnum.of(req.getStatus()))")
    SysParamValueUpdateParam convert(SysParamValueUpdateRequest req);

    /**
     * Convert SysParamValueDeleteParam to SysParamValueDeleteArg
     */
    SysParamValueDeleteArg convert(SysParamValueDeleteParam param);

    /**
     * Convert SysParamValueDeleteRequest to SysParamValueDeleteParam
     */
    SysParamValueDeleteParam convert(SysParamValueDeleteRequest req);

} 