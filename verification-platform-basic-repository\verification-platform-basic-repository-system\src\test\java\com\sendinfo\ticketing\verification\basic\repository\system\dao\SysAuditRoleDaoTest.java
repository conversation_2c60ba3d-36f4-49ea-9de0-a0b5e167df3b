package com.sendinfo.ticketing.verification.basic.repository.system.dao;

import com.alibaba.nacos.api.exception.NacosException;
import com.sendinfo.ticketing.verification.basic.model.system.SysAuditRole;
import com.sendinfo.ticketing.verification.basic.repository.common.config.SaasPwMybatisConfig;
import com.sendinfo.ticketing.verification.basic.repository.common.sharding.DynamicDataSourceMappingConfigPullStorage;
import com.sendinfo.ticketing.verification.basic.repository.system.arg.SysAuditRoleDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.system.arg.SysAuditRoleQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.system.arg.SysAuditRoleUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.system.dao.impl.SysAuditRoleDaoImpl;
import com.sendinfo.ticketing.verification.basic.repository.system.dataobject.SysAuditRoleDO;
import org.junit.Test;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.AbstractTransactionalJUnit4SpringContextTests;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static org.junit.Assert.*;
import static org.junit.Assert.assertNotNull;

/**
 * 票型角色数据访问层单元测试
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootTest(classes = SysAuditRoleDaoTest.Config.class)
@EnableAutoConfiguration
@TestPropertySource(locations = "classpath:test-application.properties")
public class SysAuditRoleDaoTest extends AbstractTransactionalJUnit4SpringContextTests {

    @Autowired
    private SysAuditRoleDao sysAuditRoleDao;

    private static final String TEST_USER = "test_user";
    private static final String TEST_CORP_CODE = "TEST_CORP_001";

    @Configuration
    @Import(SaasPwMybatisConfig.class)
    static class Config {
        @Bean
        public SysAuditRoleDao sysAuditRoleDao(SqlSessionTemplate sqlSessionTemplate) {
            return new SysAuditRoleDaoImpl(sqlSessionTemplate);
        }
        @Bean
        DynamicDataSourceMappingConfigPullStorage dynamicDataSourceMappingConfigPullStorage(@Value("${nacos.config.server-addr}") String serverAddr,
                                                                                            @Value("${nacos.config.namespace}") String namespace,
                                                                                            @Value("${verification-platform-basic.nacos.tenant-datasource-mapping.data-id:saas-pw-datasource-sharding-mapping.json}") String dataId,
                                                                                            @Value("${verification-platform-basic.nacos.tenant-datasource-mapping.group:DEFAULT_GROUP}") String group,
                                                                                            @Value("${verification-platform-basic.nacos.tenant-datasource-mapping.pulled.timeout:3000}") Integer timeout) throws NacosException {
            return new DynamicDataSourceMappingConfigPullStorage(serverAddr, namespace, dataId, group, timeout);
        }
    }

    /**
     * 测试插入操作
     */
    @Test
    public void testInsert() {
        SysAuditRoleDO entity = createTestDO();
        sysAuditRoleDao.insert(entity);
        assertNotNull("插入后主键ID应该被自动生成", entity.getId());
        SysAuditRoleDO inserted = sysAuditRoleDao.queryById(entity.getId());
        assertNotNull("插入后应能通过ID查询到记录", inserted);
        assertEquals("企业码应该一致", entity.getCorpCode(), inserted.getCorpCode());
    }

    /**
     * 测试更新操作
     */
    @Test
    public void testUpdateByArg() {
        SysAuditRoleDO entity = createTestDO();
        sysAuditRoleDao.insert(entity);
        SysAuditRoleUpdateArg updateArg = new SysAuditRoleUpdateArg();
        updateArg.setId(entity.getId());
        updateArg.setRoleName("更新角色");
        updateArg.setRoleType(2);
        updateArg.setSubsystemId(100);
        updateArg.setUseFlag("T");
        updateArg.setRemark("更新备注");
        updateArg.setModifyBy(TEST_USER);
        int updateCount = sysAuditRoleDao.updateByArg(updateArg);
        assertEquals("应该更新1条记录", 1, updateCount);
        SysAuditRoleDO updated = sysAuditRoleDao.queryById(entity.getId());
        assertEquals("角色名称应该被更新", "更新角色", updated.getRoleName());
        assertEquals("权限类型应该被更新", Integer.valueOf(2), updated.getRoleType());
        assertEquals("子系统ID应该被更新", Integer.valueOf(100), updated.getSubsystemId());
        assertEquals("启用状态应该被更新", "T", updated.getUseFlag());
        assertEquals("备注应该被更新", "更新备注", updated.getRemark());
    }

    /**
     * 测试逻辑删除操作
     */
    @Test
    public void testSoftDeleteByArg() {
        SysAuditRoleDO entity = createTestDO();
        sysAuditRoleDao.insert(entity);
        SysAuditRoleDeleteArg deleteArg = new SysAuditRoleDeleteArg();
        deleteArg.setId(entity.getId());
        deleteArg.setCorpCode(entity.getCorpCode());
        deleteArg.setModifyBy(TEST_USER);
        int deleteCount = sysAuditRoleDao.softDeleteByArg(deleteArg);
        assertEquals("应该删除1条记录", 1, deleteCount);
        SysAuditRoleDO deleted = sysAuditRoleDao.queryById(entity.getId());
        assertNull("删除后应无法通过ID查询到记录", deleted);
    }

    /**
     * 测试按ID查询
     */
    @Test
    public void testQueryById() {
        SysAuditRoleDO entity = createTestDO();
        sysAuditRoleDao.insert(entity);
        SysAuditRoleDO queried = sysAuditRoleDao.queryById(entity.getId());
        assertNotNull("应能通过ID查询到记录", queried);
        assertEquals("角色名称应该一致", entity.getRoleName(), queried.getRoleName());
    }

    /**
     * 测试条件查询
     */
    @Test
    public void testQueryByArg() {
        SysAuditRoleDO entity = createTestDO();
        sysAuditRoleDao.insert(entity);
        SysAuditRoleQueryArg queryArg = new SysAuditRoleQueryArg();
        queryArg.setCorpCode(entity.getCorpCode());
        queryArg.setRoleName(entity.getRoleName());
        List<SysAuditRoleDO> results = sysAuditRoleDao.queryByArg(queryArg);
        assertNotNull("查询结果不应为null", results);
        assertFalse("查询结果不应为空", results.isEmpty());
        assertEquals("应该查询到1条记录", 1, results.size());
    }

    /**
     * 测试条件计数
     */
    @Test
    public void testCountByArg() {
        SysAuditRoleDO entity = createTestDO();
        sysAuditRoleDao.insert(entity);
        SysAuditRoleQueryArg queryArg = new SysAuditRoleQueryArg();
        queryArg.setCorpCode(entity.getCorpCode());
        Integer count = sysAuditRoleDao.countByArg(queryArg);
        assertNotNull("计数结果不应为null", count);
        assertEquals("应该计数到1条记录", Integer.valueOf(1), count);
    }

    /**
     * 测试批量插入
     */
    @Test
    public void testBatchInsert() {
        SysAuditRoleDO entity1 = createTestDO();
        SysAuditRoleDO entity2 = createTestDO();
        entity2.setRoleName("测试角色2");
        List<SysAuditRoleDO> entities = List.of(entity1, entity2);
        sysAuditRoleDao.batchInsert(entities);
        assertNotNull("批量插入后主键ID应该被自动生成", entity1.getId());
        assertNotNull("批量插入后主键ID应该被自动生成", entity2.getId());
    }

    /**
     * 测试按ID批量查询
     */
    @Test
    public void testQuerySysAuditRoleByIds() {
        SysAuditRoleDO entity1 = createTestDO();
        SysAuditRoleDO entity2 = createTestDO();
        entity2.setRoleName("测试角色2");
        List<SysAuditRoleDO> entities = List.of(entity1, entity2);
        sysAuditRoleDao.batchInsert(entities);

        Set<Integer> ids = entities.stream().map(SysAuditRoleDO::getId).collect(Collectors.toSet());
        List<SysAuditRole> queried = sysAuditRoleDao.querySysAuditRoleByIds(ids, TEST_CORP_CODE);
        assertNotNull("批量查询不为空", queried);
        assertEquals("应该计数到2条记录", 2, queried.size());
    }

    /**
     * 创建测试用的DO对象
     */
    private SysAuditRoleDO createTestDO() {
        SysAuditRoleDO entity = new SysAuditRoleDO();
        entity.setCorpCode(TEST_CORP_CODE);
        entity.setRoleName("测试角色");
        entity.setRoleType(1);
        entity.setSubsystemId(50);
        entity.setUseFlag("T");
        entity.setRemark("测试备注");
        entity.setCreateBy(TEST_USER);
        entity.setModifyBy(TEST_USER);
        entity.setDeleted("F");
        return entity;
    }
} 