package com.sendinfo.ticketing.verification.basic.repository.pay.dao;

import com.alibaba.nacos.api.exception.NacosException;
import com.sendinfo.ticketing.verification.basic.repository.common.config.SaasPwMybatisConfig;
import com.sendinfo.ticketing.verification.basic.repository.common.sharding.DynamicDataSourceMappingConfigPullStorage;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.MainbodyChanleDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.MainbodyChanleQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.MainbodyChanleUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dao.impl.MainbodyChanleDaoImpl;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.MainbodyChanleDO;
import org.junit.Test;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.AbstractTransactionalJUnit4SpringContextTests;

import java.util.Date;
import java.util.List;
import java.util.Random;

import static org.junit.Assert.*;

/**
 * 商户渠道配置数据访问层单元测试
 * <p>
 * 本测试类覆盖了MainbodyChanleDao的所有方法，包括：
 * 1. 插入操作测试
 * 2. 批量插入操作测试
 * 3. 根据ID查询测试
 * 4. 条件查询测试
 * 5. 计数查询测试
 * 6. 更新操作测试
 * 7. 软删除操作测试
 * 8. 根据ID删除测试
 * <p>
 * 测试目的：验证MainbodyChanleDao的所有方法都能正常工作，确保数据访问层的正确性
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootTest(classes = MainbodyChanleDaoTest.Config.class)
@EnableAutoConfiguration
@TestPropertySource(locations = "classpath:test-application.properties")
public class MainbodyChanleDaoTest extends AbstractTransactionalJUnit4SpringContextTests {

    @Autowired
    private MainbodyChanleDao mainbodyChanleDao;

    /**
     * 测试用的企业编码
     */
    private static final String TEST_CORP_CODE = "TEST_CORP_001";

    /**
     * 测试用户
     */
    private static final String TEST_USER = "test_user";

    /**
     * 测试配置类
     * 配置MyBatis和DAO Bean
     */
    @Configuration
    @Import(SaasPwMybatisConfig.class)
    static class Config {

        @Bean
        MainbodyChanleDao mainbodyChanleDao(@Qualifier(SaasPwMybatisConfig.BEAN_NAME_SQL_SESSION_TEMPLATE) SqlSessionTemplate sqlSessionTemplate) {
            return new MainbodyChanleDaoImpl(sqlSessionTemplate);
        }

        @Bean
        DynamicDataSourceMappingConfigPullStorage dynamicDataSourceMappingConfigPullStorage(@Value("${nacos.config.server-addr}") String serverAddr,
                                                                                            @Value("${nacos.config.namespace}") String namespace,
                                                                                            @Value("${verification-platform-basic.nacos.tenant-datasource-mapping.data-id:saas-pw-datasource-sharding-mapping.json}") String dataId,
                                                                                            @Value("${verification-platform-basic.nacos.tenant-datasource-mapping.group:DEFAULT_GROUP}") String group,
                                                                                            @Value("${verification-platform-basic.nacos.tenant-datasource-mapping.pulled.timeout:3000}") Integer timeout) throws NacosException {
            return new DynamicDataSourceMappingConfigPullStorage(serverAddr, namespace, dataId, group, timeout);
        }
    }

    /**
     * 测试插入操作
     * <p>
     * 测试步骤：
     * 1. 创建测试商户渠道配置数据
     * 2. 执行插入操作
     * 3. 验证主键ID被自动生成
     * 4. 通过ID查询验证记录确实被插入
     * 5. 验证插入的数据字段值正确
     */
    @Test
    public void testInsert() {
        // 1. 创建测试商户渠道配置数据
        MainbodyChanleDO record = createTestDO();

        // 2. 执行插入操作
        mainbodyChanleDao.insert(record);

        // 3. 验证主键ID被自动生成
        assertNotNull("插入后主键ID应该被自动生成", record.getId());

        // 4. 通过ID查询验证记录确实被插入
        MainbodyChanleDO insertedRecord = mainbodyChanleDao.queryById(record.getId());
        assertNotNull("插入后应能通过ID查询到记录", insertedRecord);
        assertEquals("支付渠道ID应该一致", record.getPayChanleId(), insertedRecord.getPayChanleId());
        assertEquals("主体ID应该一致", record.getMainbodyId(), insertedRecord.getMainbodyId());
        assertEquals("企业编码应该一致", TEST_CORP_CODE, insertedRecord.getCorpCode());
    }

    /**
     * 测试批量插入操作
     * <p>
     * 测试步骤：
     * 1. 创建多个测试商户渠道配置数据
     * 2. 执行批量插入操作
     * 3. 验证所有记录的主键ID都被自动生成
     * 4. 通过ID查询验证所有记录都被正确插入
     */
    @Test
    public void testBatchInsert() {
        // 1. 创建多个测试商户渠道配置数据
        List<MainbodyChanleDO> recordList = new java.util.ArrayList<>();
        for (int i = 0; i < 3; i++) {
            MainbodyChanleDO record = createTestDO();
            recordList.add(record);
        }

        // 2. 执行批量插入操作
        mainbodyChanleDao.batchInsert(recordList);

        // 3. 查询插入数据
        MainbodyChanleQueryArg queryArg = new MainbodyChanleQueryArg();
        queryArg.setCorpCode(TEST_CORP_CODE);
        List<MainbodyChanleDO> recordDOList = mainbodyChanleDao.queryByArg(queryArg);

        // 4. 通过ID查询验证所有记录都被正确插入
        for (MainbodyChanleDO record : recordDOList) {
            assertNotNull("每个记录都应该生成ID", record.getId());
        }
    }

    /**
     * 测试根据ID查询操作
     * <p>
     * 测试步骤：
     * 1. 插入测试记录
     * 2. 使用插入记录的ID进行查询
     * 3. 验证查询结果正确
     */
    @Test
    public void testQueryById() {
        // 1. 插入测试记录
        MainbodyChanleDO record = createTestDO();
        mainbodyChanleDao.insert(record);

        // 2. 使用插入记录的ID进行查询
        MainbodyChanleDO queriedRecord = mainbodyChanleDao.queryById(record.getId());

        // 3. 验证查询结果正确
        assertNotNull("应该能查询到记录", queriedRecord);
        assertEquals("ID应该一致", record.getId(), queriedRecord.getId());
        assertEquals("支付渠道ID应该一致", record.getPayChanleId(), queriedRecord.getPayChanleId());
        assertEquals("企业编码应该一致", TEST_CORP_CODE, queriedRecord.getCorpCode());
    }

    /**
     * 测试基本的更新功能
     * <p>
     * 目的：验证 updateByArg 方法的基本更新功能
     * 步骤：
     * 1. 插入一条测试记录
     * 2. 创建更新参数，修改部分字段
     * 3. 执行更新操作
     * 4. 查询更新后的记录，验证字段值已正确更新
     */
    @Test
    public void testUpdateByArg() {
        // 1. 插入测试记录
        MainbodyChanleDO record = createTestDO();
        mainbodyChanleDao.insert(record);

        // 2. 创建更新参数
        MainbodyChanleUpdateArg updateArg = new MainbodyChanleUpdateArg();
        updateArg.setId(record.getId());
        updateArg.setCorpCode(TEST_CORP_CODE);
        updateArg.setPayChanleId(999L);
        updateArg.setMainbodyId(888L);
        updateArg.setRemark("更新后的备注");
        updateArg.setModifyBy("testUpdater");

        // 3. 执行更新操作
        int updateCount = mainbodyChanleDao.updateByArg(updateArg);
        assertEquals("应该更新1条记录", 1, updateCount);

        // 4. 验证更新结果
        MainbodyChanleDO updatedRecord = mainbodyChanleDao.queryById(record.getId());
        assertEquals("支付渠道ID应该被更新", Long.valueOf(999L), updatedRecord.getPayChanleId());
        assertEquals("主体ID应该被更新", Long.valueOf(888L), updatedRecord.getMainbodyId());
        assertEquals("备注应该被更新", "更新后的备注", updatedRecord.getRemark());
    }

    /**
     * 测试软删除功能
     * <p>
     * 目的：验证 softDeleteByArg 方法的软删除功能
     * 步骤：
     * 1. 插入一条测试记录
     * 2. 创建删除参数
     * 3. 执行软删除操作
     * 4. 验证记录被标记为已删除（deleted = 'T'）
     * 5. 验证通过ID查询无法再查询到该记录
     */
    @Test
    public void testSoftDeleteByArg() {
        // 1. 插入测试记录
        MainbodyChanleDO record = createTestDO();
        mainbodyChanleDao.insert(record);

        // 2. 创建删除参数
        MainbodyChanleDeleteArg deleteArg = new MainbodyChanleDeleteArg();
        deleteArg.setId(record.getId());
        deleteArg.setCorpCode(TEST_CORP_CODE);
        deleteArg.setModifyBy("testDeleter");

        // 3. 执行软删除操作
        int deleteCount = mainbodyChanleDao.softDeleteByArg(deleteArg);
        assertEquals("应该删除1条记录", 1, deleteCount);

        // 4. 验证软删除结果
        MainbodyChanleDO deletedRecord = mainbodyChanleDao.queryById(record.getId());
        assertNull("软删除后应该无法通过ID查询到记录", deletedRecord);
    }

    /**
     * 测试条件查询功能
     * <p>
     * 目的：验证 queryByArg 方法的条件查询功能
     * 步骤：
     * 1. 插入多条测试记录
     * 2. 创建查询参数，设置不同的查询条件
     * 3. 执行查询操作
     * 4. 验证查询结果符合预期
     */
    @Test
    public void testQueryByArg() {
        // 1. 插入多条测试记录
        MainbodyChanleDO record1 = createTestDO();
        record1.setPayChanleId(1001L);
        mainbodyChanleDao.insert(record1);

        MainbodyChanleDO record2 = createTestDO();
        record2.setPayChanleId(1002L);
        mainbodyChanleDao.insert(record2);

        // 2. 创建查询参数
        MainbodyChanleQueryArg queryArg = new MainbodyChanleQueryArg();
        queryArg.setCorpCode(TEST_CORP_CODE);
        queryArg.setPayChanleId(1001L);

        // 3. 执行查询操作
        List<MainbodyChanleDO> resultList = mainbodyChanleDao.queryByArg(queryArg);

        // 4. 验证查询结果
        assertNotNull("查询结果不应该为null", resultList);
        assertTrue("应该查询到至少1条记录", resultList.size() >= 1);

        // 验证所有记录的企业编码都正确
        for (MainbodyChanleDO result : resultList) {
            assertEquals("企业编码应该一致", TEST_CORP_CODE, result.getCorpCode());
            assertEquals("支付渠道ID应该一致", Long.valueOf(1001L), result.getPayChanleId());
        }
    }

    /**
     * 测试计数查询功能
     * <p>
     * 目的：验证 countByArg 方法的计数查询功能
     * 步骤：
     * 1. 插入多条测试记录
     * 2. 创建查询参数
     * 3. 执行计数查询操作
     * 4. 验证计数结果正确
     */
    @Test
    public void testCountByArg() {
        // 1. 插入多条测试记录
        for (int i = 0; i < 3; i++) {
            MainbodyChanleDO record = createTestDO();
            mainbodyChanleDao.insert(record);
        }

        // 2. 创建查询参数
        MainbodyChanleQueryArg queryArg = new MainbodyChanleQueryArg();
        queryArg.setCorpCode(TEST_CORP_CODE);

        // 3. 执行计数查询操作
        int count = mainbodyChanleDao.countByArg(queryArg);

        // 4. 验证计数结果
        assertTrue("应该查询到至少3条记录", count >= 3);
    }

    /**
     * 测试根据ID删除功能
     * <p>
     * 目的：验证 deleteById 方法的删除功能
     * 步骤：
     * 1. 插入一条测试记录
     * 2. 执行根据ID删除操作
     * 3. 验证记录被删除
     */
    @Test
    public void testDeleteById() {
        // 1. 插入测试记录
        MainbodyChanleDO record = createTestDO();
        mainbodyChanleDao.insert(record);

        // 2. 执行根据ID删除操作
        int deleteCount = mainbodyChanleDao.deleteById(record.getId());
        assertEquals("应该删除1条记录", 1, deleteCount);

        // 3. 验证删除结果
        MainbodyChanleDO deletedRecord = mainbodyChanleDao.queryById(record.getId());
        assertNull("删除后应该无法通过ID查询到记录", deletedRecord);
    }

    /**
     * 创建测试用的数据对象
     *
     * @return 测试用的MainbodyChanleDO对象
     */
    private MainbodyChanleDO createTestDO() {
        MainbodyChanleDO record = new MainbodyChanleDO();

        record.setCorpCode(TEST_CORP_CODE);
        record.setPayChanleId(99L);
        record.setMainbodyId(99L);
        record.setMerchantId(99L);
        record.setRemark("测试备注" + String.valueOf(new Random().nextInt(1000)));
        record.setCreateTime(new Date());
        record.setCreateBy(TEST_USER);
        record.setModifyTime(new Date());
        record.setModifyBy(TEST_USER);
        record.setDeleted("F");

        return record;
    }
} 