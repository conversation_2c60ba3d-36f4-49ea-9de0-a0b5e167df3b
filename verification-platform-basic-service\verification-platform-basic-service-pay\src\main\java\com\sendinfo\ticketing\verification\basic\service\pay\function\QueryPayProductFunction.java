package com.sendinfo.ticketing.verification.basic.service.pay.function;

import com.sendinfo.ticketing.verification.basic.api.pay.request.PayProductQueryCondition;
import com.sendinfo.ticketing.verification.basic.api.pay.request.PayProductQueryRequest;
import com.sendinfo.ticketing.verification.basic.component.pay.PayProductReadComponent;
import com.sendinfo.ticketing.verification.basic.component.pay.converter.PayProductConverter;
import com.sendinfo.ticketing.verification.basic.model.pay.PayProduct;
import com.sendinfo.ticketing.verification.basic.model.pay.error.PayErrorDef;
import com.sendinfo.ticketing.verification.basic.service.pay.enums.PayProductAttachmentKey;
import com.sendinfo.ticketing.verification.common.api.request.PageRequest;
import com.sendinfo.ticketing.verification.common.service.function.QueryPageByConditionFunction;
import com.sendinfo.ticketing.verification.common.service.function.QuerySingleByIdFunction;
import com.sendinfo.ticketing.verification.common.service.stereotype.Function;
import com.sendinfo.ticketing.verification.common.service.support.QueryPageByConditionLogicAction;
import com.sendinfo.ticketing.verification.common.service.support.QuerySingleByIdLogicAction;
import com.sendinfo.ticketing.verification.flow.Hint;
import com.sendinfo.ticketing.verification.flow.Question;
import com.sendinfo.ticketing.verification.flow.function.LogicAction;
import lombok.Getter;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 支付产品工具类
 * <AUTHOR>
 */
@Getter
@Function("queryPayProductFunction")
public class QueryPayProductFunction implements QuerySingleByIdFunction<Long>,
        QueryPageByConditionFunction<PayProductQueryCondition> {

    private final PayProductReadComponent payProductReadComponent;
    private final PayProductConverter converter;
    private final LogicAction<Long> querySingleById;
    private final LogicAction<PageRequest<PayProductQueryCondition>> queryPageByCondition;

    public QueryPayProductFunction(PayProductReadComponent payProductReadComponent,
                                   PayProductConverter converter) {
        this.payProductReadComponent = payProductReadComponent;
        this.converter = converter;
        this.querySingleById = new QuerySingleByIdLogicAction<>(payProductReadComponent,
                PayProductAttachmentKey.PAY_PRODUCT_ATTACHMENT_KEY,
                PayErrorDef.QUERY_PAY_PRODUCT_ERROR);
        this.queryPageByCondition = new QueryPageByConditionLogicAction<>(payProductReadComponent, converter::r_pr2p,
                PayProductAttachmentKey.PAY_PRODUCT_DATA_LIST_ATTACHMENT_KEY,
                PayProductAttachmentKey.PAY_PRODUCT_DATA_COUNT_ATTACHMENT_KEY);
    }

    public Hint queryPayProducts(Question<PayProductQueryRequest> question) {
        PayProductQueryRequest req = question.getBody();
        List<PayProduct> payProducts = payProductReadComponent.queryPayProducts(req.getCorpCode());

        question.setAttachment(PayProductAttachmentKey.PAY_PRODUCT_DATA_LIST_ATTACHMENT_KEY, payProducts);
        return Hint.gotoNext();
    }
}