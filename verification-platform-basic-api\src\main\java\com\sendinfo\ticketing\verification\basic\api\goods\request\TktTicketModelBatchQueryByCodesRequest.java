package com.sendinfo.ticketing.verification.basic.api.goods.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Set;

/**
 * @Auther: chengjie.li
 * @Date: 2025/6/10 20:27
 */
@Getter
@Setter
@ToString(callSuper = true)
public class TktTicketModelBatchQueryByCodesRequest implements Serializable {

    private static final long serialVersionUID = 7004343121377386872L;
    /**
     * 票型编码
     */
    @NotEmpty
    private Set<String> ticketCodes;

    /**
     * 企业编码
     */
    @NotNull
    private String corpCode;
}
