package com.sendinfo.ticketing.verification.basic.api.infra.request;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 组件分类查询条件
 * 
 * <AUTHOR> 2025-07-22 11:45:32
 */
@Getter
@Setter
@ToString
public class FeatureComponentCategoryQueryCondition implements Serializable {
    private static final long serialVersionUID = -4876543210123456790L;

    /**
     * 企业编码
     */
    @NotBlank(message = "企业编码不能为空")
    private String corpCode;

    /**
     * 分类名称(模糊查询)
     */
    @Size(max = 64, message = "分类名称长度不能超过64")
    private String categoryName;
}
