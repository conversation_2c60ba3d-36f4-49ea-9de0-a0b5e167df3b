/**
 * System parameter group component relation read component implementation
 *
 * <AUTHOR> 2025-01-24 15:30:00
 */
package com.sendinfo.ticketing.verification.basic.component.infra.impl;

import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

import com.sendinfo.ticketing.verification.basic.component.infra.SysParamGroupComponentRelationReadComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.converter.SysParamGroupComponentRelationConverter;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamGroupComponentRelationQueryParam;
import com.sendinfo.ticketing.verification.basic.model.infra.SysParamGroupComponentRelation;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamGroupComponentRelationQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.SysParamGroupComponentRelationDao;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.SysParamGroupComponentRelationDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedCountRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedPageRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleRead;

/**
 * 参数分组组件关联查询组件实现类，负责查询参数分组组件关联的业务逻辑
 *
 * <AUTHOR> 2025-01-24 15:30:00
 */
@Component("sysParamGroupComponentRelationReadComponent")
@Getter
public class SysParamGroupComponentRelationReadComponentImpl
        implements SysParamGroupComponentRelationReadComponent,
        DaoBasedSingleRead<Long, SysParamGroupComponentRelationQueryParam, SysParamGroupComponentRelation, SysParamGroupComponentRelationDO>,
        DaoBasedPageRead<Long, SysParamGroupComponentRelationQueryParam, SysParamGroupComponentRelation, SysParamGroupComponentRelationDO, SysParamGroupComponentRelationQueryArg>,
        DaoBasedCountRead<Long, SysParamGroupComponentRelationQueryParam, SysParamGroupComponentRelation, SysParamGroupComponentRelationQueryArg> {

    private final SysParamGroupComponentRelationDao dao;
    private final SysParamGroupComponentRelationConverter converter;

    public SysParamGroupComponentRelationReadComponentImpl(SysParamGroupComponentRelationDao dao, SysParamGroupComponentRelationConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }

    @Override
    public List<SysParamGroupComponentRelation> queryListByGroupCode(String groupCode, String corpCode) {
        List<SysParamGroupComponentRelationDO> dataObjects = dao.queryListByGroupCode(groupCode, corpCode);
        return dataObjects.stream()
                .map(converter::r_d2m)
                .collect(Collectors.toList());
    }

    @Override
    public List<SysParamGroupComponentRelation> queryListByComponentCode(String componentCode, String corpCode) {
        List<SysParamGroupComponentRelationDO> dataObjects = dao.queryListByComponentCode(componentCode, corpCode);
        return dataObjects.stream()
                .map(converter::r_d2m)
                .collect(Collectors.toList());
    }

    @Override
    public SysParamGroupComponentRelation queryByGroupAndComponent(String groupCode, String componentCode, String corpCode) {
        SysParamGroupComponentRelationDO dataObject = dao.queryByGroupAndComponent(groupCode, componentCode, corpCode);
        return dataObject != null ? converter.r_d2m(dataObject) : null;
    }
}