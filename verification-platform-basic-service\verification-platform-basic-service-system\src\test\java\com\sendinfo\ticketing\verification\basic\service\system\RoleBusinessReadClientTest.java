package com.sendinfo.ticketing.verification.basic.service.system;

import com.alibaba.fastjson.JSON;
import com.sendinfo.ticketing.verification.basic.api.system.RoleBusinessReadService;
import com.sendinfo.ticketing.verification.basic.api.system.request.RoleBusinessQueryRequest;
import com.sendinfo.ticketing.verification.basic.api.system.request.RoleBusinessRoleIdsQueryRequest;
import com.sendinfo.ticketing.verification.basic.model.system.RoleBusiness;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;
import org.apache.dubbo.config.ReferenceConfig;
import org.apache.dubbo.config.RegistryConfig;
import org.apache.dubbo.config.bootstrap.DubboBootstrap;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 角色业务权限读取服务客户端测试
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/4 16:20
 */
public class RoleBusinessReadClientTest {

	public static void main(String[] args) {
		// 配置注册中心
		RegistryConfig registryConfig = new RegistryConfig();
		registryConfig.setAddress("nacos://testnacos.sendinfocs.com:8848?namespace=bb13d33c-bda0-4552-91c3-173151d43186");

		// 配置DubboBootstrap
		DubboBootstrap bootstrap = DubboBootstrap.getInstance()
				.application("role-business-read-client")
				.registry(registryConfig);

		// 创建服务引用
		ReferenceConfig<RoleBusinessReadService> referenceConfig = new ReferenceConfig<>();
		referenceConfig.setInterface(RoleBusinessReadService.class);
		referenceConfig.setVersion("1.0.0");
		referenceConfig.setTimeout(10000);
		referenceConfig.setCheck(false);

		bootstrap.reference(referenceConfig);
		bootstrap.start();

		// 获取服务代理
		RoleBusinessReadService roleBusinessReadService = referenceConfig.get();

		try {
			// 测试根据主键ID查询角色业务权限
			System.out.println("=== 测试根据主键ID查询角色业务权限 ===");
			ResultModel<RoleBusiness> singleResult = roleBusinessReadService.queryById(1L);
			if (singleResult != null && singleResult.isSuccess()) {
				System.out.println("单个查询成功：" + JSON.toJSONString(singleResult.getModel()));
			} else {
				System.out.println("单个查询失败：" + (singleResult != null ? singleResult.getErrorMessage() : "返回null"));
			}

			// 测试根据条件查询角色业务权限列表
			System.out.println("\n=== 测试根据条件查询角色业务权限列表 ===");
			RoleBusinessQueryRequest queryRequest = new RoleBusinessQueryRequest();
			queryRequest.setCorpCode("TEST_CORP");
			queryRequest.setRoleId(1);
			queryRequest.setBusinessId(1);
			queryRequest.setBusinessType("travel");
			ResultModel<List<RoleBusiness>> listResult = roleBusinessReadService.queryList(queryRequest);
			if (listResult != null && listResult.isSuccess()) {
				System.out.println("列表查询成功，数据条数：" + (listResult.getModel() != null ? listResult.getModel().size() : 0));
				System.out.println("返回数据：" + JSON.toJSONString(listResult.getModel()));
			} else {
				System.out.println("列表查询失败：" + (listResult != null ? listResult.getErrorMessage() : "返回null"));
			}

			// 测试根据角色ID集合查询角色业务权限列表
			System.out.println("\n=== 测试根据角色ID集合查询角色业务权限列表 ===");
			RoleBusinessRoleIdsQueryRequest roleIdsQueryRequest = new RoleBusinessRoleIdsQueryRequest();
			roleIdsQueryRequest.setCorpCode("TEST_CORP");
			roleIdsQueryRequest.setBusinessType("travel");
			Set<Long> roleIds = new HashSet<>();
			roleIds.add(1L);
			roleIds.add(2L);
			roleIdsQueryRequest.setRoleIds(roleIds);
			ResultModel<List<RoleBusiness>> roleIdsResult = roleBusinessReadService.queryByRoleIds(roleIdsQueryRequest);
			if (roleIdsResult != null && roleIdsResult.isSuccess()) {
				System.out.println("角色ID集合查询成功，数据条数：" + (roleIdsResult.getModel() != null ? roleIdsResult.getModel().size() : 0));
				System.out.println("返回数据：" + JSON.toJSONString(roleIdsResult.getModel()));
			} else {
				System.out.println("角色ID集合查询失败：" + (roleIdsResult != null ? roleIdsResult.getErrorMessage() : "返回null"));
			}

		} catch (Exception e) {
			System.err.println("调用异常：" + e.getMessage());
			e.printStackTrace();
		} finally {
			// 清理资源
			bootstrap.stop();
		}
	}
} 