package com.sendinfo.ticketing.verification.basic.component.pay.converter;

import com.sendinfo.ticketing.verification.basic.component.pay.param.TktSellerPayawayCreateParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.TktSellerPayawayQueryParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.TktSellerPayawayUpdateParam;
import com.sendinfo.ticketing.verification.basic.enums.CommonUseFlagEnum;
import com.sendinfo.ticketing.verification.basic.model.pay.TktSellerPayaway;
import com.sendinfo.ticketing.verification.basic.model.pay.enums.ClientTypeEnum;
import com.sendinfo.ticketing.verification.basic.model.pay.enums.SaleModelEnum;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.TktSellerPayawayQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.TktSellerPayawayUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.TktSellerPayawayDO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;

/**
 * TktSellerPayawayConverter单元测试
 * 测试售票员收款方式转换器的各种转换功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RunWith(MockitoJUnitRunner.class)
public class TktSellerPayawayConverterTest {

    @InjectMocks
    private TktSellerPayawayConverter tktSellerPayawayConverter;

    // 测试常量
    private static final String TEST_CORP_CODE = "TEST_CORP_001";
    private static final Long TEST_ID = 1L;
    private static final String TEST_SELLER = "test_seller";
    private static final Long TEST_ACC_ID = 100L;
    private static final String TEST_PAY_AWAY = "现金";
    private static final Integer TEST_SORT = 1;
    private static final Long TEST_PAY_ID = 200L;
    private static final String TEST_CREATE_BY = "testUser";
    private static final String TEST_MODIFY_BY = "testUser";

    /**
     * 测试目的：验证TktSellerPayawayCreateParam转换为TktSellerPayawayDO的功能
     * 测试步骤：
     * 1. 创建完整的TktSellerPayawayCreateParam对象
     * 2. 调用c_p2d方法进行转换
     * 3. 验证转换结果的正确性
     */
    @Test
    public void testCreateParam2Do_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        TktSellerPayawayCreateParam createParam = createTktSellerPayawayCreateParam();

        // 2. 执行转换
        TktSellerPayawayDO result = tktSellerPayawayConverter.c_p2d(createParam);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", result);
        assertEquals("企业编码应该一致", TEST_CORP_CODE, result.getCorpCode());
        assertEquals("售票员应该一致", TEST_SELLER, result.getSeller());
        assertEquals("账户ID应该一致", TEST_ACC_ID, result.getAccId());
        assertEquals("售票模式应该一致", SaleModelEnum.NORMAL_SALE.getCode(), result.getSaleModel());
        assertEquals("客户类型应该一致", ClientTypeEnum.INDIVIDUAL.getCode(), result.getClientType());
        assertEquals("支付方式应该一致", TEST_PAY_AWAY, result.getPayAway());
        assertEquals("排序应该一致", TEST_SORT, result.getSort());
        assertEquals("启用状态应该一致", CommonUseFlagEnum.ENABLED.getCode(), result.getUseFlag());
        assertEquals("支付ID应该一致", TEST_PAY_ID, result.getPayId());
        assertEquals("创建人应该一致", TEST_CREATE_BY, result.getCreateBy());
    }

    /**
     * 测试目的：验证TktSellerPayawayCreateParam为null时的处理
     */
    @Test
    public void testCreateParam2Do_WithNullInput_ShouldReturnNull() {
        // 1. 执行转换
        TktSellerPayawayDO result = tktSellerPayawayConverter.c_p2d(null);

        // 2. 验证结果
        assertNull("输入为null时应该返回null", result);
    }

    /**
     * 测试目的：验证TktSellerPayawayDO转换为TktSellerPayaway的功能
     * 测试步骤：
     * 1. 创建完整的TktSellerPayawayDO对象
     * 2. 调用r_d2m方法进行转换
     * 3. 验证转换结果的正确性
     */
    @Test
    public void testReadDo2Model_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        TktSellerPayawayDO tktSellerPayawayDO = createTktSellerPayawayDO();

        // 2. 执行转换
        TktSellerPayaway result = tktSellerPayawayConverter.r_d2m(tktSellerPayawayDO);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", result);
        assertEquals("ID应该一致", TEST_ID, result.getId());
        assertEquals("企业编码应该一致", TEST_CORP_CODE, result.getCorpCode());
        assertEquals("售票员应该一致", TEST_SELLER, result.getSeller());
        assertEquals("账户ID应该一致", TEST_ACC_ID, result.getAccId());
        assertEquals("售票模式应该一致", SaleModelEnum.NORMAL_SALE, result.getSaleModel());
        assertEquals("客户类型应该一致", ClientTypeEnum.INDIVIDUAL, result.getClientType());
        assertEquals("支付方式应该一致", TEST_PAY_AWAY, result.getPayAway());
        assertEquals("排序应该一致", TEST_SORT, result.getSort());
        assertEquals("启用状态应该一致", CommonUseFlagEnum.ENABLED, result.getUseFlag());
        assertEquals("支付ID应该一致", TEST_PAY_ID, result.getPayId());
        assertEquals("创建人应该一致", TEST_CREATE_BY, result.getCreateBy());
        assertEquals("修改人应该一致", TEST_MODIFY_BY, result.getModifyBy());
    }

    /**
     * 测试目的：验证TktSellerPayawayDO为null时的处理
     */
    @Test
    public void testReadDo2Model_WithNullInput_ShouldReturnNull() {
        // 1. 执行转换
        TktSellerPayaway result = tktSellerPayawayConverter.r_d2m(null);

        // 2. 验证结果
        assertNull("输入为null时应该返回null", result);
    }

    /**
     * 测试目的：验证List<TktSellerPayawayDO>转换为List<TktSellerPayaway>的功能
     * 测试步骤：
     * 1. 创建包含多个TktSellerPayawayDO的列表
     * 2. 调用r_ds2ms方法进行转换
     * 3. 验证转换结果的正确性
     */
    @Test
    public void testReadDoList2ModelList_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        List<TktSellerPayawayDO> tktSellerPayawayDOList = Arrays.asList(
                createTktSellerPayawayDO(),
                createTktSellerPayawayDO()
        );

        // 2. 执行转换
        List<TktSellerPayaway> result = tktSellerPayawayConverter.r_ds2ms(tktSellerPayawayDOList);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", result);
        assertEquals("列表大小应该一致", 2, result.size());

        // 验证第一个元素
        TktSellerPayaway firstResult = result.get(0);
        assertEquals("第一个元素的ID应该一致", TEST_ID, firstResult.getId());
        assertEquals("第一个元素的企业编码应该一致", TEST_CORP_CODE, firstResult.getCorpCode());
        assertEquals("第一个元素的售票员应该一致", TEST_SELLER, firstResult.getSeller());
        assertEquals("第一个元素的账户ID应该一致", TEST_ACC_ID, firstResult.getAccId());
        assertEquals("第一个元素的售票模式应该一致", SaleModelEnum.NORMAL_SALE, firstResult.getSaleModel());
        assertEquals("第一个元素的客户类型应该一致", ClientTypeEnum.INDIVIDUAL, firstResult.getClientType());
        assertEquals("第一个元素的支付方式应该一致", TEST_PAY_AWAY, firstResult.getPayAway());
        assertEquals("第一个元素的排序应该一致", TEST_SORT, firstResult.getSort());
        assertEquals("第一个元素的启用状态应该一致", CommonUseFlagEnum.ENABLED, firstResult.getUseFlag());
        assertEquals("第一个元素的支付ID应该一致", TEST_PAY_ID, firstResult.getPayId());

        // 验证第二个元素
        TktSellerPayaway secondResult = result.get(1);
        assertEquals("第二个元素的ID应该一致", TEST_ID, secondResult.getId());
        assertEquals("第二个元素的企业编码应该一致", TEST_CORP_CODE, secondResult.getCorpCode());
        assertEquals("第二个元素的售票员应该一致", TEST_SELLER, secondResult.getSeller());
        assertEquals("第二个元素的账户ID应该一致", TEST_ACC_ID, secondResult.getAccId());
        assertEquals("第二个元素的售票模式应该一致", SaleModelEnum.NORMAL_SALE, secondResult.getSaleModel());
        assertEquals("第二个元素的客户类型应该一致", ClientTypeEnum.INDIVIDUAL, secondResult.getClientType());
        assertEquals("第二个元素的支付方式应该一致", TEST_PAY_AWAY, secondResult.getPayAway());
        assertEquals("第二个元素的排序应该一致", TEST_SORT, secondResult.getSort());
        assertEquals("第二个元素的启用状态应该一致", CommonUseFlagEnum.ENABLED, secondResult.getUseFlag());
        assertEquals("第二个元素的支付ID应该一致", TEST_PAY_ID, secondResult.getPayId());
    }

    /**
     * 测试目的：验证List<TktSellerPayawayDO>为null时的处理
     */
    @Test
    public void testReadDoList2ModelList_WithNullInput_ShouldReturnNull() {
        // 1. 执行转换
        List<TktSellerPayaway> result = tktSellerPayawayConverter.r_ds2ms(null);

        // 2. 验证结果
        assertNull("输入为null时应该返回null", result);
    }

    /**
     * 测试目的：验证空列表的处理
     */
    @Test
    public void testReadDoList2ModelList_WithEmptyList_ShouldReturnEmptyList() {
        // 1. 创建空列表
        List<TktSellerPayawayDO> emptyList = Arrays.asList();

        // 2. 执行转换
        List<TktSellerPayaway> result = tktSellerPayawayConverter.r_ds2ms(emptyList);

        // 3. 验证结果
        assertNotNull("结果不应为null", result);
        assertTrue("结果应该是空列表", result.isEmpty());
    }

    /**
     * 测试目的：验证TktSellerPayawayQueryParam转换为TktSellerPayawayQueryArg的功能
     * 测试步骤：
     * 1. 创建完整的TktSellerPayawayQueryParam对象
     * 2. 调用r_p2a方法进行转换
     * 3. 验证转换结果的正确性
     */
    @Test
    public void testReadQueryParam2QueryArg_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        TktSellerPayawayQueryParam queryParam = createTktSellerPayawayQueryParam();

        // 2. 执行转换
        TktSellerPayawayQueryArg result = tktSellerPayawayConverter.r_p2a(queryParam);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", result);
        assertEquals("企业编码应该一致", TEST_CORP_CODE, result.getCorpCode());
        assertEquals("售票员应该一致", TEST_SELLER, result.getSeller());
        assertEquals("账户ID应该一致", TEST_ACC_ID, result.getAccId());
        assertEquals("售票模式应该一致", SaleModelEnum.NORMAL_SALE.getCode(), result.getSaleModel());
        assertEquals("客户类型应该一致", ClientTypeEnum.INDIVIDUAL.getCode(), result.getClientType());
        assertEquals("支付方式应该一致", TEST_PAY_AWAY, result.getPayAway());
        assertEquals("启用状态应该一致", CommonUseFlagEnum.ENABLED.getCode(), result.getUseFlag());
        assertEquals("支付ID应该一致", TEST_PAY_ID, result.getPayId());
    }

    /**
     * 测试目的：验证TktSellerPayawayQueryParam为null时的处理
     */
    @Test
    public void testReadQueryParam2QueryArg_WithNullInput_ShouldReturnNull() {
        // 1. 执行转换
        TktSellerPayawayQueryArg result = tktSellerPayawayConverter.r_p2a(null);

        // 2. 验证结果
        assertNull("输入为null时应该返回null", result);
    }

    /**
     * 测试目的：验证TktSellerPayawayUpdateParam转换为TktSellerPayawayUpdateArg的功能
     * 测试步骤：
     * 1. 创建完整的TktSellerPayawayUpdateParam对象
     * 2. 调用u_p2a方法进行转换
     * 3. 验证转换结果的正确性
     */
    @Test
    public void testUpdateParam2UpdateArg_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        TktSellerPayawayUpdateParam updateParam = createTktSellerPayawayUpdateParam();
        TktSellerPayaway currentModel = createTktSellerPayaway();

        // 2. 执行转换
        TktSellerPayawayUpdateArg result = tktSellerPayawayConverter.u_p2a(updateParam, currentModel);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", result);
        assertEquals("企业编码应该一致", TEST_CORP_CODE, result.getCorpCode());
        assertEquals("售票员应该一致", TEST_SELLER, result.getSeller());
        assertEquals("账户ID应该一致", TEST_ACC_ID, result.getAccId());
        assertEquals("售票模式应该一致", SaleModelEnum.NORMAL_SALE.getCode(), result.getSaleModel());
        assertEquals("客户类型应该一致", ClientTypeEnum.INDIVIDUAL.getCode(), result.getClientType());
        assertEquals("支付方式应该一致", TEST_PAY_AWAY, result.getPayAway());
        assertEquals("排序应该一致", TEST_SORT, result.getSort());
        assertEquals("启用状态应该一致", CommonUseFlagEnum.ENABLED.getCode(), result.getUseFlag());
        assertEquals("支付ID应该一致", TEST_PAY_ID, result.getPayId());
        assertEquals("修改人应该一致", TEST_MODIFY_BY, result.getModifyBy());
    }

    /**
     * 测试目的：验证TktSellerPayawayUpdateParam为null时的处理
     */
    @Test
    public void testUpdateParam2UpdateArg_WithNullInput_ShouldReturnNull() {
        // 1. 创建当前模型
        TktSellerPayaway currentModel = createTktSellerPayaway();

        // 2. 执行转换
        TktSellerPayawayUpdateArg result = tktSellerPayawayConverter.u_p2a(null, currentModel);

        // 3. 验证结果
        assertNull("输入为null时应该返回null", result);
    }

    // ==================== 测试数据构建方法 ====================

    /**
     * 创建TktSellerPayawayCreateParam测试数据
     */
    private TktSellerPayawayCreateParam createTktSellerPayawayCreateParam() {
        TktSellerPayawayCreateParam createParam = new TktSellerPayawayCreateParam();
        createParam.setCorpCode(TEST_CORP_CODE);
        createParam.setSeller(TEST_SELLER);
        createParam.setAccId(TEST_ACC_ID);
        createParam.setSaleModel(SaleModelEnum.NORMAL_SALE);
        createParam.setClientType(ClientTypeEnum.INDIVIDUAL);
        createParam.setPayAway(TEST_PAY_AWAY);
        createParam.setSort(TEST_SORT);
        createParam.setUseFlag(CommonUseFlagEnum.ENABLED);
        createParam.setPayId(TEST_PAY_ID);
        createParam.setCreateBy(TEST_CREATE_BY);
        createParam.setModifyBy(TEST_MODIFY_BY);
        return createParam;
    }

    /**
     * 创建TktSellerPayawayDO测试数据
     */
    private TktSellerPayawayDO createTktSellerPayawayDO() {
        TktSellerPayawayDO tktSellerPayawayDO = new TktSellerPayawayDO();
        tktSellerPayawayDO.setId(TEST_ID);
        tktSellerPayawayDO.setCorpCode(TEST_CORP_CODE);
        tktSellerPayawayDO.setSeller(TEST_SELLER);
        tktSellerPayawayDO.setAccId(TEST_ACC_ID);
        tktSellerPayawayDO.setSaleModel(SaleModelEnum.NORMAL_SALE.getCode());
        tktSellerPayawayDO.setClientType(ClientTypeEnum.INDIVIDUAL.getCode());
        tktSellerPayawayDO.setPayAway(TEST_PAY_AWAY);
        tktSellerPayawayDO.setSort(TEST_SORT);
        tktSellerPayawayDO.setUseFlag(CommonUseFlagEnum.ENABLED.getCode());
        tktSellerPayawayDO.setPayId(TEST_PAY_ID);
        tktSellerPayawayDO.setCreateBy(TEST_CREATE_BY);
        tktSellerPayawayDO.setModifyBy(TEST_MODIFY_BY);
        return tktSellerPayawayDO;
    }

    /**
     * 创建TktSellerPayawayQueryParam测试数据
     */
    private TktSellerPayawayQueryParam createTktSellerPayawayQueryParam() {
        TktSellerPayawayQueryParam queryParam = new TktSellerPayawayQueryParam();
        queryParam.setCorpCode(TEST_CORP_CODE);
        queryParam.setSeller(TEST_SELLER);
        queryParam.setAccId(TEST_ACC_ID);
        queryParam.setSaleModel(SaleModelEnum.NORMAL_SALE);
        queryParam.setClientType(ClientTypeEnum.INDIVIDUAL);
        queryParam.setPayAway(TEST_PAY_AWAY);
        queryParam.setUseFlag(CommonUseFlagEnum.ENABLED);
        queryParam.setPayId(TEST_PAY_ID);
        return queryParam;
    }

    /**
     * 创建TktSellerPayawayUpdateParam测试数据
     */
    private TktSellerPayawayUpdateParam createTktSellerPayawayUpdateParam() {
        TktSellerPayawayUpdateParam updateParam = new TktSellerPayawayUpdateParam();
        updateParam.setCorpCode(TEST_CORP_CODE);
        updateParam.setSeller(TEST_SELLER);
        updateParam.setAccId(TEST_ACC_ID);
        updateParam.setSaleModel(SaleModelEnum.NORMAL_SALE);
        updateParam.setClientType(ClientTypeEnum.INDIVIDUAL);
        updateParam.setPayAway(TEST_PAY_AWAY);
        updateParam.setSort(TEST_SORT);
        updateParam.setUseFlag(CommonUseFlagEnum.ENABLED);
        updateParam.setPayId(TEST_PAY_ID);
        updateParam.setModifyBy(TEST_MODIFY_BY);
        return updateParam;
    }

    /**
     * 创建TktSellerPayaway测试数据
     */
    private TktSellerPayaway createTktSellerPayaway() {
        TktSellerPayaway tktSellerPayaway = new TktSellerPayaway();
        tktSellerPayaway.setId(TEST_ID);
        tktSellerPayaway.setCorpCode(TEST_CORP_CODE);
        tktSellerPayaway.setSeller(TEST_SELLER);
        tktSellerPayaway.setAccId(TEST_ACC_ID);
        tktSellerPayaway.setSaleModel(SaleModelEnum.NORMAL_SALE);
        tktSellerPayaway.setClientType(ClientTypeEnum.INDIVIDUAL);
        tktSellerPayaway.setPayAway(TEST_PAY_AWAY);
        tktSellerPayaway.setSort(TEST_SORT);
        tktSellerPayaway.setUseFlag(CommonUseFlagEnum.ENABLED);
        tktSellerPayaway.setPayId(TEST_PAY_ID);
        tktSellerPayaway.setCreateBy(TEST_CREATE_BY);
        tktSellerPayaway.setModifyBy(TEST_MODIFY_BY);
        return tktSellerPayaway;
    }

} 