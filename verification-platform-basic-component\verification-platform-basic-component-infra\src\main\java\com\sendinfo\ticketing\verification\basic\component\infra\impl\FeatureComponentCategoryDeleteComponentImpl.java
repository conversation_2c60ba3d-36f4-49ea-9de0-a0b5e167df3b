package com.sendinfo.ticketing.verification.basic.component.infra.impl;

import lombok.Getter;
import org.springframework.stereotype.Component;

import com.sendinfo.ticketing.verification.basic.component.infra.FeatureComponentCategoryDeleteComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.converter.FeatureComponentCategoryConverter;
import com.sendinfo.ticketing.verification.basic.component.infra.param.FeatureComponentCategoryDeleteParam;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.FeatureComponentCategoryDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.FeatureComponentCategoryDao;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleDelete;

/**
 * 组件分类删除组件实现
 * 
 * <AUTHOR> 2025-07-22 11:45:32
 */
@Component("featureComponentCategoryDeleteComponent")
@Getter
public class FeatureComponentCategoryDeleteComponentImpl
        implements FeatureComponentCategoryDeleteComponent,
        DaoBasedSingleDelete<Long, FeatureComponentCategoryDeleteParam, FeatureComponentCategoryDeleteArg> {

    private final FeatureComponentCategoryDao dao;
    private final FeatureComponentCategoryConverter converter;

    public FeatureComponentCategoryDeleteComponentImpl(FeatureComponentCategoryDao dao, FeatureComponentCategoryConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }
}
