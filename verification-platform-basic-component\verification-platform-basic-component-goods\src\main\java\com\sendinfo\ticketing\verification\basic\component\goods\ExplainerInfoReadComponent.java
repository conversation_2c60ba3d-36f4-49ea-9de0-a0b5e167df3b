package com.sendinfo.ticketing.verification.basic.component.goods;

import com.sendinfo.ticketing.verification.basic.component.goods.param.ExplainerInfoQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.ExplainerInfo;
import com.sendinfo.ticketing.verification.common.component.ReadComponent;

import java.util.List;

/**
 * 讲解人读取组件接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ExplainerInfoReadComponent extends ReadComponent<Long, ExplainerInfoQueryParam, ExplainerInfo> {

    /**
     * 模糊查询讲解人列表
     *
     * @param queryParam 讲解人查询参数
     * @return 讲解人列表
     */
    List<ExplainerInfo> queryExplainerList(ExplainerInfoQueryParam queryParam);
} 