package com.sendinfo.ticketing.verification.basic.component.goods.mapper;

import com.sendinfo.ticketing.verification.basic.api.goods.request.ExplainerPayInfoQueryRequest;
import com.sendinfo.ticketing.verification.basic.component.goods.param.ExplainerPayInfoQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.ExplainerPayInfo;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.ExplainerPayInfoQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.ExplainerPayInfoDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 服务类目计费规则对象转换映射器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface ExplainerPayInfoMapper {

    ExplainerPayInfoMapper INSTANCE = Mappers.getMapper(ExplainerPayInfoMapper.class);

    /**
     * 查询参数转换为查询参数
     *
     * @param queryParam 查询参数
     * @return 查询参数
     */
    @Mapping(target = "useFlag", expression = "java(queryParam.getUseFlag() != null ? queryParam.getUseFlag().getCode() : null)")
    ExplainerPayInfoQueryArg convert(ExplainerPayInfoQueryParam queryParam);

    /**
     * 数据对象转换为模型对象
     *
     * @param dataObject 数据对象
     * @return 模型对象
     */
    @Mapping(target = "useFlag", expression = "java(com.sendinfo.ticketing.verification.basic.enums.ExplainerUserFlagEnum.ofCode(dataObject.getUseFlag()))")
    ExplainerPayInfo convert(ExplainerPayInfoDO dataObject);

    /**
     * 查询参数转换为查询参数
     *
     * @param queryRequest 查询参数
     * @return 查询参数
     */
    @Mapping(target = "useFlag", expression = "java(com.sendinfo.ticketing.verification.basic.enums.ExplainerUserFlagEnum.ofCode(queryRequest.getUseFlag()))")
    ExplainerPayInfoQueryParam convert(ExplainerPayInfoQueryRequest queryRequest);
} 