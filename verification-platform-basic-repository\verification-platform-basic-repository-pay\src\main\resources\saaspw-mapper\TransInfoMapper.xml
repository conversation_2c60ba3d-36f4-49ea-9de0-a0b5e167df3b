<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sendinfo.ticketing.verification.basic.repository.pay.dao.TransInfoDao">

    <resultMap id="BaseResultMap" type="com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.TransInfoDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="CORP_CODE" property="corpCode" jdbcType="VARCHAR"/>
        <result column="TRANS_NO" property="transNo" jdbcType="VARCHAR"/>
        <result column="TRANS_TYPE" property="transType" jdbcType="TINYINT"/>
        <result column="TRADE_ID" property="tradeId" jdbcType="BIGINT"/>
        <result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="PAY_AWAY" property="payAway" jdbcType="VARCHAR"/>
        <result column="GATEWAY" property="gateway" jdbcType="INTEGER"/>
        <result column="GATEWAY_TRANS_NO" property="gatewayTransNo" jdbcType="VARCHAR"/>
        <result column="BUSINESS_TYPE" property="businessType" jdbcType="INTEGER"/>
        <result column="PAY_AMOUNT" property="payAmount" jdbcType="DECIMAL"/>
        <result column="LOGIN_ID" property="loginId" jdbcType="BIGINT"/>
        <result column="LOGIN_NAME" property="loginName" jdbcType="VARCHAR"/>
        <result column="PAY_STATUS" property="payStatus" jdbcType="VARCHAR"/>
        <result column="REPORT_TIME" property="reportTime" jdbcType="TIMESTAMP"/>
        <result column="PAY_TIME" property="payTime" jdbcType="TIMESTAMP"/>
        <result column="REPORT_INFO" property="reportInfo" jdbcType="VARCHAR"/>
        <result column="PAY_TYPE_VIEW" property="payTypeView" jdbcType="VARCHAR"/>
        <result column="CREATE_BY" property="createBy" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="MODIFY_BY" property="modifyBy" jdbcType="VARCHAR"/>
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="DELETED" property="deleted" jdbcType="VARCHAR"/>
        <result column="PAY_TOTAL" property="payTotal" jdbcType="DECIMAL"/>
        <result column="REFUND_PAY_TRANSNO" property="refundPayTransno" jdbcType="VARCHAR"/>
        <result column="PAY_TYPE" property="payType" jdbcType="VARCHAR"/>
        <result column="MAINBODY_PAYID" property="mainbodyPayid" jdbcType="BIGINT"/>
        <result column="PAY_INFO" property="payInfo" jdbcType="VARCHAR"/>
        <result column="EXTEND_PARAM_JSON" property="extendParamJson" jdbcType="VARCHAR"/>
        <result column="TID" property="tid" jdbcType="VARCHAR"/>
        <result column="PAY_INTEGRAL_SUM" property="payIntegralSum" jdbcType="INTEGER"/>
        <result column="PAY_INTEGRAL_TOTAL" property="payIntegralTotal" jdbcType="DECIMAL"/>
        <result column="OTHER_TRANS_NO" property="otherTransNo" jdbcType="VARCHAR"/>
        <result column="SETTLEMENT" property="settlement" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="tableName">trans_info</sql>

    <sql id="allColumns">
        id, CORP_CODE, TRANS_NO, TRANS_TYPE, TRADE_ID, TRADE_CODE, PAY_AWAY, GATEWAY, GATEWAY_TRANS_NO,
        BUSINESS_TYPE, PAY_AMOUNT, LOGIN_ID, LOGIN_NAME, PAY_STATUS, REPORT_TIME, PAY_TIME, REPORT_INFO,
        PAY_TYPE_VIEW, CREATE_BY, CREATE_TIME, MODIFY_BY, MODIFY_TIME, DELETED, PAY_TOTAL, REFUND_PAY_TRANSNO,
        PAY_TYPE, MAINBODY_PAYID, PAY_INFO, EXTEND_PARAM_JSON, TID, PAY_INTEGRAL_SUM, PAY_INTEGRAL_TOTAL,
        OTHER_TRANS_NO, SETTLEMENT
    </sql>

    <sql id="insertColumns">
        CORP_CODE, TRANS_NO, TRANS_TYPE, TRADE_ID, TRADE_CODE, PAY_AWAY, GATEWAY, GATEWAY_TRANS_NO,
        BUSINESS_TYPE, PAY_AMOUNT, LOGIN_ID, LOGIN_NAME, PAY_STATUS, REPORT_TIME, PAY_TIME, REPORT_INFO,
        PAY_TYPE_VIEW, CREATE_BY, MODIFY_BY, DELETED, PAY_TOTAL, REFUND_PAY_TRANSNO, PAY_TYPE, MAINBODY_PAYID,
        PAY_INFO, EXTEND_PARAM_JSON, TID, PAY_INTEGRAL_SUM, PAY_INTEGRAL_TOTAL, OTHER_TRANS_NO, SETTLEMENT,
        CREATE_TIME, MODIFY_TIME
    </sql>

    <insert id="insert" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.TransInfoDO"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="tableName"/> (<include refid="insertColumns"/>)
        VALUES (
            #{corpCode,jdbcType=VARCHAR}, #{transNo,jdbcType=VARCHAR}, #{transType,jdbcType=TINYINT},
            #{tradeId,jdbcType=BIGINT}, #{tradeCode,jdbcType=VARCHAR}, #{payAway,jdbcType=VARCHAR},
            #{gateway,jdbcType=INTEGER}, #{gatewayTransNo,jdbcType=VARCHAR}, #{businessType,jdbcType=INTEGER},
            #{payAmount,jdbcType=DECIMAL}, #{loginId,jdbcType=BIGINT}, #{loginName,jdbcType=VARCHAR},
            #{payStatus,jdbcType=VARCHAR}, #{reportTime,jdbcType=TIMESTAMP}, #{payTime,jdbcType=TIMESTAMP},
            #{reportInfo,jdbcType=VARCHAR}, #{payTypeView,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR},
            #{modifyBy,jdbcType=VARCHAR}, #{deleted,jdbcType=VARCHAR}, #{payTotal,jdbcType=DECIMAL},
            #{refundPayTransno,jdbcType=VARCHAR}, #{payType,jdbcType=VARCHAR}, #{mainbodyPayid,jdbcType=BIGINT},
            #{payInfo,jdbcType=VARCHAR}, #{extendParamJson,jdbcType=VARCHAR}, #{tid,jdbcType=VARCHAR},
            #{payIntegralSum,jdbcType=INTEGER}, #{payIntegralTotal,jdbcType=DECIMAL}, #{otherTransNo,jdbcType=VARCHAR},
            #{settlement,jdbcType=VARCHAR}, NOW(), NOW()
        )
    </insert>

    <select id="queryById" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT <include refid="allColumns"/>
        FROM <include refid="tableName"/>
        WHERE id = #{id,jdbcType=BIGINT}
          AND deleted = 'F'
    </select>

    <update id="updateByArg" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.arg.TransInfoUpdateArg">
        UPDATE <include refid="tableName"/>
        <set>
            <if test="payAway != null">PAY_AWAY = #{payAway,jdbcType=VARCHAR},</if>
            <if test="gatewayTransNo != null">GATEWAY_TRANS_NO = #{gatewayTransNo,jdbcType=VARCHAR},</if>
            <if test="payStatus != null">PAY_STATUS = #{payStatus,jdbcType=VARCHAR},</if>
            <if test="reportTime != null">REPORT_TIME = #{reportTime,jdbcType=TIMESTAMP},</if>
            <if test="payTime != null">PAY_TIME = #{payTime,jdbcType=TIMESTAMP},</if>
            <if test="reportInfo != null">REPORT_INFO = #{reportInfo,jdbcType=VARCHAR},</if>
            <if test="payTypeView != null">PAY_TYPE_VIEW = #{payTypeView,jdbcType=VARCHAR},</if>
            <if test="refundPayTransno != null">REFUND_PAY_TRANSNO = #{refundPayTransno,jdbcType=VARCHAR},</if>
            <if test="payType != null">PAY_TYPE = #{payType,jdbcType=VARCHAR},</if>
            <if test="mainbodyPayid != null">MAINBODY_PAYID = #{mainbodyPayid,jdbcType=BIGINT},</if>
            <if test="payInfo != null">PAY_INFO = #{payInfo,jdbcType=VARCHAR},</if>
            <if test="extendParamJson != null">EXTEND_PARAM_JSON = #{extendParamJson,jdbcType=VARCHAR},</if>
            <if test="tid != null">TID = #{tid,jdbcType=VARCHAR},</if>
            <if test="payIntegralSum != null">PAY_INTEGRAL_SUM = #{payIntegralSum,jdbcType=INTEGER},</if>
            <if test="payIntegralTotal != null">PAY_INTEGRAL_TOTAL = #{payIntegralTotal,jdbcType=DECIMAL},</if>
            <if test="otherTransNo != null">OTHER_TRANS_NO = #{otherTransNo,jdbcType=VARCHAR},</if>
            <if test="settlement != null">SETTLEMENT = #{settlement,jdbcType=VARCHAR},</if>
            <choose>
                <when test="modifyBy != null and modifyBy != ''">MODIFY_BY = #{modifyBy,jdbcType=VARCHAR},</when>
                <otherwise>MODIFY_BY = 'SYSTEM_UA',</otherwise>
            </choose>
            MODIFY_TIME = NOW()
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
          AND CORP_CODE = #{corpCode,jdbcType=VARCHAR}
          AND deleted = 'F'
    </update>

    <update id="softDeleteByArg" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.arg.TransInfoDeleteArg">
        UPDATE <include refid="tableName"/>
        SET deleted = 'T',
            <choose>
                <when test="modifyBy != null and modifyBy != ''">MODIFY_BY = #{modifyBy,jdbcType=VARCHAR},</when>
                <otherwise>MODIFY_BY = 'SYSTEM_SDA',</otherwise>
            </choose>
            MODIFY_TIME = NOW()
        WHERE id = #{id,jdbcType=BIGINT}
          AND CORP_CODE = #{corpCode,jdbcType=VARCHAR}
          AND deleted = 'F'
    </update>

    <update id="deleteById" parameterType="java.lang.Long">
        UPDATE <include refid="tableName"/>
        SET deleted = 'T',
            MODIFY_BY = 'SYSTEM_DBI',
            MODIFY_TIME = NOW()
        WHERE id = #{id,jdbcType=BIGINT}
          AND deleted = 'F'
    </update>

    <select id="countByArg" resultType="int" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.arg.TransInfoQueryArg">
        SELECT COUNT(*)
        FROM <include refid="tableName"/>
        <where>
            CORP_CODE = #{corpCode,jdbcType=VARCHAR}
            AND deleted = 'F'
            <if test="transNo != null and transNo != ''">AND TRANS_NO = #{transNo,jdbcType=VARCHAR}</if>
            <if test="tradeId != null">AND TRADE_ID = #{tradeId,jdbcType=BIGINT}</if>
            <if test="tradeCode != null and tradeCode != ''">AND TRADE_CODE = #{tradeCode,jdbcType=VARCHAR}</if>
            <if test="payStatus != null">AND PAY_STATUS = #{payStatus,jdbcType=VARCHAR}</if>
            <if test="refundPayTransno != null and refundPayTransno != ''">AND REFUND_PAY_TRANSNO = #{refundPayTransno,jdbcType=VARCHAR}</if>
            <if test="gateway != null">AND GATEWAY = #{gateway,jdbcType=INTEGER}</if>
            <if test="gatewayTransNo != null and gatewayTransNo != ''">AND GATEWAY_TRANS_NO = #{gatewayTransNo,jdbcType=VARCHAR}</if>
        </where>
    </select>

    <select id="queryByArg" resultMap="BaseResultMap" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.arg.TransInfoQueryArg">
        SELECT <include refid="allColumns"/>
        FROM <include refid="tableName"/>
        <where>
            CORP_CODE = #{corpCode,jdbcType=VARCHAR}
            AND deleted = 'F'
            <if test="transNo != null and transNo != ''">AND TRANS_NO = #{transNo,jdbcType=VARCHAR}</if>
            <if test="tradeId != null">AND TRADE_ID = #{tradeId,jdbcType=BIGINT}</if>
            <if test="tradeCode != null and tradeCode != ''">AND TRADE_CODE = #{tradeCode,jdbcType=VARCHAR}</if>
            <if test="payStatus != null">AND PAY_STATUS = #{payStatus,jdbcType=VARCHAR}</if>
            <if test="refundPayTransno != null and refundPayTransno != ''">AND REFUND_PAY_TRANSNO = #{refundPayTransno,jdbcType=VARCHAR}</if>
            <if test="gateway != null">AND GATEWAY = #{gateway,jdbcType=INTEGER}</if>
            <if test="gatewayTransNo != null and gatewayTransNo != ''">AND GATEWAY_TRANS_NO = #{gatewayTransNo,jdbcType=VARCHAR}</if>
        </where>
        ORDER BY id DESC
        <if test="limit != null and offset != null">
            LIMIT #{offset,jdbcType=INTEGER}, #{limit,jdbcType=INTEGER}
        </if>
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO <include refid="tableName"/> (<include refid="insertColumns"/>)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.corpCode,jdbcType=VARCHAR}, #{item.transNo,jdbcType=VARCHAR}, #{item.transType,jdbcType=TINYINT},
                #{item.tradeId,jdbcType=BIGINT}, #{item.tradeCode,jdbcType=VARCHAR}, #{item.payAway,jdbcType=VARCHAR},
                #{item.gateway,jdbcType=INTEGER}, #{item.gatewayTransNo,jdbcType=VARCHAR}, #{item.businessType,jdbcType=INTEGER},
                #{item.payAmount,jdbcType=DECIMAL}, #{item.loginId,jdbcType=BIGINT}, #{item.loginName,jdbcType=VARCHAR},
                #{item.payStatus,jdbcType=VARCHAR}, #{item.reportTime,jdbcType=TIMESTAMP}, #{item.payTime,jdbcType=TIMESTAMP},
                #{item.reportInfo,jdbcType=VARCHAR}, #{item.payTypeView,jdbcType=VARCHAR}, #{item.createBy,jdbcType=VARCHAR},
                #{item.modifyBy,jdbcType=VARCHAR}, #{item.deleted,jdbcType=VARCHAR}, #{item.payTotal,jdbcType=DECIMAL},
                #{item.refundPayTransno,jdbcType=VARCHAR}, #{item.payType,jdbcType=VARCHAR}, #{item.mainbodyPayid,jdbcType=BIGINT},
                #{item.payInfo,jdbcType=VARCHAR}, #{item.extendParamJson,jdbcType=VARCHAR}, #{item.tid,jdbcType=VARCHAR},
                #{item.payIntegralSum,jdbcType=INTEGER}, #{item.payIntegralTotal,jdbcType=DECIMAL}, #{item.otherTransNo,jdbcType=VARCHAR},
                #{item.settlement,jdbcType=VARCHAR}, NOW(), NOW()
            )
        </foreach>
    </insert>

    <select id="queryByTransNo" resultMap="BaseResultMap">
        SELECT <include refid="allColumns"/>
        FROM <include refid="tableName"/>
        WHERE TRANS_NO = #{transNo,jdbcType=VARCHAR}
          AND CORP_CODE = #{corpCode,jdbcType=VARCHAR}
          AND deleted = 'F'
        LIMIT 1
    </select>

    <select id="queryByTradeCode" resultMap="BaseResultMap">
        SELECT <include refid="allColumns"/>
        FROM <include refid="tableName"/>
        WHERE TRADE_CODE = #{tradeCode,jdbcType=VARCHAR}
          AND CORP_CODE = #{corpCode,jdbcType=VARCHAR}
          AND deleted = 'F'
        LIMIT 1
    </select>

    <select id="queryListByTradeCode" resultMap="BaseResultMap">
        SELECT <include refid="allColumns"/>
        FROM <include refid="tableName"/>
        WHERE TRADE_CODE = #{tradeCode,jdbcType=VARCHAR}
        AND CORP_CODE = #{corpCode,jdbcType=VARCHAR}
        AND deleted = 'F'
    </select>

    <select id="queryListByTradeCodeAndTransType"
            resultType="com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.TransInfoDO">
        SELECT <include refid="allColumns"/>
            FROM <include refid="tableName"/>
                WHERE TRADE_CODE = #{tradeCode,jdbcType=VARCHAR}
                  AND CORP_CODE = #{corpCode,jdbcType=VARCHAR}
                  AND TRANS_TYPE = #{transType,jdbcType=TINYINT}
                  AND deleted = 'F'
    </select>

</mapper>