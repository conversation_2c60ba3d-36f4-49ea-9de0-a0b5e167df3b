package com.sendinfo.ticketing.verification.basic.component.goods.impl;

import com.sendinfo.ticketing.verification.basic.component.goods.converter.TktSaleRuleConverter;
import com.sendinfo.ticketing.verification.basic.model.goods.TktSaleRule;
import com.sendinfo.ticketing.verification.basic.repository.goods.dao.TktSaleRuleDao;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TktSaleRuleDO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

/**
 * TktSaleRuleReadComponent 单元测试类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RunWith(MockitoJUnitRunner.class)
public class TktSaleRuleReadComponentImplTest {

    @Mock
    private TktSaleRuleConverter converter;

    @Mock
    private TktSaleRuleDao dao;

    @InjectMocks
    private TktSaleRuleReadComponentImpl tktSaleRuleReadComponent;

    // 测试常量
    private static final String TEST_CORP_CODE = "TEST_CORP_CODE";
    private static final Long TEST_TICKET_ID_1 = 1L;
    private static final Long TEST_TICKET_ID_2 = 2L;
    private static final String TEST_SALE_MODEL_1 = "0";
    private static final String TEST_SALE_MODEL_2 = "1";
    private static final String TEST_CONSUMER_FLAG_1 = "T";
    private static final String TEST_CONSUMER_FLAG_2 = "F";
    private static final String TEST_TRAVEL_FLAG_1 = "T";
    private static final String TEST_TRAVEL_FLAG_2 = "F";
    private static final String TEST_SEASON_TYPE_OFF_1 = "T";
    private static final String TEST_SEASON_TYPE_OFF_2 = "F";
    private static final String TEST_SEASON_TYPE_BUSY_1 = "T";
    private static final String TEST_SEASON_TYPE_BUSY_2 = "F";
    private static final String TEST_SALE_CHANNEL_1 = "0";
    private static final String TEST_SALE_CHANNEL_2 = "1";
    private static final String TEST_SALE_LIMIT_1 = "T";
    private static final String TEST_SALE_LIMIT_2 = "F";
    private static final Integer TEST_MIN_NUM_1 = 1;
    private static final Integer TEST_MIN_NUM_2 = 2;
    private static final Integer TEST_MAX_NUM_1 = 10;
    private static final Integer TEST_MAX_NUM_2 = 20;
    private static final String TEST_SALE_REG_IDCARD_1 = "T";
    private static final String TEST_SALE_REG_IDCARD_2 = "F";
    private static final String TEST_BUY_LIMIT_FLAG_1 = "T";
    private static final String TEST_BUY_LIMIT_FLAG_2 = "F";
    private static final Integer TEST_TIME_LIMIT_TYPE_1 = 1;
    private static final Integer TEST_TIME_LIMIT_TYPE_2 = 2;
    private static final Integer TEST_TIME_LIMIT_VAL_1 = 24;
    private static final Integer TEST_TIME_LIMIT_VAL_2 = 48;
    private static final Integer TEST_LIMIT_AMOUNT_1 = 5;
    private static final Integer TEST_LIMIT_AMOUNT_2 = 10;
    private static final String TEST_AGE_LIMIT_FLAG_1 = "T";
    private static final String TEST_AGE_LIMIT_FLAG_2 = "F";
    private static final String TEST_CREATE_BY = "TEST_CREATE_BY";
    private static final String TEST_MODIFY_BY = "TEST_MODIFY_BY";

    private TktSaleRuleDO tktSaleRuleDO1;
    private TktSaleRuleDO tktSaleRuleDO2;
    private TktSaleRule tktSaleRule1;
    private TktSaleRule tktSaleRule2;

    @Before
    public void setUp() {
        // 准备第一个测试数据对象 - 正常售票规则
        tktSaleRuleDO1 = new TktSaleRuleDO();
        tktSaleRuleDO1.setTicketId(TEST_TICKET_ID_1);
        tktSaleRuleDO1.setSaleModel(TEST_SALE_MODEL_1);
        tktSaleRuleDO1.setConsumerFlag(TEST_CONSUMER_FLAG_1);
        tktSaleRuleDO1.setTravelFlag(TEST_TRAVEL_FLAG_1);
        tktSaleRuleDO1.setSeasonTypeOff(TEST_SEASON_TYPE_OFF_1);
        tktSaleRuleDO1.setSeasonTypeBusy(TEST_SEASON_TYPE_BUSY_1);
        tktSaleRuleDO1.setSaleChannel(TEST_SALE_CHANNEL_1);
        tktSaleRuleDO1.setSaleLimit(TEST_SALE_LIMIT_1);
        tktSaleRuleDO1.setMinNum(TEST_MIN_NUM_1);
        tktSaleRuleDO1.setMaxNum(TEST_MAX_NUM_1);
        tktSaleRuleDO1.setSaleRegIdcard(TEST_SALE_REG_IDCARD_1);
        tktSaleRuleDO1.setBuyLimitFlag(TEST_BUY_LIMIT_FLAG_1);
        tktSaleRuleDO1.setTimeLimitType(TEST_TIME_LIMIT_TYPE_1);
        tktSaleRuleDO1.setTimeLimitVal(TEST_TIME_LIMIT_VAL_1);
        tktSaleRuleDO1.setLimitAmount(TEST_LIMIT_AMOUNT_1);
        tktSaleRuleDO1.setAgeLimitFlag(TEST_AGE_LIMIT_FLAG_1);
        tktSaleRuleDO1.setCorpCode(TEST_CORP_CODE);

        // 准备第二个测试数据对象 - 预售票规则
        tktSaleRuleDO2 = new TktSaleRuleDO();
        tktSaleRuleDO2.setTicketId(TEST_TICKET_ID_2);
        tktSaleRuleDO2.setSaleModel(TEST_SALE_MODEL_2);
        tktSaleRuleDO2.setConsumerFlag(TEST_CONSUMER_FLAG_2);
        tktSaleRuleDO2.setTravelFlag(TEST_TRAVEL_FLAG_2);
        tktSaleRuleDO2.setSeasonTypeOff(TEST_SEASON_TYPE_OFF_2);
        tktSaleRuleDO2.setSeasonTypeBusy(TEST_SEASON_TYPE_BUSY_2);
        tktSaleRuleDO2.setSaleChannel(TEST_SALE_CHANNEL_2);
        tktSaleRuleDO2.setSaleLimit(TEST_SALE_LIMIT_2);
        tktSaleRuleDO2.setMinNum(TEST_MIN_NUM_2);
        tktSaleRuleDO2.setMaxNum(TEST_MAX_NUM_2);
        tktSaleRuleDO2.setSaleRegIdcard(TEST_SALE_REG_IDCARD_2);
        tktSaleRuleDO2.setBuyLimitFlag(TEST_BUY_LIMIT_FLAG_2);
        tktSaleRuleDO2.setTimeLimitType(TEST_TIME_LIMIT_TYPE_2);
        tktSaleRuleDO2.setTimeLimitVal(TEST_TIME_LIMIT_VAL_2);
        tktSaleRuleDO2.setLimitAmount(TEST_LIMIT_AMOUNT_2);
        tktSaleRuleDO2.setAgeLimitFlag(TEST_AGE_LIMIT_FLAG_2);
        tktSaleRuleDO2.setCorpCode(TEST_CORP_CODE);

        // 准备对应的模型对象 - 正常售票规则
        tktSaleRule1 = new TktSaleRule();
        tktSaleRule1.setCorpCode(TEST_CORP_CODE);
        tktSaleRule1.setTicketId(TEST_TICKET_ID_1);
        tktSaleRule1.setSaleModel(TEST_SALE_MODEL_1);
        tktSaleRule1.setConsumerFlag(TEST_CONSUMER_FLAG_1);
        tktSaleRule1.setTravelFlag(TEST_TRAVEL_FLAG_1);
        tktSaleRule1.setSeasonTypeOff(TEST_SEASON_TYPE_OFF_1);
        tktSaleRule1.setSeasonTypeBusy(TEST_SEASON_TYPE_BUSY_1);
        tktSaleRule1.setSaleChannel(TEST_SALE_CHANNEL_1);
        tktSaleRule1.setSaleLimit(TEST_SALE_LIMIT_1);
        tktSaleRule1.setMinNum(TEST_MIN_NUM_1);
        tktSaleRule1.setMaxNum(TEST_MAX_NUM_1);
        tktSaleRule1.setSaleRegIdcard(TEST_SALE_REG_IDCARD_1);
        tktSaleRule1.setBuyLimitFlag(TEST_BUY_LIMIT_FLAG_1);
        tktSaleRule1.setTimeLimitType(TEST_TIME_LIMIT_TYPE_1);
        tktSaleRule1.setTimeLimitVal(TEST_TIME_LIMIT_VAL_1);
        tktSaleRule1.setLimitAmount(TEST_LIMIT_AMOUNT_1);
        tktSaleRule1.setAgeLimitFlag(TEST_AGE_LIMIT_FLAG_1);
        tktSaleRule1.setCreateBy(TEST_CREATE_BY);
        tktSaleRule1.setModifyBy(TEST_MODIFY_BY);

        // 准备对应的模型对象 - 预售票规则
        tktSaleRule2 = new TktSaleRule();
        tktSaleRule2.setCorpCode(TEST_CORP_CODE);
        tktSaleRule2.setTicketId(TEST_TICKET_ID_2);
        tktSaleRule2.setSaleModel(TEST_SALE_MODEL_2);
        tktSaleRule2.setConsumerFlag(TEST_CONSUMER_FLAG_2);
        tktSaleRule2.setTravelFlag(TEST_TRAVEL_FLAG_2);
        tktSaleRule2.setSeasonTypeOff(TEST_SEASON_TYPE_OFF_2);
        tktSaleRule2.setSeasonTypeBusy(TEST_SEASON_TYPE_BUSY_2);
        tktSaleRule2.setSaleChannel(TEST_SALE_CHANNEL_2);
        tktSaleRule2.setSaleLimit(TEST_SALE_LIMIT_2);
        tktSaleRule2.setMinNum(TEST_MIN_NUM_2);
        tktSaleRule2.setMaxNum(TEST_MAX_NUM_2);
        tktSaleRule2.setSaleRegIdcard(TEST_SALE_REG_IDCARD_2);
        tktSaleRule2.setBuyLimitFlag(TEST_BUY_LIMIT_FLAG_2);
        tktSaleRule2.setTimeLimitType(TEST_TIME_LIMIT_TYPE_2);
        tktSaleRule2.setTimeLimitVal(TEST_TIME_LIMIT_VAL_2);
        tktSaleRule2.setLimitAmount(TEST_LIMIT_AMOUNT_2);
        tktSaleRule2.setAgeLimitFlag(TEST_AGE_LIMIT_FLAG_2);
        tktSaleRule2.setCreateBy(TEST_CREATE_BY);
        tktSaleRule2.setModifyBy(TEST_MODIFY_BY);
    }

    /**
     * 测试根据票型ID查询售票规则 - 正常场景
     */
    @Test
    public void testQuerySaleRuleByTicketId_ShouldReturnTktSaleRule() {
        // 1. 准备测试数据
        when(dao.querySaleRuleByTicketId(TEST_TICKET_ID_1, TEST_CORP_CODE))
                .thenReturn(tktSaleRuleDO1);
        when(converter.r_d2m(tktSaleRuleDO1)).thenReturn(tktSaleRule1);

        // 2. 执行测试
        TktSaleRule result = tktSaleRuleReadComponent.querySaleRuleByTicketId(
                TEST_TICKET_ID_1, TEST_CORP_CODE);

        // 3. 验证结果
        assertNotNull("结果不应为null", result);
        assertEquals("企业编码应该一致", TEST_CORP_CODE, result.getCorpCode());
        assertEquals("票型ID应该一致", TEST_TICKET_ID_1, result.getTicketId());
        assertEquals("销售模式应该一致", TEST_SALE_MODEL_1, result.getSaleModel());
        assertEquals("散客可售标志应该一致", TEST_CONSUMER_FLAG_1, result.getConsumerFlag());
        assertEquals("旅行社可售标志应该一致", TEST_TRAVEL_FLAG_1, result.getTravelFlag());
        assertEquals("淡季可售标志应该一致", TEST_SEASON_TYPE_OFF_1, result.getSeasonTypeOff());
        assertEquals("旺季可售标志应该一致", TEST_SEASON_TYPE_BUSY_1, result.getSeasonTypeBusy());
        assertEquals("销售渠道应该一致", TEST_SALE_CHANNEL_1, result.getSaleChannel());
        assertEquals("是否限制购买数量应该一致", TEST_SALE_LIMIT_1, result.getSaleLimit());
        assertEquals("最少购票数量应该一致", TEST_MIN_NUM_1, result.getMinNum());
        assertEquals("最多购票数量应该一致", TEST_MAX_NUM_1, result.getMaxNum());
        assertEquals("购票登记证件应该一致", TEST_SALE_REG_IDCARD_1, result.getSaleRegIdcard());
        assertEquals("游客购票限制应该一致", TEST_BUY_LIMIT_FLAG_1, result.getBuyLimitFlag());
        assertEquals("限购时间类型应该一致", TEST_TIME_LIMIT_TYPE_1, result.getTimeLimitType());
        assertEquals("时间限制值应该一致", TEST_TIME_LIMIT_VAL_1, result.getTimeLimitVal());
        assertEquals("限购张数应该一致", TEST_LIMIT_AMOUNT_1, result.getLimitAmount());
        assertEquals("游客年龄限制应该一致", TEST_AGE_LIMIT_FLAG_1, result.getAgeLimitFlag());
        assertEquals("创建人应该一致", TEST_CREATE_BY, result.getCreateBy());
        assertEquals("修改人应该一致", TEST_MODIFY_BY, result.getModifyBy());

        // 4. 验证方法调用
        Mockito.verify(dao).querySaleRuleByTicketId(TEST_TICKET_ID_1, TEST_CORP_CODE);
        Mockito.verify(converter).r_d2m(tktSaleRuleDO1);
    }

    /**
     * 测试根据票型ID查询售票规则 - 无结果场景
     */
    @Test
    public void testQuerySaleRuleByTicketId_WhenNoResult_ShouldReturnNull() {
        // 1. 准备测试数据
        when(dao.querySaleRuleByTicketId(TEST_TICKET_ID_1, TEST_CORP_CODE))
                .thenReturn(null);

        // 2. 执行测试
        TktSaleRule result = tktSaleRuleReadComponent.querySaleRuleByTicketId(
                TEST_TICKET_ID_1, TEST_CORP_CODE);

        // 3. 验证结果
        assertNull("结果应该为null", result);

        // 4. 验证方法调用
        Mockito.verify(dao).querySaleRuleByTicketId(TEST_TICKET_ID_1, TEST_CORP_CODE);
        Mockito.verify(converter).r_d2m(null);
    }

    /**
     * 测试批量查询售票规则 - 正常场景
     */
    @Test
    public void testBatchQuerySaleRuleByTicketIds_ShouldReturnList() {
        // 1. 准备测试数据
        Set<Long> ticketIds = new HashSet<>(Arrays.asList(TEST_TICKET_ID_1, TEST_TICKET_ID_2));
        List<TktSaleRuleDO> saleRuleDOList = Arrays.asList(tktSaleRuleDO1, tktSaleRuleDO2);
        List<TktSaleRule> expectedSaleRuleList = Arrays.asList(tktSaleRule1, tktSaleRule2);

        when(dao.batchQuerySaleRuleByTicketIds(ticketIds, TEST_CORP_CODE))
                .thenReturn(saleRuleDOList);
        when(converter.r_ds2ms(saleRuleDOList)).thenReturn(expectedSaleRuleList);

        // 2. 执行测试
        List<TktSaleRule> result = tktSaleRuleReadComponent.batchQuerySaleRuleByTicketIds(
                ticketIds, TEST_CORP_CODE);

        // 3. 验证结果
        assertNotNull("结果不应为null", result);
        assertEquals("结果列表大小应该一致", 2, result.size());
        assertEquals("第一个元素的企业编码应该一致", TEST_CORP_CODE, result.get(0).getCorpCode());
        assertEquals("第一个元素的票型ID应该一致", TEST_TICKET_ID_1, result.get(0).getTicketId());
        assertEquals("第一个元素的销售模式应该一致", TEST_SALE_MODEL_1, result.get(0).getSaleModel());
        assertEquals("第一个元素的散客可售标志应该一致", TEST_CONSUMER_FLAG_1, result.get(0).getConsumerFlag());
        assertEquals("第一个元素的旅行社可售标志应该一致", TEST_TRAVEL_FLAG_1, result.get(0).getTravelFlag());
        assertEquals("第一个元素的淡季可售标志应该一致", TEST_SEASON_TYPE_OFF_1, result.get(0).getSeasonTypeOff());
        assertEquals("第一个元素的旺季可售标志应该一致", TEST_SEASON_TYPE_BUSY_1, result.get(0).getSeasonTypeBusy());
        assertEquals("第一个元素的销售渠道应该一致", TEST_SALE_CHANNEL_1, result.get(0).getSaleChannel());
        assertEquals("第一个元素的是否限制购买数量应该一致", TEST_SALE_LIMIT_1, result.get(0).getSaleLimit());
        assertEquals("第一个元素的最少购票数量应该一致", TEST_MIN_NUM_1, result.get(0).getMinNum());
        assertEquals("第一个元素的最多购票数量应该一致", TEST_MAX_NUM_1, result.get(0).getMaxNum());
        assertEquals("第一个元素的购票登记证件应该一致", TEST_SALE_REG_IDCARD_1, result.get(0).getSaleRegIdcard());
        assertEquals("第一个元素的游客购票限制应该一致", TEST_BUY_LIMIT_FLAG_1, result.get(0).getBuyLimitFlag());
        assertEquals("第一个元素的限购时间类型应该一致", TEST_TIME_LIMIT_TYPE_1, result.get(0).getTimeLimitType());
        assertEquals("第一个元素的时间限制值应该一致", TEST_TIME_LIMIT_VAL_1, result.get(0).getTimeLimitVal());
        assertEquals("第一个元素的限购张数应该一致", TEST_LIMIT_AMOUNT_1, result.get(0).getLimitAmount());
        assertEquals("第一个元素的游客年龄限制应该一致", TEST_AGE_LIMIT_FLAG_1, result.get(0).getAgeLimitFlag());

        assertEquals("第二个元素的票型ID应该一致", TEST_TICKET_ID_2, result.get(1).getTicketId());
        assertEquals("第二个元素的销售模式应该一致", TEST_SALE_MODEL_2, result.get(1).getSaleModel());
        assertEquals("第二个元素的散客可售标志应该一致", TEST_CONSUMER_FLAG_2, result.get(1).getConsumerFlag());
        assertEquals("第二个元素的旅行社可售标志应该一致", TEST_TRAVEL_FLAG_2, result.get(1).getTravelFlag());
        assertEquals("第二个元素的淡季可售标志应该一致", TEST_SEASON_TYPE_OFF_2, result.get(1).getSeasonTypeOff());
        assertEquals("第二个元素的旺季可售标志应该一致", TEST_SEASON_TYPE_BUSY_2, result.get(1).getSeasonTypeBusy());
        assertEquals("第二个元素的销售渠道应该一致", TEST_SALE_CHANNEL_2, result.get(1).getSaleChannel());
        assertEquals("第二个元素的是否限制购买数量应该一致", TEST_SALE_LIMIT_2, result.get(1).getSaleLimit());
        assertEquals("第二个元素的最少购票数量应该一致", TEST_MIN_NUM_2, result.get(1).getMinNum());
        assertEquals("第二个元素的最多购票数量应该一致", TEST_MAX_NUM_2, result.get(1).getMaxNum());
        assertEquals("第二个元素的购票登记证件应该一致", TEST_SALE_REG_IDCARD_2, result.get(1).getSaleRegIdcard());
        assertEquals("第二个元素的游客购票限制应该一致", TEST_BUY_LIMIT_FLAG_2, result.get(1).getBuyLimitFlag());
        assertEquals("第二个元素的限购时间类型应该一致", TEST_TIME_LIMIT_TYPE_2, result.get(1).getTimeLimitType());
        assertEquals("第二个元素的时间限制值应该一致", TEST_TIME_LIMIT_VAL_2, result.get(1).getTimeLimitVal());
        assertEquals("第二个元素的限购张数应该一致", TEST_LIMIT_AMOUNT_2, result.get(1).getLimitAmount());
        assertEquals("第二个元素的游客年龄限制应该一致", TEST_AGE_LIMIT_FLAG_2, result.get(1).getAgeLimitFlag());

        // 4. 验证方法调用
        Mockito.verify(dao).batchQuerySaleRuleByTicketIds(ticketIds, TEST_CORP_CODE);
    }

    /**
     * 测试批量查询售票规则 - 空集合场景
     */
    @Test
    public void testBatchQuerySaleRuleByTicketIds_WhenEmptySet_ShouldReturnEmptyList() {
        // 1. 准备测试数据
        Set<Long> emptyTicketIds = new HashSet<>();
        when(dao.batchQuerySaleRuleByTicketIds(emptyTicketIds, TEST_CORP_CODE))
                .thenReturn(Collections.emptyList());

        // 2. 执行测试
        List<TktSaleRule> result = tktSaleRuleReadComponent.batchQuerySaleRuleByTicketIds(
                emptyTicketIds, TEST_CORP_CODE);

        // 3. 验证结果
        assertNotNull("结果不应为null", result);
        assertTrue("结果应该是空列表", result.isEmpty());

        // 4. 验证方法调用
        Mockito.verify(dao).batchQuerySaleRuleByTicketIds(emptyTicketIds, TEST_CORP_CODE);
    }

    /**
     * 测试批量查询售票规则 - 无结果场景
     */
    @Test
    public void testBatchQuerySaleRuleByTicketIds_WhenNoResult_ShouldReturnEmptyList() {
        // 1. 准备测试数据
        Set<Long> ticketIds = new HashSet<>(Arrays.asList(TEST_TICKET_ID_1, TEST_TICKET_ID_2));
        when(dao.batchQuerySaleRuleByTicketIds(ticketIds, TEST_CORP_CODE))
                .thenReturn(Collections.emptyList());

        // 2. 执行测试
        List<TktSaleRule> result = tktSaleRuleReadComponent.batchQuerySaleRuleByTicketIds(
                ticketIds, TEST_CORP_CODE);

        // 3. 验证结果
        assertNotNull("结果不应为null", result);
        assertTrue("结果应该是空列表", result.isEmpty());

        // 4. 验证方法调用
        Mockito.verify(dao).batchQuerySaleRuleByTicketIds(ticketIds, TEST_CORP_CODE);
    }
} 