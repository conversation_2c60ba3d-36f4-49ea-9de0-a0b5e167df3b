package com.sendinfo.ticketing.verification.basic.repository.pay.dao;

import com.alibaba.nacos.api.exception.NacosException;
import com.sendinfo.ticketing.verification.basic.repository.common.config.SaasPwMybatisConfig;
import com.sendinfo.ticketing.verification.basic.repository.common.sharding.DynamicDataSourceMappingConfigPullStorage;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dao.impl.SysMainbodyDaoImpl;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysMainbodyDO;
import org.junit.Test;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.AbstractTransactionalJUnit4SpringContextTests;

import java.util.List;
import java.util.Random;

import static org.junit.Assert.*;

/**
 * 经营主体数据访问层单元测试
 * <p>
 * 本测试类覆盖了SysMainbodyDao的所有方法，包括：
 * 1. 插入操作测试
 * 2. 批量插入操作测试
 * 3. 根据ID查询测试
 * 4. 条件查询测试
 * 5. 计数查询测试
 * 6. 更新操作测试
 * 7. 软删除操作测试
 * 8. 分页查询测试
 * 9. 自定义查询方法测试
 * <p>
 * 测试目的：验证SysMainbodyDao的所有方法都能正常工作，确保数据访问层的正确性
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootTest(classes = SysMainbodyDaoTest.Config.class)
@EnableAutoConfiguration
@TestPropertySource(locations = "classpath:test-application.properties")
public class SysMainbodyDaoTest extends AbstractTransactionalJUnit4SpringContextTests {

    @Autowired
    private SysMainbodyDao sysMainbodyDao;

    /**
     * 测试用的企业编码
     */
    private static final String TEST_CORP_CODE = "TEST_CORP_001";

    /**
     * 测试用户
     */
    private static final String TEST_USER = "test_user";

    /**
     * 测试配置类
     * 配置MyBatis和DAO Bean
     */
    @Configuration
    @Import(SaasPwMybatisConfig.class)
    static class Config {

        @Bean
        public SysMainbodyDao sysMainbodyDao(@Qualifier(SaasPwMybatisConfig.BEAN_NAME_SQL_SESSION_TEMPLATE) SqlSessionTemplate sqlSessionTemplate) {
            return new SysMainbodyDaoImpl(sqlSessionTemplate);
        }

        @Bean
        DynamicDataSourceMappingConfigPullStorage dynamicDataSourceMappingConfigPullStorage(@Value("${nacos.config.server-addr}") String serverAddr,
                                                                                            @Value("${nacos.config.namespace}") String namespace,
                                                                                            @Value("${verification-platform-basic.nacos.tenant-datasource-mapping.data-id:saas-pw-datasource-sharding-mapping.json}") String dataId,
                                                                                            @Value("${verification-platform-basic.nacos.tenant-datasource-mapping.group:DEFAULT_GROUP}") String group,
                                                                                            @Value("${verification-platform-basic.nacos.tenant-datasource-mapping.pulled.timeout:3000}") Integer timeout) throws NacosException {
            return new DynamicDataSourceMappingConfigPullStorage(serverAddr, namespace, dataId, group, timeout);
        }
    }

    /**
     * 测试插入操作
     * <p>
     * 测试步骤：
     * 1. 创建测试经营主体数据
     * 2. 执行插入操作
     * 3. 验证主键ID被自动生成
     * 4. 通过ID查询验证记录确实被插入
     * 5. 验证插入的数据字段值正确
     */
    @Test
    public void testInsert() {
        // 1. 创建测试经营主体数据
        SysMainbodyDO sysMainbodyDO = createTestDO();

        // 2. 执行插入操作
        sysMainbodyDao.insert(sysMainbodyDO);

        // 3. 验证主键ID被自动生成
        assertNotNull("插入后主键ID应该被自动生成", sysMainbodyDO.getId());

        // 4. 通过ID查询验证记录确实被插入
        SysMainbodyDO insertedMainbody = sysMainbodyDao.queryById(sysMainbodyDO.getId());
        assertNotNull("插入后应能通过ID查询到记录", insertedMainbody);
        assertEquals("经营主体名称应该一致", sysMainbodyDO.getMainbodyName(), insertedMainbody.getMainbodyName());
        assertEquals("企业编码应该一致", TEST_CORP_CODE, insertedMainbody.getCorpCode());
        assertEquals("商户号应该一致", sysMainbodyDO.getMerchantNo(), insertedMainbody.getMerchantNo());
    }

    /**
     * 测试批量插入操作
     * <p>
     * 测试步骤：
     * 1. 创建多个测试经营主体数据
     * 2. 执行批量插入操作
     * 3. 验证所有记录的主键ID都被自动生成
     * 4. 通过ID查询验证所有记录都被正确插入
     */
    @Test
    public void testBatchInsert() {
        // 1. 创建多个测试经营主体数据
        List<SysMainbodyDO> mainbodyList = new java.util.ArrayList<>();
        for (int i = 0; i < 3; i++) {
            SysMainbodyDO sysMainbodyDO = createTestDO();
            mainbodyList.add(sysMainbodyDO);
        }

        // 2. 执行批量插入操作
        sysMainbodyDao.batchInsert(mainbodyList);

        // 3. 查询插入数据
        SysMainbodyQueryArg queryArg = new SysMainbodyQueryArg();
        queryArg.setCorpCode(TEST_CORP_CODE);
        List<SysMainbodyDO> mainbodyDOList = sysMainbodyDao.queryByArg(queryArg);

        // 4. 通过ID查询验证所有记录都被正确插入
        for (SysMainbodyDO sysMainbodyDO : mainbodyDOList) {
            assertNotNull("每个记录都应该生成ID", sysMainbodyDO.getId());
        }
    }

    /**
     * 测试根据ID查询操作
     * <p>
     * 测试步骤：
     * 1. 插入测试记录
     * 2. 使用插入记录的ID进行查询
     * 3. 验证查询结果正确
     */
    @Test
    public void testQueryById() {
        // 1. 插入测试记录
        SysMainbodyDO sysMainbodyDO = createTestDO();
        sysMainbodyDao.insert(sysMainbodyDO);

        // 2. 使用插入记录的ID进行查询
        SysMainbodyDO queriedMainbody = sysMainbodyDao.queryById(sysMainbodyDO.getId());

        // 3. 验证查询结果正确
        assertNotNull("应该能查询到记录", queriedMainbody);
        assertEquals("ID应该一致", sysMainbodyDO.getId(), queriedMainbody.getId());
        assertEquals("经营主体名称应该一致", sysMainbodyDO.getMainbodyName(), queriedMainbody.getMainbodyName());
        assertEquals("企业编码应该一致", TEST_CORP_CODE, queriedMainbody.getCorpCode());
    }

    /**
     * 测试基本的更新功能
     * <p>
     * 目的：验证 updateByArg 方法的基本更新功能
     * 步骤：
     * 1. 插入一条测试记录
     * 2. 创建更新参数，修改部分字段
     * 3. 执行更新操作
     * 4. 查询更新后的记录，验证字段值已正确更新
     */
    @Test
    public void testUpdateByArg() {
        // 1. 插入测试记录
        SysMainbodyDO sysMainbodyDO = createTestDO();
        sysMainbodyDao.insert(sysMainbodyDO);

        // 2. 创建更新参数
        SysMainbodyUpdateArg updateArg = new SysMainbodyUpdateArg();
        updateArg.setId(sysMainbodyDO.getId());
        updateArg.setCorpCode(TEST_CORP_CODE);
        updateArg.setMainbodyName("更新后的经营主体名称");
        updateArg.setLinkMan("李四");
        updateArg.setModifyBy("testUpdater");

        // 3. 执行更新操作
        int updateCount = sysMainbodyDao.updateByArg(updateArg);
        assertEquals("应该更新1条记录", 1, updateCount);

        // 4. 验证更新结果
        SysMainbodyDO updatedMainbody = sysMainbodyDao.queryById(sysMainbodyDO.getId());
        assertEquals("经营主体名称应该被更新", "更新后的经营主体名称", updatedMainbody.getMainbodyName());
        assertEquals("联系人应该被更新", "李四", updatedMainbody.getLinkMan());
    }

    /**
     * 测试分页查询功能
     * <p>
     * 测试步骤：
     * 1. 插入多条测试记录
     * 2. 使用分页参数进行查询
     * 3. 验证分页结果正确
     * 4. 验证计数功能
     */
    @Test
    public void testPageableQuery() {
        // 1. 插入3条测试记录
        for (int i = 1; i <= 3; i++) {
            SysMainbodyDO sysMainbodyDO = createTestDO();
            sysMainbodyDao.insert(sysMainbodyDO);
        }

        // 2. 使用分页参数查询第一页（2条记录）
        SysMainbodyQueryArg queryArg = new SysMainbodyQueryArg();
        queryArg.setCorpCode(TEST_CORP_CODE);
        queryArg.setOffset(0);
        queryArg.setLimit(2);

        List<SysMainbodyDO> firstPageResults = sysMainbodyDao.queryByArg(queryArg);
        assertEquals("第一页应该返回2条记录", 2, firstPageResults.size());

        // 3. 查询第二页（1条记录）
        queryArg.setOffset(2);
        queryArg.setLimit(2);
        List<SysMainbodyDO> secondPageResults = sysMainbodyDao.queryByArg(queryArg);
        assertEquals("第二页应该返回1条记录", 1, secondPageResults.size());

        // 4. 验证计数功能
        queryArg.setOffset(null);
        queryArg.setLimit(null);
        int totalCount = sysMainbodyDao.countByArg(queryArg);
        assertEquals("总计数应该是3", 3, totalCount);
    }

    /**
     * 测试条件查询功能
     * <p>
     * 测试步骤：
     * 1. 插入多个测试记录
     * 2. 创建查询条件
     * 3. 执行条件查询
     * 4. 验证查询结果符合条件
     */
    @Test
    public void testQueryByArg() {
        // 1. 插入多个测试记录
        SysMainbodyDO mainbody1 = createTestDO();
        mainbody1.setMainbodyName("测试经营主体A");
        sysMainbodyDao.insert(mainbody1);

        SysMainbodyDO mainbody2 = createTestDO();
        mainbody2.setMainbodyName("测试经营主体B");
        sysMainbodyDao.insert(mainbody2);

        // 2. 创建查询条件
        SysMainbodyQueryArg queryArg = new SysMainbodyQueryArg();
        queryArg.setCorpCode(TEST_CORP_CODE);
        queryArg.setMainbodyName("测试经营主体A");

        // 3. 执行条件查询
        List<SysMainbodyDO> resultList = sysMainbodyDao.queryByArg(queryArg);

        // 4. 验证查询结果符合条件
        assertNotNull("查询结果不应为null", resultList);
        assertTrue("应该查询到记录", resultList.size() > 0);
        for (SysMainbodyDO mainbody : resultList) {
            assertEquals("企业编码应该一致", TEST_CORP_CODE, mainbody.getCorpCode());
            assertTrue("经营主体名称应该包含查询条件", mainbody.getMainbodyName().contains("测试经营主体A"));
        }
    }

    /**
     * 测试计数查询操作
     * <p>
     * 测试步骤：
     * 1. 插入多个测试记录
     * 2. 创建查询条件
     * 3. 执行计数查询
     * 4. 验证计数结果正确
     */
    @Test
    public void testCountByArg() {
        // 1. 插入多个测试记录
        SysMainbodyDO mainbody1 = createTestDO();
        mainbody1.setMainbodyName("计数测试主体A");
        sysMainbodyDao.insert(mainbody1);

        SysMainbodyDO mainbody2 = createTestDO();
        mainbody2.setMainbodyName("计数测试主体B");
        sysMainbodyDao.insert(mainbody2);

        // 2. 创建查询条件
        SysMainbodyQueryArg queryArg = new SysMainbodyQueryArg();
        queryArg.setCorpCode(TEST_CORP_CODE);
        queryArg.setMainbodyName("计数测试");

        // 3. 执行计数查询
        int count = sysMainbodyDao.countByArg(queryArg);

        // 4. 验证计数结果正确
        assertTrue("计数结果应该大于0", count >= 2);
    }

    /**
     * 测试根据ID删除操作
     * <p>
     * 测试步骤：
     * 1. 插入测试记录
     * 2. 执行根据ID删除操作
     * 3. 验证记录被删除
     */
    @Test
    public void testDeleteById() {
        // 1. 插入测试记录
        SysMainbodyDO sysMainbodyDO = createTestDO();
        sysMainbodyDao.insert(sysMainbodyDO);

        // 2. 执行根据ID删除操作
        sysMainbodyDao.deleteById(sysMainbodyDO.getId());

        // 3. 验证记录被删除
        SysMainbodyDO deletedMainbody = sysMainbodyDao.queryById(sysMainbodyDO.getId());
        assertNull("删除后通过ID查询应该返回null", deletedMainbody);
    }

    /**
     * 创建测试数据对象
     *
     * @return 测试用的SysMainbodyDO对象
     */
    private SysMainbodyDO createTestDO() {
        SysMainbodyDO sysMainbodyDO = new SysMainbodyDO();

        sysMainbodyDO.setCorpCode(TEST_CORP_CODE);
        sysMainbodyDO.setMainbodyName("测试经营主体_" + System.currentTimeMillis() + "_" + new Random().nextInt(1000));
        sysMainbodyDO.setMainbodyShortName("测试主体");
        sysMainbodyDO.setMerchantNo("TEST_MERCHANT_" + System.currentTimeMillis());
        sysMainbodyDO.setLinkMan("张三");
        sysMainbodyDO.setLinkTel("13800138000");
        sysMainbodyDO.setRemark("这是一个测试经营主体");
        sysMainbodyDO.setMainbodyNumber("MB_" + System.currentTimeMillis());
        sysMainbodyDO.setDeptId(1L);
        sysMainbodyDO.setCreateBy(TEST_USER);
        sysMainbodyDO.setModifyBy(TEST_USER);
        sysMainbodyDO.setDeleted("F");

        return sysMainbodyDO;
    }
} 