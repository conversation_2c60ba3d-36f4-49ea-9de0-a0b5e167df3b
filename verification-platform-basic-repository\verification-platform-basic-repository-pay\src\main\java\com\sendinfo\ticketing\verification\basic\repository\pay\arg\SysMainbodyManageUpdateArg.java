package com.sendinfo.ticketing.verification.basic.repository.pay.arg;

import com.sendinfo.ticketing.verification.common.repository.arg.AbstractUpdateArg;
import com.sendinfo.ticketing.verification.common.repository.arg.StatusUpdater;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 经营主体支付配置更新参数
 * 用于封装sys_mainbody_manage表的更新条件和目标字段，支持租户隔离
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class SysMainbodyManageUpdateArg extends AbstractUpdateArg<Long> {
    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 主体商户号
     */
    private String merchantNo;

    /**
     * 支付应用ID(支付中心分配)
     */
    private String merchantSourceNo;

    /**
     * 支付渠道名称
     */
    private String payChannelName;

    /**
     * 支付渠道编号
     */
    private String payChannelCode;

    /**
     * 商户公钥
     */
    private String paycenterPublicKey;

    /**
     * 商户私钥
     */
    private String mchPrivateKey;

    /**
     * 启用状态：T:启用（默认）,F:不启用
     */
    private String useFlag;

    /**
     * 启用状态更新器
     */
    private StatusUpdater<String> useFlagUpdater;

    /**
     * logo
     */
    private String logo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 结算主体商户号
     */
    private String seettlementMerchantNo;

    /**
     * 修改人
     */
    private String modifyBy;
} 