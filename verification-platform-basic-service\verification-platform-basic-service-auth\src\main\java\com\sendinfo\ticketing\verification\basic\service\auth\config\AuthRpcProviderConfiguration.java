package com.sendinfo.ticketing.verification.basic.service.auth.config;

import com.sendinfo.ticketing.verification.basic.api.auth.UserSessionDataReadService;
import org.apache.dubbo.config.ServiceConfig;
import org.apache.dubbo.config.bootstrap.DubboBootstrap;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2025/6/23 15:18
 */
@Configuration("authRpcProviderConfiguration")
public class AuthRpcProviderConfiguration {

    @Bean("userSessionDataReadServiceServiceConfig")
    public ServiceConfig<UserSessionDataReadService> userSessionDataReadServiceServiceConfig(
            DubboBootstrap dubboBootstrap,
            @Qualifier("userSessionDataReadService") UserSessionDataReadService userSessionDataReadService,
            @Value("${verification-platform-basic.rpc.provider.group:}") String group,
            @Value("${verification-platform-basic.rpc.provider.timeout:1000}") int timeout,
            @Value("${verification-platform-basic.rpc.provider.version:1.0.0}") String version) {
        ServiceConfig<UserSessionDataReadService> sc = new ServiceConfig<>();
        sc.setId("userSessionDataReadServiceServiceConfig");
        sc.setInterface(UserSessionDataReadService.class);
        sc.setVersion(version);
        sc.setGroup(group);
        sc.setTimeout(timeout);
        sc.setRef(userSessionDataReadService);
        dubboBootstrap.service(sc);
        return sc;
    }


}
