package com.sendinfo.ticketing.verification.basic.component.goods.param;

import com.sendinfo.ticketing.verification.common.component.param.AbstractQueryParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 门票类型查询参数
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString(callSuper = true)
public class TktTicketTypeQueryParam extends AbstractQueryParam {

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 检票码打印方式 1:现场打印 2:预印刷
     */
    private String printType;

    /**
     * 库存模式 1:明细库存,2:数量库存(只限现场打印)
     */
    private String stoModel;

    /**
     * 门票类型名称
     */
    private String ticketTypeName;

    /**
     * 检票码前缀
     */
    private String ticketTypeCode;

    /**
     * 检票码长度
     */
    private Integer ticketCodeLen;

    /**
     * 迁移数据
     */
    private String transferParam;
} 