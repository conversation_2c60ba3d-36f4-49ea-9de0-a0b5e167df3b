package com.sendinfo.ticketing.verification.basic.component.goods.converter;

import com.sendinfo.ticketing.verification.basic.api.goods.request.ExplainerPayInfoQueryRequest;
import com.sendinfo.ticketing.verification.basic.component.goods.mapper.ExplainerPayInfoMapper;
import com.sendinfo.ticketing.verification.basic.component.goods.param.ExplainerPayInfoQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.ExplainerPayInfo;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.ExplainerPayInfoQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.ExplainerPayInfoDO;
import com.sendinfo.ticketing.verification.common.component.converter.ReadDo2ModelConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadParam2ArgConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadReq2ParamConverter;
import org.springframework.stereotype.Component;

/**
 * 服务类目计费规则转换器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("explainerPayInfoConverter")
public class ExplainerPayInfoConverter implements ReadDo2ModelConverter<ExplainerPayInfoDO, ExplainerPayInfo>,
        ReadParam2ArgConverter<ExplainerPayInfoQueryParam, ExplainerPayInfoQueryArg>,
        ReadReq2ParamConverter<ExplainerPayInfoQueryRequest, ExplainerPayInfoQueryParam> {
    @Override
    public ExplainerPayInfo r_d2m(ExplainerPayInfoDO dataObject) {
        return ExplainerPayInfoMapper.INSTANCE.convert(dataObject);
    }

    @Override
    public ExplainerPayInfoQueryArg r_p2a(ExplainerPayInfoQueryParam param) {
        return ExplainerPayInfoMapper.INSTANCE.convert(param);
    }

    @Override
    public ExplainerPayInfoQueryParam r_r2p(ExplainerPayInfoQueryRequest queryRequest) {
        return ExplainerPayInfoMapper.INSTANCE.convert(queryRequest);
    }
} 