package com.sendinfo.ticketing.verification.basic.component.infra.impl;

import lombok.Getter;
import org.springframework.stereotype.Component;

import com.sendinfo.ticketing.verification.basic.component.infra.SysParamComponentDefinitionManagementComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.converter.SysParamComponentDefinitionConverter;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamComponentDefinitionUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.infra.SysParamComponentDefinition;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamComponentDefinitionUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.SysParamComponentDefinitionDao;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleUpdate;

/**
 * 系统参数组件定义管理组件实现
 */
@Component("sysParamComponentDefinitionManagementComponent")
@Getter
public class SysParamComponentDefinitionManagementComponentImpl
        implements SysParamComponentDefinitionManagementComponent,
        DaoBasedSingleUpdate<SysParamComponentDefinitionUpdateParam, SysParamComponentDefinitionUpdateArg, SysParamComponentDefinition> {

    private final SysParamComponentDefinitionDao dao;
    private final SysParamComponentDefinitionConverter converter;

    public SysParamComponentDefinitionManagementComponentImpl(SysParamComponentDefinitionDao dao, SysParamComponentDefinitionConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }
}
