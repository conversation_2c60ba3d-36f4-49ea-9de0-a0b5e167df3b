package com.sendinfo.ticketing.verification.basic.component.account;

import com.sendinfo.ticketing.verification.basic.component.account.param.CapitalAccountQueryParam;
import com.sendinfo.ticketing.verification.basic.model.account.CapitalAccount;
import com.sendinfo.ticketing.verification.common.component.ReadComponent;

import java.util.List;

/**
 * 资金账户读取组件接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface CapitalAccountReadComponent extends ReadComponent<Long, CapitalAccountQueryParam, CapitalAccount> {

    /**
     * 模糊查询资金账户列表
     *
     * @param queryParam 资金账户查询参数
     * @return 资金账户列表
     */
    List<CapitalAccount> queryAccountList(CapitalAccountQueryParam queryParam);
} 