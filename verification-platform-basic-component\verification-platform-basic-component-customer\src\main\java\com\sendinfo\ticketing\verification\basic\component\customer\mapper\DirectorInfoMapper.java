package com.sendinfo.ticketing.verification.basic.component.customer.mapper;

import com.sendinfo.ticketing.verification.basic.component.customer.param.DirectorInfoCreateParam;
import com.sendinfo.ticketing.verification.basic.component.customer.param.DirectorInfoQueryParam;
import com.sendinfo.ticketing.verification.basic.component.customer.param.DirectorInfoUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.customer.DirectorInfo;
import com.sendinfo.ticketing.verification.basic.repository.customer.arg.DirectorInfoQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.customer.arg.DirectorInfoUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.customer.dataobject.DirectorInfoDO;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 主管信息对象转换映射器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface DirectorInfoMapper {

    DirectorInfoMapper INSTANCE = Mappers.getMapper(DirectorInfoMapper.class);

    /**
     * 创建参数转换为数据对象
     *
     * @param createParam 创建参数
     * @return 数据对象
     */
    DirectorInfoDO convert(DirectorInfoCreateParam createParam);

    /**
     * 查询参数转换为查询参数
     *
     * @param queryParam 查询参数
     * @return 查询参数
     */
    DirectorInfoQueryArg convert(DirectorInfoQueryParam queryParam);

    /**
     * 更新参数转换为更新参数
     *
     * @param updateParam 更新参数
     * @return 更新参数
     */
    DirectorInfoUpdateArg convert(DirectorInfoUpdateParam updateParam);

    /**
     * 数据对象转换为模型对象
     *
     * @param dataObject 数据对象
     * @return 模型对象
     */
    DirectorInfo convert(DirectorInfoDO dataObject);
} 