package com.sendinfo.ticketing.verification.basic.service.auth.jwt.support;

import com.sendinfo.ticketing.verification.basic.exception.VerificationBizRuntimeException;
import com.sendinfo.ticketing.verification.basic.model.auth.error.AuthErrorDef;
import com.sendinfo.ticketing.verification.basic.service.auth.jwt.JwtIdentity;
import com.sendinfo.ticketing.verification.basic.service.auth.jwt.JwtParserFactory;
import com.sendinfo.ticketing.verification.basic.service.auth.jwt.JwtTokenGetterFactory;
import com.sendinfo.ticketing.verification.basic.service.auth.jwt.JwtTokenParseService;
import io.jsonwebtoken.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2025-07-23 14:39:59
 */
@Component("jwtTokenParseServiceSupport")
@Slf4j
public class JwtTokenParseServiceSupport implements JwtTokenParseService {

    private final JwtParserFactory jwtParserFactory;
    private final JwtTokenGetterFactory jwtTokenGetterFactory;

    public JwtTokenParseServiceSupport(JwtParserFactory jwtParserFactory,
                                       JwtTokenGetterFactory jwtTokenGetterFactory) {
        this.jwtParserFactory = jwtParserFactory;
        this.jwtTokenGetterFactory = jwtTokenGetterFactory;
    }

    @Override
    public Pair<String, Claims> getClaimsForJwtToken(Map<String, String> headers, JwtIdentity identity) {
        Optional<String> jwtTokenOpt = jwtTokenGetterFactory.getJwtToken(headers, identity);
        if (jwtTokenOpt.isPresent()) {
            String token = jwtTokenOpt.get();
            JwtParser jwtParser = jwtParserFactory.getParser(identity);
            try {
                Claims claims = jwtParser.parseClaimsJws(token).getBody();
                return Pair.of(token, claims);
            } catch (ExpiredJwtException e) {
                log.warn("JWT validation failed: Token expired at {}. Message: {}", e.getClaims().getExpiration(), e.getMessage());
                throw new VerificationBizRuntimeException(AuthErrorDef.JWT_TOKEN_EXPIRED, e);
            } catch (UnsupportedJwtException e) {
                log.warn("JWT validation failed: Unsupported token format. Message: {}", e.getMessage());
                throw new VerificationBizRuntimeException(AuthErrorDef.JWT_TOKEN_PARSE_ERROR, e);
            } catch (MalformedJwtException e) {
                log.warn("JWT validation failed: Malformed token. Message: {}", e.getMessage());
                throw new VerificationBizRuntimeException(AuthErrorDef.JWT_TOKEN_PARSE_ERROR, e);
            } catch (SignatureException e) {
                log.warn("JWT validation failed: Invalid signature. Message: {}", e.getMessage());
                throw new VerificationBizRuntimeException(AuthErrorDef.JWT_TOKEN_PARSE_ERROR, e);
            } catch (IllegalArgumentException e) {
                log.warn("JWT validation failed: Illegal argument during parsing. Message: {}", e.getMessage());
                throw new VerificationBizRuntimeException(AuthErrorDef.JWT_TOKEN_PARSE_ERROR, e);
            } catch (JwtException e) {
                log.warn("JWT validation failed: General JWT processing error. Message: {}", e.getMessage());
                throw new VerificationBizRuntimeException(AuthErrorDef.JWT_TOKEN_PARSE_ERROR, e);
            } catch (Exception e) {
                log.error("Unexpected error during JWT parsing/validation: {}", e.getMessage(), e);
                throw new VerificationBizRuntimeException(AuthErrorDef.JWT_TOKEN_PARSE_ERROR, e);
            }
        } else {
            throw new VerificationBizRuntimeException(AuthErrorDef.HTTP_HEADER_JWT_TOKEN_NOT_EXIST);
        }
    }
}
