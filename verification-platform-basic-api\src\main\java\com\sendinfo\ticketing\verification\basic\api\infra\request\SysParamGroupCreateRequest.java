package com.sendinfo.ticketing.verification.basic.api.infra.request;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 系统参数分组创建请求
 * 
 * <AUTHOR> 2025-07-22 11:45:32
 */
@Getter
@Setter
@ToString
public class SysParamGroupCreateRequest implements Serializable {
    private static final long serialVersionUID = -7654321098765432102L;

    /**
     * 企业编码
     */
    @NotBlank(message = "企业编码不能为空")
    private String corpCode;

    /**
     * 功能模块编码(菜单编码)
     */
    @NotBlank(message = "功能模块编码不能为空")
    @Size(max = 128, message = "功能模块编码长度不能超过128")
    private String moduleCode;

    /**
     * 分组名称
     */
    @NotBlank(message = "分组名称不能为空")
    @Size(max = 128, message = "分组名称长度不能超过128")
    private String groupName;

    /**
     * 分组描述
     */
    @Size(max = 256, message = "分组描述长度不能超过256")
    private String description;

    /**
     * 排序
     */
    @NotNull(message = "排序不能为空")
    private Integer sortNo;

    /**
     * 模块类型：1表示前端；2表示后台
     */
    @NotNull(message = "模块类型不能为空")
    private Integer moduleType;

    /**
     * 创建人
     */
    @NotBlank(message = "创建人不能为空")
    private String createBy;
}
