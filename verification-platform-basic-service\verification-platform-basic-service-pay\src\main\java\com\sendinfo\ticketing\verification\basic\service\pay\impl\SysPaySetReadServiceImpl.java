package com.sendinfo.ticketing.verification.basic.service.pay.impl;

import com.sendinfo.ticketing.verification.basic.api.pay.request.SysPaySetEnableQueryRequest;
import com.sendinfo.ticketing.verification.basic.api.pay.SysPaySetReadService;
import com.sendinfo.ticketing.verification.basic.exception.TicketPlatformBizRuntimeException;
import com.sendinfo.ticketing.verification.basic.model.pay.SysPaySet;
import com.sendinfo.ticketing.verification.basic.model.pay.error.PayErrorDef;
import com.sendinfo.ticketing.verification.basic.service.pay.enums.PayAttachmentKey;
import com.sendinfo.ticketing.verification.basic.service.pay.function.QuerySysPaySetFunction;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;
import com.sendinfo.ticketing.verification.common.api.result.Results;
import com.sendinfo.ticketing.verification.common.service.support.ApiCommonFailedBiActions;
import com.sendinfo.ticketing.verification.flow.FlowAgentBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Auther: chengjie.li
 * @Date: 2025/7/25 11:35
 */
@Slf4j
@Service("sysPaySetReadService")
public class SysPaySetReadServiceImpl implements SysPaySetReadService {
    private final FlowAgentBuilder flowAgentBuilder;

    private final QuerySysPaySetFunction querySysPaySetFunction;

    public SysPaySetReadServiceImpl(FlowAgentBuilder flowAgentBuilder,
                                    QuerySysPaySetFunction querySysPaySetFunction) {
        this.flowAgentBuilder = flowAgentBuilder;
        this.querySysPaySetFunction = querySysPaySetFunction;
    }

    @Override
    public ResultModel<List<SysPaySet>> queryEnableListByPayTypeAndId(SysPaySetEnableQueryRequest request) {
        return flowAgentBuilder.<SysPaySetEnableQueryRequest, ResultModel<List<SysPaySet>>>validateThenChooseBuilder()
                .appendLogicAction(querySysPaySetFunction::queryEnableListByPayTypeAndId)
                .withSuccessfulAction(q -> {
                    List<SysPaySet> sysPaySetList = q.getAttachment(PayAttachmentKey.SYS_PAY_SET_DATA_LIST_ATTACHMENT_KEY);
                    return Results.success(sysPaySetList);
                })
                .withFailedBiAction(ApiCommonFailedBiActions::reasonMappingToResult)
                .withExceptionBiAction((q, th) -> {
                    log.error("[SysPaySetReadServiceImpl] queryEnableList error.question is {}", q, th);
                    return Results.fail(PayErrorDef.QUERY_SYS_PAY_SET_ERROR);
                })
                .rethrowException(TicketPlatformBizRuntimeException.class::isInstance)
                .build()
                .prompt(request)
                .getResult();
    }
}
