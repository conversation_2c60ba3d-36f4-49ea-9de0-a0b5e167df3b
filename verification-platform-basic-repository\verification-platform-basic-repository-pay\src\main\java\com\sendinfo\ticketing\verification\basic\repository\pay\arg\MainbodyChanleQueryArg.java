package com.sendinfo.ticketing.verification.basic.repository.pay.arg;

import com.sendinfo.ticketing.verification.common.repository.arg.AbstractQueryArg;
import com.sendinfo.ticketing.verification.common.repository.arg.Pageable;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 商户渠道配置查询参数
 * 用于封装mainbody_chanle表的查询条件，支持分页、排序、租户隔离
 * 包含所有可查询字段和分页参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class MainbodyChanleQueryArg extends AbstractQueryArg implements Pageable {
    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 支付渠道ID
     */
    private Long payChanleId;

    /**
     * 主体ID
     */
    private Long mainbodyId;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 分页偏移量
     */
    private Integer offset;

    /**
     * 分页大小
     */
    private Integer limit;
} 