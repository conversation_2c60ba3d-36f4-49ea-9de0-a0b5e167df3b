<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sendinfo.ticketing.verification.basic.repository.pay.dao.TktSellerPayawayDao">

    <resultMap id="BaseResultMap" type="com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.TktSellerPayawayDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="CORP_CODE" property="corpCode" jdbcType="VARCHAR"/>
        <result column="SELLER" property="seller" jdbcType="VARCHAR"/>
        <result column="ACC_ID" property="accId" jdbcType="BIGINT"/>
        <result column="SALE_MODEL" property="saleModel" jdbcType="INTEGER"/>
        <result column="CLIENT_TYPE" property="clientType" jdbcType="INTEGER"/>
        <result column="PAY_AWAY" property="payAway" jdbcType="VARCHAR"/>
        <result column="SORT" property="sort" jdbcType="INTEGER"/>
        <result column="USE_FLAG" property="useFlag" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="CREATE_BY" property="createBy" jdbcType="VARCHAR"/>
        <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="MODIFY_BY" property="modifyBy" jdbcType="VARCHAR"/>
        <result column="DELETED" property="deleted" jdbcType="VARCHAR"/>
        <result column="PAY_ID" property="payId" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="tableName">tkt_seller_payaway</sql>

    <sql id="allColumns">
        id, CORP_CODE, SELLER, ACC_ID, SALE_MODEL, CLIENT_TYPE, PAY_AWAY, SORT, USE_FLAG,
        CREATE_TIME, CREATE_BY, MODIFY_TIME, MODIFY_BY, DELETED, PAY_ID
    </sql>

    <sql id="insertColumns">
        CORP_CODE, SELLER, ACC_ID, SALE_MODEL, CLIENT_TYPE, PAY_AWAY, SORT, USE_FLAG,
        CREATE_BY, MODIFY_BY, DELETED, PAY_ID, CREATE_TIME, MODIFY_TIME
    </sql>

    <insert id="insert" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.TktSellerPayawayDO" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO <include refid="tableName"/> (<include refid="insertColumns"/>)
        VALUES (
            #{corpCode,jdbcType=VARCHAR}, #{seller,jdbcType=VARCHAR}, #{accId,jdbcType=BIGINT},
            #{saleModel,jdbcType=INTEGER}, #{clientType,jdbcType=INTEGER}, #{payAway,jdbcType=VARCHAR},
            #{sort,jdbcType=INTEGER}, #{useFlag,jdbcType=VARCHAR},
            #{createBy,jdbcType=VARCHAR}, #{modifyBy,jdbcType=VARCHAR}, #{deleted,jdbcType=VARCHAR},
            #{payId,jdbcType=BIGINT}, NOW(), NOW()
        )
    </insert>

    <select id="queryById" resultMap="BaseResultMap" parameterType="map">
        SELECT <include refid="allColumns"/>
        FROM <include refid="tableName"/>
        WHERE id = #{id,jdbcType=BIGINT}
          AND deleted = 'F'
    </select>

    <select id="queryByArg" resultMap="BaseResultMap" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.arg.TktSellerPayawayQueryArg">
        SELECT <include refid="allColumns"/>
        FROM <include refid="tableName"/>
        WHERE deleted = 'F'
        <if test="corpCode != null and corpCode != ''">
            AND CORP_CODE = #{corpCode,jdbcType=VARCHAR}
        </if>
        <if test="seller != null and seller != ''">
            AND SELLER = #{seller,jdbcType=VARCHAR}
        </if>
        <if test="accId != null">
            AND ACC_ID = #{accId,jdbcType=BIGINT}
        </if>
        <if test="saleModel != null">
            AND SALE_MODEL = #{saleModel,jdbcType=INTEGER}
        </if>
        <if test="clientType != null">
            AND CLIENT_TYPE = #{clientType,jdbcType=INTEGER}
        </if>
        <if test="payAway != null and payAway != ''">
            AND PAY_AWAY = #{payAway,jdbcType=VARCHAR}
        </if>
        <if test="useFlag != null and useFlag != ''">
            AND USE_FLAG = #{useFlag,jdbcType=VARCHAR}
        </if>
        <if test="payId != null">
            AND PAY_ID = #{payId,jdbcType=BIGINT}
        </if>
        ORDER BY id DESC
    </select>

    <select id="countByArg" resultType="int" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.arg.TktSellerPayawayQueryArg">
        SELECT COUNT(*)
        FROM <include refid="tableName"/>
        WHERE deleted = 'F'
        <if test="corpCode != null and corpCode != ''">
            AND CORP_CODE = #{corpCode,jdbcType=VARCHAR}
        </if>
        <if test="seller != null and seller != ''">
            AND SELLER = #{seller,jdbcType=VARCHAR}
        </if>
        <if test="accId != null">
            AND ACC_ID = #{accId,jdbcType=BIGINT}
        </if>
        <if test="saleModel != null">
            AND SALE_MODEL = #{saleModel,jdbcType=INTEGER}
        </if>
        <if test="clientType != null">
            AND CLIENT_TYPE = #{clientType,jdbcType=INTEGER}
        </if>
        <if test="payAway != null and payAway != ''">
            AND PAY_AWAY = #{payAway,jdbcType=VARCHAR}
        </if>
        <if test="useFlag != null and useFlag != ''">
            AND USE_FLAG = #{useFlag,jdbcType=VARCHAR}
        </if>
        <if test="payId != null">
            AND PAY_ID = #{payId,jdbcType=BIGINT}
        </if>
    </select>

    <update id="updateByArg" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.arg.TktSellerPayawayUpdateArg">
        UPDATE <include refid="tableName"/>
        <set>
            <if test="seller != null and seller != ''">SELLER = #{seller,jdbcType=VARCHAR},</if>
            <if test="accId != null">ACC_ID = #{accId,jdbcType=BIGINT},</if>
            <if test="saleModel != null">SALE_MODEL = #{saleModel,jdbcType=INTEGER},</if>
            <if test="clientType != null">CLIENT_TYPE = #{clientType,jdbcType=INTEGER},</if>
            <if test="payAway != null and payAway != ''">PAY_AWAY = #{payAway,jdbcType=VARCHAR},</if>
            <if test="sort != null">SORT = #{sort,jdbcType=INTEGER},</if>
            <if test="useFlag != null">USE_FLAG = #{useFlag,jdbcType=VARCHAR},</if>
            <if test="useFlagUpdater != null and useFlagUpdater.target != null">USE_FLAG = #{useFlagUpdater.target,jdbcType=VARCHAR},</if>
            <if test="payId != null">PAY_ID = #{payId,jdbcType=BIGINT},</if>
            <if test="modifyBy != null and modifyBy != ''">MODIFY_BY = #{modifyBy,jdbcType=VARCHAR},</if>
            MODIFY_TIME = NOW()
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
          AND CORP_CODE = #{corpCode,jdbcType=VARCHAR}
          AND deleted = 'F'
            <if test="useFlagUpdater != null and useFlagUpdater.current != null">
                AND USE_FLAG = #{useFlagUpdater.current,jdbcType=VARCHAR}
            </if>
    </update>

    <update id="softDeleteByArg" parameterType="com.sendinfo.ticketing.verification.basic.repository.pay.arg.TktSellerPayawayDeleteArg">
        UPDATE <include refid="tableName"/>
        SET deleted = 'T',
            MODIFY_BY = #{modifyBy,jdbcType=VARCHAR},
            MODIFY_TIME = NOW()
        WHERE id = #{id,jdbcType=BIGINT}
          AND deleted = 'F'
    </update>

    <delete id="deleteById" parameterType="map">
        DELETE FROM <include refid="tableName"/>
        WHERE id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO <include refid="tableName"/> (<include refid="insertColumns"/>)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.corpCode,jdbcType=VARCHAR}, #{item.seller,jdbcType=VARCHAR}, #{item.accId,jdbcType=BIGINT},
                #{item.saleModel,jdbcType=INTEGER}, #{item.clientType,jdbcType=INTEGER}, #{item.payAway,jdbcType=VARCHAR},
                #{item.sort,jdbcType=INTEGER}, #{item.useFlag,jdbcType=VARCHAR},
                #{item.createBy,jdbcType=VARCHAR}, #{item.modifyBy,jdbcType=VARCHAR}, #{item.deleted,jdbcType=VARCHAR},
                #{item.payId,jdbcType=BIGINT}, NOW(), NOW()
            )
        </foreach>
    </insert>

    <select id="queryByAccId" resultMap="BaseResultMap" parameterType="map">
        SELECT <include refid="allColumns"/>
        FROM <include refid="tableName"/>
        WHERE deleted = 'F'
          AND CORP_CODE = #{corpCode,jdbcType=VARCHAR}
          AND ACC_ID = #{accId,jdbcType=BIGINT}
        ORDER BY SORT ASC, id DESC
    </select>
    <select id="queryEnableSellerPayAwayList"
            resultType="com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.TktSellerPayawayDO">
        SELECT <include refid="allColumns"/>
        FROM <include refid="tableName"/>
        WHERE deleted = 'F'
        AND USE_FLAG = 'T'
        AND CORP_CODE = #{corpCode,jdbcType=VARCHAR}
        AND ACC_ID = #{accId,jdbcType=BIGINT}
        AND CLIENT_TYPE = #{clientType,jdbcType=INTEGER}
        AND SALE_MODEL = #{saleModel,jdbcType=INTEGER}
    </select>

</mapper> 