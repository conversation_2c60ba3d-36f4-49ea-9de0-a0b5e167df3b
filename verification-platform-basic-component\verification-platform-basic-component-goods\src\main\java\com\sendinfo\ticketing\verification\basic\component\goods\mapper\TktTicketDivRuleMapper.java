package com.sendinfo.ticketing.verification.basic.component.goods.mapper;

import com.sendinfo.ticketing.verification.basic.component.goods.param.TktTicketDivRuleQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TktTicketDivRule;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TktTicketDivRuleQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TktTicketDivRuleDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Auther: chengjie.li
 * @Date: 2025/6/12 13:46
 */
@Mapper
public interface TktTicketDivRuleMapper {

    TktTicketDivRuleMapper INSTANCE = Mappers.getMapper(TktTicketDivRuleMapper.class);

    TktTicketDivRule convert(TktTicketDivRuleDO dataObject);

    TktTicketDivRuleQueryArg convert(TktTicketDivRuleQueryParam param);

}
