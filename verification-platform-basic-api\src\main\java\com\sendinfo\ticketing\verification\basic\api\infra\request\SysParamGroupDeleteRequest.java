package com.sendinfo.ticketing.verification.basic.api.infra.request;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 系统参数分组删除请求
 * 
 * <AUTHOR> 2025-07-22 11:45:32
 */
@Getter
@Setter
@ToString
public class SysParamGroupDeleteRequest implements Serializable {
    private static final long serialVersionUID = -7654321098765432105L;

    /**
     * 参数分组ID
     */
    @NotNull(message = "参数分组ID不能为空")
    private Long id;

    /**
     * 企业编码
     */
    @NotBlank(message = "企业编码不能为空")
    private String corpCode;

    /**
     * 修改人
     */
    private String modifyBy;
}
