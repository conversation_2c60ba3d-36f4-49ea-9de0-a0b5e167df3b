package com.sendinfo.ticketing.verification.basic.component.goods;

import com.sendinfo.ticketing.verification.basic.component.goods.param.TktSaleRuleQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TktSaleRule;
import com.sendinfo.ticketing.verification.common.component.ReadComponent;

import java.util.List;
import java.util.Set;

/**
 * @Auther: chengjie.li
 * @Date: 2025/6/11 19:21
 */
public interface TktSaleRuleReadComponent extends ReadComponent<Long, TktSaleRuleQueryParam, TktSaleRule> {
    /**
     * 根据企业编码和票型ID查询售票规则
     *
     * @param ticketId 票型ID
     * @param corpCode 企业编码
     * @return 售票规则
     */
    TktSaleRule querySaleRuleByTicketId(Long ticketId, String corpCode);

    /**
     * 根据票型ID集合查询售票规则
     *
     * @param ticketIds 票型ID集合
     * @param corpCode  企业编码
     * @return 售票规则列表
     */
    List<TktSaleRule> batchQuerySaleRuleByTicketIds(Set<Long> ticketIds, String corpCode);
}
