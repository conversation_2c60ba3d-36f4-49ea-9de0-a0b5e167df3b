package com.sendinfo.ticketing.verification.basic.api.park;

import com.sendinfo.ticketing.verification.basic.api.park.request.TktGateGroupQueryCondition;
import com.sendinfo.ticketing.verification.basic.model.park.TktGateGroup;
import com.sendinfo.ticketing.verification.common.api.request.PageRequest;
import com.sendinfo.ticketing.verification.common.api.result.PageResultModel;

public interface TktGateGroupReadService {
    /**
     * 查询检票点组
     * @param pageRequest 分页请求
     * @return 检票点组列表
     */
    PageResultModel<TktGateGroup> queryTktGateGroup(PageRequest<TktGateGroupQueryCondition> pageRequest);
}
