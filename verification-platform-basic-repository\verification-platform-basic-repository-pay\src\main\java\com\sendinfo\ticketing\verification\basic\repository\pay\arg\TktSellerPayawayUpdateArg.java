package com.sendinfo.ticketing.verification.basic.repository.pay.arg;

import com.sendinfo.ticketing.verification.common.repository.arg.AbstractUpdateArg;
import com.sendinfo.ticketing.verification.common.repository.arg.StatusUpdater;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 售票员收款方式更新参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class TktSellerPayawayUpdateArg extends AbstractUpdateArg<Long> {
    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 售票员
     */
    private String seller;

    /**
     * 账户ID
     */
    private Long accId;

    /**
     * 售票模式：1:正常出票 2:预售票 3:电子商务票 4:手工票补录 5:剧院售票6：自助机7:扫码入园
     */
    private Integer saleModel;

    /**
     * 客户类型（1：散客 2：团队...）
     */
    private Integer clientType;

    /**
     * 支付方式
     */
    private String payAway;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 启用状态：T:启用（默认）,F:不启用
     */
    private String useFlag;

    /**
     * 启用状态更新器
     */
    private StatusUpdater<String> useFlagUpdater;

    /**
     * 支付ID
     */
    private Long payId;

    /**
     * 修改人
     */
    private String modifyBy;
} 