package com.sendinfo.ticketing.verification.basic.service.pay.function;

import com.sendinfo.ticketing.verification.basic.api.pay.request.TktMainbodyBusQueryRequest;
import com.sendinfo.ticketing.verification.basic.component.pay.TktMainbodyBusReadComponent;
import com.sendinfo.ticketing.verification.basic.component.pay.converter.TktMainbodyBusConverter;
import com.sendinfo.ticketing.verification.basic.service.pay.enums.TktMainbodyBusAttachmentKey;
import com.sendinfo.ticketing.verification.common.service.function.QueryListByRequestFunction;
import com.sendinfo.ticketing.verification.common.service.stereotype.Function;
import com.sendinfo.ticketing.verification.common.service.support.QueryListByRequestLogicAction;
import com.sendinfo.ticketing.verification.flow.function.LogicAction;
import lombok.Getter;

/**
 * @Auther: chengjie.li
 * @Date: 2025/6/13 14:52
 */
@Getter
@Function("queryTktMainbodyBusListFunction")
public class QueryTktMainbodyBusListFunction implements QueryListByRequestFunction<TktMainbodyBusQueryRequest> {

    private final TktMainbodyBusReadComponent tktMainbodyBusReadComponent;

    private final TktMainbodyBusConverter converter;

    private final LogicAction<TktMainbodyBusQueryRequest> queryListByRequest;

    public QueryTktMainbodyBusListFunction(TktMainbodyBusReadComponent tktMainbodyBusReadComponent, TktMainbodyBusConverter converter) {
        this.tktMainbodyBusReadComponent = tktMainbodyBusReadComponent;
        this.converter = converter;
        this.queryListByRequest = new QueryListByRequestLogicAction<>(tktMainbodyBusReadComponent,
                this.converter::r_r2p, TktMainbodyBusAttachmentKey.TKT_MAINBODY_BUS_DATA_LIST_ATTACHMENT_KEY);

    }


}
