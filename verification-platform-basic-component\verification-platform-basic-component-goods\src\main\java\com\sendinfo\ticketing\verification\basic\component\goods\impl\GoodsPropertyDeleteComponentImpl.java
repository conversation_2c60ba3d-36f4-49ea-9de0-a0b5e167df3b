package com.sendinfo.ticketing.verification.basic.component.goods.impl;

import com.sendinfo.ticketing.verification.basic.component.goods.GoodsPropertyDeleteComponent;
import com.sendinfo.ticketing.verification.basic.component.goods.converter.GoodsPropertyConverter;
import com.sendinfo.ticketing.verification.basic.component.goods.param.GoodsPropertyDeleteParam;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.GoodsPropertyDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dao.GoodsPropertyDao;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleDelete;
import lombok.Getter;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025-07-18 17:34
 */
@Getter
@Component("goodsPropertyDeleteComponent")
public class GoodsPropertyDeleteComponentImpl implements GoodsPropertyDeleteComponent,
        DaoBasedSingleDelete<Long, GoodsPropertyDeleteParam, GoodsPropertyDeleteArg> {

    private final GoodsPropertyDao dao;
    private final GoodsPropertyConverter converter;

    public GoodsPropertyDeleteComponentImpl(GoodsPropertyDao dao
            , GoodsPropertyConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }
}
