package com.sendinfo.ticketing.verification.basic.component.goods.param;

import com.sendinfo.ticketing.verification.common.component.param.AbstractUpdateParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2025-07-24 10:41
 */
@Getter
@Setter
@ToString(callSuper = true)
public class UserTicketRelationUpdateParam extends AbstractUpdateParam<Long> {

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 操作人
     */
    private String modifyBy;
}
