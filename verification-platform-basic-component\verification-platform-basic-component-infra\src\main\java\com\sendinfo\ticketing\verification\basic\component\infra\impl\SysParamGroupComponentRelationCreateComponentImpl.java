/**
 * System parameter group component relation create component implementation
 *
 * <AUTHOR> 2025-01-24 15:30:00
 */
package com.sendinfo.ticketing.verification.basic.component.infra.impl;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.List;

import com.sendinfo.ticketing.verification.basic.component.infra.SysParamGroupComponentRelationCreateComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.SysParamValueCreateComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.converter.SysParamGroupComponentRelationConverter;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamGroupComponentRelationCreateParam;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamValueCreateParam;
import com.sendinfo.ticketing.verification.basic.exception.VerificationBizRuntimeException;
import com.sendinfo.ticketing.verification.basic.model.infra.error.InfraErrorDef;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.SysParamGroupComponentRelationDao;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.SysParamGroupComponentRelationDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedBatchCreate;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleCreate;

/**
 * 参数分组组件关联创建组件实现类，负责创建参数分组组件关联的业务逻辑
 *
 * <AUTHOR> 2025-01-24 15:30:00
 */
@Component("sysParamGroupComponentRelationCreateComponent")
@Getter
public class SysParamGroupComponentRelationCreateComponentImpl
        implements SysParamGroupComponentRelationCreateComponent,
        DaoBasedSingleCreate<Long, SysParamGroupComponentRelationCreateParam, SysParamGroupComponentRelationDO>,
        DaoBasedBatchCreate<Long, SysParamGroupComponentRelationCreateParam, SysParamGroupComponentRelationDO> {

    private final SysParamGroupComponentRelationDao dao;
    private final SysParamGroupComponentRelationConverter converter;
    private final TransactionTemplate transactionTemplate;
    private final SysParamValueCreateComponent sysParamValueCreateComponent;

    public SysParamGroupComponentRelationCreateComponentImpl(SysParamGroupComponentRelationDao dao,
                                                             SysParamGroupComponentRelationConverter converter,
                                                             @Qualifier("ticketInfraTransactionTemplate") TransactionTemplate transactionTemplate,
                                                             SysParamValueCreateComponent sysParamValueCreateComponent) {
        this.dao = dao;
        this.converter = converter;
        this.transactionTemplate = transactionTemplate;
        this.sysParamValueCreateComponent = sysParamValueCreateComponent;
    }

    @Override
    public List<Long> batchCreateRelationAndSysParamDefaultValue(List<SysParamGroupComponentRelationCreateParam> relationCreateParams, List<SysParamValueCreateParam> sysParamValueCreateParams) {
        return transactionTemplate.execute(status -> {
            List<Long> relationIds = this.batchCreate(relationCreateParams);
            List<Long> sysParamValueIds = sysParamValueCreateComponent.batchCreate(sysParamValueCreateParams);
            if (relationIds.size() != sysParamValueIds.size()) {
                throw new VerificationBizRuntimeException(InfraErrorDef.CREATE_SYSPARAM_VALUE_ERROR);
            }
            return relationIds;
        });
    }
}