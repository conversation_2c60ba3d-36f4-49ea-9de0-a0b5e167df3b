package com.sendinfo.ticketing.verification.basic.component.goods.mapper;

import com.sendinfo.ticketing.verification.basic.api.goods.request.TktRefundRuleQueryRequest;
import com.sendinfo.ticketing.verification.basic.component.goods.param.TktRefundRuleQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TktRefundRule;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TktRefundRuleQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TktRefundRuleDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface TktRefundRuleMapper {

    TktRefundRuleMapper INSTANCE = Mappers.getMapper(TktRefundRuleMapper.class);

    /**
     * convert TktRefundRuleDO -> TktRefundRule
     *
     * @param dataObject
     * @return  TktRefundRule
     */
    TktRefundRule convert(TktRefundRuleDO dataObject);

    /**
     * convert TktRefundRuleQueryParam -> TktRefundRuleQueryArg
     *
     * @param param
     * @return  TktRefundRuleQueryArg
     */
    TktRefundRuleQueryArg convert(TktRefundRuleQueryParam param);

    /**
     * convert TktRefundRuleQueryRequest -> TktRefundRuleQueryParam
     *
     * @param request
     * @return  TktRefundRuleQueryParam
     */
    TktRefundRuleQueryParam convert(TktRefundRuleQueryRequest request);
} 