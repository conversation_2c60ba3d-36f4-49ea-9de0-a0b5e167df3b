package com.sendinfo.ticketing.verification.basic.repository.pay.arg;

import com.sendinfo.ticketing.verification.common.repository.arg.AbstractUpdateArg;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 经营主体子商户更新参数
 * 用于封装sys_mainbody_manage_sub_merchants表的更新条件和目标字段，支持租户隔离
 * 包含所有可更新字段和状态安全更新器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class SysMainbodyManageSubMerchantsUpdateArg extends AbstractUpdateArg<Long> {
    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 子商户名称
     */
    private String subMerchantsName;

    /**
     * 子商户号
     */
    private String merchantNo;

    /**
     * 修改人
     */
    private String modifyBy;
} 