package com.sendinfo.ticketing.verification.basic.api.config.result;

import com.sendinfo.ticketing.verification.basic.model.config.TenantTesbConfig;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/2 17:09
 **/
@Getter
@Setter
@ToString
public class TenantTesbConfigResult extends TenantTesbConfig implements Serializable {
    private static final long serialVersionUID = -2644752331498345082L;
    /**
     * 子节点
     */
    private List<TenantTesbConfig> children;
}
