package com.sendinfo.ticketing.verification.basic.model.pay.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 业务类型枚举
 * 0=门票, 1=剧院, 2=滑雪场, 4=一卡通, 6=租赁, 13=存包柜
 */
@Getter
public enum BusinessTypeEnum {
    TICKET(0, "门票"),
    THEATER(1, "剧院"),
    SKI(2, "滑雪场"),
    CARD(4, "一卡通"),
    RENT(6, "租赁"),
    LOCKER(13, "存包柜");

    @JsonValue
    private final int code;
    private final String description;

    BusinessTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    private static final Map<Integer, BusinessTypeEnum> CODE_MAP =
            Arrays.stream(values()).collect(Collectors.toMap(BusinessTypeEnum::getCode, Function.identity()));

    @JsonCreator
    public static BusinessTypeEnum ofCode(Integer code) {
        return code == null ? null : CODE_MAP.get(code);
    }
} 