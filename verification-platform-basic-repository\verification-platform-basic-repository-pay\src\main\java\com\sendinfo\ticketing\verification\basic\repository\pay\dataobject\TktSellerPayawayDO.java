package com.sendinfo.ticketing.verification.basic.repository.pay.dataobject;

import com.sendinfo.ticketing.verification.common.repository.dataobject.AbstractTenantBaseDO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 售票员收款方式数据对象
 * 对应数据库表 tkt_seller_payaway
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class TktSellerPayawayDO extends AbstractTenantBaseDO<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 售票员
     */
    private String seller;

    /**
     * 账户ID
     */
    private Long accId;

    /**
     * 售票模式：1:正常出票 2:预售票 3:电子商务票 4:手工票补录 5:剧院售票6：自助机7:扫码入园
     */
    private Integer saleModel;

    /**
     * 客户类型（1：散客 2：团队...）
     */
    private Integer clientType;

    /**
     * 支付方式
     */
    private String payAway;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 启用状态：T:启用（默认）,F:不启用
     */
    private String useFlag;

    /**
     * 支付ID
     */
    private Long payId;
} 