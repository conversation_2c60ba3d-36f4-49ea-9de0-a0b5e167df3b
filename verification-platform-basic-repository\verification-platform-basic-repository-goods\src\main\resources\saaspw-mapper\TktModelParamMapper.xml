<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sendinfo.ticketing.verification.basic.repository.goods.dao.TktModelParamDao">

    <resultMap id="BaseResultMap"
               type="com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TktModelParamDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="corp_code" property="corpCode" jdbcType="VARCHAR"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="model_type" property="modelType" jdbcType="CHAR"/>
        <result column="model_kind" property="modelKind" jdbcType="INTEGER"/>
        <result column="back_img" property="backImg" jdbcType="VARCHAR"/>
        <result column="page_content" property="pageContent" jdbcType="LONGVARCHAR"/>
        <result column="hn_print_content" property="hnPrintContent" jdbcType="LONGVARCHAR"/>
        <result column="print_content" property="printContent" jdbcType="LONGVARCHAR"/>
        <result column="width" property="width" jdbcType="INTEGER"/>
        <result column="height" property="height" jdbcType="INTEGER"/>
        <result column="print_direction" property="printDirection" jdbcType="INTEGER"/>
        <result column="page_info" property="pageInfo" jdbcType="LONGVARCHAR"/>
        <result column="use_flag" property="useFlag" jdbcType="CHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="modify_by" property="modifyBy" jdbcType="VARCHAR"/>
        <result column="deleted" property="deleted" jdbcType="CHAR"/>
        <result column="role_apply" property="roleApply" jdbcType="INTEGER"/>
        <result column="model_code" property="modelCode" jdbcType="VARCHAR"/>
        <result column="continuous_print" property="continuousPrint" jdbcType="CHAR"/>
        <result column="transfer_param" property="transferParam" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="tableName">tkt_model_param</sql>
    
    <sql id="allColumns">
        id, corp_code, title, model_type, model_kind, back_img, page_content, 
        hn_print_content, print_content, width, height, print_direction, 
        page_info, use_flag, create_time, create_by, modify_time, modify_by, 
        deleted, role_apply, model_code, continuous_print, transfer_param
    </sql>
    
    <sql id="insertColumns">
        id, corp_code, title, model_type, model_kind, back_img, page_content,
        hn_print_content, print_content, width, height, print_direction, 
        page_info, use_flag, create_by, modify_by, deleted, role_apply, 
        model_code, continuous_print, transfer_param, create_time, modify_time
    </sql>

    <!-- 基础CRUD方法 -->
    <insert id="insert" parameterType="com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TktModelParamDO"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="tableName"/> (<include refid="insertColumns"/>)
        VALUES (
            #{id}, #{corpCode}, #{title}, #{modelType}, #{modelKind}, #{backImg}, #{pageContent},
            #{hnPrintContent}, #{printContent}, #{width}, #{height}, #{printDirection},
            #{pageInfo}, #{useFlag}, #{createBy}, #{modifyBy}, #{deleted}, #{roleApply},
            #{modelCode}, #{continuousPrint}, #{transferParam}, NOW(), NOW()
        )
    </insert>

    <select id="queryById" resultMap="BaseResultMap" parameterType="map">
        SELECT <include refid="allColumns"/>
        FROM <include refid="tableName"/>
        WHERE id = #{id}
          AND deleted = 'F'
    </select>

    <update id="updateByArg" parameterType="com.sendinfo.ticketing.verification.basic.repository.goods.arg.TktModelParamUpdateArg">
        UPDATE <include refid="tableName"/>
        <set>
            <if test="title != null">title = #{title},</if>
            <if test="modelType != null">model_type = #{modelType},</if>
            <if test="modelKind != null">model_kind = #{modelKind},</if>
            <if test="backImg != null">back_img = #{backImg},</if>
            <if test="pageContent != null">page_content = #{pageContent},</if>
            <if test="hnPrintContent != null">hn_print_content = #{hnPrintContent},</if>
            <if test="printContent != null">print_content = #{printContent},</if>
            <if test="width != null">width = #{width},</if>
            <if test="height != null">height = #{height},</if>
            <if test="printDirection != null">print_direction = #{printDirection},</if>
            <if test="pageInfo != null">page_info = #{pageInfo},</if>
            <if test="useFlag != null">use_flag = #{useFlag},</if>
            <if test="roleApply != null">role_apply = #{roleApply},</if>
            <if test="modelCode != null">model_code = #{modelCode},</if>
            <if test="continuousPrint != null">continuous_print = #{continuousPrint},</if>
            <if test="transferParam != null">transfer_param = #{transferParam},</if>
            <if test="modifyBy != null">modify_by = #{modifyBy},</if>
            modify_time = NOW()
        </set>
        WHERE id = #{id}
          AND corp_code = #{corpCode}
          AND deleted = 'F'
    </update>

    <update id="softDeleteByArg" parameterType="com.sendinfo.ticketing.verification.basic.repository.goods.arg.TktModelParamDeleteArg">
        UPDATE <include refid="tableName"/>
        SET deleted = 'T',
            modify_by = #{modifyBy},
            modify_time = NOW()
        WHERE id = #{id}
          AND corp_code = #{corpCode}
          AND deleted = 'F'
    </update>

    <update id="deleteById" parameterType="map">
        UPDATE <include refid="tableName"/>
        SET deleted = 'T',
            modify_by = #{modifyBy},
            modify_time = NOW()
        WHERE id = #{id}
          AND corp_code = #{corpCode}
          AND deleted = 'F'
    </update>

    <select id="countByArg" resultType="int" parameterType="com.sendinfo.ticketing.verification.basic.repository.goods.arg.TktModelParamQueryArg">
        SELECT COUNT(*)
        FROM <include refid="tableName"/>
        <where>
            corp_code = #{corpCode}
            AND deleted = 'F'
            <if test="title != null and title != ''">
                AND title LIKE CONCAT('%', #{title}, '%')
            </if>
            <if test="modelType != null">
                AND model_type = #{modelType}
            </if>
            <if test="modelKind != null">
                AND model_kind = #{modelKind}
            </if>
            <if test="useFlag != null">
                AND use_flag = #{useFlag}
            </if>
            <if test="modelCode != null and modelCode != ''">
                AND model_code = #{modelCode}
            </if>
        </where>
    </select>

    <select id="queryByArg" resultMap="BaseResultMap" parameterType="com.sendinfo.ticketing.verification.basic.repository.goods.arg.TktModelParamQueryArg">
        SELECT <include refid="allColumns"/>
        FROM <include refid="tableName"/>
        <where>
            corp_code = #{corpCode}
            AND deleted = 'F'
            <if test="title != null and title != ''">
                AND title LIKE CONCAT('%', #{title}, '%')
            </if>
            <if test="modelType != null">
                AND model_type = #{modelType}
            </if>
            <if test="modelKind != null">
                AND model_kind = #{modelKind}
            </if>
            <if test="useFlag != null">
                AND use_flag = #{useFlag}
            </if>
            <if test="modelCode != null and modelCode != ''">
                AND model_code = #{modelCode}
            </if>
        </where>
        <if test="sortItems != null and !sortItems.isEmpty()">
            ORDER BY
            <foreach collection="sortItems" item="sortItem" separator=",">
                `${sortItem.column}` ${sortItem.type.name}
            </foreach>
        </if>
        <if test="limit != null and offset != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <select id="batchQueryTicketModelParamByIds" resultMap="BaseResultMap" parameterType="map">
        SELECT <include refid="allColumns"/>
        FROM <include refid="tableName"/>
        WHERE id IN
        <foreach item="item" collection="ids" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
          AND corp_code = #{corpCode}
          AND deleted = 'F'
    </select>
</mapper>
