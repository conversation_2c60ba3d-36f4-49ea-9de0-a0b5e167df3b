package com.sendinfo.ticketing.verification.basic.repository.goods.arg;

import com.sendinfo.ticketing.verification.common.repository.arg.AbstractQueryArg;
import com.sendinfo.ticketing.verification.common.repository.arg.Pageable;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

@Getter
@Setter
@ToString(callSuper = true)
@Builder
public class TicketCalendarPriceQueryArg extends AbstractQueryArg implements Pageable {
    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 票型ID
     */
    private Long ticketId;

    /**
     * 开始时间范围查询
     */
    private LocalDateTime beginTimeStart;
    private LocalDateTime beginTimeEnd;

    /**
     * 结束时间范围查询
     */
    private LocalDateTime endTimeStart;
    private LocalDateTime endTimeEnd;

    /**
     * 星期几查询
     */
    private String weeDay;

    /**
     * 分页偏移量
     */
    private Integer offset;

    /**
     * 分页大小
     */
    private Integer limit;
}
