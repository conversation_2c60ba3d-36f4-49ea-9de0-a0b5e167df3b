package com.sendinfo.ticketing.verification.basic.api.infra.request;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 参数命名空间更新请求
 */
@Getter
@Setter
@ToString
public class SysParamNamespaceUpdateRequest implements Serializable {
    private static final long serialVersionUID = -6543210987654321099L;

    /**
     * 企业编码
     */
    @NotBlank(message = "企业编码不能为空")
    private String corpCode;

    /**
     * 主键ID
     */
    @NotNull(message = "ID不能为空")
    private Long id;

    /**
     * 命名空间名称
     */
    @Size(max = 128, message = "命名空间名称长度不能超过128")
    private String namespaceName;

    /**
     * 命名空间描述
     */
    @Size(max = 256, message = "命名空间描述长度不能超过256")
    private String description;

    /**
     * 组件分类Id，infra_feature_component_category主键id
     */
    private Long categoryId;

    /**
     * 排序
     */
    private Integer sortNo;

    /**
     * 状态,0-禁用,1-启用
     */
    private Integer status;

    /**
     * 修改人
     */
    private String modifyBy;
}