package com.sendinfo.ticketing.verification.basic.component.goods.param;

import com.sendinfo.ticketing.verification.basic.enums.ExplainerUserFlagEnum;
import com.sendinfo.ticketing.verification.common.component.param.AbstractQueryParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Set;

/**
 * 服务类目计费规则查询参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class ExplainerPayInfoQueryParam extends AbstractQueryParam {

    /** 企业编码 */
    private String corpCode;
    /** 服务类目ID集合 */
    private Set<Long> serviceCategoryIds;
    /** 支付名称 */
    private String payName;
    /** 计费方式(票务字典) */
    private String payType;
    /** 状态 */
    private ExplainerUserFlagEnum useFlag;
} 