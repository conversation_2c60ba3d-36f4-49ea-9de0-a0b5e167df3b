package com.sendinfo.ticketing.verification.basic.api.goods;

import com.sendinfo.ticketing.verification.basic.api.goods.request.TktTicketDivRuleBatchQueryByTicketIdsRequest;
import com.sendinfo.ticketing.verification.basic.api.goods.request.TktTicketDivRuleQueryByTicketIdRequest;
import com.sendinfo.ticketing.verification.basic.model.goods.TktTicketDivRule;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import java.util.List;

/**
 * @Auther: chengjie.li
 * @Date: 2025/6/12 11:36
 */
public interface TktTicketDivRuleReadService {

    /**
     * 根据票型ID查询分成规则
     *
     * @param request 票型ID、corpCode
     * @return 分成规则列表
     */
    ResultModel<List<TktTicketDivRule>> listTicketDivRuleByTicketId(TktTicketDivRuleQueryByTicketIdRequest request);

    /**
     * 根据票型ID查询分成规则
     *
     * @param request 请求
     * @return 分成规则列表
     */
    ResultModel<List<TktTicketDivRule>> batchListTicketDivRuleByTicketIds(TktTicketDivRuleBatchQueryByTicketIdsRequest request);
}
