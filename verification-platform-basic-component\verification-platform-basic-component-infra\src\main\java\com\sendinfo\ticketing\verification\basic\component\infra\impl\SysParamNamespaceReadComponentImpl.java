package com.sendinfo.ticketing.verification.basic.component.infra.impl;

import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

import com.sendinfo.ticketing.verification.basic.component.infra.SysParamNamespaceReadComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.converter.SysParamNamespaceConverter;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamNamespaceQueryParam;
import com.sendinfo.ticketing.verification.basic.model.infra.SysParamNamespace;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamNamespaceQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.SysParamNamespaceDao;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.SysParamNamespaceDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedCountRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedPageRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleRead;

/**
 * 参数命名空间查询组件实现类，负责查询参数命名空间的业务逻辑
 *
 * <AUTHOR> 2025-07-21 15:30:00
 */
@Component("sysParamNamespaceReadComponent")
@Getter
public class SysParamNamespaceReadComponentImpl
        implements SysParamNamespaceReadComponent,
        DaoBasedSingleRead<Long, SysParamNamespaceQueryParam, SysParamNamespace, SysParamNamespaceDO>,
        DaoBasedPageRead<Long, SysParamNamespaceQueryParam, SysParamNamespace, SysParamNamespaceDO, SysParamNamespaceQueryArg>,
        DaoBasedCountRead<Long, SysParamNamespaceQueryParam, SysParamNamespace, SysParamNamespaceQueryArg> {

    private final SysParamNamespaceDao dao;
    private final SysParamNamespaceConverter converter;

    public SysParamNamespaceReadComponentImpl(SysParamNamespaceDao dao, SysParamNamespaceConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }

    @Override
    public SysParamNamespace queryByNamespaceCode(String namespaceCode, String corpCode) {
        SysParamNamespaceDO dataObject = dao.queryByNamespaceCode(namespaceCode, corpCode);
        return dataObject != null ? converter.r_d2m(dataObject) : null;
    }

    @Override
    public List<SysParamNamespace> queryListByModuleCode(String moduleCode, String corpCode) {
        List<SysParamNamespaceDO> dataObjects = dao.queryListByModuleCode(moduleCode, corpCode);
        return dataObjects.stream()
                .map(converter::r_d2m)
                .collect(Collectors.toList());
    }

    @Override
    public List<SysParamNamespace> queryListByGroupCode(String groupCode, String corpCode) {
        List<SysParamNamespaceDO> dataObjects = dao.queryListByGroupCode(groupCode, corpCode);
        return dataObjects.stream()
                .map(converter::r_d2m)
                .collect(Collectors.toList());
    }
}