package com.sendinfo.ticketing.verification.basic.component.infra.impl;

import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.List;

import com.sendinfo.ticketing.verification.basic.component.infra.FeatureComponentCategoryReadComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.converter.FeatureComponentCategoryConverter;
import com.sendinfo.ticketing.verification.basic.component.infra.param.FeatureComponentCategoryQueryParam;
import com.sendinfo.ticketing.verification.basic.model.infra.FeatureComponentCategory;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.FeatureComponentCategoryQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.FeatureComponentCategoryDao;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.FeatureComponentCategoryDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedCountRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedPageRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleRead;

/**
 * 组件分类查询组件实现
 * 
 * <AUTHOR> 2025-07-22 11:45:32
 */
@Component("featureComponentCategoryReadComponent")
@Getter
public class FeatureComponentCategoryReadComponentImpl
        implements FeatureComponentCategoryReadComponent,
        DaoBasedPageRead<Long, FeatureComponentCategoryQueryParam, FeatureComponentCategory, FeatureComponentCategoryDO, FeatureComponentCategoryQueryArg>,
        DaoBasedSingleRead<Long, FeatureComponentCategoryQueryParam, FeatureComponentCategory, FeatureComponentCategoryDO>,
        DaoBasedCountRead<Long, FeatureComponentCategoryQueryParam, FeatureComponentCategory, FeatureComponentCategoryQueryArg> {

    private final FeatureComponentCategoryDao dao;
    private final FeatureComponentCategoryConverter converter;

    public FeatureComponentCategoryReadComponentImpl(FeatureComponentCategoryDao dao,
            FeatureComponentCategoryConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }

    @Override
    public FeatureComponentCategory queryByCategoryName(String categoryName, String corpCode) {
        FeatureComponentCategoryDO dataObject = dao.queryByCategoryName(categoryName, corpCode);
        return dataObject != null ? converter.r_d2m(dataObject) : null;
    }

    @Override
    public List<FeatureComponentCategory> queryAllList(String corpCode) {
        return converter.r_ds2ms(dao.queryAllList(corpCode));
    }
}
