package com.sendinfo.ticketing.verification.basic.repository.pay.dao;

import com.alibaba.nacos.api.exception.NacosException;
import com.sendinfo.ticketing.verification.basic.repository.common.config.SaasPwMybatisConfig;
import com.sendinfo.ticketing.verification.basic.repository.common.sharding.DynamicDataSourceMappingConfigPullStorage;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.TktSellerPayawayDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.TktSellerPayawayQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.TktSellerPayawayUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dao.impl.TktSellerPayawayDaoImpl;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.TktSellerPayawayDO;
import com.sendinfo.ticketing.verification.common.repository.arg.StatusUpdater;
import org.junit.Test;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.AbstractTransactionalJUnit4SpringContextTests;

import java.util.List;
import java.util.Optional;
import java.util.Random;

import static org.junit.Assert.*;

/**
 * 售票员收款方式数据访问层单元测试
 * <p>
 * 本测试类覆盖了TktSellerPayawayDao的所有方法，包括：
 * 1. 插入操作测试
 * 2. 批量插入操作测试
 * 3. 根据ID查询测试
 * 4. 条件查询测试
 * 5. 计数查询测试
 * 6. 更新操作测试
 * 7. 软删除操作测试
 * 8. 自定义查询方法测试
 * <p>
 * 测试目的：验证TktSellerPayawayDao的所有方法都能正常工作，确保数据访问层的正确性
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootTest(classes = TktSellerPayawayDaoTest.Config.class)
@EnableAutoConfiguration
@TestPropertySource(locations = "classpath:test-application.properties")
public class TktSellerPayawayDaoTest extends AbstractTransactionalJUnit4SpringContextTests {

    @Autowired
    private TktSellerPayawayDao tktSellerPayawayDao;

    /**
     * 测试用的企业编码
     */
    private static final String TEST_CORP_CODE = "TEST_CORP_001";

    /**
     * 测试用户
     */
    private static final String TEST_USER = "test_user";

    /**
     * 测试售票员
     */
    private static final String TEST_SELLER = "test_seller";

    /**
     * 测试账户ID
     */
    private static final Long TEST_ACC_ID = 1L;

    /**
     * 测试支付ID
     */
    private static final Long TEST_PAY_ID = 1L;

    /**
     * 测试配置类
     * 配置MyBatis和DAO Bean
     */
    @Configuration
    @Import(SaasPwMybatisConfig.class)
    static class Config {

        @Bean
        public TktSellerPayawayDao tktSellerPayawayDao(@Qualifier(SaasPwMybatisConfig.BEAN_NAME_SQL_SESSION_TEMPLATE) SqlSessionTemplate sqlSessionTemplate) {
            return new TktSellerPayawayDaoImpl(sqlSessionTemplate);
        }

        @Bean
        DynamicDataSourceMappingConfigPullStorage dynamicDataSourceMappingConfigPullStorage(@Value("${nacos.config.server-addr}") String serverAddr,
                                                                                            @Value("${nacos.config.namespace}") String namespace,
                                                                                            @Value("${verification-platform-basic.nacos.tenant-datasource-mapping.data-id:saas-pw-datasource-sharding-mapping.json}") String dataId,
                                                                                            @Value("${verification-platform-basic.nacos.tenant-datasource-mapping.group:DEFAULT_GROUP}") String group,
                                                                                            @Value("${verification-platform-basic.nacos.tenant-datasource-mapping.pulled.timeout:3000}") Integer timeout) throws NacosException {
            return new DynamicDataSourceMappingConfigPullStorage(serverAddr, namespace, dataId, group, timeout);
        }
    }

    /**
     * 测试插入操作
     * <p>
     * 测试步骤：
     * 1. 创建测试售票员收款方式数据
     * 2. 执行插入操作
     * 3. 验证主键ID被自动生成
     * 4. 通过ID查询验证记录确实被插入
     * 5. 验证插入的数据字段值正确
     */
    @Test
    public void testInsert() {
        // 1. 创建测试售票员收款方式数据
        TktSellerPayawayDO tktSellerPayawayDO = createTestDO();

        // 2. 执行插入操作
        tktSellerPayawayDao.insert(tktSellerPayawayDO);

        // 3. 验证主键ID被自动生成
        assertNotNull("插入后主键ID应该被自动生成", tktSellerPayawayDO.getId());

        // 4. 通过ID查询验证记录确实被插入
        TktSellerPayawayDO insertedTktSellerPayaway = tktSellerPayawayDao.queryById(tktSellerPayawayDO.getId());
        assertNotNull("插入后应能通过ID查询到记录", insertedTktSellerPayaway);
        assertEquals("企业编码应该一致", TEST_CORP_CODE, insertedTktSellerPayaway.getCorpCode());
        assertEquals("售票员应该一致", tktSellerPayawayDO.getSeller(), insertedTktSellerPayaway.getSeller());
        assertEquals("账户ID应该一致", tktSellerPayawayDO.getAccId(), insertedTktSellerPayaway.getAccId());
    }

    /**
     * 测试批量插入操作
     * <p>
     * 测试步骤：
     * 1. 创建多个测试售票员收款方式数据
     * 2. 执行批量插入操作
     * 3. 验证所有记录的主键ID都被自动生成
     * 4. 通过条件查询验证所有记录都被正确插入
     */
    @Test
    public void testBatchInsert() {
        // 1. 创建多个测试售票员收款方式数据
        List<TktSellerPayawayDO> tktSellerPayawayList = new java.util.ArrayList<>();
        for (int i = 0; i < 3; i++) {
            TktSellerPayawayDO tktSellerPayawayDO = createTestDO();
            tktSellerPayawayList.add(tktSellerPayawayDO);
        }

        // 2. 执行批量插入操作
        tktSellerPayawayDao.batchInsert(tktSellerPayawayList);

        // 3. 查询插入数据
        TktSellerPayawayQueryArg queryArg = new TktSellerPayawayQueryArg();
        queryArg.setCorpCode(TEST_CORP_CODE);
        List<TktSellerPayawayDO> tktSellerPayawayDOList = tktSellerPayawayDao.queryByArg(queryArg);

        // 4. 验证所有记录都被正确插入
        assertTrue("应该插入至少3条记录", tktSellerPayawayDOList.size() >= 3);
        for (TktSellerPayawayDO tktSellerPayawayDO : tktSellerPayawayDOList) {
            assertNotNull("每个记录都应该生成ID", tktSellerPayawayDO.getId());
        }
    }

    /**
     * 测试根据ID查询操作
     * <p>
     * 测试步骤：
     * 1. 插入测试记录
     * 2. 使用插入记录的ID进行查询
     * 3. 验证查询结果正确
     */
    @Test
    public void testQueryById() {
        // 1. 插入测试记录
        TktSellerPayawayDO tktSellerPayawayDO = createTestDO();
        tktSellerPayawayDao.insert(tktSellerPayawayDO);

        // 2. 使用插入记录的ID进行查询
        TktSellerPayawayDO queriedTktSellerPayaway = tktSellerPayawayDao.queryById(tktSellerPayawayDO.getId());

        // 3. 验证查询结果正确
        assertNotNull("应该能查询到记录", queriedTktSellerPayaway);
        assertEquals("ID应该一致", tktSellerPayawayDO.getId(), queriedTktSellerPayaway.getId());
        assertEquals("企业编码应该一致", TEST_CORP_CODE, queriedTktSellerPayaway.getCorpCode());
        assertEquals("售票员应该一致", tktSellerPayawayDO.getSeller(), queriedTktSellerPayaway.getSeller());
    }

    /**
     * 测试条件查询操作
     * <p>
     * 测试步骤：
     * 1. 插入测试记录
     * 2. 使用查询参数进行条件查询
     * 3. 验证查询结果正确
     */
    @Test
    public void testQueryByArg() {
        // 1. 插入测试记录
        TktSellerPayawayDO tktSellerPayawayDO = createTestDO();
        tktSellerPayawayDao.insert(tktSellerPayawayDO);

        // 2. 使用查询参数进行条件查询
        TktSellerPayawayQueryArg queryArg = new TktSellerPayawayQueryArg();
        queryArg.setCorpCode(TEST_CORP_CODE);
        queryArg.setSeller(tktSellerPayawayDO.getSeller());
        List<TktSellerPayawayDO> tktSellerPayawayList = tktSellerPayawayDao.queryByArg(queryArg);

        // 3. 验证查询结果正确
        assertFalse("应该能查询到记录", tktSellerPayawayList.isEmpty());
        TktSellerPayawayDO queriedTktSellerPayaway = tktSellerPayawayList.get(0);
        assertEquals("企业编码应该一致", TEST_CORP_CODE, queriedTktSellerPayaway.getCorpCode());
        assertEquals("售票员应该一致", tktSellerPayawayDO.getSeller(), queriedTktSellerPayaway.getSeller());
    }

    /**
     * 测试计数查询操作
     * <p>
     * 测试步骤：
     * 1. 插入测试记录
     * 2. 使用查询参数进行计数查询
     * 3. 验证计数结果正确
     */
    @Test
    public void testCountByArg() {
        // 1. 插入测试记录
        TktSellerPayawayDO tktSellerPayawayDO = createTestDO();
        tktSellerPayawayDao.insert(tktSellerPayawayDO);

        // 2. 使用查询参数进行计数查询
        TktSellerPayawayQueryArg queryArg = new TktSellerPayawayQueryArg();
        queryArg.setCorpCode(TEST_CORP_CODE);
        long count = tktSellerPayawayDao.countByArg(queryArg);

        // 3. 验证计数结果正确
        assertTrue("计数应该大于0", count > 0);
    }

    /**
     * 测试更新操作
     * <p>
     * 测试步骤：
     * 1. 插入测试记录
     * 2. 创建更新参数并设置新的值
     * 3. 执行更新操作
     * 4. 验证更新结果
     * 5. 查询更新后的记录验证字段值已更新
     */
    @Test
    public void testUpdateByArg() {
        // 1. 插入测试记录
        TktSellerPayawayDO tktSellerPayawayDO = createTestDO();
        tktSellerPayawayDao.insert(tktSellerPayawayDO);

        // 2. 创建更新参数并设置新的值
        TktSellerPayawayUpdateArg updateArg = new TktSellerPayawayUpdateArg();
        updateArg.setId(tktSellerPayawayDO.getId());
        updateArg.setCorpCode(tktSellerPayawayDO.getCorpCode());
        updateArg.setSeller("更新后的售票员");
        updateArg.setSort(999);
        updateArg.setModifyBy(TEST_USER);

        // 3. 执行更新操作
        int updateCount = tktSellerPayawayDao.updateByArg(updateArg);

        // 4. 验证更新结果
        assertEquals("应该更新1条记录", 1, updateCount);

        // 5. 查询更新后的记录验证字段值已更新
        TktSellerPayawayDO updatedTktSellerPayaway = tktSellerPayawayDao.queryById(tktSellerPayawayDO.getId());
        assertEquals("售票员应该已更新", "更新后的售票员", updatedTktSellerPayaway.getSeller());
        assertEquals("排序应该已更新", Integer.valueOf(999), updatedTktSellerPayaway.getSort());
    }

    /**
     * 测试带状态更新器的更新操作
     * <p>
     * 测试步骤：
     * 1. 插入测试记录（设置为未启用状态）
     * 2. 创建更新参数，使用状态更新器
     * 3. 执行更新操作
     * 4. 验证更新结果
     * 5. 查询更新后的记录验证状态已更新
     */
    @Test
    public void testUpdateByArgWithStatusUpdater() {
        // 1. 插入测试记录（设置为未启用状态）
        TktSellerPayawayDO tktSellerPayawayDO = createTestDO();
        tktSellerPayawayDO.setUseFlag("F"); // 未启用状态
        tktSellerPayawayDao.insert(tktSellerPayawayDO);

        // 2. 创建更新参数，使用状态更新器
        TktSellerPayawayUpdateArg updateArg = new TktSellerPayawayUpdateArg();
        updateArg.setId(tktSellerPayawayDO.getId());
        updateArg.setCorpCode(tktSellerPayawayDO.getCorpCode());
        updateArg.setUseFlagUpdater(new StatusUpdater<>("F", "T"));
        updateArg.setModifyBy(TEST_USER);

        // 3. 执行更新操作
        int updateCount = tktSellerPayawayDao.updateByArg(updateArg);

        // 4. 验证更新结果
        assertEquals("应该更新1条记录", 1, updateCount);

        // 5. 查询更新后的记录验证状态已更新
        TktSellerPayawayDO updatedTktSellerPayaway = tktSellerPayawayDao.queryById(tktSellerPayawayDO.getId());
        assertEquals("启用标识应该已更新为T", "T", updatedTktSellerPayaway.getUseFlag());

        // 6. 测试错误的状态更新（当前状态不匹配）
        updateArg.setUseFlagUpdater(new StatusUpdater<>("F", "T"));
        int wrongUpdateCount = tktSellerPayawayDao.updateByArg(updateArg);
        assertEquals("状态不匹配时应该更新0条记录", 0, wrongUpdateCount);
    }

    /**
     * 测试软删除操作
     * <p>
     * 测试步骤：
     * 1. 插入测试记录
     * 2. 执行软删除操作
     * 3. 验证记录被软删除（通过ID查询不到）
     * 4. 验证物理删除操作
     */
    @Test
    public void testSoftDeleteByArg() {
        // 1. 插入测试记录
        TktSellerPayawayDO tktSellerPayawayDO = createTestDO();
        tktSellerPayawayDao.insert(tktSellerPayawayDO);

        // 2. 执行软删除操作
        TktSellerPayawayDeleteArg deleteArg = new TktSellerPayawayDeleteArg();
        deleteArg.setId(tktSellerPayawayDO.getId());
        deleteArg.setModifyBy(TEST_USER);
        tktSellerPayawayDao.softDeleteByArg(deleteArg);

        // 3. 验证记录被软删除（通过ID查询不到）
        TktSellerPayawayDO deletedTktSellerPayaway = tktSellerPayawayDao.queryById(tktSellerPayawayDO.getId());
        assertNull("软删除后应该查询不到记录", deletedTktSellerPayaway);

        // 4. 验证物理删除操作
        tktSellerPayawayDao.deleteById(tktSellerPayawayDO.getId());
        TktSellerPayawayDO physicallyDeletedTktSellerPayaway = tktSellerPayawayDao.queryById(tktSellerPayawayDO.getId());
        assertNull("物理删除后应该查询不到记录", physicallyDeletedTktSellerPayaway);
    }

    /**
     * 测试根据账户ID查询操作
     * <p>
     * 测试步骤：
     * 1. 插入测试记录
     * 2. 使用账户ID进行查询
     * 3. 验证查询结果正确
     */
    @Test
    public void testQueryByAccId() {
        // 1. 插入测试记录
        TktSellerPayawayDO tktSellerPayawayDO = createTestDO();
        tktSellerPayawayDao.insert(tktSellerPayawayDO);

        // 2. 使用账户ID进行查询
        List<TktSellerPayawayDO> tktSellerPayawayList = tktSellerPayawayDao.queryByAccId(TEST_ACC_ID, TEST_CORP_CODE);

        // 3. 验证查询结果正确
        assertFalse("应该能查询到记录", tktSellerPayawayList.isEmpty());
        TktSellerPayawayDO queriedTktSellerPayaway = tktSellerPayawayList.get(0);
        assertEquals("账户ID应该一致", TEST_ACC_ID, queriedTktSellerPayaway.getAccId());
        assertEquals("企业编码应该一致", TEST_CORP_CODE, queriedTktSellerPayaway.getCorpCode());
    }

    /**
     * 测试查询售票员启用的支付方式
     * <p>
     * 测试步骤：
     * 1. 插入多个测试记录（启用和未启用状态）
     * 2. 创建查询参数（账户ID、客户类型、售票模式、企业编码）
     * 3. 执行查询操作
     * 4. 验证查询结果只包含启用状态的记录
     * 5. 验证查询结果符合条件
     */
    @Test
    public void testQueryEnableSellerPayAwayList_ShouldReturnEnabledPayaways() {
        // 1. 插入多个测试记录（启用和未启用状态）
        TktSellerPayawayDO enabledPayaway1 = createTestDO();
        enabledPayaway1.setUseFlag("T");
        enabledPayaway1.setClientType(1);
        enabledPayaway1.setSaleModel(1);
        tktSellerPayawayDao.insert(enabledPayaway1);

        TktSellerPayawayDO enabledPayaway2 = createTestDO();
        enabledPayaway2.setUseFlag("T");
        enabledPayaway2.setClientType(1);
        enabledPayaway2.setSaleModel(1);
        tktSellerPayawayDao.insert(enabledPayaway2);

        TktSellerPayawayDO disabledPayaway = createTestDO();
        disabledPayaway.setUseFlag("F");
        disabledPayaway.setClientType(1);
        disabledPayaway.setSaleModel(1);
        tktSellerPayawayDao.insert(disabledPayaway);

        // 2. 创建查询参数
        Long accId = TEST_ACC_ID;
        Integer clientType = 1;
        Integer saleModel = 1;

        // 3. 执行查询操作
        List<TktSellerPayawayDO> result = tktSellerPayawayDao.queryEnableSellerPayAwayList(accId, clientType, saleModel, TEST_CORP_CODE);

        // 4. 验证查询结果只包含启用状态的记录
        assertNotNull("查询结果不应为null", result);
        assertTrue("应该返回启用状态的记录", result.size() >= 2);

        // 5. 验证查询结果符合条件
        for (TktSellerPayawayDO payaway : result) {
            assertEquals("所有记录的使用标志应该为T", "T", payaway.getUseFlag());
            assertEquals("所有记录的账户ID应该一致", accId, payaway.getAccId());
            assertEquals("所有记录的客户类型应该一致", clientType, payaway.getClientType());
            assertEquals("所有记录的售票模式应该一致", saleModel, payaway.getSaleModel());
            assertEquals("所有记录的企业编码应该一致", TEST_CORP_CODE, payaway.getCorpCode());
        }
    }

    /**
     * 测试空账户ID的查询
     * <p>
     * 测试步骤：
     * 1. 插入测试记录
     * 2. 使用空账户ID进行查询
     * 3. 验证查询结果
     */
    @Test
    public void testQueryEnableSellerPayAwayList_WithNullAccId() {
        // 1. 插入测试记录
        TktSellerPayawayDO tktSellerPayawayDO = createTestDO();
        tktSellerPayawayDao.insert(tktSellerPayawayDO);

        // 2. 使用空账户ID进行查询
        List<TktSellerPayawayDO> result = tktSellerPayawayDao.queryEnableSellerPayAwayList(null, 1, 1, TEST_CORP_CODE);

        // 3. 验证查询结果
        assertNotNull("查询结果不应为null", result);
        // 根据实际业务逻辑，可能返回空结果或所有匹配的记录
    }

    /**
     * 测试空客户类型的查询
     * <p>
     * 测试步骤：
     * 1. 插入测试记录
     * 2. 使用空客户类型进行查询
     * 3. 验证查询结果
     */
    @Test
    public void testQueryEnableSellerPayAwayList_WithNullClientType() {
        // 1. 插入测试记录
        TktSellerPayawayDO tktSellerPayawayDO = createTestDO();
        tktSellerPayawayDao.insert(tktSellerPayawayDO);

        // 2. 使用空客户类型进行查询
        List<TktSellerPayawayDO> result = tktSellerPayawayDao.queryEnableSellerPayAwayList(TEST_ACC_ID, null, 1, TEST_CORP_CODE);

        // 3. 验证查询结果
        assertNotNull("查询结果不应为null", result);
        // 根据实际业务逻辑，可能返回空结果或所有匹配的记录
    }

    /**
     * 测试空售票模式的查询
     * <p>
     * 测试步骤：
     * 1. 插入测试记录
     * 2. 使用空售票模式进行查询
     * 3. 验证查询结果
     */
    @Test
    public void testQueryEnableSellerPayAwayList_WithNullSaleModel() {
        // 1. 插入测试记录
        TktSellerPayawayDO tktSellerPayawayDO = createTestDO();
        tktSellerPayawayDao.insert(tktSellerPayawayDO);

        // 2. 使用空售票模式进行查询
        List<TktSellerPayawayDO> result = tktSellerPayawayDao.queryEnableSellerPayAwayList(TEST_ACC_ID, 1, null, TEST_CORP_CODE);

        // 3. 验证查询结果
        assertNotNull("查询结果不应为null", result);
        // 根据实际业务逻辑，可能返回空结果或所有匹配的记录
    }

    /**
     * 测试混合状态的查询
     * <p>
     * 测试步骤：
     * 1. 插入多个不同状态的测试记录
     * 2. 创建包含所有记录的查询参数
     * 3. 执行查询操作
     * 4. 验证只返回启用状态的记录
     */
    @Test
    public void testQueryEnableSellerPayAwayList_WithMixedStatus() {
        // 1. 插入多个不同状态的测试记录
        TktSellerPayawayDO enabledPayaway = createTestDO();
        enabledPayaway.setUseFlag("T");
        enabledPayaway.setClientType(2);
        enabledPayaway.setSaleModel(2);
        tktSellerPayawayDao.insert(enabledPayaway);

        TktSellerPayawayDO disabledPayaway1 = createTestDO();
        disabledPayaway1.setUseFlag("F");
        disabledPayaway1.setClientType(2);
        disabledPayaway1.setSaleModel(2);
        tktSellerPayawayDao.insert(disabledPayaway1);

        TktSellerPayawayDO disabledPayaway2 = createTestDO();
        disabledPayaway2.setUseFlag("F");
        disabledPayaway2.setClientType(2);
        disabledPayaway2.setSaleModel(2);
        tktSellerPayawayDao.insert(disabledPayaway2);

        // 2. 创建包含所有记录的查询参数
        Long accId = TEST_ACC_ID;
        Integer clientType = 2;
        Integer saleModel = 2;

        // 3. 执行查询操作
        List<TktSellerPayawayDO> result = tktSellerPayawayDao.queryEnableSellerPayAwayList(accId, clientType, saleModel, TEST_CORP_CODE);

        // 4. 验证只返回启用状态的记录
        assertNotNull("查询结果不应为null", result);
        assertTrue("应该至少返回1条启用状态的记录", result.size() >= 1);
        for (TktSellerPayawayDO payaway : result) {
            assertEquals("返回的记录状态应该为启用", "T", payaway.getUseFlag());
            assertEquals("返回的记录客户类型应该一致", clientType, payaway.getClientType());
            assertEquals("返回的记录售票模式应该一致", saleModel, payaway.getSaleModel());
        }
    }

    /**
     * 测试不同参数组合的查询
     * <p>
     * 测试步骤：
     * 1. 插入不同参数组合的测试记录
     * 2. 使用特定参数组合进行查询
     * 3. 验证只返回指定参数组合的记录
     */
    @Test
    public void testQueryEnableSellerPayAwayList_WithDifferentParameters() {
        // 1. 插入不同参数组合的测试记录
        TktSellerPayawayDO payaway1 = createTestDO();
        payaway1.setUseFlag("T");
        payaway1.setClientType(1);
        payaway1.setSaleModel(1);
        tktSellerPayawayDao.insert(payaway1);

        TktSellerPayawayDO payaway2 = createTestDO();
        payaway2.setUseFlag("T");
        payaway2.setClientType(2);
        payaway2.setSaleModel(1);
        tktSellerPayawayDao.insert(payaway2);

        TktSellerPayawayDO payaway3 = createTestDO();
        payaway3.setUseFlag("T");
        payaway3.setClientType(1);
        payaway3.setSaleModel(2);
        tktSellerPayawayDao.insert(payaway3);

        // 2. 使用特定参数组合进行查询
        List<TktSellerPayawayDO> result1 = tktSellerPayawayDao.queryEnableSellerPayAwayList(TEST_ACC_ID, 1, 1, TEST_CORP_CODE);
        List<TktSellerPayawayDO> result2 = tktSellerPayawayDao.queryEnableSellerPayAwayList(TEST_ACC_ID, 2, 1, TEST_CORP_CODE);
        List<TktSellerPayawayDO> result3 = tktSellerPayawayDao.queryEnableSellerPayAwayList(TEST_ACC_ID, 1, 2, TEST_CORP_CODE);

        // 3. 验证只返回指定参数组合的记录
        assertNotNull("查询结果1不应为null", result1);
        assertTrue("查询结果1应该包含记录", result1.size() >= 1);
        for (TktSellerPayawayDO payaway : result1) {
            assertEquals("查询结果1的客户类型应该为1", Optional.of(1).get(), payaway.getClientType());
            assertEquals("查询结果1的售票模式应该为1", Optional.of(1).get(), payaway.getSaleModel());
        }

        assertNotNull("查询结果2不应为null", result2);
        assertTrue("查询结果2应该包含记录", result2.size() >= 1);
        for (TktSellerPayawayDO payaway : result2) {
            assertEquals("查询结果2的客户类型应该为2", Optional.of(2).get(), payaway.getClientType());
            assertEquals("查询结果2的售票模式应该为1", Optional.of(1).get(), payaway.getSaleModel());
        }

        assertNotNull("查询结果3不应为null", result3);
        assertTrue("查询结果3应该包含记录", result3.size() >= 1);
        for (TktSellerPayawayDO payaway : result3) {
            assertEquals("查询结果3的客户类型应该为1", Optional.of(1).get(), payaway.getClientType());
            assertEquals("查询结果3的售票模式应该为2", Optional.of(2).get(), payaway.getSaleModel());
        }
    }

    /**
     * 测试不存在的账户ID查询
     * <p>
     * 测试步骤：
     * 1. 插入测试记录
     * 2. 使用不存在的账户ID进行查询
     * 3. 验证返回空结果
     */
    @Test
    public void testQueryEnableSellerPayAwayList_WithNonExistentAccId() {
        // 1. 插入测试记录
        TktSellerPayawayDO tktSellerPayawayDO = createTestDO();
        tktSellerPayawayDao.insert(tktSellerPayawayDO);

        // 2. 使用不存在的账户ID进行查询
        Long nonExistentAccId = 99999L;
        List<TktSellerPayawayDO> result = tktSellerPayawayDao.queryEnableSellerPayAwayList(nonExistentAccId, 1, 1, TEST_CORP_CODE);

        // 3. 验证返回空结果
        assertNotNull("查询结果不应为null", result);
        assertTrue("不存在的账户ID应该返回空结果", result.isEmpty());
    }

    /**
     * 创建测试数据对象
     *
     * @return 测试用的TktSellerPayawayDO对象
     */
    private TktSellerPayawayDO createTestDO() {
        TktSellerPayawayDO tktSellerPayawayDO = new TktSellerPayawayDO();

        tktSellerPayawayDO.setCorpCode(TEST_CORP_CODE);
        tktSellerPayawayDO.setSeller(TEST_SELLER);
        tktSellerPayawayDO.setAccId(TEST_ACC_ID);
        tktSellerPayawayDO.setSaleModel(1);
        tktSellerPayawayDO.setClientType(1);
        tktSellerPayawayDO.setPayAway("现金");
        tktSellerPayawayDO.setSort(new Random().nextInt(100));
        tktSellerPayawayDO.setUseFlag("T");
        tktSellerPayawayDO.setPayId(TEST_PAY_ID);

        tktSellerPayawayDO.setCreateBy(TEST_USER);
        tktSellerPayawayDO.setModifyBy(TEST_USER);
        tktSellerPayawayDO.setDeleted("F");

        return tktSellerPayawayDO;
    }
} 