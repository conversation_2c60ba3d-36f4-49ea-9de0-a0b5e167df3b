package com.sendinfo.ticketing.verification.basic.repository.pay.dao.impl;

import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageMmSubMerchantsDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageMmSubMerchantsQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageMmSubMerchantsUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dao.SysMainbodyManageMmSubMerchantsDao;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysMainbodyManageMmSubMerchantsDO;
import com.sendinfo.ticketing.verification.repository.jdbc.Statement;
import com.sendinfo.ticketing.verification.repository.jdbc.mybatis.*;
import lombok.Getter;
import org.apache.ibatis.session.SqlSession;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 经营主体支付配置子商户关联表数据访问实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Repository("sysMainbodyManageMmSubMerchantsDao")
public class SysMainbodyManageMmSubMerchantsDaoImpl implements SysMainbodyManageMmSubMerchantsDao,
        SqlSessionGenericDAO<Long, SysMainbodyManageMmSubMerchantsDO, SysMainbodyManageMmSubMerchantsUpdateArg, SysMainbodyManageMmSubMerchantsDeleteArg>,
        SqlSessionCountableDAO<SysMainbodyManageMmSubMerchantsQueryArg>,
        SqlSessionQueryableDAO<SysMainbodyManageMmSubMerchantsQueryArg, SysMainbodyManageMmSubMerchantsDO>,
        SqlSessionBatchInsertDAO<Long, SysMainbodyManageMmSubMerchantsDO>,
        SqlSessionDAO {

    private final SqlSession sqlSession;

    private final Statement statement;

    public SysMainbodyManageMmSubMerchantsDaoImpl(@Qualifier("saasPwSqlSessionTemplate") SqlSessionTemplate sqlSessionTemplate) {
        this.sqlSession = sqlSessionTemplate;
        this.statement = Statement.of(SysMainbodyManageMmSubMerchantsDao.class);
    }

    @Override
    public List<SysMainbodyManageMmSubMerchantsDO> queryByMainbodyManageId(Long mainbodyManageId, String corpCode) {
        Map<String, Object> params = new HashMap<>();
        params.put("mainbodyManageId", mainbodyManageId);
        params.put("corpCode", corpCode);
        return sqlSession.selectList(statement.get("queryByMainbodyManageId"), params);
    }

    @Override
    public List<SysMainbodyManageMmSubMerchantsDO> queryBySubMerchantsId(Long subMerchantsId, String corpCode) {
        Map<String, Object> params = new HashMap<>();
        params.put("subMerchantsId", subMerchantsId);
        params.put("corpCode", corpCode);
        return sqlSession.selectList(statement.get("queryBySubMerchantsId"), params);
    }

    @Override
    public List<SysMainbodyManageMmSubMerchantsDO> queryBySubMerchantsIdSet(Set<Long> subMerchantsIdSet, String corpCode) {
        // 参数验证
        if (subMerchantsIdSet == null || subMerchantsIdSet.isEmpty()) {
            return new java.util.ArrayList<>();
        }
        
        Map<String, Object> params = new HashMap<>();
        params.put("subMerchantsIdSet", subMerchantsIdSet);
        params.put("corpCode", corpCode);
        return sqlSession.selectList(statement.get("queryBySubMerchantsIdSet"), params);
    }
} 