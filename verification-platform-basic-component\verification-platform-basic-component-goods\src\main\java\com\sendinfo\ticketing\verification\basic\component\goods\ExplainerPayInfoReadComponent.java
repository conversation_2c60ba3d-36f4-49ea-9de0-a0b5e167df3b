package com.sendinfo.ticketing.verification.basic.component.goods;

import com.sendinfo.ticketing.verification.basic.component.goods.param.ExplainerPayInfoQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.ExplainerPayInfo;
import com.sendinfo.ticketing.verification.common.component.ReadComponent;

import java.util.List;

/**
 * 服务类目计费规则读取组件接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ExplainerPayInfoReadComponent extends ReadComponent<Long, ExplainerPayInfoQueryParam, ExplainerPayInfo> {

    /**
     * 模糊查询服务类目计费规则列表
     *
     * @param queryParam 查询参数
     * @return 计费规则列表
     */
    List<ExplainerPayInfo> queryPayInfoList(ExplainerPayInfoQueryParam queryParam);
} 