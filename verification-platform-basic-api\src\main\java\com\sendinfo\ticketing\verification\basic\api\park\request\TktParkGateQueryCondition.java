package com.sendinfo.ticketing.verification.basic.api.park.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2025/6/8 16:08
 **/
@Getter
@Setter
@ToString(callSuper = true)
public class TktParkGateQueryCondition implements Serializable {
    private static final long serialVersionUID = -6685541419919509619L;

    @NotNull(message = "corpCode不能为空")
    /** 租户标识，必填，确保租户数据隔离 */
    private String corpCode;
    /** 景区分区ID */
    private Long parkZoneId;

    /** 通道名称 */
    private String gateName;

    /** 通道编号 */
    private Integer gateNo;

    /** 通道IP */
    private String gateIp;

    /** 通道方向:IN:入口通道  OUT:出口通道 */
    private String gateDirection;

    /** 顺序 */
    private Integer sortNum;

    /** 是否启用人脸 */
    private String checkFaceFlag;

    /** 是否校验检票时间F不校验，T校验，默认校验 */
    private String isCheckTimeLimit;

    /** 是否设置扫码入园票型 F:未设置（默认）T：设置 */
    private String qrTicketFlag;

    /** 扫码入园票型ID */
    private Long qrTicketId;

    /** 扫码入园票型名称 */
    private String qrTicketName;

    /** 检票模式:1:刷一次检一次（默认）2：一检全检 */
    private Integer checkModel;

    /** 备注 */
    private String remark;

    /** 是否开启分时校验 F:未开启（默认）T：开启 */
    private String daypartFlag;

    /** 是否开启代理检票点(T:开启 F:关闭) */
    private String agentParkFlag;

    /** 代理检票点ID */
    private Long agentParkId;

    /** 是否为虚拟通道 T:是虚拟通道,F:正常通道 */
    private String isVirtualGate;

    /** 开启人脸后支持 1：人证比对 2：人脸补录 3：人脸比对 */
    private String checkFaceTypes;

    /** 端类型 1：pc端 2：移动端 */
    private Integer terminalType;

    /** 迁移数据id与type */
    private String transferParam;

    /** 场所编号 */
    private String placeNo;

    /** 校验防疫开关 T:是 F:否 */
    private String verifyAntiepidemicFlag;

    /** 默认通道：0普通1默认 */
    private Integer defaultSetting;
}
