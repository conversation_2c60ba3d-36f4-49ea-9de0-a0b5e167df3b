package com.sendinfo.ticketing.verification.basic.component.customer;

import com.sendinfo.ticketing.verification.basic.component.customer.param.GuideQueryParam;
import com.sendinfo.ticketing.verification.basic.model.customer.Guide;
import com.sendinfo.ticketing.verification.common.component.ReadComponent;

import java.util.List;

/**
 * 导游读取组件接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface GuideReadComponent extends ReadComponent<Long, GuideQueryParam, Guide> {

    /**
     * 模糊查询导游列表
     *
     * @param fuzzyName 模糊词
     * @param corpCode  企业编码
     * @return 导游列表
     */
    List<Guide> fuzzyQueryGuideList(String fuzzyName, String corpCode);
}