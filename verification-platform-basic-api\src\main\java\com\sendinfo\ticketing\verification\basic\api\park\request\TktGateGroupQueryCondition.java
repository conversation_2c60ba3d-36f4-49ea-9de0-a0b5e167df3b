package com.sendinfo.ticketing.verification.basic.api.park.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2025/7/3 14:40
 **/
@Getter
@Setter
@ToString(callSuper = true)
public class TktGateGroupQueryCondition implements Serializable {
    private static final long serialVersionUID = -2881702070048051842L;
    /**
     * 企业编号
     */
    @NotNull(message = "corpCode不能为空")
    private String corpCode;
    /**
     * 检票组名称
     */
    private String groupNameLike;
}
