package com.sendinfo.ticketing.verification.basic.component.goods.param;

import com.sendinfo.ticketing.verification.common.component.param.AbstractCreateParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2025-07-24 10:41
 */
@Getter
@Setter
@ToString(callSuper = true)
public class UserTicketRelationCreateParam  extends AbstractCreateParam {

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 操作人
     */
    private String modifyBy;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户登录名
     */
    private String userAccName;

    /**
     * 票型编码
     */
    private Long ticketId;

    /**
     * 票型编码
     */
    private String ticketCode;

    /**
     * 原表修改数据时间
     */
    private Date operateTime;
}
