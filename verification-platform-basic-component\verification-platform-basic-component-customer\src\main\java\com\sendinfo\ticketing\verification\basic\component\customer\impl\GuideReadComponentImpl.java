package com.sendinfo.ticketing.verification.basic.component.customer.impl;

import com.sendinfo.ticketing.verification.basic.component.customer.GuideReadComponent;
import com.sendinfo.ticketing.verification.basic.component.customer.converter.GuideConverter;
import com.sendinfo.ticketing.verification.basic.component.customer.param.GuideQueryParam;
import com.sendinfo.ticketing.verification.basic.model.customer.Guide;
import com.sendinfo.ticketing.verification.basic.repository.customer.arg.GuideQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.customer.dao.GuideDao;
import com.sendinfo.ticketing.verification.basic.repository.customer.dataobject.GuideDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedCountRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedListRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleRead;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 导游读取组件实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("guideReadComponent")
@Getter
public class GuideReadComponentImpl implements
        GuideReadComponent,
        DaoBasedSingleRead<Long, GuideQueryParam, Guide, GuideDO>,
        DaoBasedListRead<Long, GuideQueryParam, Guide, GuideDO, GuideQueryArg>,
        DaoBasedCountRead<Long, GuideQueryParam, Guide, GuideQueryArg> {

    private final GuideDao dao;
    private final GuideConverter converter;

    public GuideReadComponentImpl(GuideDao dao, GuideConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }

    @Override
    public List<Guide> fuzzyQueryGuideList(String fuzzyName, String corpCode) {
        return converter.r_ds2ms(dao.fuzzyQueryGuideList(fuzzyName, corpCode));
    }
}