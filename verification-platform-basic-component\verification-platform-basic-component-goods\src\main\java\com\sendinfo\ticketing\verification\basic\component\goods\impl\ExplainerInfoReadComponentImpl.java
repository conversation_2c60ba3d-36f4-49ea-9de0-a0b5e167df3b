package com.sendinfo.ticketing.verification.basic.component.goods.impl;

import com.sendinfo.ticketing.verification.basic.component.goods.ExplainerInfoReadComponent;
import com.sendinfo.ticketing.verification.basic.component.goods.converter.ExplainerInfoConverter;
import com.sendinfo.ticketing.verification.basic.component.goods.param.ExplainerInfoQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.ExplainerInfo;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.ExplainerInfoQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dao.ExplainerInfoDao;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.ExplainerInfoDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedCountRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedListRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleRead;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 讲解人读取组件实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("explainerInfoReadComponent")
@Getter
public class ExplainerInfoReadComponentImpl implements ExplainerInfoReadComponent,
        DaoBasedSingleRead<Long, ExplainerInfoQueryParam, ExplainerInfo, ExplainerInfoDO>,
        DaoBasedListRead<Long, ExplainerInfoQueryParam, ExplainerInfo, ExplainerInfoDO, ExplainerInfoQueryArg>,
        DaoBasedCountRead<Long, ExplainerInfoQueryParam, ExplainerInfo, ExplainerInfoQueryArg> {

    private final ExplainerInfoDao dao;
    private final ExplainerInfoConverter converter;

    public ExplainerInfoReadComponentImpl(ExplainerInfoDao dao,
                                          ExplainerInfoConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }

    @Override
    public List<ExplainerInfo> queryExplainerList(ExplainerInfoQueryParam queryParam) {
        List<ExplainerInfoDO> doList = dao.queryExplainerList(converter.r_p2a(queryParam));
        return doList.stream().map(converter::r_d2m).collect(Collectors.toList());
    }
} 