package com.sendinfo.ticketing.verification.basic.model.pay;

import com.sendinfo.ticketing.verification.basic.model.pay.properties.SysMainbodyManageSubMerchantsDefinitionPropertyKey;
import com.sendinfo.ticketing.verification.common.model.properties.AbstractProperties;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 经营主体子商户模型对象
 * 用于业务逻辑层和数据展示层之间的数据传输
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class SysMainbodyManageSubMerchants extends AbstractProperties<SysMainbodyManageSubMerchantsDefinitionPropertyKey> implements Serializable {

    private static final long serialVersionUID = -1620093990466284302L;
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 子商户名称
     */
    private String subMerchantsName;

    /**
     * 子商户号
     */
    private String merchantNo;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String modifyBy;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 删除标志F未删除,T已删除
     */
    private String deleted;
} 