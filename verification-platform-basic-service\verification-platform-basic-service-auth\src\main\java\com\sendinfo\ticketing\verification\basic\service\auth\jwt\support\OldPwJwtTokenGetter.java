package com.sendinfo.ticketing.verification.basic.service.auth.jwt.support;

import com.sendinfo.ticketing.verification.basic.service.auth.config.OldPwJwtConfigurationProperties;
import com.sendinfo.ticketing.verification.basic.service.auth.jwt.JwtIdentity;
import com.sendinfo.ticketing.verification.basic.service.auth.jwt.JwtTokenGetter;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2025-07-23 14:32:32
 */
@Component("oldPwJwtTokenGetter")
public class OldPwJwtTokenGetter implements JwtTokenGetter {
    private final OldPwJwtConfigurationProperties properties;

    public OldPwJwtTokenGetter(OldPwJwtConfigurationProperties oldPwJwtConfigurationProperties) {
        this.properties = oldPwJwtConfigurationProperties;
    }

    @Override
    public JwtIdentity support() {
        return JwtIdentity.OLD_PW;
    }

    @Override
    public Optional<String> getJwtToken(Map<String, String> headers) {
        String headKey = properties.getHeadKey();
        String token = headers.get(headKey);
        return Optional.ofNullable(token);
    }
}
