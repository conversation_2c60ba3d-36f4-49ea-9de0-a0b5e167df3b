package com.sendinfo.ticketing.verification.basic.repository.pay.arg;

import com.sendinfo.ticketing.verification.common.repository.arg.AbstractUpdateArg;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 经营主体支付配置关联子商户表更新参数
 * <p>
 * 本类用于封装sys_mainbody_manage_mm_sub_merchants表的更新条件，
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class SysMainbodyManageMmSubMerchantsUpdateArg extends AbstractUpdateArg<Long> {

    /**
     * 企业编码
     * 用于租户隔离，必填字段
     */
    private String corpCode;

    /**
     * 经营主体支付配置ID
     */
    private Long mainbodyManageId;

    /**
     * 子商户名称
     * 子商户的显示名称
     */
    private String subMerchantsName;

    /**
     * 子商户ID
     */
    private Long subMerchantsId;

    /**
     * 子商户号
     * 子商户的唯一标识号
     */
    private String merchantNo;

    /**
     * 修改人
     * 记录最后修改操作的用户
     */
    private String modifyBy;
} 