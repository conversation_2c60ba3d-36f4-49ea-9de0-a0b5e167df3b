package com.sendinfo.ticketing.verification.basic.component.goods.param;

import com.sendinfo.ticketing.verification.common.component.param.AbstractCreateParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 服务类目计费规则创建参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class ExplainerPayInfoCreateParam extends AbstractCreateParam {

    /** 企业编码 */
    private String corpCode;
    /** 服务类目ID */
    private Long serviceCategoryId;
    /** 支付名称 */
    private String payName;
    /** 计费方式(票务字典) */
    private String payType;
    /** 金额 */
    private BigDecimal price;
    /** 状态 */
    private String useFlag;
    /** 备注 */
    private String remark;
    /** 创建人 */
    private String createBy;
} 