package com.sendinfo.ticketing.verification.basic.api.infra.request;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 系统参数定义查询条件
 */
@Getter
@Setter
@ToString
public class SysParamDefinitionQueryCondition implements Serializable {
    private static final long serialVersionUID = -4876543210123456789L;
    /**
     * 企业编码
     */
    @NotBlank(message = "企业编码不能为空")
    private String corpCode;

    /**
     * 参数编码
     */
    @Size(max = 100, message = "参数编码长度不能超过100")
    private String paramCode;

    /**
     * 组件编码
     */
    @Size(max = 64, message = "组件编码长度不能超过64")
    private String componentCode;

    /**
     * 参数名称(模糊查询)
     */
    @Size(max = 100, message = "参数名称长度不能超过100")
    private String paramName;

    /**
     * 状态：1-启用，0-禁用
     */
    private Integer status;
}