package com.sendinfo.ticketing.verification.basic.repository.pay.dao;

import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysPaySetDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysPaySetQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysPaySetUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysPaySetDO;
import com.sendinfo.ticketing.verification.common.repository.BatchInsertDAO;
import com.sendinfo.ticketing.verification.common.repository.CountableDAO;
import com.sendinfo.ticketing.verification.common.repository.GenericDAO;
import com.sendinfo.ticketing.verification.common.repository.QueryableDAO;

import java.util.List;
import java.util.Set;

/**
 * 支付标签数据访问接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface SysPaySetDao extends
        GenericDAO<Long, SysPaySetDO, SysPaySetUpdateArg, SysPaySetDeleteArg>,
        CountableDAO<SysPaySetQueryArg>,
        QueryableDAO<SysPaySetQueryArg, SysPaySetDO>,
        BatchInsertDAO<Long, SysPaySetDO> {

    /**
     * 查询启用的支付标签
     *
     * @param payType  支付类型
     * @param idSet    主键集合
     * @param corpCode 企业编码
     * @return 启用的支付标签
     */
    List<SysPaySetDO> queryEnableListByPayTypeAndId(String payType, Set<Long> idSet, String corpCode);
}