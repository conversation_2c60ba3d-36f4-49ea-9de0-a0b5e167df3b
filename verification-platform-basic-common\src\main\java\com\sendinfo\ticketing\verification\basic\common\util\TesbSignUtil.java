package com.sendinfo.ticketing.verification.basic.common.util;

import com.sendinfo.ticketing.verification.basic.common.log.VerificationPlatformBasicLoggers;
import lombok.Getter;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

public class TesbSignUtil {


    private TesbSignUtil() {
        throw new UnsupportedOperationException("Utility class");
    }

    /**
     * 计算签名
     *
     * @param esbSecret 密钥
     * @param paramMap  参数映射
     * @return 签名值
     * @throws IllegalArgumentException 如果入参为空
     */
    public static String getSign(String esbSecret, Map<String, String> paramMap) {
        // 参数校验
        if (StringUtils.isBlank(esbSecret)) {
            throw new IllegalArgumentException("ESB secret cannot be empty");
        }
        if (paramMap == null || paramMap.isEmpty()) {
            throw new IllegalArgumentException("Parameter map cannot be empty");
        }

        try {
            // 创建参数Map副本，避免修改原始数据
            Map<String, String> sortedParams = new HashMap<>(paramMap);
            sortedParams.remove(TesbRequestEnum.SIGN.getValue());

            // 构建签名字符串
            String signContent = buildSignContent(esbSecret, sortedParams);

            // 计算MD5并转大写
            String sign = new String(DigestUtils.md5(signContent), StandardCharsets.UTF_8).toUpperCase();

            VerificationPlatformBasicLoggers.API_SERVICE_LOGGER.debug("Generated sign for params: {}, sign: {}", sortedParams, sign);
            return sign;

        } catch (Exception e) {
            VerificationPlatformBasicLoggers.API_SERVICE_LOGGER.error("Failed to generate sign for params: {}", paramMap, e);
            throw new IllegalArgumentException("Failed to generate sign", e);
        }
    }

    /**
     * 构建签名内容字符串
     */
    private static String buildSignContent(String secret, Map<String, String> params) {
        StringBuilder content = new StringBuilder(secret);

        // 参数按键名自然排序
        params.entrySet().stream()
                .filter(entry -> StringUtils.isNotBlank(entry.getValue())) // 过滤空值
                .sorted(Map.Entry.comparingByKey())
                .forEach(entry -> content
                        .append(entry.getKey())
                        .append(entry.getValue()));

        content.append(secret);

        return content.toString();
    }


    /**
     * ESB请求参数枚举
     */
    @Getter
    public enum TesbRequestEnum {
        SIGN("sign", "签名"),
        APP_KEY("appKey", "应用标识"),
        TIMESTAMP("timestamp", "时间戳"),
        VERSION("version", "接口版本"),
        ;

        private final String value;
        private final String description;

        TesbRequestEnum(String value, String description) {
            this.value = value;
            this.description = description;
        }
    }
}
