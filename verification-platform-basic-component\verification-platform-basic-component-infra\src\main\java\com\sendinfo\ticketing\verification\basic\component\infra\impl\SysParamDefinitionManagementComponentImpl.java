package com.sendinfo.ticketing.verification.basic.component.infra.impl;

import lombok.Getter;
import org.springframework.stereotype.Component;

import com.sendinfo.ticketing.verification.basic.component.infra.SysParamDefinitionManagementComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.converter.SysParamDefinitionConverter;
import com.sendinfo.ticketing.verification.basic.component.infra.param.SysParamDefinitionUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.infra.SysParamDefinition;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.SysParamDefinitionUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.SysParamDefinitionDao;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleUpdate;

@Component("sysParamDefinitionManagementComponent")
@Getter
public class SysParamDefinitionManagementComponentImpl
        implements SysParamDefinitionManagementComponent,
        DaoBasedSingleUpdate<SysParamDefinitionUpdateParam, SysParamDefinitionUpdateArg, SysParamDefinition> {

    private final SysParamDefinitionDao dao;
    private final SysParamDefinitionConverter converter;

    public SysParamDefinitionManagementComponentImpl(SysParamDefinitionDao dao, SysParamDefinitionConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }
} 