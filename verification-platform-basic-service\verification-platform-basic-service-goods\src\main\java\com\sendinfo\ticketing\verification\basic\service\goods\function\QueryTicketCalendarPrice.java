package com.sendinfo.ticketing.verification.basic.service.goods.function;

import com.sendinfo.ticketing.verification.basic.component.goods.TicketCalendarPriceReadComponent;
import com.sendinfo.ticketing.verification.basic.model.goods.TicketCalendarPrice;
import com.sendinfo.ticketing.verification.common.service.stereotype.Function;
import com.sendinfo.ticketing.verification.flow.Hint;
import com.sendinfo.ticketing.verification.flow.Question;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;

import static com.sendinfo.ticketing.verification.basic.service.goods.enums.GoodsAttachmentKey.TICKET_CALENDAR_PRICE_DATA_LIST_ATTACHMENT_KEY;

/**
 * <AUTHOR>
 * @since 2025/7/28 13:47
 **/
@Function("queryTicketCalendarPrice")
public class QueryTicketCalendarPrice {

    private final TicketCalendarPriceReadComponent ticketCalendarPriceReadComponent;

    public QueryTicketCalendarPrice(TicketCalendarPriceReadComponent ticketCalendarPriceReadComponent) {
        this.ticketCalendarPriceReadComponent = ticketCalendarPriceReadComponent;
    }

    public Hint queryTicketCalendarPrice(Question<Pair<List<Long>,String>> question) {
        Pair<List<Long>, String> body = question.getBody();
        List<Long> ticketIds = body.getLeft();
        String corpCode = body.getRight();
        List<TicketCalendarPrice> ticketCalendarPrices =
                ticketCalendarPriceReadComponent.batchQueryByTicketIds(ticketIds, corpCode);

        question.setAttachment(TICKET_CALENDAR_PRICE_DATA_LIST_ATTACHMENT_KEY, ticketCalendarPrices);
        return Hint.gotoNext();
    }
}
