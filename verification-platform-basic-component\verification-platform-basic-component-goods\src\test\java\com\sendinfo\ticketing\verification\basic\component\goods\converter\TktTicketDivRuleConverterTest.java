package com.sendinfo.ticketing.verification.basic.component.goods.converter;

import com.sendinfo.ticketing.verification.basic.component.goods.param.TktTicketDivRuleQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TktTicketDivRule;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TktTicketDivRuleQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TktTicketDivRuleDO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

/**
 * @Auther: chengjie.li
 * @Date: 2025/6/18 12:30
 */
@RunWith(MockitoJUnitRunner.class)
public class TktTicketDivRuleConverterTest {

    @InjectMocks
    private TktTicketDivRuleConverter tktTicketDivRuleConverter;

    private static final String TEST_CORP_CODE = "TEST_CORP_001";
    private static final Long TEST_TICKET_ID = 1L;
    private static final String TEST_TICKET_CODE = "TEST_TICKET_001";
    private static final String TEST_GROUP_FLAG = "T";
    private static final Integer TEST_PARK_ID = 100;
    private static final String TEST_PARK_NAME = "TEST_PARK_NAME";
    private static final String TEST_TICKET_TYPE = "门票";
    private static final String TEST_DIV_METHOD = "0";
    private static final BigDecimal TEST_DIV_MONEY = new BigDecimal("10.50");
    private static final Integer TEST_DECIMAL_PROCESS_METHOD = 0;
    private static final BigDecimal TEST_PARK_PRICE = new BigDecimal("50.00");
    private static final Integer TEST_PRIORITY = 1;
    private static final Integer TEST_USE_FLAG = 1;
    private static final String TEST_DIV_MUST_FLAG = "T";
    private static final String TEST_DIV_CAMPSUR_FLAG = "F";
    private static final Integer TEST_CAMPSUR_SCALE = 80;
    private static final BigDecimal TEST_TRAVEL_DIV_MONEY = new BigDecimal("5.00");

    /**
     * 测试目的：验证TktTicketDivRuleDO转换为TktTicketDivRule的功能
     * 测试步骤：
     * 1. 创建完整的TktTicketDivRuleDO对象
     * 2. 调用r_d2m方法进行转换
     * 3. 验证转换结果的正确性
     */
    @Test
    public void testReadDo2Model_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        TktTicketDivRuleDO tktTicketDivRuleDO = createTktTicketDivRuleDO();

        // 2. 执行转换
        TktTicketDivRule result = tktTicketDivRuleConverter.r_d2m(tktTicketDivRuleDO);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", result);
        // assertEquals("企业编码应该一致", TEST_CORP_CODE, result.getCorpCode()); // 可能不可用
        assertEquals("票型ID应该一致", TEST_TICKET_ID, result.getTicketId());
        assertEquals("票型编号应该一致", TEST_TICKET_CODE, result.getTicketCode());
        assertEquals("是否景点组应该一致", TEST_GROUP_FLAG, result.getGroupFlag());
        assertEquals("景点ID应该一致", TEST_PARK_ID, result.getParkId());
        assertEquals("景区名称应该一致", TEST_PARK_NAME, result.getParkName());
        assertEquals("票型类型应该一致", TEST_TICKET_TYPE, result.getTicketType());
        assertEquals("分成方式应该一致", TEST_DIV_METHOD, result.getDivMethod());
        assertEquals("分成金额应该一致", TEST_DIV_MONEY, result.getDivMoney());
        assertEquals("分成小数处理方式应该一致", TEST_DECIMAL_PROCESS_METHOD, result.getDecimalProcessMethod());
        assertEquals("单景点价格应该一致", TEST_PARK_PRICE, result.getParkPrice());
        assertEquals("分成优先级应该一致", TEST_PRIORITY, result.getPriority());
        assertEquals("启用标志应该一致", TEST_USE_FLAG, result.getUseFlag());
        assertEquals("是否必定分成点应该一致", TEST_DIV_MUST_FLAG, result.getDivMustFlag());
        assertEquals("是否盈余分成点应该一致", TEST_DIV_CAMPSUR_FLAG, result.getDivCampsurFlag());
        assertEquals("盈余分成比例应该一致", TEST_CAMPSUR_SCALE, result.getCampsurScale());
        assertEquals("旅代分成份额应该一致", TEST_TRAVEL_DIV_MONEY, result.getTravelDivMoney());
    }

    /**
     * 测试目的：验证TktTicketDivRuleQueryParam转换为TktTicketDivRuleQueryArg的功能
     * 测试步骤：
     * 1. 创建完整的TktTicketDivRuleQueryParam对象
     * 2. 调用r_p2a方法进行转换
     * 3. 验证转换结果的正确性
     */
    @Test
    public void testReadQueryParam2QueryArg_ShouldConvertSuccessfully() {
        // 1. 创建测试输入数据
        TktTicketDivRuleQueryParam queryParam = createTktTicketDivRuleQueryParam();

        // 2. 执行转换
        TktTicketDivRuleQueryArg queryArg = tktTicketDivRuleConverter.r_p2a(queryParam);

        // 3. 验证转换结果
        assertNotNull("转换结果不应为null", queryArg);
        assertEquals("企业编码应该一致", TEST_CORP_CODE, queryArg.getCorpCode());
        assertEquals("票型ID应该一致", TEST_TICKET_ID, queryArg.getTicketId());
    }

    private TktTicketDivRuleQueryParam createTktTicketDivRuleQueryParam() {
        TktTicketDivRuleQueryParam queryParam = new TktTicketDivRuleQueryParam();
        queryParam.setCorpCode(TEST_CORP_CODE);
        queryParam.setTicketId(TEST_TICKET_ID);
        queryParam.setTicketCode(TEST_TICKET_CODE);
        queryParam.setGroupFlag(TEST_GROUP_FLAG);
        queryParam.setParkId(TEST_PARK_ID);
        queryParam.setParkName(TEST_PARK_NAME);
        queryParam.setTicketType(TEST_TICKET_TYPE);
        queryParam.setDivMethod(TEST_DIV_METHOD);
        queryParam.setUseFlag(TEST_USE_FLAG.shortValue());
        queryParam.setDivMustFlag(TEST_DIV_MUST_FLAG);
        queryParam.setDivCampsurFlag(TEST_DIV_CAMPSUR_FLAG);
        return queryParam;
    }

    private TktTicketDivRuleDO createTktTicketDivRuleDO() {
        TktTicketDivRuleDO divRuleDO = new TktTicketDivRuleDO();
        divRuleDO.setCorpCode(TEST_CORP_CODE);
        divRuleDO.setTicketId(TEST_TICKET_ID);
        divRuleDO.setTicketCode(TEST_TICKET_CODE);
        divRuleDO.setGroupFlag(TEST_GROUP_FLAG);
        divRuleDO.setParkId(TEST_PARK_ID);
        divRuleDO.setParkName(TEST_PARK_NAME);
        divRuleDO.setTicketType(TEST_TICKET_TYPE);
        divRuleDO.setDivMethod(TEST_DIV_METHOD);
        divRuleDO.setDivMoney(TEST_DIV_MONEY);
        divRuleDO.setDecimalProcessMethod(TEST_DECIMAL_PROCESS_METHOD);
        divRuleDO.setParkPrice(TEST_PARK_PRICE);
        divRuleDO.setPriority(TEST_PRIORITY);
        divRuleDO.setUseFlag(TEST_USE_FLAG);
        divRuleDO.setDivMustFlag(TEST_DIV_MUST_FLAG);
        divRuleDO.setDivCampsurFlag(TEST_DIV_CAMPSUR_FLAG);
        divRuleDO.setCampsurScale(TEST_CAMPSUR_SCALE);
        divRuleDO.setTravelDivMoney(TEST_TRAVEL_DIV_MONEY);
        return divRuleDO;
    }
} 